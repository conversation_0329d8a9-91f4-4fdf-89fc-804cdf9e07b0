ARG DOCKER_VERSION=28.1.1
ARG ALPINE_VERSION=3.21
ARG DOCKER_BASE_IMAGE=docker:${DOCKER_VERSION}-alpine${ALPINE_VERSION}

FROM ${DOCKER_BASE_IMAGE} AS static-docker-source
FROM gcr.io/google.com/cloudsdktool/google-cloud-cli:alpine
COPY --from=static-docker-source /usr/local/libexec/docker/cli-plugins/docker-buildx /usr/local/libexec/docker/cli-plugins/docker-buildx


#
# ---- Base Node ----  ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
#FROM ${DOCKER_BASE_IMAGE} AS base
RUN    apk add --no-cache \
         bash

# Don't run production as root
#RUN   addgroup --system --gid 1000 allo
#RUN   adduser --system --uid 1000 allo

#USER allo
# ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
#

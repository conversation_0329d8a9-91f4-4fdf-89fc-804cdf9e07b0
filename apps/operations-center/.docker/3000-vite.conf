server {
    server_tokens   off;

    listen          3000;
    server_name     localhost;
    #root            /usr/share/nginx/html;
    root            /tmp/nginx/html;
    include         mime.types;
    charset         UTF-8;
    source_charset  UTF-8;

    gzip            on;
    gzip_vary       on;
    gzip_http_version  1.1;
    gzip_comp_level 5;
    gzip_types
                    application/atom+xml
                    application/javascript
                    application/json
                    application/rss+xml
                    application/vnd.ms-fontobject
                    application/x-font-ttf
                    application/x-web-app-manifest+json
                    application/xhtml+xml
                    application/xml
                    font/opentype
                    image/svg+xml
                    image/x-icon
                    text/css
                    text/plain
                    text/x-component;
    gzip_proxied    no-cache no-store private expired auth;
    gzip_min_length 32;
    gunzip          on;

    location = /healthz/stub_status {
        access_log   off;
        allow        127.0.0.1;
        deny all;
        default_type text/plain;
        stub_status  on;
    }

    location = /healthz {
        access_log   off;
        default_type text/html;
        return 200;
    }

    location = /assets/remoteEntry.js {
        expires off;
        add_header 'Access-Control-Allow-Origin' '*';
        add_header Cache-Control "public, max-age=0, s-maxage=0, must-revalidate" always;
        try_files $uri =404;
    }
    location ^~ /assets/ {
        add_header 'Access-Control-Allow-Origin' '*';
        add_header Cache-Control "public, max-age=31536000, s-maxage=31536000, immutable";
        try_files $uri =404;
    }
    location / {
        autoindex off;
        expires off;
        add_header 'Access-Control-Allow-Origin' '*';
        add_header Cache-Control "public, max-age=0, s-maxage=0, must-revalidate" always;
        index index.html;
        try_files $uri $uri/ /index.html =404;
    }

}

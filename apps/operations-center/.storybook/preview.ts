import type { Preview } from "@storybook/react";
import { withTheme } from "./decorators";
import "../src/globals.css";
import "@fontsource-variable/inter";
import "@fontsource-variable/bricolage-grotesque";

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    backgrounds: {
      default: "light",
      values: [
        {
          name: "light",
          value: "#fafafa",
        },
        {
          name: "dark",
          value: "#1a1a1a",
        },
      ],
    },
  },

  tags: ["autodocs"],
};

export default preview;

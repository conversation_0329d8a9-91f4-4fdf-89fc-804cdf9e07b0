# allO POS prototype

## Getting Started

First, run the development server:

```bash
npm run dev
```

Open [http://localhost:3001](http://localhost:3001) with your browser to see the result.

## Mock Data Structure

### Entities

Check `src/lib/mock-data/...` for properties of the entities described below.

| Entity               | Description                                                                                                                                                                                     |
| -------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Space                | An area within a restaurant (e.g., main room, patio, wine bar).                                                                                                                                 |
| Table                | Physical dining locations where customers are seated, associated with a specific `Space`.                                                                                                       |
| Menu                 | Restaurant's menu structure. An array of `MenuSuperFolder` objects.                                                                                                                             |
| MenuSuperFolder      | Top-level menu categories (with emoji and color), which contain an array of `Product` or `MenuFolder` items.                                                                                    |
| MenuFolder           | A sub-category within the menu structure. Contains either nested `MenuFolder` objects or `Product` items.                                                                                       |
| Product              | An individual menu item with price, configuration options, and suggested notes.                                                                                                                 |
| ProductConfiguration | Group of available options for a `Product` (e.g., cooking preferences, sides). Contains an array of `ProductOption` objects.                                                                    |
| ProductOption        | Individual configuration option for a `Product` with its own price and quantity settings.                                                                                                       |
| CartItem             | Represents a pending configured `Product` (with options, notes, and quantity) that hasn't yet been ordered.                                                                                     |
| OrderItem            | Represents a configured `Product` that has been sent to the kitchen. Includes additional information such as `createdAt` timestamp and an `isCancelled` flag.                                   |
| Order                | Represents a customer's (or group of customers') visit to the restaurant. Created when an empty table is accessed, but only assigned to a waiter when the first item(s) is sent to the kitchen. |

### State Management

Pending Cart Items (items not yet sent to kitchen) are tracked and managed locally (`src/lib/cart/store.ts`), and only sent to the server when ordered.

The server state (`src/lib/mock-data/server-state.ts`) maintains:

- Active dining sessions
- Order history
- Waiter assignments
- Table status

### API Simulation

The mock API (`src/lib/mock-data/api.ts`) includes:

- Simulated network delays (32-1024ms)
- Optional error simulation
- Standard REST endpoints for all operations

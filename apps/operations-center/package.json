{"name": "operations-center", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev:federation": "FEDERATION=true concurrently \"vite build --watch\" \"vite preview --port 3002\"", "typecheck": "tsc --noEmit", "dev": "vite --port=3002", "build": "vite build", "serve": "vite preview", "start": "vite", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write ."}, "dependencies": {"@allo/ui": "^0.0.46", "@fontsource-variable/bricolage-grotesque": "^5.2.5", "@fontsource-variable/inter": "^5.2.5", "@tailwindcss/vite": "4.0.17", "@tanstack/react-query": "~5.71.5", "@tanstack/react-router": "~1.114.25", "@tanstack/router-devtools": "~1.114.25", "lucide-react": "^0.476.0", "motion": "^12.5.0", "postcss-preset-env": "^10.2.0", "posthog-js": "^1.235.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-error-boundary": "^5.0.0", "react-swipeable": "^7.0.2", "tailwindcss": "4.0.17", "vite-plugin-pwa": "^0.21.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@originjs/vite-plugin-federation": "^1.4.1", "@tanstack/router-plugin": "^1.114.25", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-react": "^4.3.4", "chroma-js": "^3.1.2", "concurrently": "^9.1.2", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-tsconfig-paths": "^5.1.4"}, "browserslist": ["Chrome >= 64", "Firefox >= 58", "Safari >= 11.1", "Edge >= 17", "Opera >= 50"]}
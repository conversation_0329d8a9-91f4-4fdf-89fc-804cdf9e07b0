import valueParser from "postcss-value-parser";
import postcssPresetEnv from "postcss-preset-env";
import postcss, { type Plugin } from "postcss";
// @ts-expect-error chroma-js is not typed
import chroma from "chroma-js";

// replaces infinity with 9999999
const infinityKeyword = (): Plugin => {
  const INFINITY_REPLACEMENT = "9999999";

  return {
    postcssPlugin: "postcss-infinity-keyword",
    Once(root) {
      root.walkDecls((decl) => {
        if (/infinity/.test(decl.value)) {
          if (decl.value === "infinity") {
            decl.value = INFINITY_REPLACEMENT;
          } else {
            const parsed = valueParser(decl.value);
            let hasChanged = false;

            parsed.walk((node) => {
              if (node.type === "word" && node.value === "infinity") {
                node.value = INFINITY_REPLACEMENT;
                hasChanged = true;
              }
            });

            if (hasChanged) {
              decl.value = parsed.toString();
            }
          }
        }
      });
    },
  };
};

// replaces rotate, scale, and translate shortcuts (e.g. translate: (...)) with 3d transforms
const transformShortcuts = (): Plugin => {
  const TRANSFORM_DEFAULTS = {
    rotate: [0, 0, 1, "0deg"],
    scale: [1, 1, 1],
    translate: [0, 0, 0],
  };

  const getTransformFragments = (value: string): string[] => {
    // check if value is a series of non-separated css variables, which can happen after css minification
    if (/^(var\(--[^)]+\)){2,}$/.test(value)) {
      return value.match(/var\(.+?\)/g) || [];
    }

    // otherwise, split by whitespace
    return value.split(/[\s\n\t]+/);
  };

  return {
    postcssPlugin: "postcss-transform-shortcut",
    Once(root) {
      const fallbackAtRule = postcss.atRule({
        name: "supports",
        params: "not (translate: 0)",
      });

      root.walkRules((rule) => {
        const transforms: string[] = [];

        rule.walkDecls(({ prop, value }) => {
          if (/^(rotate|scale|translate)$/.test(prop)) {
            const values = getTransformFragments(value);

            const values3d = [
              ...TRANSFORM_DEFAULTS[prop as keyof typeof TRANSFORM_DEFAULTS],
            ];

            if (prop === "rotate" && values?.length === 1) {
              values3d.splice(-1, 1, ...values);
            } else {
              values3d.splice(0, values?.length, ...values);
            }

            transforms.push(`${prop}3d(${values3d.join(",")})`);
          }
        });

        if (transforms?.length) {
          const fallbackRule = postcss.rule({
            selector: rule.selector,
          });

          fallbackRule.append({
            prop: "transform",
            value: transforms.join(" "),
          });

          fallbackAtRule.append(fallbackRule);
        }
      });

      if (fallbackAtRule.nodes?.length) {
        root.append(fallbackAtRule);
      }
    },
  };
};

// adds fallbacks for dynamic viewport units (dvh, lvh, svh, dvw)
const dynamicViewportUnits = (): Plugin => {
  const UNITS = {
    dvh: "vh",
    lvh: "vh",
    svh: "vh",
    dvw: "vw",
  };

  const HAS_UNIT_REGEX = new RegExp(`(${Object.keys(UNITS).join("|")})`, "g");

  return {
    postcssPlugin: "postcss-dynamic-viewport-units",
    Once(root) {
      root.walkRules((rule) => {
        rule.walkDecls((decl) => {
          if (HAS_UNIT_REGEX.test(decl.value)) {
            const units = decl.value.match(HAS_UNIT_REGEX);
            if (!units) return;

            const fallbackValue = units.reduce((acc, unit) => {
              return acc.replace(unit, UNITS[unit as keyof typeof UNITS]);
            }, decl.value);

            const fallbackDeclaration = postcss.decl({
              prop: decl.prop,
              value: fallbackValue,
            });

            const fallbackRule = postcss.rule({
              selector: rule.selector,
            });

            const fallbackAtRule = postcss.atRule({
              name: "supports",
              params: `${units.map((unit) => `(not (${decl.prop}: 1${unit}))`).join(" or ")}`,
            });

            fallbackRule.append(fallbackDeclaration);
            fallbackAtRule.append(fallbackRule);
            root.append(fallbackAtRule);
          }
        });
      });
    },
  };
};

// ensure empty fallbacks in css variables have a space: (e.g. 'var(--color,)' -> 'var(--color, )')
const emptyVarFallback = (): Plugin => {
  return {
    postcssPlugin: "postcss-empty-var-fallback",
    OnceExit(root) {
      const EMPTY_FALLBACK_VAR_REGEX = /var\(--.*?,\)/g;
      root.walkDecls((decl) => {
        if (EMPTY_FALLBACK_VAR_REGEX.test(decl.value)) {
          const cssVars = decl.value.match(EMPTY_FALLBACK_VAR_REGEX);
          if (!cssVars) return;

          const value = cssVars.reduce((acc, cssVar) => {
            return acc.replace(cssVar, cssVar.replace(",)", ", )"));
          }, decl.value);

          decl.value = value;
        }
      });
    },
  };
};

// converts tailwind color tokens from oklch (with color-mix opacity modifier) to hsl
/**
 * The s700 lacks support for the modern color formats we're using (oklch and color-mix).
 * Because the UI defines themes (light and dark) that contextually override the color css variables, we can't simply
 * do a direct conversion of the colors to a supported format (because at any given usage, we can't know which theme is being inherited).
 *
 * This approach converts the color tokens to HSL values, and ensures every token usage is wrapped in the hsl(...) function.
 * For the opacity modifiers (e.g. text-accent/60), the alpha values are appended to the hsl(...) function (e.g. hsl(var(--color-accent) / 0.6)).
 * The benefit of this approach is that the actual color data is still defined in a css variable, and inherited from the set theme.
 *
 * Limitations:
 * - It only works with tokens defined in the theme using oklch or color-mix format.
 * - Doesn't work with custom color variables defined outside @theme (e.g. '[--my-color: var(--color-accent)]  bg-[--alpha(var(--my-color), 50%)]' wouldn't work)
 * - Only compatible with Tailwind v4.0.*. In v4.1.*, Tailwind handles color-mix differently: it places color-mix inside @supports rules, and adds a fallback using the base token without any opacity modifier.
 *   The problem then is that the CSS bundler (or another tool, I'm not sure) then sometimes combines classes with and without opacity into a single rule, because they map to the same CSS declaration:
 *
 *        .bg-foreground,
 *        .bg-foreground/5 {
 *          background-color: var(--color-foreground);
 *        }
 *
 *   This makes it pretty hard to polyfill the opacity modifier, since we can't separate the rules.
 *   When this issue is fixed and we upgrade, this polyfill has to be updated to transform the fallback values instead of color-mix(...) value.
 *   Or, if they add a config to disable this color-mix @supports feature, we can just upgrade without any changes to this code.
 *
 */
const tailwindHslColors = (): Plugin => {
  const CSS_COLOR_ALIASES = {
    white: "0deg 0% 100%",
    black: "0deg 0% 0%",
  } as const;

  const tokens = new Map<string, { hsl: string; alpha: number }>();
  let tokenNameUnion = "";

  const isSupportedColorFormat = (value: string) => {
    return (
      /color-mix.*oklch\(.*\).*transparent/.test(value) ||
      /color-mix.*var\(--color-.*\).*transparent/.test(value) ||
      /oklch/.test(value)
    );
  };

  const extractOklch = (str: string): [number, number, number] | null => {
    const match = str.match(/oklch\(([\d.]+)%\s+([\d.]+)\s+([\d.]+)\)/);
    if (!match) return null;

    return [parseFloat(match[1]), parseFloat(match[2]), parseFloat(match[3])];
  };

  return {
    postcssPlugin: "postcss-tailwind-hsl-colors",
    Once(root) {
      // convert all oklch tokens to hsl values ([hue]deg [saturation]% [lightness]%)
      root.walkDecls((declaration) => {
        const { prop, value } = declaration;
        const [, name] = /--color-(.*)/.exec(prop) || [];

        if (name) {
          // check for oklch colors declarations
          if (isSupportedColorFormat(value)) {
            let hsl: string | null = null;

            const oklch = extractOklch(value);
            const referenceToken = /var\(--color-([^)]+)\)/.exec(value)?.[1];

            // if the color is oklch
            if (oklch) {
              const [h, s, l] = chroma
                .oklch(oklch[0] / 100, oklch[1], oklch[2])
                .hsl();

              hsl = `${h}deg ${s * 100}% ${l * 100}%`;
            }

            // is color is a reference to another token
            if (referenceToken && tokens.has(referenceToken)) {
              hsl = tokens.get(referenceToken)!.hsl;
            }

            if (!hsl) {
              return console.warn(
                `[postcss-tailwind-hsl-colors]: Could not resolve color of token '--color-${name}: ${value}'.`
              );
            }

            const [, strAlpha] = /(\d+)%\s*,\s*transparent/.exec(value) || [];
            const alpha = strAlpha ? Number(strAlpha) / 100 : 1;

            declaration.value = hsl;
            tokens.set(name, { hsl, alpha });

            return;
          }

          // aliases
          if (value in CSS_COLOR_ALIASES) {
            declaration.value =
              CSS_COLOR_ALIASES[value as keyof typeof CSS_COLOR_ALIASES];
            tokens.set(name, { hsl: declaration.value, alpha: 1 });

            return;
          }

          if (/var\(--color-.*\)/.test(value)) {
            return;
          }

          return console.warn(
            `[postcss-tailwind-hsl-colors]: Unsupported color format on '--color-${name}: ${value}'. Please use oklch() or color-mix(... var(--color-...)).`
          );
        }
      });

      tokenNameUnion = [...tokens.keys()].join("|");

      // replace all uses of color tokens with hsl(var(--color))
      root.walkDecls((declaration) => {
        const { prop, value } = declaration;

        // escape when used on other color tokens (.e.g --color-*: var(--color-*))
        // otherwise we'll end up with hsl(hsl(var(--color-)))
        if (/--color-/.test(prop)) return;

        if (/var\(--color/.test(value)) {
          const foundTokens = Array.from(
            value.matchAll(/var\(--color-([^)]+)\)/g),
            (match) => match[1]
          );

          const transformedValue = foundTokens.reduce((acc, tokenName) => {
            const token = tokens.get(tokenName);
            if (!token) return acc;

            return acc.replace(
              `var(--color-${tokenName})`,
              `hsl(var(--color-${tokenName})${token.alpha !== 1 ? ` / ${token.alpha}` : ""})`
            );
          }, value);

          declaration.value = transformedValue;
        }
      });
    },

    // replace all instances of opacity modifiers with hsl(var(--color-[token-name]) / <alpha-value>)
    Rule(rule) {
      const selector = rule.selector.replace(/\\/g, "");

      const classModifierMatch = new RegExp(
        `(${tokenNameUnion})/([0-9]+)`
      ).exec(selector);

      const utilityMatch = new RegExp(
        `--alpha\\(var\\(--color-(${tokenNameUnion})\\)/([0-9]+).*`
      ).exec(selector);

      const match = classModifierMatch || utilityMatch;
      if (!match) return;

      const [, tokenName, alpha] = match;
      const token = tokens.get(tokenName);
      if (!token) return;

      const tokenColorMixRegex = new RegExp(
        `color-mix\\(.+?(var\\(--color-${tokenName}\\)|${tokenName}).+?\\)`
      );

      const oklabRegex = new RegExp(`oklab\\((.*)\\)`);

      let changed = false;
      rule.walkDecls((decl) => {
        // find the declaration that contains the opacity modifier
        const [instance] =
          decl.value.match(tokenColorMixRegex) ||
          decl.value.match(oklabRegex) ||
          [];

        if (!instance) {
          // if it already includes the transformed color token, we can assume it's already been polyfilled
          // I found these second-passes to be common on rules with pseudo-classes
          if (decl.value.includes(`hsl(var(--color-${tokenName}) / `)) {
            changed = true;
          }

          return;
        }

        decl.value = decl.value.replace(
          instance,
          `hsl(var(--color-${tokenName}) / ${(token.alpha * Number(alpha)) / 100})`
        );

        changed = true;
      });

      if (!changed) {
        console.warn(
          `[postcss-tailwind-hsl-colors]: Could not polyfill opacity modifier for '${tokenName}' in '${rule.selector}'.`
        );
      }
    },
  };
};

// remove tailwind gradient color format in gradients (e.g. 'to right in oklab' -> 'to right')
const tailwindGradientColorSpace = (): Plugin => {
  return {
    postcssPlugin: "postcss-tailwind-gradient-color-space",
    Once(root) {
      root.walkDecls((decl) => {
        if (decl.prop === "--tw-gradient-position") {
          decl.value = decl.value.replace(" in oklab", "");
        }
      });
    },
  };
};

const config = {
  plugins: [
    infinityKeyword(),
    transformShortcuts(),
    tailwindHslColors(),
    dynamicViewportUnits(),
    emptyVarFallback(),
    tailwindGradientColorSpace(),
    postcssPresetEnv({
      browsers: [
        "Chrome >= 64",
        "Firefox >= 58",
        "Safari >= 11.1",
        "Edge >= 17",
        "Opera >= 50",
      ],
      features: {
        "oklab-function": false,
        "color-functional-notation": false,
        "color-function": false,
        "color-mix": false,
        "logical-viewport-units": true,
      },
    }),
  ],
};

export default config;

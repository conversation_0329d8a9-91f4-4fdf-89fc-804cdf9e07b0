import { cn } from "@allo/ui";
import { ComponentPropsWithRef } from "react";

export const Error = ({
  children,
  className,
}: ComponentPropsWithRef<"div">) => {
  return (
    <div
      className={cn(
        "flex size-full flex-col items-center justify-center gap-1 p-8 text-center",
        className
      )}
    >
      {children}
    </div>
  );
};

export const ErrorTitle = ({
  children,
  className,
}: ComponentPropsWithRef<"h1">) => {
  return <h1 className={cn("text-md", className)}>{children}</h1>;
};

export const ErrorDescription = ({
  children,
  className,
}: ComponentPropsWithRef<"p">) => {
  return (
    <p className={cn("text-foreground-secondary max-w-md text-sm", className)}>
      {children}
    </p>
  );
};

export const ErrorActions = ({
  children,
  className,
}: ComponentPropsWithRef<"div">) => {
  return (
    <div
      className={cn(
        "mt-4 flex flex-wrap items-center justify-center gap-2",
        className
      )}
    >
      {children}
    </div>
  );
};

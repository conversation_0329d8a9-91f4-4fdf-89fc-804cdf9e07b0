import { Children, ComponentProps, ReactNode } from "react";
import { cn } from "@allo/ui";
import { getGridCellVertices } from "@/lib/utils/math";

const generateCellDataAttributes = (
  index: number,
  total: number,
  columns: number[]
) => {
  const attrs: Record<string, string> = {};

  for (const numColumns of columns) {
    const vertices = getGridCellVertices(index, total, numColumns);

    for (const vertex of vertices) {
      attrs[`data-${numColumns}c-${vertex}`] = "";
    }
  }

  return attrs;
};

interface MenuProductCardGridProps extends ComponentProps<"ul"> {
  children: ReactNode[] | ReactNode;
  size?: "full" | "tight";
}

export const MenuProductCardGrid = ({
  children,
  className,
  size = "full",
  ...props
}: MenuProductCardGridProps) => {
  return (
    <ul
      {...props}
      className={cn(
        "grid grid-cols-1 gap-0.25",
        size === "full" && "grid-cols-2",
        className
      )}
    >
      {Children.map(children, (child, index) => (
        <li
          key={index}
          {...generateCellDataAttributes(
            index,
            Children.count(children),
            [1, 2]
          )}
          className={cn(
            "rounded-none *:rounded-[inherit]",
            // 1 column
            size === "tight" && [
              "data-[1c-tl]:rounded-tl-xl",
              "data-[1c-tr]:rounded-tr-xl",
              "data-[1c-bl]:rounded-bl-xl",
              "data-[1c-br]:rounded-br-xl",
            ],
            // 2 columns
            size === "full" && [
              "rounded-none",
              "data-[2c-tl]:rounded-tl-xl",
              "data-[2c-tr]:rounded-tr-xl",
              "data-[2c-bl]:rounded-bl-xl",
              "data-[2c-br]:rounded-br-xl",
            ]
          )}
        >
          {child}
        </li>
      ))}
    </ul>
  );
};

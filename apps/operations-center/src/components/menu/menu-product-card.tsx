import { Product } from "@/lib/mock-data/menu";
import { useProductConfigurationDrawer } from "@/components/product-configuration/drawer";
import { Button, cn, <PERSON>, Badge } from "@allo/ui";
import { useMemo } from "react";
import { Trash2, <PERSON>, Minus, Plus } from "lucide-react";
import { isInteractiveElement } from "@/lib/utils/dom";
import { useCart, useCartActions } from "@/lib/cart/store";
import { getOrderOptions } from "@/lib/queries";
import { useQuery } from "@tanstack/react-query";
import {
  productToCartItem,
  calculateProductQuantityInCart,
} from "@/lib/cart/utils";

interface MenuProductCardProps {
  product: Product;
  orderId: string;
  className?: string;
}

export const MenuProductCard = ({
  product,
  orderId,
  className,
}: MenuProductCardProps) => {
  const { open: openConfigurationDrawer } = useProductConfigurationDrawer();

  const cart = useCart(orderId);
  const { addItem, updateItem, removeItem } = useCartActions(orderId);
  const order = useQuery(getOrderOptions(orderId));

  const quantity = useMemo(
    () => ({
      pending: calculateProductQuantityInCart(product.id, cart),
      ordered: calculateProductQuantityInCart(
        product.id,
        order.data?.ordered || []
      ),
    }),
    [product.id, cart, order.data?.ordered]
  );

  const handleIncrement = () => {
    // TODO: add extras
    if (product.options.length > 0) {
      openConfigurationDrawer({
        product,
        onSubmit: (cartItem) => addItem(cartItem),
      });
    } else {
      addItem(productToCartItem(product));
    }
  };

  const handleDecrement = () => {
    const item = cart.find(({ productId }) => productId === product.id);
    if (!item) return;

    if (item.quantity === 1) {
      removeItem(item.id);
    } else {
      updateItem(item.id, { ...item, quantity: item.quantity - 1 });
    }
  };

  return (
    <div
      className={cn(
        "bg-background-highlight outline-border flex h-24 cursor-pointer flex-col justify-between p-2 text-sm outline",
        className
      )}
      onClick={(e) => {
        if (!isInteractiveElement(e.target as HTMLElement)) {
          e.preventDefault();
          handleIncrement();
        }
      }}
      inert={product.isSoldOut}
    >
      <p className="flex justify-between gap-1">
        <span className="line-clamp-2">{product.name}</span>
        {!!quantity.ordered && (
          <span className="bg-background-low flex h-fit gap-1 rounded-sm px-1.5 py-1.25 text-xs [font-size:--spacing(2.5)] leading-none">
            <Flame />
            {quantity.ordered}
          </span>
        )}
      </p>
      <div className="flex items-end justify-between gap-1">
        {product.isSoldOut ? (
          <Badge size="xs" className="font-medium">
            Sold Out
          </Badge>
        ) : (
          <Price
            amount={product.unitPrice}
            className="text-foreground-secondary"
          />
        )}
        <div className="flex items-center gap-2">
          {quantity.pending > 0 && (
            <Button
              size="sm"
              square
              aria-label="Remove Product"
              onClick={handleDecrement}
            >
              {quantity.pending > 1 ? <Minus /> : <Trash2 />}
            </Button>
          )}
          <Button
            variant={quantity.pending > 0 ? "accent" : "primary"}
            size="sm"
            square
            aria-label="Add Product"
            onClick={handleIncrement}
            disabled={product.isSoldOut}
          >
            {quantity.pending > 0 ? quantity.pending : <Plus />}
          </Button>
        </div>
      </div>
    </div>
  );
};

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@allo/ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Check, X } from "lucide-react";
import { getOrderOptions, getOrdersOptions } from "@/lib/queries";
import { postCancelOrder } from "@/lib/mock-data/api";
import { useToast } from "@/components/ui/toast";
import { useCartStore } from "@/lib/cart/store";
import { useNavigate } from "@tanstack/react-router";
import { Order } from "@/lib/mock-data/order";

interface CancelOrderProps {
  order: Order;
  onSuccess: () => void;
}

export const CancelOrder = ({ order, onSuccess }: CancelOrderProps) => {
  const { toast } = useToast();
  const navigate = useNavigate();

  const deleteCart = useCartStore((state) => state.deleteCart);
  const queryClient = useQueryClient();

  const cancelOrderMutation = useMutation({
    mutationFn: async () => {
      return postCancelOrder(order.id);
    },
    onSuccess: async () => {
      queryClient.setQueryData(getOrdersOptions().queryKey, (data) => {
        return data?.filter((order) => order.id !== order.id);
      });

      await navigate({ to: "/dine-in" });

      queryClient.invalidateQueries({
        queryKey: getOrdersOptions().queryKey,
      });

      queryClient.removeQueries({
        queryKey: getOrderOptions(order.id).queryKey,
      });

      deleteCart(order.id);
    },
    onSettled: async (_, error) => {
      onSuccess();

      toast({
        icon: error ? X : Check,
        message: error
          ? "Failed to cancel order"
          : "Order successfully cancelled",
        variant: error ? "error" : "success",
        delay: 100,
      });
    },
  });

  return (
    <>
      <DrawerHeader>
        <DrawerTitle>Cancel Order</DrawerTitle>
      </DrawerHeader>
      <div className="p-2.5 text-sm">
        <p className="text-foreground">
          Are you sure you want to cancel the current order?
        </p>
        <p className="text-foreground-secondary">
          This action will cancel all items already sent to the kitchen.
        </p>
      </div>
      <DrawerActions>
        <Button
          className="grow"
          variant="negative"
          isLoading={cancelOrderMutation.isPending}
          onClick={() => cancelOrderMutation.mutate()}
        >
          Cancel Order
        </Button>
        <DrawerClose asChild>
          <Button square>
            <X />
          </Button>
        </DrawerClose>
      </DrawerActions>
    </>
  );
};

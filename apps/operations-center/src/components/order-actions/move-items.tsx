import { useEffect, useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Check, X } from "lucide-react";
import {
  Button,
  Checkbox,
  DrawerActions,
  DrawerClose,
  DrawerHeader,
  DrawerTitle,
  Listbox,
  ListboxOption,
  ListboxOptions,
  ListboxTrigger,
  Price,
  cn,
} from "@allo/ui";
import {
  CartItemCard,
  CartItemCardContent,
  CartItemCardDetails,
  CartItemCardRow,
  CartItemCardTitle,
} from "@/components/cart/cart-item-card";
import { CartItemPriceDetails } from "@/components/cart/cart-item-price-details";
import { TableSelect } from "@/components/table-select";
import { useToast } from "@/components/ui/toast";
import { useFreeze } from "@/lib/hooks/use-freeze";
import { useCart, useCartStore } from "@/lib/cart/store";
import { calculateCartItemTotalPrice } from "@/lib/cart/utils";
import { getOrderOptions, getOrdersOptions } from "@/lib/queries";
import { CartItem } from "@/lib/cart/types";
import { Table } from "@/lib/mock-data/tables";
import { fetchTableOrder, postCreateOrder } from "@/lib/mock-data/api";
import { Order } from "@/lib/mock-data/order";

interface MoveItemsMutationParams {
  table: Table;
  items: CartItem[];
}

interface MoveItemsProps {
  order: Order;
  onSuccess: () => void;
}

export const MoveItems = ({ order, onSuccess }: MoveItemsProps) => {
  const { toast } = useToast();

  const cart = useCart(order.id);
  const cartItems = useFreeze(cart, true);
  const moveItems = useCartStore((state) => state.moveItems);
  const queryClient = useQueryClient();

  const [isSelectingTable, setIsSelectingTable] = useState(false);
  const [table, setTable] = useState<Table | null>(null);
  const [itemsToMove, setItemsToMove] = useState<CartItem[]>([]);

  const moveItemsMutation = useMutation({
    mutationFn: async ({ table }: MoveItemsMutationParams) => {
      const existingOrder = await fetchTableOrder(table.id);

      if (existingOrder) {
        return existingOrder;
      }

      return postCreateOrder(table);
    },
    onSuccess: async (destinationOrder, { items }) => {
      await queryClient.invalidateQueries({
        queryKey: getOrdersOptions().queryKey,
      });

      queryClient.setQueryData(
        getOrderOptions(order.id).queryKey,
        destinationOrder
      );

      moveItems(order.id, destinationOrder.id, items);
    },
    onSettled: (_, error) => {
      onSuccess();

      toast({
        icon: error ? X : Check,
        message: error ? "Failed to move items" : "Items successfully moved",
        variant: error ? "error" : "success",
        delay: 200,
      });
    },
  });

  // reset state when drawer closes
  useEffect(() => {
    if (!open) {
      setItemsToMove([]);
      setIsSelectingTable(false);
    }
  }, [open]);

  return (
    <>
      <DrawerHeader>
        <DrawerTitle>Move Items</DrawerTitle>
      </DrawerHeader>
      <ItemSelection items={cartItems} onChange={setItemsToMove} />
      <DrawerActions className="flex flex-col gap-1.5">
        {isSelectingTable && (
          <TableSelect currentTableId={order.tableId} onChange={setTable} />
        )}
        <div className="flex w-full gap-1.5">
          {!isSelectingTable && (
            <Button
              variant="accent"
              className="grow"
              disabled={itemsToMove.length === 0}
              onClick={() => setIsSelectingTable(true)}
            >
              Select table...
            </Button>
          )}
          {isSelectingTable && (
            <Button
              variant="accent"
              className="grow"
              disabled={!table || itemsToMove.length === 0}
              isLoading={moveItemsMutation.isPending}
              onClick={() => {
                if (table) {
                  moveItemsMutation.mutate({
                    table,
                    items: itemsToMove,
                  });
                }
              }}
            >
              Move
            </Button>
          )}
          <DrawerClose asChild>
            <Button square>
              <X className="size-4" />
            </Button>
          </DrawerClose>
        </div>
      </DrawerActions>
    </>
  );
};

interface ItemSelectionProps {
  items: CartItem[];
  onChange: (items: CartItem[]) => void;
}

const ItemSelection = ({ items, onChange }: ItemSelectionProps) => {
  const [selectedItems, setSelectedItems] = useState<Map<string, CartItem>>(
    new Map()
  );
  const [quantities, setQuantities] = useState<Map<string, number>>(new Map());

  useEffect(() => {
    setQuantities(new Map(items.map((item) => [item.id, 1])));
  }, [items]);

  useEffect(() => {
    const itemList = Array.from(selectedItems.values()).map((item) => ({
      ...item,
      quantity: quantities.get(item.id) || 1,
    }));

    onChange(itemList);
  }, [selectedItems, quantities, onChange]);

  return (
    <ul>
      {items.map((item) => {
        const isSelected = selectedItems.has(item.id);

        return (
          <li key={item.id} className="group">
            <label>
              <CartItemCard
                className={cn(
                  "group-last:border-b-0",
                  isSelected && "from-accent/6 to-accent/2 bg-gradient-to-r"
                )}
              >
                <Checkbox
                  className="mr-1.5"
                  checked={isSelected}
                  onChange={(e) => {
                    setSelectedItems((prev) => {
                      if (e.target.checked) {
                        prev.set(item.id, item);
                      } else {
                        prev.delete(item.id);
                      }

                      return new Map(prev);
                    });
                  }}
                />
                <CartItemCardContent>
                  <CartItemCardRow>
                    <CartItemCardTitle>{item.name}</CartItemCardTitle>
                    <div>x{item.quantity}</div>
                  </CartItemCardRow>
                  <CartItemCardDetails>
                    <CartItemPriceDetails item={item} />
                  </CartItemCardDetails>
                  <CartItemCardRow>
                    <Listbox
                      disabled={item.quantity === 1}
                      value={quantities.get(item.id)}
                      onChange={(value) => {
                        setQuantities((prev) => {
                          prev.set(item.id, value || 1);
                          return new Map(prev);
                        });
                      }}
                    >
                      {/* FUTURE: create ui variant when/if this is approved */}
                      <ListboxTrigger className="chunky h-7 w-16 border-none px-2 text-xs">
                        <span className="ml-1">{quantities.get(item.id)}</span>
                      </ListboxTrigger>
                      <ListboxOptions>
                        {Array.from({ length: item.quantity }).map(
                          (_, index) => (
                            <ListboxOption key={index} value={index + 1}>
                              {index + 1}
                            </ListboxOption>
                          )
                        )}
                      </ListboxOptions>
                    </Listbox>
                    <Price amount={calculateCartItemTotalPrice(item)} />
                  </CartItemCardRow>
                </CartItemCardContent>
              </CartItemCard>
            </label>
          </li>
        );
      })}
    </ul>
  );
};

export const useCanMoveItems = (order: Order | undefined) => {
  const [canMoveItems, setCanMoveItems] = useState(false);

  useEffect(() => {
    if (!order) return;

    const validate = () => {
      const cart = useCartStore.getState().getCart(order.id, false);

      if (cart) {
        setCanMoveItems(cart.length > 0);
      }
    };

    const unsubscribe = useCartStore.subscribe(validate);
    validate();

    return () => {
      unsubscribe();
      setCanMoveItems(false);
    };
  }, [order]);

  return canMoveItems;
};

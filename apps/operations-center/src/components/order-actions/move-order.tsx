import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@allo/ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Check, X } from "lucide-react";
import { getOrdersOptions } from "@/lib/queries";
import { TableSelect } from "@/components/table-select";
import { useState } from "react";
import { Table } from "@/lib/mock-data/tables";
import { fetchTableOrder, postMoveOrder } from "@/lib/mock-data/api";
import { useToast } from "@/components/ui/toast";
import { useCartStore } from "@/lib/cart/store";
import { Order } from "@/lib/mock-data/order";

interface MoveOrderProps {
  order: Order;
  onSuccess: () => void;
}

export const MoveOrder = ({ order, onSuccess }: MoveOrderProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const mergeCarts = useCartStore((state) => state.mergeCarts);

  const [selectedTable, setSelectedTable] = useState<Table | null>(null);

  const moveOrderMutation = useMutation({
    mutationFn: async ({ table }: { table: Table }) => {
      return postMoveOrder(order.id, table);
    },
    onMutate: async ({ table }: { table: Table }) => {
      const existingTableOrder = await fetchTableOrder(table.id);

      if (existingTableOrder) {
        return { existingTableOrder };
      }

      return null;
    },
    onSuccess: async (...args) => {
      const [, , context] = args;

      await queryClient.refetchQueries({
        queryKey: getOrdersOptions().queryKey,
      });

      if (context?.existingTableOrder) {
        mergeCarts(context.existingTableOrder.id, order.id);
      }
    },
    onSettled: (_, error) => {
      onSuccess();

      toast({
        icon: error ? X : Check,
        message: error ? "Failed to move order" : "Order successfully moved",
        variant: error ? "error" : "success",
        delay: 200,
      });
    },
  });

  return (
    <>
      <DrawerHeader>
        <DrawerTitle>Move Order</DrawerTitle>
      </DrawerHeader>
      <div className="px-2.5">
        <TableSelect
          currentTableId={order.tableId}
          onChange={setSelectedTable}
        />
      </div>
      <DrawerActions>
        <Button
          className="grow"
          variant="accent"
          disabled={!selectedTable}
          isLoading={moveOrderMutation.isPending}
          onClick={() => {
            if (selectedTable) {
              moveOrderMutation.mutate({ table: selectedTable });
            }
          }}
        >
          Move
        </Button>
        <DrawerClose asChild>
          <Button square>
            <X />
          </Button>
        </DrawerClose>
      </DrawerActions>
    </>
  );
};

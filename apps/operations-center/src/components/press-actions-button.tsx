import {
  cn,
  Drawer,
  <PERSON>er<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>er<PERSON>it<PERSON>,
} from "@allo/ui";
import { useState } from "react";
import { LucideIcon, XIcon } from "lucide-react";
import { useLongPress } from "@/lib/hooks/use-long-press";
import { HitArea } from "./ui/hit-area";

export type PressAction = {
  id: string;
  label: string;
  icon?: LucideIcon;
  variant?: "default" | "negative";
  onClick: () => void;
  disabled?: boolean;
};

interface PressActionsButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  tap?: PressAction[];
  longPress?: PressAction[];
  drawerTitle?: string;
}

/**
 * Component for handling configurable tap and long press actions
 */
export const PressActionsButton = ({
  children,
  tap: tapActions = [],
  longPress: longPressActions = [],
  drawerTitle,
  ...props
}: PressActionsButtonProps) => {
  const [isTapDrawerOpen, setIsTapDrawerOpen] = useState(false);
  const [isLongPressDrawerOpen, setIsLongPressDrawerOpen] = useState(false);

  const longPressMethods = useLongPress({
    onClick: (e) => {
      if (tapActions.length === 0) return;

      // stop event so it doesn't trigger the dialog's backdrop click
      e.preventDefault();

      if (tapActions.length === 1) {
        tapActions[0].onClick?.();
      } else {
        setIsTapDrawerOpen(true);
      }
    },
    onLongPress: () => {
      if (longPressActions.length === 0) return;

      if (longPressActions.length === 1) {
        longPressActions[0].onClick?.();
      } else {
        setIsLongPressDrawerOpen(true);
      }
    },
  });

  return (
    <>
      <button {...props} {...longPressMethods}>
        {children}
      </button>

      {tapActions.length > 1 && (
        <PressActionDrawer
          title={drawerTitle ?? "Actions"}
          open={isTapDrawerOpen}
          onOpenChange={setIsTapDrawerOpen}
          actions={tapActions}
        />
      )}
      {longPressActions.length > 1 && (
        <PressActionDrawer
          title={drawerTitle ?? "Actions"}
          open={isLongPressDrawerOpen}
          onOpenChange={setIsLongPressDrawerOpen}
          actions={longPressActions}
        />
      )}
    </>
  );
};

interface PressActionDrawerProps {
  title: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  actions: PressAction[];
}

const PressActionDrawer = ({
  title,
  actions,
  open,
  onOpenChange,
}: PressActionDrawerProps) => {
  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent>
        <DrawerHeader className="flex items-center justify-between px-4 py-5">
          <DrawerTitle className="text-base leading-none">{title}</DrawerTitle>
          <DrawerClose className="relative size-3.75">
            <HitArea offset={3} />
            <XIcon className="size-full" />
          </DrawerClose>
        </DrawerHeader>
        <ul className="bg-border-soft mb-6 space-y-px">
          {actions.map(
            ({
              id,
              label,
              icon: Icon,
              variant = "default",
              onClick,
              disabled,
            }) => (
              <li key={id} className="bg-background-high">
                <button
                  onClick={() => {
                    onClick?.();
                    onOpenChange(false);
                  }}
                  disabled={disabled}
                  className={cn(
                    "text-foreground flex h-16 items-center gap-2.5 px-4 text-base",
                    variant === "negative" && "text-negative",
                    variant === "default" &&
                      "[&_.lucide]:text-foreground-secondary",
                    disabled && "opacity-50"
                  )}
                >
                  {Icon && <Icon className="size-4" />}
                  {label}
                </button>
              </li>
            )
          )}
        </ul>
      </DrawerContent>
    </Drawer>
  );
};

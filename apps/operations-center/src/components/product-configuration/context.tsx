import { createContext, Dispatch, use, useReducer, useState } from "react";

import { CartItem, CartItemOption } from "@/lib/cart/types";
import { productToCartItem } from "@/lib/cart/utils";
import { getCartItemFirstGroupError } from "@/lib/cart/validation";
import { Product } from "@/lib/mock-data/menu";

/**
 * Product Configuration System
 *
 * This system allows for dynamic product configuration with the following components:
 *
 * 1. Provider: Creates the configuration context and manages state
 *    - Handles cart item state through a reducer
 *    - Manages validation of configuration groups
 *    - Processes submission and cancellation
 *
 * 2. Context: Shares configuration state across components
 *    - Exposes product data, cart item state, and dispatch functions
 *    - Provides validation information
 *
 * 3. Components:
 *    - Options: Renders product configuration options (required/optional selections)
 *    - Notes: Allows adding custom notes to the cart item
 *    - Actions: Provides quantity selection and add/update/cancel buttons
 *
 * Anatomy
 * <ProductConfigurationProvider>
 *    <ProductConfigurationOptions />
 *    <ProductConfigurationNotes />
 *    <ProductConfigurationActions />
 * </ProductConfigurationProvider>
 */

type CartItemSet = {
  type: "set";
  payload: CartItemOption;
};

type CartItemSetInitial = {
  type: "set-initial";
  payload: CartItem;
};

type CartItemSetInGroup = {
  type: "set-in-group";
  payload: CartItemOption;
};

type CartItemRemove = {
  type: "remove";
  payload: string;
};

type CartItemQuantity = {
  type: "quantity";
  payload: number;
};

type CartItemSetNotes = {
  type: "set-notes";
  payload: string[];
};

type CartItemReset = {
  type: "reset";
};

type CartItemAction =
  | CartItemSet
  | CartItemSetInitial
  | CartItemSetInGroup
  | CartItemRemove
  | CartItemQuantity
  | CartItemSetNotes
  | CartItemReset;

const cartItemReducer = (state: CartItem, action: CartItemAction) => {
  switch (action.type) {
    case "set":
      return {
        ...state,
        options: [
          ...state.options.filter((o) => o.id !== action.payload.id),
          action.payload,
        ],
      };
    case "set-initial":
      return action.payload;
    case "set-in-group":
      return {
        ...state,
        options: [
          ...state.options.filter((o) => o.groupId !== action.payload.groupId),
          action.payload,
        ],
      };
    case "remove":
      return {
        ...state,
        options: state.options.filter((o) => o.id !== action.payload),
      };
    case "quantity":
      return {
        ...state,
        quantity: action.payload,
      };
    case "set-notes":
      return {
        ...state,
        notes: action.payload,
      };
    default:
      return state;
  }
};

interface ProductConfigurationContext {
  product: Product;
  cartItem: CartItem;
  initialCartItem?: CartItem;
  liveValidationGroups: Map<string, number>;
  dispatch: Dispatch<CartItemAction>;
  submit: () => void;
  cancel: () => void;
  isPending: boolean;
}

const ProductConfigurationContext =
  createContext<ProductConfigurationContext | null>(null);

// eslint-disable-next-line react-refresh/only-export-components
export const useProductConfigurationContext = () => {
  const ctx = use(ProductConfigurationContext);

  if (!ctx) {
    throw new Error(
      "ProductConfigurationContext not found. Make sure to wrap your component in a ProductConfigurationProvider"
    );
  }

  return ctx;
};

export interface ProductConfigurationProviderProps {
  product: Product;
  initialCartItem?: CartItem;
  onSubmit: (cartItem: CartItem) => unknown;
  onCancel?: () => void;
  children: React.ReactNode;
  freeze?: boolean;
}

export function ProductConfigurationProvider({
  product,
  initialCartItem,
  onSubmit,
  onCancel,
  children,
}: ProductConfigurationProviderProps) {
  const [isPending, setIsPending] = useState(false);

  const [cartItem, dispatch] = useReducer(
    cartItemReducer,
    initialCartItem ?? productToCartItem(product)
  );

  const [liveValidationGroups, setLiveValidationGroups] = useState(
    new Map<string, number>()
  );

  const handleSubmit = async () => {
    const error = getCartItemFirstGroupError(cartItem, product);

    if (error) {
      document
        .getElementById(error.groupId)
        ?.scrollIntoView({ behavior: "smooth" });

      return setLiveValidationGroups((prev) => {
        prev.set(error.groupId, Date.now());
        return new Map(prev);
      });
    } else {
      setIsPending(true);
      await onSubmit(cartItem);
      setIsPending(false);
    }
  };

  const handleCancel = () => {
    onCancel?.();
  };

  return (
    <ProductConfigurationContext
      value={{
        product,
        cartItem,
        initialCartItem,
        liveValidationGroups,
        dispatch,
        submit: handleSubmit,
        cancel: handleCancel,
        isPending,
      }}
    >
      {children}
    </ProductConfigurationContext>
  );
}

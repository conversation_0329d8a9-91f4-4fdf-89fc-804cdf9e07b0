import { useProductConfigurationContext } from "./context";
import { NotesInput } from "../notes-input";

export const ProductConfigurationNotes = () => {
  const { product, cartItem, dispatch, isPending } =
    useProductConfigurationContext();

  return (
    <div inert={isPending}>
      <div className="flex items-center gap-1.5 px-2 py-3">
        <h2>Notes</h2>
      </div>
      <NotesInput
        customNotePlaceholder="Type a custom note..."
        defaultValue={cartItem.notes}
        presets={product.suggestedNotes}
        onChange={(notes) => {
          dispatch({
            type: "set-notes",
            payload: notes,
          });
        }}
      />
    </div>
  );
};

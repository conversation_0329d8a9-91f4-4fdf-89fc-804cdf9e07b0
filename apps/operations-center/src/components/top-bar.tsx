import { cn } from "@allo/ui";
import { FlatButton } from "@/components/ui/flat-button";
import { MenuIcon, X } from "lucide-react";
import {
  ComponentPropsWithRef,
  createContext,
  Fragment,
  ReactNode,
  use,
  useCallback,
  useLayoutEffect,
  useState,
} from "react";

import {
  BreadcrumbItem,
  Breadcrumbs,
  BreadcrumbSeparator,
  BreadcrumbsProvider,
  useBreadcrumbs,
} from "@/components/ui/breadcrumbs";
import { ModeSwitcher } from "@/components/mode-switcher";
import { PrototypeSettingsMenu } from "@/components/prototype-settings";
import { useSearch } from "@tanstack/react-router";

type TopBarContext = {
  actionSlots: { id: string; children: ReactNode }[];
  registerActionSlot: (id: string, children: ReactNode) => void;
};

const TopBarContext = createContext<TopBarContext>({
  actionSlots: [],
  registerActionSlot: () => {},
});

const useTopBar = () => {
  const context = use(TopBarContext);

  if (!context) {
    throw new Error("useTopBar must be used within a TopBarProvider");
  }

  return context;
};

export const TopBarProvider = ({ children }: { children: ReactNode }) => {
  const [actionSlots, setActionSlots] = useState<TopBarContext["actionSlots"]>(
    []
  );

  const registerActionSlot = useCallback((id: string, children: ReactNode) => {
    setActionSlots((prev) => [
      ...(children ? [{ id, children }] : []),
      ...prev.filter((slot) => slot.id !== id),
    ]);
  }, []);

  return (
    <TopBarContext value={{ actionSlots, registerActionSlot }}>
      <BreadcrumbsProvider>{children}</BreadcrumbsProvider>
    </TopBarContext>
  );
};

export const TopBar = ({
  className,
  ...props
}: ComponentPropsWithRef<"header">) => {
  const [isSettingsMenuOpen, setIsSettingsMenuOpen] = useState(false);
  const { actionSlots } = useTopBar();
  const { items } = useBreadcrumbs();

  const params = useSearch({ strict: false });

  return (
    <header
      className={cn(
        "bg-background-highlight border-border relative z-30 max-w-screen border-b",
        className
      )}
      {...props}
    >
      {params.showTopBar && (
        <div className="flex items-center">
          <FlatButton onClick={() => setIsSettingsMenuOpen((prev) => !prev)}>
            {isSettingsMenuOpen ? <X /> : <MenuIcon />}
          </FlatButton>
          <ModeSwitcher />
          {isSettingsMenuOpen && <PrototypeSettingsMenu />}
        </div>
      )}

      {(items.length > 0 || actionSlots.length > 0) && (
        <div className="flex h-11 justify-between gap-4 px-2.5 pt-0.5">
          <Breadcrumbs className="shrink overflow-hidden pt-1.5">
            {(items) =>
              items.map(({ id, label, ...props }, index, arr) => {
                const previous = arr[index - 1];
                const isLast = index === arr.length - 1;

                return (
                  <Fragment key={id}>
                    {previous && (
                      <BreadcrumbSeparator
                        type={
                          previous.variant === "badge" ? "slash" : "chevron"
                        }
                      />
                    )}
                    <BreadcrumbItem {...props} truncate={!isLast}>
                      {label}
                    </BreadcrumbItem>
                  </Fragment>
                );
              })
            }
          </Breadcrumbs>
          {actionSlots.length > 0 && (
            <div className="flex shrink-0 items-center gap-1.5">
              {actionSlots.map(({ id, children }) => (
                <Fragment key={id}>{children}</Fragment>
              ))}
            </div>
          )}
        </div>
      )}
    </header>
  );
};

interface TopBarActionsPortalProps {
  id: string;
  children: ReactNode;
}

export const TopBarActionsPortal = ({
  id,
  children,
}: TopBarActionsPortalProps) => {
  const { registerActionSlot } = useTopBar();

  useLayoutEffect(() => {
    registerActionSlot(id, children);

    return () => registerActionSlot(id, null);
  }, [children, id, registerActionSlot]);

  return null;
};

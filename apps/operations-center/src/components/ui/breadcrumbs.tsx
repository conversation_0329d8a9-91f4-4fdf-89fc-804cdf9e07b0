/* eslint-disable react-refresh/only-export-components */
import { cn, Skeleton } from "@allo/ui";
import {
  ComponentPropsWithRef,
  createContext,
  ReactNode,
  use,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { ChevronRight } from "lucide-react";
import { useStableValue } from "@/lib/hooks/use-stable-value";

interface BreadcrumbItem {
  id: string;
  label: string;
  variant?: "default" | "badge";
  loading?: boolean;
}

interface BreadcrumbsContext {
  items: BreadcrumbItem[];
  push: (breadcrumb: BreadcrumbItem) => void;
  remove: (id: string) => void;
}

const BreadcrumbsContext = createContext<BreadcrumbsContext>({
  items: [],
  push: () => {},
  remove: () => {},
});

export const BreadcrumbsProvider = ({ children }: { children: ReactNode }) => {
  const [items, setItems] = useState<BreadcrumbItem[]>([]);
  // used to maintain order of items with same id between re-renders
  const positions = useRef<Map<string, number>>(new Map());

  const push: BreadcrumbsContext["push"] = useCallback((breadcrumb) => {
    setItems((prev) => {
      const position = positions.current.get(breadcrumb.id) ?? prev.length;

      const updatedItems = [
        ...prev.slice(0, position),
        breadcrumb,
        ...prev.slice(position),
      ];

      const newPosition = updatedItems.findIndex((b) => b.id === breadcrumb.id);
      positions.current.set(breadcrumb.id, newPosition);

      return updatedItems;
    });
  }, []);

  const remove: BreadcrumbsContext["remove"] = useCallback((id) => {
    setItems((prev) => prev.filter((b) => b.id !== id));
  }, []);

  const context = useMemo(
    () => ({ items, push, remove }),
    [items, push, remove]
  );

  return <BreadcrumbsContext value={context}>{children}</BreadcrumbsContext>;
};

export const useBreadcrumbs = () => {
  const ctx = use(BreadcrumbsContext);

  if (!ctx) {
    throw new Error("BreadcrumbsProvider not found");
  }

  return ctx;
};

export const useRegisterBreadcrumb = (items: BreadcrumbItem[]) => {
  const { push, remove } = useBreadcrumbs();
  const stableItems = useStableValue(items);

  useEffect(() => {
    stableItems.forEach((item) => push(item));
    return () => stableItems.forEach((item) => remove(item.id));
  }, [push, remove, stableItems]);
};

interface BreadcrumbsProps
  extends Omit<ComponentPropsWithRef<"ol">, "children"> {
  children?: (items: BreadcrumbItem[]) => ReactNode;
}

export const Breadcrumbs = ({
  children,
  className,
  ...props
}: BreadcrumbsProps) => {
  const { items } = useBreadcrumbs();

  return (
    <ol
      {...props}
      className={cn("text-foreground flex items-center text-sm", className)}
    >
      {children && children(items)}
      {!children &&
        items.map((item) => <BreadcrumbItem key={item.id} {...item} />)}
    </ol>
  );
};

interface BreadcrumbItemProps extends ComponentPropsWithRef<"li"> {
  variant?: "default" | "badge";
  loading?: boolean;
  truncate?: boolean;
}

export const BreadcrumbItem = ({
  className,
  children,
  variant = "default",
  loading = false,
  truncate = true,
  ...props
}: BreadcrumbItemProps) => {
  return (
    <li
      {...props}
      className={cn(
        "whitespace-nowrap",
        variant === "default" && "py-1",
        truncate && variant === "default" && "truncate",
        variant === "badge" &&
          !loading &&
          "text-background bg-foreground rounded-sm px-1 text-xs leading-[--spacing(4.5)] font-medium",
        className
      )}
    >
      {loading ? <Skeleton className="h-4 w-12 rounded-sm" /> : children}
    </li>
  );
};

interface BreadcrumbSeparatorProps
  extends Omit<ComponentPropsWithRef<"li">, "children"> {
  type: "slash" | "chevron";
}

export const BreadcrumbSeparator = ({
  className,
  type,
  ...props
}: BreadcrumbSeparatorProps) => {
  return (
    <li
      {...props}
      className={cn(
        "text-foreground-secondary",
        type === "slash" && "px-2",
        type === "chevron" && "flex size-4 items-center justify-center",
        className
      )}
    >
      {type === "slash" ? (
        " / "
      ) : (
        <ChevronRight className="size-4 stroke-[1.5px]" />
      )}
    </li>
  );
};

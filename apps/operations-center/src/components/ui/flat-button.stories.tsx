import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { FlatButton } from './flat-button';
import { MenuIcon, X, Plus, Settings } from 'lucide-react';

const meta: Meta<typeof FlatButton> = {
  title: 'UI/FlatButton',
  component: FlatButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    children: {
      control: 'text',
      description: 'Button content',
    },
    square: {
      control: 'boolean',
      description: 'Makes the button square-shaped',
    },
    isLoading: {
      control: 'boolean',
      description: 'Shows loading spinner',
    },
    asChild: {
      control: 'boolean',
      description: 'Renders as child component using Slot',
    },
    disabled: {
      control: 'boolean',
      description: 'Disables the button',
    },
    onClick: {
      action: 'clicked',
      description: 'Click handler',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default button
export const Default: Story = {
  args: {
    children: 'Button',
  },
};

// Button with text
export const WithText: Story = {
  args: {
    children: 'Click me',
  },
};

// Button with icon
export const WithIcon: Story = {
  args: {
    children: <MenuIcon />,
    square: true,
  },
};

// Button with icon and text
export const WithIconAndText: Story = {
  args: {
    children: (
      <>
        <Plus />
        Add Item
      </>
    ),
  },
};

// Square button
export const Square: Story = {
  args: {
    children: <X />,
    square: true,
  },
};

// Loading state
export const Loading: Story = {
  args: {
    children: 'Loading...',
    isLoading: true,
  },
};

// Loading square button
export const LoadingSquare: Story = {
  args: {
    children: <Settings />,
    square: true,
    isLoading: true,
  },
};

// Disabled button
export const Disabled: Story = {
  args: {
    children: 'Disabled',
    disabled: true,
  },
};

// Custom className
export const CustomStyle: Story = {
  args: {
    children: 'Custom Style',
    className: 'bg-blue-500 text-white hover:bg-blue-600',
  },
};

// All variants showcase
export const AllVariants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4 items-center">
      <FlatButton>Default</FlatButton>
      <FlatButton square>
        <MenuIcon />
      </FlatButton>
      <FlatButton isLoading>Loading</FlatButton>
      <FlatButton disabled>Disabled</FlatButton>
      <FlatButton>
        <Plus />
        With Icon
      </FlatButton>
    </div>
  ),
  parameters: {
    controls: { disable: true },
  },
};

import { create } from "zustand";

export type AuthToken = {
  token: string | null;
  restaurantId: string | null;
};

interface AuthStore {
  auth: AuthToken & { restaurantId: string | null };
}

interface AuthActions {
  setAuth: (token: AuthStore["auth"]) => AuthStore;
  getAuth(): AuthToken;
}

export const useAuthStore = create<AuthStore & AuthActions>((set, get) => ({
  auth: {
    token: null,
    restaurantId: null,
  },
  setAuth: ({ token, restaurantId }) => {
    set(() => {
      return {
        auth: { token, restaurantId },
      };
    });

    return get();
  },
  getAuth: () => {
    return get().auth;
  },
}));

export const useAuth = () => {
  return useAuthStore((state) => state.getAuth());
};

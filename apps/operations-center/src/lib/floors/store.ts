import { create } from "zustand";
import { Table } from "../mock-data/tables";

export type Floor = {
  id: string;
  name: string;
  tables: Table[];
};

export type FloorsStore = {
  floors: Floor[];
};

export type FloorsActions = {
  setFloors: (floors: FloorsStore["floors"]) => void;
};

export const useFloorsStore = create<FloorsStore & FloorsActions>(
  (set, get) => ({
    floors: [],
    setFloors: (floors) => {
      set(() => {
        return {
          floors,
        };
      });

      return get().floors;
    },
    getFloors: () => {
      return get().floors;
    },
    clearFloors: () => {
      set(() => {
        return {
          floors: [],
        };
      });
    },
  })
);

export const useFloors = () => {
  return useFloorsStore((state) => state.floors);
};

export const useSetFloors = () => {
  return useFloorsStore((state) => state.setFloors);
};

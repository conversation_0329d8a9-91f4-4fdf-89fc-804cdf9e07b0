let currentColorIndex = 0;

const COLORS: `#${string}`[] = [
  "#D25054",
  "#4995AB",
  "#5676AE",
  "#8AB47F",
  "#8669B0",
  "#BA935D",
  "#DD7254",
  "#A64271",
  "#4C907E",
  "#316151",
  "#594771",
];

const COLORS_BY_NAME = {
  SALMON_500: "#D25054",
  AQUA_500: "#4995AB",
  BLUE_500: "#5676AE",
  GREEN_500: "#8AB47F",
  PURPLE_700: "#8669B0",
  YELLOW_700: "#BA935D",
  RED_500: "#DD7254",
  PINK_500: "#A64271",
  GREEN_600: "#4C907E",
  LIME_500: "#316151",
  INDIGO_500: "#594771",
  GREY_800: "#333332",
  DEFAULT: "#757574",
};

export const getSuperFolderColorByName = (
  name: keyof typeof COLORS_BY_NAME
) => {
  return COLORS_BY_NAME[name] || COLORS_BY_NAME.DEFAULT;
};

export const getSuperFolderColor = () => {
  const color = COLORS[currentColorIndex];
  currentColorIndex = (currentColorIndex + 1) % COLORS.length;
  return color;
};

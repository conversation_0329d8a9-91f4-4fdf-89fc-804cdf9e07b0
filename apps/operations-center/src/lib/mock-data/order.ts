import { ProductOption } from "./menu";

export type Order = {
  id: string;
  tableId: string;
  spaceId: string;
  tableName: string;
  ordered: (OrderItem | CancelledOrderItem)[];
  assignedTo: string | null;
  assignedAt: Date | null;
};

export type OrderItemOption = Pick<
  ProductOption,
  "id" | "name" | "min" | "max" | "unitPrice" | "initialQuantity"
> & {
  groupId: string;
  quantity: number;
};

export type OrderItem = {
  id: string;
  productId: string;
  name: string;
  image?: string;
  quantity: number;
  baseUnitPrice: number;
  options: OrderItemOption[];
  notes: string[];
  createdAt: Date;
  isCancelled: boolean;
};

export type CancelledOrderItem = OrderItem & {
  isCancelled: true;
  cancelledAt: Date;
  cancelReasons: string[];
};

export const cancelItemSuggestedReasons = [
  "Mistake",
  "Customer changed their mind",
];

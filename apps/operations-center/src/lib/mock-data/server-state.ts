import { CancelledOrderItem, Order, OrderItem } from "./order";
import { uuid } from "../utils/string";
import { Table } from "./tables";

export const generateOrderId = (tableId: string, id?: string) => {
  return `${tableId}-${id ?? uuid().split("-")[0]}`;
};

interface State {
  orders: Record<string, Order>;
}

interface Actions {
  getOrder: (id: string) => Order;
  createOrder: (table: Table) => Order;
  assignOrder: (orderId: string, assignee: string) => Order;
  moveOrder: (orderId: string, destinationTable: Table) => void;
  cancelOrder: (orderId: string) => void;
  orderItems: (
    orderId: string,
    items: Omit<OrderItem, "createdAt" | "isCancelled">[]
  ) => Order;
  cancelItem: (
    orderId: string,
    itemId: string,
    quantity: number,
    reasons: string[]
  ) => void;
}

export const state: State & Actions = {
  orders: {},
  getOrder(id: string) {
    const order = this.orders[id];

    if (!order) {
      throw new Error("Could not find order with id: " + id);
    }

    return order;
  },
  createOrder(table: Table) {
    const id = generateOrderId(table.id);

    this.orders[id] = {
      id,
      tableId: table.id,
      tableName: table.label || table.code || table.id,
      spaceId: table.floorId,
      assignedTo: null,
      assignedAt: null,
      ordered: [],
    };

    return this.orders[id];
  },
  assignOrder(orderId, assignee) {
    const order = this.getOrder(orderId);

    this.orders[orderId] = {
      ...order,
      assignedTo: assignee,
      assignedAt: new Date(),
    };

    return this.orders[orderId];
  },
  moveOrder(orderId, destinationTable) {
    const order = this.getOrder(orderId);

    const existingTableOrder = Object.values(this.orders).find(
      (order) => order.tableId === destinationTable.id
    );

    this.orders[orderId] = {
      ...order,
      tableId: destinationTable.id,
      tableName: destinationTable.label,
      spaceId: destinationTable.floorId,
    };

    if (existingTableOrder) {
      this.orders[orderId] = {
        ...this.orders[orderId],
        ordered: [
          ...this.orders[orderId].ordered,
          ...existingTableOrder.ordered,
        ],
      };

      delete this.orders[existingTableOrder.id];
    }

    return this.orders[orderId];
  },
  cancelOrder(orderId) {
    delete this.orders[orderId];
  },
  orderItems(orderId, items) {
    const order = this.getOrder(orderId);

    this.orders[orderId] = {
      ...order,
      ordered: [
        ...order.ordered,
        ...items.map((item) => ({
          ...item,
          createdAt: new Date(),
          isCancelled: false,
        })),
      ],
    };

    return this.orders[orderId];
  },
  cancelItem(orderId, itemId, quantity, reasons) {
    const order = this.getOrder(orderId);
    const item = order.ordered.find(
      (item) => item.id === itemId && !item.isCancelled
    );

    if (!item) {
      throw new Error("Could not find item with id: " + itemId);
    }

    if (item.quantity < quantity) {
      throw new Error("Quantity is less than the quantity to cancel");
    }

    const cancelledItem: CancelledOrderItem = {
      ...item,
      isCancelled: true,
      quantity,
      cancelReasons: reasons,
      cancelledAt: new Date(),
    };

    if (item.quantity === quantity) {
      this.orders[orderId] = {
        ...order,
        ordered: [
          ...order.ordered.filter(({ id }) => id !== itemId),
          cancelledItem,
        ],
      };
    } else {
      const leftOverItem = {
        ...item,
        id: uuid(),
        quantity: item.quantity - quantity,
      };

      this.orders[orderId] = {
        ...order,
        ordered: [
          ...order.ordered.filter(({ id }) => id !== itemId),
          leftOverItem,
          cancelledItem,
        ],
      };
    }
  },
};

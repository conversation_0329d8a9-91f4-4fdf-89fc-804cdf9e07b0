export type SpaceStatus =
  | "Regular"
  | "Busy"
  | "Very Busy"
  | "Completely Full"
  | "Closed";
export type Space = {
  id: string;
  name: string;
  status: SpaceStatus;
  isClosed: boolean;
};

export const spaces: Space[] = [
  { id: "main-room", name: "Main Room", status: "Regular", isClosed: false },
  {
    id: "cocktail-bar",
    name: "Cocktail Bar",
    status: "Regular",
    isClosed: false,
  },
  { id: "wine-bar", name: "Wine Bar", status: "Regular", isClosed: false },
  {
    id: "interior-garden",
    name: "Interior Garden",
    status: "Regular",
    isClosed: false,
  },
  { id: "patio", name: "<PERSON><PERSON>", status: "Closed", isClosed: true },
];

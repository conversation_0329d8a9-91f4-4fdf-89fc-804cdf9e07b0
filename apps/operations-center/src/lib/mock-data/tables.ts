export interface Space {
  id: string;
  creationTime: string;
  modificationTime: string;
  restaurantId: string;
  name: string;
  tables: Table[];
}

export interface Table {
  id: string;
  creationTime: string;
  modificationTime: string;
  restaurantId: string;
  floorId: string;
  code: string;
  label: string;
  labelI18n: {
    [key: string]: string;
  };
  category: "INSIDE" | string;
  minimumCapacity: number;
  capacity: number;
  layout: TableLayout;
  status: "TAKEN" | "AVAILABLE" | string;
  orderId?: string;
  orderingDeviceIds: string[];
  deleted: boolean;
  reservable: boolean;
}

export interface TableLayout {
  x: number;
  y: number;
  w: number;
  h: number;
  shape: "SQUARE" | string;
  angle: number;
}

export const tables: Table[] = [
  { id: "1", label: "T1", spaceId: "main-room" },
  { id: "6", label: "T6", spaceId: "main-room" },
  { id: "7", label: "T7", spaceId: "main-room" },
  { id: "8", label: "T8", spaceId: "main-room" },
  { id: "9", label: "T9", spaceId: "main-room" },
  { id: "10", label: "T10", spaceId: "main-room" },
  { id: "11", label: "T11", spaceId: "main-room" },
  { id: "12", label: "T12", spaceId: "main-room" },
  { id: "2", label: "C2", spaceId: "cocktail-bar" },
  { id: "3", label: "W3", spaceId: "wine-bar" },
  { id: "4", label: "G4", spaceId: "interior-garden" },
  { id: "5", label: "P5", spaceId: "patio" },
];

import { literal } from "@/lib/utils/types";

import { UserConfigFieldGroup } from "./types";

const pressActions = [
  { id: literal("open-table"), title: "Open Table" },
  { id: literal("print-receipt"), title: "Print Receipt" },
  { id: literal("payment"), title: "Payment" },
  { id: literal("move-order"), title: "Move Order" },
  { id: literal("move-items"), title: "Move Items" },
  { id: literal("cancel-order"), title: "Cancel Order" },
];

export const schema = {
  views: {
    tables: [
      {
        type: "multi-select",
        title: "Table Tap Actions",
        description: "Select the actions you want to display on tables",
        id: literal("tap-actions"),
        min: 1,
        options: pressActions.map((action) => ({
          ...action,
          default: action.id === "open-table",
          readOnly: action.id === "open-table",
        })),
      },
      {
        type: "multi-select",
        title: "Table Long-press Actions",
        description: "Select the actions you want to display on tables",
        id: literal("long-press-actions"),
        options: pressActions,
      },
      {
        type: "select",
        title: "Table Size",
        description: "Select how you want to view your tables",
        id: literal("size"),
        options: [
          {
            id: literal("sm"),
            title: "Small",
            description: "Show only the table code",
          },
          {
            id: literal("md"),
            title: "Medium",
            default: true,
            description: "Show up to 2 properties",
          },
          {
            id: literal("lg"),
            title: "Large",
            description: "Show up to 5 properties",
          },
        ],
      },
      {
        type: "multi-select",
        title: "Table Properties",
        description: "Select the properties you want to display on tables",
        id: literal("properties"),
        min: 1,
        max: 5,
        options: [
          { id: literal("price"), title: "Price", default: true },
          { id: literal("time"), title: "Time", default: true },
          { id: literal("status"), title: "Status" },
          { id: literal("drinks"), title: "Drinks" },
          { id: literal("courses"), title: "Courses" },
          { id: literal("customers"), title: "Customers" },
          { id: literal("items"), title: "Items" },
        ],
      },
      {
        type: "select",
        title: "Table Alert Style",
        description: "Select the alert you want to display on tables",
        id: literal("alert-style"),
        options: [
          { id: literal("pulse"), title: "Pulse", default: true },
          { id: literal("color"), title: "Color" },
          { id: literal("outline"), title: "Outline" },
          { id: literal("spin"), title: "Spin" },
        ],
      },
    ] satisfies UserConfigFieldGroup,
  },
};

export type View = keyof typeof schema.views;

import { InferUserConfigFieldGroupValue, UserConfigFieldGroup } from "./types";

export const getGroupDefaults = <T extends UserConfigFieldGroup>(
  schema: T
): InferUserConfigFieldGroupValue<T> => {
  const values = schema.map((field) => {
    if (field.type === "select") {
      const defaultOption = field.options.find((option) => option.default);

      return [field.id, defaultOption?.id ?? ""];
    }

    const defaultOptions = field.options
      .filter((option) => option.default)
      .map((option) => option.id);

    return [field.id, defaultOptions];
  });

  return Object.fromEntries(values);
};

export const getStringifiedFieldValue = (
  schema: UserConfigFieldGroup,
  fieldId: string,
  value: string[] | string,
  separator = ", "
): string => {
  const values = [value].flat();

  return values
    .map((value) => {
      const field = schema.find((field) => field.id === fieldId);
      const option = field?.options.find((option) => option.id === value);

      return option?.title ?? value;
    })
    .join(separator);
};

import { create } from "zustand";
import { persist } from "zustand/middleware";

import { InferUserConfigFieldGroupValue } from "./schema/types";
import { getGroupDefaults } from "./schema/utils";
import { schema, View } from "./schema";

export type ViewConfigs = Record<
  View,
  (InferUserConfigFieldGroupValue<(typeof schema.views)[View]> & {
    id: string;
    title: string;
  })[]
>;

type UserConfigViews = ViewConfigs & {
  add: <T extends View>(view: T, config: ViewConfigs[T][number]) => void;
  remove: <T extends View>(view: T, id: string) => void;
  update: <T extends View>(
    view: T,
    id: string,
    config: Partial<ViewConfigs[T][number]>
  ) => void;
};

type UserConfigStore = {
  views: UserConfigViews;
};

export const createUserConfigStore = () => {
  return create<UserConfigStore>()(
    persist(
      (set) => ({
        views: {
          tables: [
            {
              ...getGroupDefaults(schema.views.tables),
              id: "default",
              title: "Essentials",
            },
          ],

          // actions
          add: (view, config) => {
            return set((state) => ({
              views: {
                ...state.views,
                [view]: [...state.views[view], config],
              },
            }));
          },
          remove: (view, id) => {
            return set((state) => ({
              views: {
                ...state.views,
                [view]: state.views[view].filter((v) => v.id !== id),
              },
            }));
          },
          update: (view, id, payload) => {
            return set((state) => ({
              views: {
                ...state.views,
                [view]: state.views[view].map((v) =>
                  v.id === id ? { ...v, ...payload } : v
                ),
              },
            }));
          },
        },
      }),
      {
        name: "user-config",
        partialize: (state) => ({ views: state.views }),
        merge: (persistedState, currentState) => {
          return {
            ...currentState,
            views: {
              ...currentState.views,
              tables: currentState.views.tables.map((table) => {
                // @ts-expect-error - TODO: fix this
                const persisted = persistedState?.views?.tables?.find(
                  // @ts-expect-error - TODO: fix this
                  (t) => t.id === table.id
                );

                return {
                  ...table,
                  ...(persisted ?? {}),
                };
              }),
            },
          };
        },
      }
    )
  );
};

export const useUserConfig = createUserConfigStore();

export const useViewConfig = (type: View) => {
  return useUserConfig((state) => state.views[type][0]);
};

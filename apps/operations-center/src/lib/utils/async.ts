export const sleep = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const randomSleep = (min: number = 0, max: number = 1000) => {
  return sleep(Math.floor(Math.random() * (max - min + 1)) + min);
};

export const debounce = <T extends (...args: Parameters<T>) => void>(
  func: T,
  intervalMs = 64
) => {
  let id: ReturnType<typeof setTimeout> | null;

  const debouncedFn = (...args: Parameters<T>) => {
    if (id) {
      clearTimeout(id);
    }

    id = setTimeout(() => {
      func(...args);
    }, intervalMs);
  };

  debouncedFn.cancel = () => {
    if (id) {
      clearTimeout(id);
      id = null;
    }
  };

  return debouncedFn;
};

export const isPromise = (value: unknown): value is Promise<unknown> => {
  return value instanceof Promise;
};

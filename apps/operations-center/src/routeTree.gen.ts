/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from "@tanstack/react-router";

// Import Routes

import { Route as rootRoute } from "./routes/__root";
import { Route as UserTestingCompleteImport } from "./routes/user-testing-complete";
import { Route as ModeImport } from "./routes/$mode";
import { Route as IndexImport } from "./routes/index";
import { Route as DineInIndexImport } from "./routes/dine-in/index";
import { Route as DineInSettingsRouteImport } from "./routes/dine-in/settings/route";
import { Route as DineInSettingsIndexImport } from "./routes/dine-in/settings/index";
import { Route as DineInSpacesSpaceImport } from "./routes/dine-in/spaces/$space";
import { Route as DineInorderOrderImport } from "./routes/dine-in/(order)/$order";
import { Route as DineInSpacesSpaceIndexImport } from "./routes/dine-in/spaces/$space.index";
import { Route as DineInorderOrderSearchImport } from "./routes/dine-in/(order)/$order.search";
import { Route as DineInorderOrderNavLayoutImport } from "./routes/dine-in/(order)/$order._nav-layout";
import { Route as DineInSettingsTablesViewIndexImport } from "./routes/dine-in/settings/tables.$view.index";
import { Route as DineInorderOrderNavLayoutmenuIndexImport } from "./routes/dine-in/(order)/$order._nav-layout/(menu)/index";
import { Route as DineInorderOrderNavLayoutmenuMenuImport } from "./routes/dine-in/(order)/$order._nav-layout/(menu)/_menu";
import { Route as DineInorderOrderNavLayoutcartCartImport } from "./routes/dine-in/(order)/$order._nav-layout/(cart)/cart";
import { Route as DineInorderOrderNavLayoutmenuMenuSplatImport } from "./routes/dine-in/(order)/$order._nav-layout/(menu)/_menu.$";

// Create Virtual Routes

const DineInorderOrderNavLayoutmenuImport = createFileRoute(
  "/dine-in/(order)/$order/_nav-layout/(menu)"
)();

// Create/Update Routes

const UserTestingCompleteRoute = UserTestingCompleteImport.update({
  id: "/user-testing-complete",
  path: "/user-testing-complete",
  getParentRoute: () => rootRoute,
} as any);

const ModeRoute = ModeImport.update({
  id: "/$mode",
  path: "/$mode",
  getParentRoute: () => rootRoute,
} as any);

const IndexRoute = IndexImport.update({
  id: "/",
  path: "/",
  getParentRoute: () => rootRoute,
} as any);

const DineInIndexRoute = DineInIndexImport.update({
  id: "/dine-in/",
  path: "/dine-in/",
  getParentRoute: () => rootRoute,
} as any);

const DineInSettingsRouteRoute = DineInSettingsRouteImport.update({
  id: "/dine-in/settings",
  path: "/dine-in/settings",
  getParentRoute: () => rootRoute,
} as any);

const DineInSettingsIndexRoute = DineInSettingsIndexImport.update({
  id: "/",
  path: "/",
  getParentRoute: () => DineInSettingsRouteRoute,
} as any);

const DineInSpacesSpaceRoute = DineInSpacesSpaceImport.update({
  id: "/dine-in/spaces/$space",
  path: "/dine-in/spaces/$space",
  getParentRoute: () => rootRoute,
} as any);

const DineInorderOrderRoute = DineInorderOrderImport.update({
  id: "/dine-in/(order)/$order",
  path: "/dine-in/$order",
  getParentRoute: () => rootRoute,
} as any);

const DineInSpacesSpaceIndexRoute = DineInSpacesSpaceIndexImport.update({
  id: "/",
  path: "/",
  getParentRoute: () => DineInSpacesSpaceRoute,
} as any);

const DineInorderOrderSearchRoute = DineInorderOrderSearchImport.update({
  id: "/search",
  path: "/search",
  getParentRoute: () => DineInorderOrderRoute,
} as any);

const DineInorderOrderNavLayoutRoute = DineInorderOrderNavLayoutImport.update({
  id: "/_nav-layout",
  getParentRoute: () => DineInorderOrderRoute,
} as any);

const DineInorderOrderNavLayoutmenuRoute =
  DineInorderOrderNavLayoutmenuImport.update({
    id: "/(menu)",
    getParentRoute: () => DineInorderOrderNavLayoutRoute,
  } as any);

const DineInSettingsTablesViewIndexRoute =
  DineInSettingsTablesViewIndexImport.update({
    id: "/tables/$view/",
    path: "/tables/$view/",
    getParentRoute: () => DineInSettingsRouteRoute,
  } as any);

const DineInorderOrderNavLayoutmenuIndexRoute =
  DineInorderOrderNavLayoutmenuIndexImport.update({
    id: "/",
    path: "/",
    getParentRoute: () => DineInorderOrderNavLayoutmenuRoute,
  } as any);

const DineInorderOrderNavLayoutmenuMenuRoute =
  DineInorderOrderNavLayoutmenuMenuImport.update({
    id: "/_menu",
    getParentRoute: () => DineInorderOrderNavLayoutmenuRoute,
  } as any);

const DineInorderOrderNavLayoutcartCartRoute =
  DineInorderOrderNavLayoutcartCartImport.update({
    id: "/(cart)/cart",
    path: "/cart",
    getParentRoute: () => DineInorderOrderNavLayoutRoute,
  } as any);

const DineInorderOrderNavLayoutmenuMenuSplatRoute =
  DineInorderOrderNavLayoutmenuMenuSplatImport.update({
    id: "/$",
    path: "/$",
    getParentRoute: () => DineInorderOrderNavLayoutmenuMenuRoute,
  } as any);

// Populate the FileRoutesByPath interface

declare module "@tanstack/react-router" {
  interface FileRoutesByPath {
    "/": {
      id: "/";
      path: "/";
      fullPath: "/";
      preLoaderRoute: typeof IndexImport;
      parentRoute: typeof rootRoute;
    };
    "/$mode": {
      id: "/$mode";
      path: "/$mode";
      fullPath: "/$mode";
      preLoaderRoute: typeof ModeImport;
      parentRoute: typeof rootRoute;
    };
    "/user-testing-complete": {
      id: "/user-testing-complete";
      path: "/user-testing-complete";
      fullPath: "/user-testing-complete";
      preLoaderRoute: typeof UserTestingCompleteImport;
      parentRoute: typeof rootRoute;
    };
    "/dine-in/settings": {
      id: "/dine-in/settings";
      path: "/dine-in/settings";
      fullPath: "/dine-in/settings";
      preLoaderRoute: typeof DineInSettingsRouteImport;
      parentRoute: typeof rootRoute;
    };
    "/dine-in/": {
      id: "/dine-in/";
      path: "/dine-in";
      fullPath: "/dine-in";
      preLoaderRoute: typeof DineInIndexImport;
      parentRoute: typeof rootRoute;
    };
    "/dine-in/(order)/$order": {
      id: "/dine-in/(order)/$order";
      path: "/dine-in/$order";
      fullPath: "/dine-in/$order";
      preLoaderRoute: typeof DineInorderOrderImport;
      parentRoute: typeof rootRoute;
    };
    "/dine-in/spaces/$space": {
      id: "/dine-in/spaces/$space";
      path: "/dine-in/spaces/$space";
      fullPath: "/dine-in/spaces/$space";
      preLoaderRoute: typeof DineInSpacesSpaceImport;
      parentRoute: typeof rootRoute;
    };
    "/dine-in/settings/": {
      id: "/dine-in/settings/";
      path: "/";
      fullPath: "/dine-in/settings/";
      preLoaderRoute: typeof DineInSettingsIndexImport;
      parentRoute: typeof DineInSettingsRouteImport;
    };
    "/dine-in/(order)/$order/_nav-layout": {
      id: "/dine-in/(order)/$order/_nav-layout";
      path: "";
      fullPath: "/dine-in/$order";
      preLoaderRoute: typeof DineInorderOrderNavLayoutImport;
      parentRoute: typeof DineInorderOrderImport;
    };
    "/dine-in/(order)/$order/search": {
      id: "/dine-in/(order)/$order/search";
      path: "/search";
      fullPath: "/dine-in/$order/search";
      preLoaderRoute: typeof DineInorderOrderSearchImport;
      parentRoute: typeof DineInorderOrderImport;
    };
    "/dine-in/spaces/$space/": {
      id: "/dine-in/spaces/$space/";
      path: "/";
      fullPath: "/dine-in/spaces/$space/";
      preLoaderRoute: typeof DineInSpacesSpaceIndexImport;
      parentRoute: typeof DineInSpacesSpaceImport;
    };
    "/dine-in/settings/tables/$view/": {
      id: "/dine-in/settings/tables/$view/";
      path: "/tables/$view";
      fullPath: "/dine-in/settings/tables/$view";
      preLoaderRoute: typeof DineInSettingsTablesViewIndexImport;
      parentRoute: typeof DineInSettingsRouteImport;
    };
    "/dine-in/(order)/$order/_nav-layout/(cart)/cart": {
      id: "/dine-in/(order)/$order/_nav-layout/(cart)/cart";
      path: "/cart";
      fullPath: "/dine-in/$order/cart";
      preLoaderRoute: typeof DineInorderOrderNavLayoutcartCartImport;
      parentRoute: typeof DineInorderOrderNavLayoutImport;
    };
    "/dine-in/(order)/$order/_nav-layout/(menu)": {
      id: "/dine-in/(order)/$order/_nav-layout/(menu)";
      path: "/";
      fullPath: "/dine-in/$order/";
      preLoaderRoute: typeof DineInorderOrderNavLayoutmenuImport;
      parentRoute: typeof DineInorderOrderNavLayoutImport;
    };
    "/dine-in/(order)/$order/_nav-layout/(menu)/_menu": {
      id: "/dine-in/(order)/$order/_nav-layout/(menu)/_menu";
      path: "/";
      fullPath: "/dine-in/$order/";
      preLoaderRoute: typeof DineInorderOrderNavLayoutmenuMenuImport;
      parentRoute: typeof DineInorderOrderNavLayoutmenuRoute;
    };
    "/dine-in/(order)/$order/_nav-layout/(menu)/": {
      id: "/dine-in/(order)/$order/_nav-layout/(menu)/";
      path: "/";
      fullPath: "/dine-in/$order/";
      preLoaderRoute: typeof DineInorderOrderNavLayoutmenuIndexImport;
      parentRoute: typeof DineInorderOrderNavLayoutmenuImport;
    };
    "/dine-in/(order)/$order/_nav-layout/(menu)/_menu/$": {
      id: "/dine-in/(order)/$order/_nav-layout/(menu)/_menu/$";
      path: "/$";
      fullPath: "/dine-in/$order/$";
      preLoaderRoute: typeof DineInorderOrderNavLayoutmenuMenuSplatImport;
      parentRoute: typeof DineInorderOrderNavLayoutmenuMenuImport;
    };
  }
}

// Create and export the route tree

interface DineInSettingsRouteRouteChildren {
  DineInSettingsIndexRoute: typeof DineInSettingsIndexRoute;
  DineInSettingsTablesViewIndexRoute: typeof DineInSettingsTablesViewIndexRoute;
}

const DineInSettingsRouteRouteChildren: DineInSettingsRouteRouteChildren = {
  DineInSettingsIndexRoute: DineInSettingsIndexRoute,
  DineInSettingsTablesViewIndexRoute: DineInSettingsTablesViewIndexRoute,
};

const DineInSettingsRouteRouteWithChildren =
  DineInSettingsRouteRoute._addFileChildren(DineInSettingsRouteRouteChildren);

interface DineInorderOrderNavLayoutmenuMenuRouteChildren {
  DineInorderOrderNavLayoutmenuMenuSplatRoute: typeof DineInorderOrderNavLayoutmenuMenuSplatRoute;
}

const DineInorderOrderNavLayoutmenuMenuRouteChildren: DineInorderOrderNavLayoutmenuMenuRouteChildren =
  {
    DineInorderOrderNavLayoutmenuMenuSplatRoute:
      DineInorderOrderNavLayoutmenuMenuSplatRoute,
  };

const DineInorderOrderNavLayoutmenuMenuRouteWithChildren =
  DineInorderOrderNavLayoutmenuMenuRoute._addFileChildren(
    DineInorderOrderNavLayoutmenuMenuRouteChildren
  );

interface DineInorderOrderNavLayoutmenuRouteChildren {
  DineInorderOrderNavLayoutmenuMenuRoute: typeof DineInorderOrderNavLayoutmenuMenuRouteWithChildren;
  DineInorderOrderNavLayoutmenuIndexRoute: typeof DineInorderOrderNavLayoutmenuIndexRoute;
}

const DineInorderOrderNavLayoutmenuRouteChildren: DineInorderOrderNavLayoutmenuRouteChildren =
  {
    DineInorderOrderNavLayoutmenuMenuRoute:
      DineInorderOrderNavLayoutmenuMenuRouteWithChildren,
    DineInorderOrderNavLayoutmenuIndexRoute:
      DineInorderOrderNavLayoutmenuIndexRoute,
  };

const DineInorderOrderNavLayoutmenuRouteWithChildren =
  DineInorderOrderNavLayoutmenuRoute._addFileChildren(
    DineInorderOrderNavLayoutmenuRouteChildren
  );

interface DineInorderOrderNavLayoutRouteChildren {
  DineInorderOrderNavLayoutcartCartRoute: typeof DineInorderOrderNavLayoutcartCartRoute;
  DineInorderOrderNavLayoutmenuRoute: typeof DineInorderOrderNavLayoutmenuRouteWithChildren;
}

const DineInorderOrderNavLayoutRouteChildren: DineInorderOrderNavLayoutRouteChildren =
  {
    DineInorderOrderNavLayoutcartCartRoute:
      DineInorderOrderNavLayoutcartCartRoute,
    DineInorderOrderNavLayoutmenuRoute:
      DineInorderOrderNavLayoutmenuRouteWithChildren,
  };

const DineInorderOrderNavLayoutRouteWithChildren =
  DineInorderOrderNavLayoutRoute._addFileChildren(
    DineInorderOrderNavLayoutRouteChildren
  );

interface DineInorderOrderRouteChildren {
  DineInorderOrderNavLayoutRoute: typeof DineInorderOrderNavLayoutRouteWithChildren;
  DineInorderOrderSearchRoute: typeof DineInorderOrderSearchRoute;
}

const DineInorderOrderRouteChildren: DineInorderOrderRouteChildren = {
  DineInorderOrderNavLayoutRoute: DineInorderOrderNavLayoutRouteWithChildren,
  DineInorderOrderSearchRoute: DineInorderOrderSearchRoute,
};

const DineInorderOrderRouteWithChildren =
  DineInorderOrderRoute._addFileChildren(DineInorderOrderRouteChildren);

interface DineInSpacesSpaceRouteChildren {
  DineInSpacesSpaceIndexRoute: typeof DineInSpacesSpaceIndexRoute;
}

const DineInSpacesSpaceRouteChildren: DineInSpacesSpaceRouteChildren = {
  DineInSpacesSpaceIndexRoute: DineInSpacesSpaceIndexRoute,
};

const DineInSpacesSpaceRouteWithChildren =
  DineInSpacesSpaceRoute._addFileChildren(DineInSpacesSpaceRouteChildren);

export interface FileRoutesByFullPath {
  "/": typeof IndexRoute;
  "/$mode": typeof ModeRoute;
  "/user-testing-complete": typeof UserTestingCompleteRoute;
  "/dine-in/settings": typeof DineInSettingsRouteRouteWithChildren;
  "/dine-in": typeof DineInIndexRoute;
  "/dine-in/$order": typeof DineInorderOrderNavLayoutRouteWithChildren;
  "/dine-in/spaces/$space": typeof DineInSpacesSpaceRouteWithChildren;
  "/dine-in/settings/": typeof DineInSettingsIndexRoute;
  "/dine-in/$order/search": typeof DineInorderOrderSearchRoute;
  "/dine-in/spaces/$space/": typeof DineInSpacesSpaceIndexRoute;
  "/dine-in/settings/tables/$view": typeof DineInSettingsTablesViewIndexRoute;
  "/dine-in/$order/cart": typeof DineInorderOrderNavLayoutcartCartRoute;
  "/dine-in/$order/": typeof DineInorderOrderNavLayoutmenuIndexRoute;
  "/dine-in/$order/$": typeof DineInorderOrderNavLayoutmenuMenuSplatRoute;
}

export interface FileRoutesByTo {
  "/": typeof IndexRoute;
  "/$mode": typeof ModeRoute;
  "/user-testing-complete": typeof UserTestingCompleteRoute;
  "/dine-in": typeof DineInIndexRoute;
  "/dine-in/$order": typeof DineInorderOrderNavLayoutmenuIndexRoute;
  "/dine-in/settings": typeof DineInSettingsIndexRoute;
  "/dine-in/$order/search": typeof DineInorderOrderSearchRoute;
  "/dine-in/spaces/$space": typeof DineInSpacesSpaceIndexRoute;
  "/dine-in/settings/tables/$view": typeof DineInSettingsTablesViewIndexRoute;
  "/dine-in/$order/cart": typeof DineInorderOrderNavLayoutcartCartRoute;
  "/dine-in/$order/$": typeof DineInorderOrderNavLayoutmenuMenuSplatRoute;
}

export interface FileRoutesById {
  __root__: typeof rootRoute;
  "/": typeof IndexRoute;
  "/$mode": typeof ModeRoute;
  "/user-testing-complete": typeof UserTestingCompleteRoute;
  "/dine-in/settings": typeof DineInSettingsRouteRouteWithChildren;
  "/dine-in/": typeof DineInIndexRoute;
  "/dine-in/(order)/$order": typeof DineInorderOrderRouteWithChildren;
  "/dine-in/spaces/$space": typeof DineInSpacesSpaceRouteWithChildren;
  "/dine-in/settings/": typeof DineInSettingsIndexRoute;
  "/dine-in/(order)/$order/_nav-layout": typeof DineInorderOrderNavLayoutRouteWithChildren;
  "/dine-in/(order)/$order/search": typeof DineInorderOrderSearchRoute;
  "/dine-in/spaces/$space/": typeof DineInSpacesSpaceIndexRoute;
  "/dine-in/settings/tables/$view/": typeof DineInSettingsTablesViewIndexRoute;
  "/dine-in/(order)/$order/_nav-layout/(cart)/cart": typeof DineInorderOrderNavLayoutcartCartRoute;
  "/dine-in/(order)/$order/_nav-layout/(menu)": typeof DineInorderOrderNavLayoutmenuRouteWithChildren;
  "/dine-in/(order)/$order/_nav-layout/(menu)/_menu": typeof DineInorderOrderNavLayoutmenuMenuRouteWithChildren;
  "/dine-in/(order)/$order/_nav-layout/(menu)/": typeof DineInorderOrderNavLayoutmenuIndexRoute;
  "/dine-in/(order)/$order/_nav-layout/(menu)/_menu/$": typeof DineInorderOrderNavLayoutmenuMenuSplatRoute;
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | "/"
    | "/$mode"
    | "/user-testing-complete"
    | "/dine-in/settings"
    | "/dine-in"
    | "/dine-in/$order"
    | "/dine-in/spaces/$space"
    | "/dine-in/settings/"
    | "/dine-in/$order/search"
    | "/dine-in/spaces/$space/"
    | "/dine-in/settings/tables/$view"
    | "/dine-in/$order/cart"
    | "/dine-in/$order/"
    | "/dine-in/$order/$";
  fileRoutesByTo: FileRoutesByTo;
  to:
    | "/"
    | "/$mode"
    | "/user-testing-complete"
    | "/dine-in"
    | "/dine-in/$order"
    | "/dine-in/settings"
    | "/dine-in/$order/search"
    | "/dine-in/spaces/$space"
    | "/dine-in/settings/tables/$view"
    | "/dine-in/$order/cart"
    | "/dine-in/$order/$";
  id:
    | "__root__"
    | "/"
    | "/$mode"
    | "/user-testing-complete"
    | "/dine-in/settings"
    | "/dine-in/"
    | "/dine-in/(order)/$order"
    | "/dine-in/spaces/$space"
    | "/dine-in/settings/"
    | "/dine-in/(order)/$order/_nav-layout"
    | "/dine-in/(order)/$order/search"
    | "/dine-in/spaces/$space/"
    | "/dine-in/settings/tables/$view/"
    | "/dine-in/(order)/$order/_nav-layout/(cart)/cart"
    | "/dine-in/(order)/$order/_nav-layout/(menu)"
    | "/dine-in/(order)/$order/_nav-layout/(menu)/_menu"
    | "/dine-in/(order)/$order/_nav-layout/(menu)/"
    | "/dine-in/(order)/$order/_nav-layout/(menu)/_menu/$";
  fileRoutesById: FileRoutesById;
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute;
  ModeRoute: typeof ModeRoute;
  UserTestingCompleteRoute: typeof UserTestingCompleteRoute;
  DineInSettingsRouteRoute: typeof DineInSettingsRouteRouteWithChildren;
  DineInIndexRoute: typeof DineInIndexRoute;
  DineInorderOrderRoute: typeof DineInorderOrderRouteWithChildren;
  DineInSpacesSpaceRoute: typeof DineInSpacesSpaceRouteWithChildren;
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  ModeRoute: ModeRoute,
  UserTestingCompleteRoute: UserTestingCompleteRoute,
  DineInSettingsRouteRoute: DineInSettingsRouteRouteWithChildren,
  DineInIndexRoute: DineInIndexRoute,
  DineInorderOrderRoute: DineInorderOrderRouteWithChildren,
  DineInSpacesSpaceRoute: DineInSpacesSpaceRouteWithChildren,
};

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/$mode",
        "/user-testing-complete",
        "/dine-in/settings",
        "/dine-in/",
        "/dine-in/(order)/$order",
        "/dine-in/spaces/$space"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/$mode": {
      "filePath": "$mode.tsx"
    },
    "/user-testing-complete": {
      "filePath": "user-testing-complete.tsx"
    },
    "/dine-in/settings": {
      "filePath": "dine-in/settings/route.tsx",
      "children": [
        "/dine-in/settings/",
        "/dine-in/settings/tables/$view/"
      ]
    },
    "/dine-in/": {
      "filePath": "dine-in/index.tsx"
    },
    "/dine-in/(order)/$order": {
      "filePath": "dine-in/(order)/$order.tsx",
      "children": [
        "/dine-in/(order)/$order/_nav-layout",
        "/dine-in/(order)/$order/search"
      ]
    },
    "/dine-in/spaces/$space": {
      "filePath": "dine-in/spaces/$space.tsx",
      "children": [
        "/dine-in/spaces/$space/"
      ]
    },
    "/dine-in/settings/": {
      "filePath": "dine-in/settings/index.tsx",
      "parent": "/dine-in/settings"
    },
    "/dine-in/(order)/$order/_nav-layout": {
      "filePath": "dine-in/(order)/$order._nav-layout.tsx",
      "parent": "/dine-in/(order)/$order",
      "children": [
        "/dine-in/(order)/$order/_nav-layout/(cart)/cart",
        "/dine-in/(order)/$order/_nav-layout/(menu)"
      ]
    },
    "/dine-in/(order)/$order/search": {
      "filePath": "dine-in/(order)/$order.search.tsx",
      "parent": "/dine-in/(order)/$order"
    },
    "/dine-in/spaces/$space/": {
      "filePath": "dine-in/spaces/$space.index.tsx",
      "parent": "/dine-in/spaces/$space"
    },
    "/dine-in/settings/tables/$view/": {
      "filePath": "dine-in/settings/tables.$view.index.tsx",
      "parent": "/dine-in/settings"
    },
    "/dine-in/(order)/$order/_nav-layout/(cart)/cart": {
      "filePath": "dine-in/(order)/$order._nav-layout/(cart)/cart.tsx",
      "parent": "/dine-in/(order)/$order/_nav-layout"
    },
    "/dine-in/(order)/$order/_nav-layout/(menu)": {
      "filePath": "dine-in/(order)/$order._nav-layout/(menu)",
      "parent": "/dine-in/(order)/$order/_nav-layout",
      "children": [
        "/dine-in/(order)/$order/_nav-layout/(menu)/_menu",
        "/dine-in/(order)/$order/_nav-layout/(menu)/"
      ]
    },
    "/dine-in/(order)/$order/_nav-layout/(menu)/_menu": {
      "filePath": "dine-in/(order)/$order._nav-layout/(menu)/_menu.tsx",
      "parent": "/dine-in/(order)/$order/_nav-layout/(menu)",
      "children": [
        "/dine-in/(order)/$order/_nav-layout/(menu)/_menu/$"
      ]
    },
    "/dine-in/(order)/$order/_nav-layout/(menu)/": {
      "filePath": "dine-in/(order)/$order._nav-layout/(menu)/index.tsx",
      "parent": "/dine-in/(order)/$order/_nav-layout/(menu)"
    },
    "/dine-in/(order)/$order/_nav-layout/(menu)/_menu/$": {
      "filePath": "dine-in/(order)/$order._nav-layout/(menu)/_menu.$.tsx",
      "parent": "/dine-in/(order)/$order/_nav-layout/(menu)/_menu"
    }
  }
}
ROUTE_MANIFEST_END */

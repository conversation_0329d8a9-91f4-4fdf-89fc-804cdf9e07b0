import {
  Button,
  cn,
  QuantitySelector,
  QuantitySelectorIncrease,
  QuantitySelectorQuantity,
  QuantitySelectorDecrease,
  Price,
  DrawerTrigger,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerActions,
  DrawerClose,
  Drawer,
} from "@allo/ui";
import { Check, TriangleAlert, X } from "lucide-react";
import { useMemo, useState } from "react";

import { useToast } from "@/components/ui/toast";
import { calculateCartItemTotalPrice } from "@/lib/cart/utils";
import { cancelItemSuggestedReasons, OrderItem } from "@/lib/mock-data/order";

import { useCartActions } from "@/lib/cart/store";
import { uuid } from "@/lib/utils/string";
import {
  CartItemCard,
  CartItemCardContent,
  CartItemCardDetails,
  CartItemCardTitle,
  CartItemCardRow,
} from "@/components/cart/cart-item-card";
import { CartItemPriceDetails } from "@/components/cart/cart-item-price-details";
import { NotesInput } from "@/components/notes-input";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { postCancelItem } from "@/lib/mock-data/api";
import { getOrderOptions } from "@/lib/queries";
import { CartItem } from "@/lib/cart/types";

interface OrderedCartItemProps {
  orderId: string;
  item: OrderItem;
}

export const OrderedCartItem = ({ orderId, item }: OrderedCartItemProps) => {
  const { addItem } = useCartActions(orderId);
  const { toast } = useToast();

  const stringifiedQuantity = useMemo(() => {
    return `${item.quantity} ${item.quantity === 1 ? "item" : "items"}`;
  }, [item.quantity]);

  const totalPrice = useMemo(() => {
    return calculateCartItemTotalPrice(item);
  }, [item]);

  const handleReorder = () => {
    addItem({ ...item, id: uuid(), quantity: 1 });

    toast({
      message: "1 item added to pending",
      variant: "success",
      icon: Check,
    });
  };

  return (
    <CartItemCard className={cn(item.isCancelled && "line-through")}>
      <CartItemCardContent>
        <CartItemCardRow>
          <CartItemCardTitle>{item.name}</CartItemCardTitle>
          {item.isCancelled ? (
            <div>
              {stringifiedQuantity + " · "}
              <Price amount={totalPrice} />
            </div>
          ) : (
            <div className="flex items-center gap-2.5 text-sm">
              <span>x{item.quantity}</span>
              <Button onClick={handleReorder}>Reorder</Button>
            </div>
          )}
        </CartItemCardRow>
        {!item.isCancelled && (
          <>
            <CartItemCardDetails>
              <CartItemPriceDetails item={item} />
            </CartItemCardDetails>
            <CartItemCardRow>
              <CancelItem orderId={orderId} item={item} />
              <div>
                {stringifiedQuantity + " · "}
                <Price amount={totalPrice} />
              </div>
            </CartItemCardRow>
          </>
        )}
      </CartItemCardContent>
    </CartItemCard>
  );
};

interface CancelItemProps {
  orderId: string;
  item: CartItem;
}

const CancelItem = ({ orderId, item }: CancelItemProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();

  const [quantity, setQuantity] = useState(1);
  const [reasons, setReasons] = useState<string[]>([]);

  const queryClient = useQueryClient();
  const cancel = useMutation({
    mutationFn: () => postCancelItem(orderId, item!.id, quantity, reasons),
    onSettled: async (data, error) => {
      if (error) {
        toast({
          message: error.message,
          variant: "error",
          icon: TriangleAlert,
          delay: 200,
        });
      } else {
        await queryClient.invalidateQueries({
          queryKey: getOrderOptions(orderId).queryKey,
        });

        toast({
          message: `${quantity} item${quantity === 1 ? "" : "s"} cancelled`,
          variant: "success",
          icon: Check,
          delay: 200,
        });

        setIsOpen(false);
      }
    },
  });

  return (
    <Drawer
      open={isOpen}
      onOpenChange={(open) => {
        if (cancel.isPending) return;
        setIsOpen(open);

        if (!open) {
          setQuantity(1);
          setReasons([]);
        }
      }}
    >
      <DrawerTrigger asChild>
        <Button size="sm" variant="negative">
          Cancel
        </Button>
      </DrawerTrigger>
      <DrawerContent inert={cancel.isPending} persistExitAnimation>
        {item && (
          <>
            <DrawerHeader>
              <DrawerTitle>Cancel "{item.name}"</DrawerTitle>
            </DrawerHeader>
            <div className="bg-background-high space-y-2.5 p-2.5 text-sm">
              <p>Notes</p>
              <NotesInput
                customNotePlaceholder="Type a custom notes..."
                presets={cancelItemSuggestedReasons}
                onChange={setReasons}
              />
            </div>
            <DrawerActions>
              <QuantitySelector
                quantity={quantity}
                min={1}
                max={item.quantity}
                onChange={setQuantity}
              >
                <QuantitySelectorDecrease />
                <QuantitySelectorQuantity />
                <QuantitySelectorIncrease />
              </QuantitySelector>
              <Button
                variant="negative"
                className="grow"
                onClick={() => cancel.mutate()}
                isLoading={cancel.isPending}
              >
                Cancel {quantity} item{quantity === 1 ? "" : "s"}
              </Button>
              <DrawerClose asChild>
                <Button square>
                  <X className="size-4" />
                </Button>
              </DrawerClose>
            </DrawerActions>
          </>
        )}
      </DrawerContent>
    </Drawer>
  );
};

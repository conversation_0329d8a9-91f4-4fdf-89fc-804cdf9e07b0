import { cn, easings, Skeleton } from "@allo/ui";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { motion } from "motion/react";
import { ComponentPropsWithRef, useEffect, useMemo } from "react";

import { BottomBarPortal } from "@/components/bottom-bar";
import { useRegisterBreadcrumb } from "@/components/ui/breadcrumbs";
import {
  SegmentedControl,
  SegmentedControlItem,
  SegmentedControlProps,
} from "@/components/ui/segmented-control";
import { Swipeable, SwipeableItem } from "@/components/swipeable";
import { useCart } from "@/lib/cart/store";
import { getOrderOptions } from "@/lib/queries";

import { PendingCartItem } from "./-components/pending-cart-item";
import { OrderedCartItem } from "./-components/ordered-cart-item";
import {
  CartItemCard,
  CartItemCardDetails,
  CartItemCardRow,
} from "@/components/cart/cart-item-card";

type CartFilter = "pending" | "to-pay";

export const Route = createFileRoute(
  "/dine-in/(order)/$order/_nav-layout/(cart)/cart"
)({
  component: TableCart,
  validateSearch: (
    search: Record<string, unknown>
  ): { status: CartFilter; showTopBar?: boolean } => {
    return {
      status: (search.status as CartFilter) || "pending",
      showTopBar: (search.showTopBar as boolean) || false,
    };
  },
});

function TableCart() {
  const { order: orderId } = Route.useParams();
  const { status } = Route.useSearch();
  const navigate = useNavigate();

  const cart = useCart(orderId);
  const order = useQuery(getOrderOptions(orderId));

  useRegisterBreadcrumb([{ id: "cart", label: "Details" }]);

  const sortedCartItems = useMemo(() => {
    const items = order.data?.ordered ?? [];

    return [...items].sort((a, b) => {
      return a.isCancelled ? 1 : b.isCancelled ? -1 : 0;
    });
  }, [order.data]);

  useEffect(() => {
    // if the cart is empty, navigate back to the tables page
    if (cart.length === 0 && order.data?.ordered.length === 0) {
      navigate({
        to: "/dine-in/$order",
        params: { order: orderId },
      });
    }
  }, [cart, order.data, navigate, orderId]);

  return (
    <>
      <Swipeable
        className="bg-background-high min-h-full w-full overflow-hidden text-sm"
        onSwipe={(dir) => {
          const newStatus = dir === "left" ? "to-pay" : "pending";
          navigate({ to: ".", search: { status: newStatus } });
        }}
      >
        {status === "pending" && (
          <SwipeableItem direction="left" key="pending">
            {cart.length === 0 && (
              <EmptyCartLabel>No pending items</EmptyCartLabel>
            )}
            <ul>
              {cart.map((item) => (
                <motion.li key={item.id} layout="position">
                  <PendingCartItem orderId={orderId} item={item} />
                </motion.li>
              ))}
            </ul>
          </SwipeableItem>
        )}
        {status === "to-pay" && (
          <SwipeableItem direction="right" key="to-pay">
            {order.isPending && <CartItemSkeleton />}
            {order.isSuccess && (
              <ul>
                {sortedCartItems.map((item) => (
                  <motion.li key={item.id} layout="position">
                    <OrderedCartItem orderId={orderId} item={item} />
                  </motion.li>
                ))}
              </ul>
            )}
            {order.isSuccess && order.data.ordered.length === 0 && (
              <EmptyCartLabel>No items to pay</EmptyCartLabel>
            )}
          </SwipeableItem>
        )}
      </Swipeable>

      {/* Status Control */}
      <BottomBarPortal id="cart-filter">
        <div className="absolute bottom-full flex w-full items-center justify-between overflow-hidden py-2">
          <MotionSegmentedControl
            value={status ?? "pending"}
            onChange={(value) => {
              navigate({ to: ".", search: { status: value as CartFilter } });
            }}
          >
            <SegmentedControlItem key="pending" value="pending">
              Pending
            </SegmentedControlItem>
            <SegmentedControlItem key="to-pay" value="to-pay">
              To Pay
            </SegmentedControlItem>
          </MotionSegmentedControl>
        </div>
      </BottomBarPortal>
    </>
  );
}

function CartItemSkeleton() {
  return (
    <CartItemCard>
      <CartItemCardRow>
        <div className="mt-2 w-full space-y-1">
          <Skeleton className="h-3 w-52" />
          <Skeleton className="h-3 w-48" />
        </div>
        <Skeleton className="h-10 w-28" />
      </CartItemCardRow>
      <CartItemCardDetails>
        <Skeleton className="h-3 w-4/4" />
        <Skeleton className="h-3 w-2/3" />
        <Skeleton className="h-3 w-3/4" />
      </CartItemCardDetails>
      <CartItemCardRow>
        <Skeleton className="h-5 w-12" />
        <Skeleton className="h-3 w-10" />
      </CartItemCardRow>
    </CartItemCard>
  );
}

const segmentControlMotionVariants = {
  hidden: {
    y: "120%",
    opacity: 0.8,
    scale: 0.95,
    transition: { duration: 0.15, ease: easings["emphasized-accelerate"] },
  },
  visible: {
    y: 0,
    opacity: 1,
    scale: 1,
    transition: { duration: 0.3, ease: easings["emphasized-decelerate"] },
  },
};

const MSegmentedControl = motion.create(SegmentedControl);

function MotionSegmentedControl({
  value,
  onChange,
  children,
}: SegmentedControlProps) {
  return (
    <MSegmentedControl
      value={value}
      onChange={onChange}
      variants={segmentControlMotionVariants}
      initial="hidden"
      animate="visible"
      exit="hidden"
    >
      {children}
    </MSegmentedControl>
  );
}

function EmptyCartLabel({
  className,
  children,
  ...props
}: ComponentPropsWithRef<"p">) {
  return (
    <p
      className={cn(
        "text-foreground-secondary py-12 text-center text-xs",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

import { darkenColor } from "@/lib/utils/color";
import { Button, ButtonProps, cn, Skeleton } from "@allo/ui";
import { ComponentPropsWithRef } from "react";

interface SuperFolderButtonProps extends ButtonProps {
  color: string;
}

export function SuperFolderButton({
  color,
  children,
  className,
  ...props
}: SuperFolderButtonProps) {
  return (
    <Button
      {...props}
      className={cn(
        "text-background h-auto min-h-17 w-30 p-2",
        "flex-col items-start justify-between text-left text-sm",
        "[--chunky-bg-color:var(--chunky-bg-color-base)]",
        "[&[aria-pressed][data-state=on]]:[--chunky-bg-color:var(--chunky-bg-color-active)]",
        className
      )}
      style={{
        "--chunky-bg-color-base": color,
        "--chunky-bg-color-active": darkenColor(color, 33),
        "--chunky-border-color": "rgba(0, 0, 0, 0.16)",
        "--chunky-depth": "4px",
      }}
    >
      {children}
    </Button>
  );
}

export const SuperFolderButtonEmoji = ({
  className,
  children,
  ...props
}: ComponentPropsWithRef<"div">) => {
  return (
    <div
      className={cn("text-xl leading-none empty:hidden", className)}
      {...props}
    >
      {children?.toString().trim() ? children : null}
    </div>
  );
};

export const SuperFolderButtonLabel = ({
  className,
  children,
  ...props
}: ComponentPropsWithRef<"div">) => {
  return (
    <div
      className={cn("whitespace-no-wrap mt-auto w-full truncate", className)}
      {...props}
    >
      {children}
    </div>
  );
};

export const SuperFolderButtonSkeleton = ({
  className,
  ...props
}: ComponentPropsWithRef<"div">) => {
  return (
    <Skeleton
      className={cn("block h-17 w-24 rounded-md", className)}
      {...props}
    />
  );
};

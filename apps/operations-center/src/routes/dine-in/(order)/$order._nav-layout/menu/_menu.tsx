import { useQuery } from "@tanstack/react-query";
import {
  createFileRoute,
  Link,
  Outlet,
  useLayoutEffect,
} from "@tanstack/react-router";

import { useSplat } from "@/lib/hooks/use-splat";
import { getVersionedMenus } from "@/lib/queries";

import {
  SuperFolderButton,
  SuperFolderButtonEmoji,
  SuperFolderButtonLabel,
  SuperFolderButtonSkeleton,
} from "./-components/super-folder-button";
import { useRef } from "react";
import { getSuperFolderColorByName } from "@/lib/mock-data/menus/colors";

export const Route = createFileRoute(
  "/dine-in/(order)/$order/_nav-layout/menu/_menu"
)({
  component: MenuLayout,
});

function MenuLayout() {
  const [superFolderId] = useSplat();
  const versionedMenus = useQuery(getVersionedMenus());
  const superFolderNavRef = useRef<HTMLUListElement>(null);

  useLayoutEffect(() => {
    const superFolderNav = superFolderNavRef.current;
    const activeSuperFolderButton = document.getElementById(superFolderId);

    if (activeSuperFolderButton && superFolderNav) {
      const SCROLL_PADDING_TOP = 6;

      superFolderNav.scrollTo({
        top:
          activeSuperFolderButton.offsetTop -
          superFolderNav.offsetTop -
          SCROLL_PADDING_TOP,
        behavior: "instant",
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="grid h-full grid-cols-[auto_1fr] overflow-hidden">
      <nav
        ref={superFolderNavRef}
        className="border-border-hard no-scrollbar overflow-y-auto border-r"
      >
        <ul className="space-y-1.5 p-2.5">
          {versionedMenus.isPending && <MenuSkeleton />}
          {versionedMenus.isSuccess &&
            versionedMenus.data.items.map((folder) => {
              const isActive = folder.id === superFolderId;

              return (
                <li key={folder.id}>
                  <SuperFolderButton
                    id={folder.id}
                    asChild
                    color={getSuperFolderColorByName(folder.color as any)}
                    aria-pressed={isActive}
                    data-state={isActive ? "on" : "off"}
                  >
                    <Link
                      from={Route.fullPath}
                      to="./$"
                      params={{ _splat: folder.id }}
                    >
                      <SuperFolderButtonEmoji>
                        {folder.emoji}
                      </SuperFolderButtonEmoji>
                      <SuperFolderButtonLabel>
                        {folder.title}
                      </SuperFolderButtonLabel>
                    </Link>
                  </SuperFolderButton>
                </li>
              );
            })}
        </ul>
      </nav>
      <div className="overflow-y-auto">
        <Outlet />
      </div>
    </div>
  );
}

function MenuSkeleton() {
  return (
    <>
      {new Array(5).fill(null).map((_, index) => (
        <li key={index}>
          <SuperFolderButtonSkeleton />
        </li>
      ))}
    </>
  );
}

import { Button, Input } from "@allo/ui";
import {
  createFileRoute,
  Link,
  useCanGoBack,
  useRouter,
} from "@tanstack/react-router";
import { BottomBarPortal } from "@/components/bottom-bar";
import { RefreshCcw, X } from "lucide-react";
import { useState } from "react";
import { useDebouncedValue } from "@/lib/hooks/use-debounced-value";
import { useRegisterBreadcrumb } from "@/components/ui/breadcrumbs";
import { fetchProducts } from "@/lib/mock-data/api";
import { useQuery } from "@tanstack/react-query";
import { MenuProductCardGrid } from "@/components/menu/menu-product-card-grid";
import { MenuProductCard } from "@/components/menu/menu-product-card";

export const Route = createFileRoute("/dine-in/(order)/$order/search")({
  component: OrderSearch,
});

function OrderSearch() {
  const params = Route.useParams();
  const router = useRouter();
  const canGoBack = useCanGoBack();

  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebouncedValue(searchQuery, 64);

  const {
    data: products,
    isSuccess,
    isError,
    isPlaceholderData,
    refetch,
  } = useQuery({
    queryKey: ["products", debouncedSearchQuery],
    staleTime: Infinity,
    throwOnError: false,
    placeholderData: (prev) => prev || [],
    queryFn: () => {
      if (debouncedSearchQuery.length === 0) {
        return [];
      }

      return fetchProducts(debouncedSearchQuery);
    },
  });

  useRegisterBreadcrumb([{ id: "search", label: "Search" }]);

  return (
    <>
      <div className="relative h-full w-full">
        {isError && (
          <div className="flex h-full w-full flex-col items-center justify-center gap-4">
            <p className="text-foreground-secondary text-sm">
              Failed to fetch products
            </p>
            <Button variant="secondary" size="sm" onClick={() => refetch()}>
              <RefreshCcw className="size-3" />
              Retry
            </Button>
          </div>
        )}
        {isSuccess && (
          <>
            {products.length === 0 && (
              <div className="flex h-full w-full flex-col items-center justify-center gap-4">
                <img
                  src="/images/search-illustration.png"
                  alt="Search empty"
                  className="size-26"
                />
                <p className="text-foreground-secondary text-sm">
                  Search, tap, nailed it!
                </p>
              </div>
            )}
            {products.length > 0 && (
              <div className="p-2.5 pb-12">
                <MenuProductCardGrid>
                  {products.map((product) => (
                    <MenuProductCard
                      key={product.id}
                      product={product}
                      orderId={params.order}
                    />
                  ))}
                </MenuProductCardGrid>
              </div>
            )}
            {isPlaceholderData && (
              <div className="bg-background-low/66 absolute inset-0 z-3 animate-pulse" />
            )}
          </>
        )}
      </div>

      <BottomBarPortal id="order-nav-bar">
        <div className="flex items-center gap-2 p-3">
          <div className="relative grow">
            <Input
              autoFocus
              id="order-search-input"
              placeholder="Name, code..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="transition-none"
            />
            {searchQuery && (
              <button
                className="bg-foreground/75 absolute top-1/2 right-3 grid size-4 -translate-y-1/2 place-content-center rounded-full"
                onClick={() => setSearchQuery("")}
              >
                <X className="text-background size-2.5 stroke-3" />
              </button>
            )}
          </div>
          {canGoBack ? (
            <Button onClick={() => router.history.back()}>Cancel</Button>
          ) : (
            <Button asChild>
              <Link from={Route.fullPath} to="..">
                Cancel
              </Link>
            </Button>
          )}
        </div>
      </BottomBarPortal>
    </>
  );
}

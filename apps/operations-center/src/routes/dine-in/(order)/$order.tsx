import { ProductConfigurationDrawerProvider } from "@/components/product-configuration/drawer";
import { fetchOrder } from "@/lib/mock-data/api";
import { getOrderOptions } from "@/lib/queries";
import { But<PERSON> } from "@allo/ui";
import { useQuery } from "@tanstack/react-query";
import {
  createFileRoute,
  Link,
  notFound,
  Outlet,
} from "@tanstack/react-router";
import {
  Error,
  ErrorTitle,
  ErrorDescription,
  ErrorActions,
} from "@/components/error";
import { isErrorWithStatus } from "@/lib/utils/error";
import { useRegisterBreadcrumb } from "@/components/ui/breadcrumbs";
import { OrderStatusBar } from "./-components/order-status-bar";
import { BottomBarPortal } from "@/components/bottom-bar";

export const Route = createFileRoute("/dine-in/(order)/$order")({
  component: OrderRoute,
  // TODO: should be beforeLoad, but its breaking the notFoundComponent
  loader: async ({ params, context: { queryClient } }) => {
    let order = queryClient.getQueryData(
      getOrderOptions(params.order).queryKey
    );

    if (!order) {
      try {
        order = await fetchOrder(params.order);
        queryClient.setQueryData(getOrderOptions(params.order).queryKey, order);
      } catch (error) {
        if (isErrorWithStatus(error) && error.status === 404) {
          throw notFound();
        }

        throw error;
      }
    }
  },
  notFoundComponent: () => (
    <Error>
      <ErrorTitle>Order not found</ErrorTitle>
      <ErrorDescription>
        The order you are looking for does not exist.
      </ErrorDescription>
      <ErrorActions>
        <Button asChild>
          <Link to="/dine-in">Back to Tables</Link>
        </Button>
      </ErrorActions>
    </Error>
  ),
});

function OrderRoute() {
  const params = Route.useParams();
  const order = useQuery(getOrderOptions(params.order));

  useRegisterBreadcrumb([
    {
      id: "table",
      label: order.data?.tableName || "",
      loading: order.isPending,
      variant: "badge",
    },
  ]);

  return (
    <ProductConfigurationDrawerProvider>
      <Outlet />
      <BottomBarPortal id="order-status-bar">
        <OrderStatusBar orderId={params.order} className="-order-1" />
      </BottomBarPortal>
    </ProductConfigurationDrawerProvider>
  );
}

import {
  Button,
  DropdownItem,
  DropdownItems,
  Dropdown,
  DropdownTrigger,
  DropdownDivider,
} from "@allo/ui";
import {
  FileInputIcon,
  FileXIcon,
  FolderOpenIcon,
  PlusIcon,
  ReceiptTextIcon,
} from "lucide-react";
import { ChevronDown } from "lucide-react";

import { getOrderOptions } from "@/lib/queries";
import { useQuery } from "@tanstack/react-query";
import { useOrderAction } from "@/components/order-actions";

interface OrderActionsDropdownProps {
  orderId: string;
}

export const OrderActionsDropdown = ({
  orderId,
}: OrderActionsDropdownProps) => {
  const order = useQuery(getOrderOptions(orderId));

  const moveOrder = useOrderAction("move-order", order.data);
  const cancelOrder = useOrderAction("cancel-order", order.data);
  const moveItems = useOrderAction("move-items", order.data);

  return (
    <Dropdown>
      <DropdownTrigger asChild>
        <Button size="sm" className="gap-0.5">
          Actions <ChevronDown className="size-4" />
        </Button>
      </DropdownTrigger>
      {order.isSuccess && (
        <DropdownItems className="[&>.lucide]:text-foreground-secondary w-50 leading-none [&>.lucide]:size-4 [&>button]:py-3">
          <DropdownItem onClick={moveItems.open} disabled={moveItems.disabled}>
            <FileInputIcon />
            Move Items
          </DropdownItem>
          <DropdownItem onClick={moveOrder.open} disabled={moveOrder.disabled}>
            <FileInputIcon />
            Move order
          </DropdownItem>
          <DropdownDivider />
          <DropdownItem disabled>
            <PlusIcon />
            Add Custom Item
          </DropdownItem>
          <DropdownDivider />
          <DropdownItem disabled>
            <ReceiptTextIcon />
            Print Receipt
          </DropdownItem>
          <DropdownItem disabled>
            <FolderOpenIcon />
            Order Summary
          </DropdownItem>
          <DropdownItem
            className="text-negative"
            onClick={cancelOrder.open}
            disabled={cancelOrder.disabled}
          >
            <FileXIcon />
            Cancel Order
          </DropdownItem>
        </DropdownItems>
      )}
    </Dropdown>
  );
};

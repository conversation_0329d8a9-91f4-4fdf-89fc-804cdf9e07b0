import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, easings, cn } from "@allo/ui";
import { motion, AnimatePresence } from "motion/react";
import { Link, useLocation, useNavigate } from "@tanstack/react-router";
import { useEffect, useMemo, useRef } from "react";
import { useBottomBar } from "@/components/bottom-bar";
import { useToast } from "@/components/ui/toast";
import { isInteractiveElement } from "@/lib/utils/dom";
import { getOrderOptions, getOrdersOptions } from "@/lib/queries";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { CookingPot } from "lucide-react";
import { postAssignOrder, postOrderItems } from "@/lib/mock-data/api";
import { useCart, useCartActions } from "@/lib/cart/store";
import {
  calculateCartTotalPrice,
  calculateCartTotalItemQuantity,
} from "@/lib/cart/utils";

const cartStatusVariants = {
  hidden: {
    height: 0,
    opacity: 0,
    transition: { duration: 0.125, ease: easings["emphasized-accelerate"] },
  },
  visible: {
    height: "auto",
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.35,
      ease: easings["emphasized-decelerate"],
    },
  },
};

interface OrderStatusBarProps {
  orderId: string;
  className?: string;
}

export const OrderStatusBar = ({ orderId, className }: OrderStatusBarProps) => {
  const navigate = useNavigate();
  const pathname = useLocation({ select: (location) => location.pathname });
  const isCartRoute = useMemo(() => pathname.includes("cart"), [pathname]);

  const { toast } = useToast();

  const queryClient = useQueryClient();
  const order = useQuery(getOrderOptions(orderId));

  const cart = useCart(orderId);
  const { clearCart } = useCartActions(orderId);

  const sendToKitchen = useMutation({
    mutationFn: async () => {
      if (!order.data?.assignedTo) {
        await postAssignOrder(orderId, "Random User"); // FIXME: name should came current user
      }

      return postOrderItems(orderId, cart);
    },
    onError: (error) => {
      toast({
        variant: "error",
        icon: CookingPot,
        message: error.message,
      });
    },
    onSuccess: async (newOrder) => {
      await queryClient.invalidateQueries({
        queryKey: getOrderOptions(orderId).queryKey,
      });

      // set the new order in the all-orders cache
      queryClient.setQueryData(getOrdersOptions().queryKey, (orders) => {
        return orders?.map((order) =>
          order.id === orderId ? newOrder : order
        );
      });

      toast({
        message: `Items successfully sent to the kitchen.`,
        variant: "success",
        icon: CookingPot,
      });

      navigate({
        to: "/dine-in/spaces/$space",
        params: { space: order.data?.spaceId || "" },
      });

      clearCart();
    },
  });

  const items = useMemo(() => {
    if (cart.length > 0) {
      return {
        num: calculateCartTotalItemQuantity(cart),
        price: calculateCartTotalPrice(cart),
        status: "pending",
      };
    } else if (order.data && order.data.ordered.length > 0) {
      return {
        num: calculateCartTotalItemQuantity(order.data.ordered),
        price: calculateCartTotalPrice(order.data.ordered),
        status: "to-pay",
      };
    }

    return null;
  }, [order.data, cart]);

  const handlePayment = () => {
    navigate({ to: "/user-testing-complete" });
  };

  const handleContainerClick = ({ target }: React.MouseEvent<HTMLElement>) => {
    if (isInteractiveElement(target)) return;

    if (isCartRoute) {
      navigate({
        to: "/dine-in/$order",
        params: { order: orderId },
      });
    } else {
      navigate({
        to: "/dine-in/$order/cart",
        params: { order: orderId },
        search: { status: items?.status as "pending" | "to-pay" },
      });
    }
  };

  return (
    <AnimatePresence initial={false}>
      {items && (
        <motion.div
          className={cn(
            "flex items-center justify-end gap-1.5 overflow-hidden px-3 *:py-3",
            className
          )}
          onClick={handleContainerClick}
          variants={cartStatusVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          inert={sendToKitchen.isPending}
        >
          <div className="mr-auto text-sm">
            {items.status === "pending" ? "Pending" : "To pay"}
            <QuantityBadge num={items.num} />
            <Price amount={items.price} className="block" />
          </div>
          {!isCartRoute && (
            <Button asChild>
              <Link
                to="/dine-in/$order/cart"
                params={{ order: orderId }}
                search={{ status: items.status as "pending" | "to-pay" }}
              >
                Details
              </Link>
            </Button>
          )}
          {items.status === "pending" && (
            <Button
              variant="accent"
              className="min-w-35"
              onClick={() => sendToKitchen.mutate()}
              isLoading={sendToKitchen.isPending}
            >
              Send to Kitchen
            </Button>
          )}
          {items.status === "to-pay" && (
            <Button
              variant="positive"
              className="min-w-35"
              onClick={handlePayment}
              isLoading={sendToKitchen.isPending}
            >
              Payment
            </Button>
          )}
          <ThemeSetter />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

const QuantityBadge = ({ num }: { num: number }) => {
  const ref = useRef<HTMLDivElement>(null);
  const previousNum = useRef(num);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    if (previousNum.current !== num) {
      const type = num > previousNum.current ? "increase" : "decrease";

      element.animate(
        [
          { transform: "scale(1)" },
          { transform: `scale(${type === "increase" ? 1.2 : 0.85})` },
          { transform: "scale(1)" },
        ],
        { duration: 200, easing: "ease-out" }
      );

      previousNum.current = num;
    }
  }, [num]);

  return (
    <Badge
      ref={ref}
      size="xs"
      className="bg-foreground/15 ml-2 min-w-5 justify-center px-1 align-bottom font-medium tabular-nums"
    >
      {num}
    </Badge>
  );
};

// Hacky way to set the theme based on the motion.div's lifecycle
const ThemeSetter = () => {
  const { setTheme } = useBottomBar();

  useEffect(() => {
    setTheme("dark");

    return () => setTheme("light");
  }, [setTheme]);

  return null;
};

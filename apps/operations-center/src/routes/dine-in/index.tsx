import {
  fetchUserPreferencies,
  fetchVersionedFloors,
} from "@/lib/mock-data/api";
import { createFileRoute, redirect } from "@tanstack/react-router";
import { getVersionedFloors } from "@/lib/queries";

export const Route = createFileRoute("/dine-in/")({
  beforeLoad: async ({ context: { queryClient } }) => {
    const queryKey = getVersionedFloors().queryKey;
    let spaces = queryClient.getQueryData(queryKey);

    if (!spaces) {
      spaces = await fetchVersionedFloors();
      fetchUserPreferencies().then((preferencies) => {
        console.log("user preferencies fetched", preferencies);
      });
      queryClient.setQueryData(queryKey, spaces);
    }

    throw redirect({
      to: "/dine-in/spaces/$space",
      params: { space: spaces?.items?.[0]?.id },
    });
  },
});

import { createFile<PERSON>out<PERSON>, <PERSON> } from "@tanstack/react-router";
import { <PERSON><PERSON>, Button } from "@allo/ui";
import { PencilIcon } from "lucide-react";
import { useUserConfig } from "@/lib/user-config/store";

import { Actions } from "./-components/actions";

export const Route = createFileRoute("/dine-in/settings/")({
  component: SettingsPage,
});

function SettingsPage() {
  const views = useUserConfig((state) => state.views);

  return (
    <div className="flex h-full flex-col [&_:is(section,header,footer)]:p-4">
      <section>
        <h2>Tables</h2>
        <p className="text-foreground-secondary mt-1">
          Create up to 4 views to help you optimize the floor management
        </p>
        <ul className="mt-4 space-y-2">
          {views.tables.map((view) => {
            return (
              <li
                key={view.id}
                className="bg-background-highlight border-border relative flex w-full items-center gap-1 rounded-2xl border p-3 text-left"
              >
                <h3 className="leading-none">{view.title}</h3>
                <Badge size="xs">Default view</Badge>
                <Link
                  from={Route.id}
                  to="./tables/$view"
                  params={{ view: view.id }}
                  className="ml-auto flex items-center gap-1 after:absolute after:inset-0"
                >
                  <PencilIcon />
                  Edit
                </Link>
              </li>
            );
          })}
        </ul>
      </section>
      <Actions>
        <Button asChild className="w-full">
          <Link to="/dine-in">Back to Tables</Link>
        </Button>
      </Actions>
    </div>
  );
}

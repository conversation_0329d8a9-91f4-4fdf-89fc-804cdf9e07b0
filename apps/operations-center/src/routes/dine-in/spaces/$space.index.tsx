import { useMemo } from "react";
import { TriangleAlert } from "lucide-react";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { cn, Skeleton } from "@allo/ui";
import {
  getOrderOptions,
  getOrdersOptions,
  getTablesOptions,
} from "@/lib/queries";
import { Order } from "@/lib/mock-data/order";
import { postCreateOrder } from "@/lib/mock-data/api";
import { useToast } from "@/components/ui/toast";
import { Table } from "@/lib/mock-data/tables";
import { useCartStore } from "@/lib/cart/store";
import { useViewConfig } from "@/lib/user-config/store";

import { TableCard } from "./-components/table-card";
import { useTableData } from "./-hooks/use-table-data";
import { PressActionsButton } from "@/components/press-actions-button";
import { useTablePressActions } from "./-hooks/use-table-press-actions";

export const Route = createFileRoute("/dine-in/spaces/$space/")({
  component: SpaceIndex,
});

function SpaceIndex() {
  const viewConfig = useViewConfig("tables");

  const params = Route.useParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const cartsByOrderId = useCartStore((state) => state.carts);
  const createCart = useCartStore((state) => state.createCart);

  const queryClient = useQueryClient();
  const tables = useQuery(getTablesOptions(params.space));
  const { data: orders } = useQuery(getOrdersOptions());

  const ordersByTableId = useMemo(() => {
    return (
      orders?.reduce(
        (acc, order) => ({ ...acc, [order.tableId]: order }),
        {} as Record<string, Order>
      ) || {}
    );
  }, [orders]);

  const createOrder = useMutation({
    mutationFn: async (table: Table) => {
      return postCreateOrder(table);
    },
    onError: (error) => {
      toast({
        icon: TriangleAlert,
        message: error.message,
        variant: "error",
      });
    },
    onSuccess: async (newOrder) => {
      navigate({
        from: Route.fullPath,
        to: "../../$order",
        params: { order: newOrder.id },
      });

      queryClient.invalidateQueries({
        queryKey: getOrdersOptions().queryKey,
      });

      createCart(newOrder.id);
      queryClient.setQueryData(getOrderOptions(newOrder.id).queryKey, newOrder);
    },
  });

  return (
    <ul
      inert={createOrder.isPending}
      className={cn(
        "grid grid-cols-[repeat(auto-fill,minmax(var(--col-size),1fr))] gap-1.5 p-2.5 pb-8",
        viewConfig.size === "sm" && "[--col-size:--spacing(17)]",
        viewConfig.size === "md" && "[--col-size:--spacing(29)]",
        viewConfig.size === "lg" && "[--col-size:--spacing(43)]"
      )}
    >
      {tables.isPending &&
        Array.from({ length: 5 }).map((_, index) => (
          <li key={index}>
            <Skeleton className="w-full pb-[100%]" />
          </li>
        ))}
      {tables.data?.map((table: Table) => {
        const order = ordersByTableId[table.id];
        const cart = cartsByOrderId[order?.id];

        const isBeingCreated =
          createOrder.variables?.id === table.id && !createOrder.error;

        return (
          <li key={table.id} className="*:block *:w-full">
            {order && cart && !isBeingCreated ? (
              <ActiveTableCard table={table} order={order} />
            ) : (
              <button
                aria-label="Create Order"
                aria-busy={isBeingCreated || undefined}
                disabled={isBeingCreated}
                onClick={() => createOrder.mutate(table)}
              >
                <TableCard
                  name={table.code}
                  size={viewConfig.size}
                  isLoading={isBeingCreated}
                />
              </button>
            )}
          </li>
        );
      })}
    </ul>
  );
}

interface ActiveTableCardProps {
  table: Table;
  order: Order;
}

export const ActiveTableCard = ({ table, order }: ActiveTableCardProps) => {
  const viewConfig = useViewConfig("tables");

  const { assignee, properties } = useTableData({ table, order });
  const tapActions = useTablePressActions(order, viewConfig["tap-actions"]);
  const longPressActions = useTablePressActions(
    order,
    viewConfig["long-press-actions"]
  );

  return (
    <PressActionsButton
      tap={tapActions}
      longPress={longPressActions}
      drawerTitle={`Table ${table.code}`}
    >
      <TableCard
        name={table.code}
        size={viewConfig.size}
        order={assignee && { assignee }}
        properties={properties}
      />
    </PressActionsButton>
  );
};

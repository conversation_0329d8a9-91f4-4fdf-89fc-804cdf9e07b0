import { useMemo } from "react";
import { createFileRoute, Link, Outlet } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import {
  <PERSON>er,
  Drawer<PERSON>ontent,
  <PERSON>er<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
  DrawerClose,
  Radio,
  Skeleton,
  Button,
  Card,
} from "@allo/ui";
import { ChevronDown, CogIcon } from "lucide-react";
import { getVersionedFloors } from "@/lib/queries";
import { useRegisterBreadcrumb } from "@/components/ui/breadcrumbs";
import { TopBarActionsPortal } from "@/components/top-bar";
import { Space } from "@/lib/mock-data/tables";

export const Route = createFileRoute("/dine-in/spaces/$space")({
  component: SpaceLayout,
});

export function SpaceLayout() {
  const params = Route.useParams();
  const spaces = useQuery(getVersionedFloors());

  useRegisterBreadcrumb([{ id: "tables", label: "Tables" }]);

  const currentSpace = useMemo(() => {
    return spaces.isSuccess
      ? (spaces.data.items &&
          spaces.data.items.find((s: Space) => s.id === params.space)) ||
          spaces.data.items[0]
      : null;
  }, [params.space, spaces]);

  return (
    <>
      <TopBarActionsPortal id="space-selector">
        <Drawer>
          <>
            {spaces.isPending && <Skeleton className="h-7 w-20 rounded-md" />}
            {spaces.isSuccess && (
              <DrawerTrigger asChild>
                <Button size="sm" className="gap-0.5">
                  {currentSpace?.name}
                  <ChevronDown className="text-foreground-secondary size-4" />
                </Button>
              </DrawerTrigger>
            )}
          </>
          {spaces.isSuccess && (
            <DrawerContent>
              <DrawerHeader>
                <DrawerTitle className="text-foreground-secondary">
                  Select Space
                </DrawerTitle>
              </DrawerHeader>
              <ul className="pb-4 text-sm">
                {spaces.data.items.map((space: Space) => (
                  <li key={space.id}>
                    <DrawerClose asChild>
                      <Link
                        to="/dine-in/spaces/$space"
                        className="flex h-12 w-full items-center justify-between gap-1.5 px-3"
                        params={{ space: space.id }}
                      >
                        {space.name}
                        {/* FIXME: status is currently not in server response */}
                        {/* {space.status !== "Regular" && (
                          <Badge
                            size="xs"
                            variant={space.isClosed ? "default" : "negative"}
                            className="ml-auto"
                          >
                            {space.status}
                          </Badge>
                        )} */}
                        <Radio checked={space.id === params.space} readOnly />
                      </Link>
                    </DrawerClose>
                  </li>
                ))}
              </ul>
            </DrawerContent>
          )}
        </Drawer>
      </TopBarActionsPortal>

      <Outlet />

      {/* TEMP: settings button */}
      <Card
        asChild
        className="fixed bottom-2.5 left-2.5 z-90 flex h-10 min-w-10 items-center justify-center gap-2 rounded-lg px-3 shadow-md"
      >
        <Link to="/dine-in/settings">
          <CogIcon className="text-foreground-secondary size-4" />
        </Link>
      </Card>
    </>
  );
}

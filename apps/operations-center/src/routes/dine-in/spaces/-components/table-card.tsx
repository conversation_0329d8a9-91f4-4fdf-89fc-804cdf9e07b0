import { useViewConfig } from "@/lib/user-config/store";
import { Avatar, AvatarFallback, cn, Duration, Price, Spinner } from "@allo/ui";
import {
  BeerIcon,
  ClockIcon,
  LayersIcon,
  ShoppingCartIcon,
  UsersIcon,
  UtensilsCrossedIcon,
  WalletIcon,
} from "lucide-react";
import { useMemo } from "react";

type PropertyId =
  | "price"
  | "status"
  | "courses"
  | "time"
  | "drinks"
  | "customers"
  | "items";

export interface TableCardProps {
  name: string;
  size: "sm" | "md" | "lg";
  order?: {
    assignee: {
      name: string;
      color: string;
    };
  };
  properties?: {
    id: PropertyId;
    value: string | number;
    alert?: boolean;
  }[];
  isLoading?: boolean;
}

// TODO: remove unused alert variants. Having multiple alert styles is temporary, and will be removed in the future.
// The code is kind of mess at the moment because of it, but I don't think its worth refactoring at the moment.
export const TableCard = ({
  name,
  size,
  order,
  properties,
  isLoading,
}: TableCardProps) => {
  const viewConfig = useViewConfig("tables");

  const assignee = useMemo(() => order?.assignee, [order]);
  const hasAlert = useMemo(
    () => properties?.some(({ alert }) => alert),
    [properties]
  );

  const sortedProperties = useMemo(() => {
    return properties?.sort(({ alert }) => (alert ? -1 : 1));
  }, [properties]);

  return (
    <div
      className={cn(
        "relative aspect-square w-full overflow-hidden rounded-4xl p-1.5",
        "*:relative before:absolute before:inset-0 before:bg-[#D9D7D6]",
        assignee && "before:bg-(--assignee-color) before:opacity-48",
        isLoading && "opacity-70",
        hasAlert &&
          viewConfig["alert-style"] === "pulse" &&
          "before:animate-pulse-opacity before:opacity-[unset]"
      )}
      style={assignee ? { "--assignee-color": assignee.color } : undefined}
    >
      <div
        className={cn(
          "relative flex h-full flex-col justify-between gap-2 overflow-hidden rounded-xl p-2",
          !!order && "bg-background-highlight",
          size === "sm" && "bg-transparent p-0"
        )}
      >
        {/* outline */}
        {hasAlert &&
          viewConfig["alert-style"] === "outline" &&
          size !== "sm" && (
            <div
              className={cn(
                "absolute inset-0 z-10 rounded-xl",
                "outline-3 -outline-offset-3 outline-(--assignee-color)",
                "animate-pulse [animation-duration:0.66s]"
              )}
            />
          )}
        {/* spin */}
        {hasAlert && viewConfig["alert-style"] === "spin" && (
          <div
            className={cn(
              "absolute inset-0 z-10",
              "before:absolute before:inset-0.5 before:rounded-lg",
              "animate-spin-conic-gradient [--color:var(--assignee-color)]",
              viewConfig.size !== "sm" && "mix-blend-multiply before:bg-white",
              viewConfig.size === "sm" &&
                "mix-blend-screen before:inset-0.75 before:bg-black"
            )}
          />
        )}
        <NameBadge name={name} active={!!order} size={size} alert={hasAlert} />
        {assignee && size !== "sm" && (
          <Avatar
            size="xs"
            className="outline-background-highlight absolute top-1 right-1 ml-auto text-xs font-medium outline"
            color="hi-contrast"
            style={{ "--avatar-bg": assignee.color }}
          >
            <AvatarFallback>{assignee.name}</AvatarFallback>
          </Avatar>
        )}
        {size !== "sm" && sortedProperties && (
          <div
            className={cn(
              "relative",
              size === "lg" ? "space-y-1" : "space-y-1.5"
            )}
          >
            {sortedProperties
              .slice(0, size === "md" ? 2 : 5)
              .map((property) => (
                <Property key={property.id} {...property} size={size} />
              ))}
          </div>
        )}
        {isLoading && (
          <div
            className={cn(
              "absolute inset-0 flex items-center justify-center",
              size == "sm" &&
                "before:bg-background-highlight/60 bg-foreground before:absolute before:inset-0"
            )}
          >
            <Spinner />
          </div>
        )}
      </div>
    </div>
  );
};

interface NameBadgeProps {
  name: string;
  active: boolean;
  size: TableCardProps["size"];
  alert?: boolean;
}

const NameBadge = ({ name, active, size, alert }: NameBadgeProps) => {
  const viewConfig = useViewConfig("tables");

  return (
    <div
      className={cn(
        "relative flex max-w-full min-w-9 items-center px-2.5 text-center *:w-full",
        "text-background-highlight text-center font-medium *:truncate *:whitespace-nowrap",
        "h-9 w-fit rounded-md text-sm",
        size === "sm" && "h-full w-full rounded-lg text-lg",
        active ? "bg-foreground" : "bg-[#7B7A79]",
        alert && [
          viewConfig["alert-style"] === "color" && "bg-negative",
          size === "sm" &&
            viewConfig["alert-style"] === "outline" &&
            "outline-4 outline-(--assignee-color)",
          size === "sm" &&
            ["outline", "pulse"].includes(viewConfig["alert-style"]) &&
            "animate-pulse-scale",
        ]
      )}
    >
      <span>{name}</span>
    </div>
  );
};

interface PropertyProps {
  id: PropertyId;
  value: string | number;
  size: TableCardProps["size"];
  alert?: boolean;
}

const PropertyIcon = ({ id }: { id: PropertyProps["id"] }) => {
  return (
    <span className="text-foreground-secondary">
      {id === "price" && <WalletIcon />}
      {id === "status" && <LayersIcon />}
      {id === "time" && <ClockIcon />}
      {id === "drinks" && <BeerIcon />}
      {id === "courses" && <UtensilsCrossedIcon />}
      {id === "customers" && <UsersIcon />}
      {id === "items" && <ShoppingCartIcon />}
    </span>
  );
};

const PropertyName = ({ id }: { id: PropertyProps["id"] }) => {
  return (
    <span className="text-foreground-secondary">
      {id === "price" && "Price"}
      {id === "status" && "Status"}
      {id === "time" && "Time"}
      {id === "drinks" && "Drinks"}
      {id === "courses" && "Courses"}
      {id === "customers" && "Customers"}
      {id === "items" && "Items"}
    </span>
  );
};

const PropertyValue = ({ id, value, size }: PropertyProps) => {
  if (size === "lg" && id === "price") return <Price amount={Number(value)} />;
  if (id === "time")
    return (
      <Duration
        amount={{ seconds: Number(value) }}
        units={["hours", "minutes"]}
      />
    );

  return <span>{value}</span>;
};

const Property = ({ id, value, size, alert }: PropertyProps) => {
  const viewConfig = useViewConfig("tables");

  return (
    <div
      key={id}
      className={cn(
        "flex items-center gap-1 font-medium *:leading-none",
        size === "md" && "text-xs",
        size === "lg" && "w-full justify-between text-sm",
        alert && [
          ["color", "outline"].includes(viewConfig["alert-style"]) &&
            "*:text-negative",
          viewConfig["alert-style"] === "pulse" && "*:text-(--assignee-color)",
        ]
      )}
    >
      {size === "md" && <PropertyIcon id={id} />}
      {size === "lg" && <PropertyName id={id} />}
      <PropertyValue id={id} value={value} size={size} />
    </div>
  );
};

import { useOrderAction } from "@/components/order-actions";
import { PressAction } from "@/components/press-actions-button";
import { useNavigate } from "@tanstack/react-router";
import {
  LampCeilingIcon,
  ReceiptTextIcon,
  WalletCardsIcon,
  FolderInputIcon,
  FolderOutputIcon,
} from "lucide-react";
import { Order } from "@/lib/mock-data/order";
import { useMemo } from "react";

export const useTablePressActions = (
  order: Order,
  filter: string[] = []
): PressAction[] => {
  const navigate = useNavigate();

  const moveOrder = useOrderAction("move-order", order);
  const moveItems = useOrderAction("move-items", order);
  const cancelOrder = useOrderAction("cancel-order", order);

  return [
    {
      id: "open-table",
      label: "Open table",
      icon: LampCeilingIcon,
      onClick: () =>
        navigate({ to: "/dine-in/$order", params: { order: order.id } }),
    },
    {
      id: "print-receipt",
      label: "Print receipt",
      icon: ReceiptTextIcon,
      onClick: () => {},
      disabled: true,
    },
    {
      id: "payment",
      label: "Payment",
      icon: WalletCardsIcon,
      onClick: () => {},
      disabled: true,
    },
    {
      id: "move-order",
      label: "Move order",
      icon: FolderInputIcon,
      onClick: moveOrder.open,
      disabled: moveOrder.disabled,
    },
    {
      id: "move-items",
      label: "Move items",
      icon: FolderInputIcon,
      onClick: moveItems.open,
      disabled: moveItems.disabled,
    },
    {
      id: "cancel-order",
      label: "Cancel order",
      icon: FolderOutputIcon,
      variant: "negative",
      onClick: cancelOrder.open,
      disabled: cancelOrder.disabled,
    },
  ].filter((action) => filter.includes(action.id)) as PressAction[];
};

export interface VersionedMenusResponse {
  menuVersion: number;
  items: MenuSuperFolder[];
}

export interface MenuSuperFolder {
  id: string;
  creationTime: string;
  modificationTime: string;
  lastModifiedByUserId: string;
  lastModifiedByAccountId: string;
  deleted: boolean;
  restricted: boolean;
  disabled: boolean;
  hidden: boolean;
  recommended: boolean;
  oneTimeMenu: boolean;
  lieferando: boolean;
  restaurantId: string;
  title: string;
  titleI18n: {
    [key: string]: string;
  };
  internalTitleI18n: {
    [key: string]: string;
  };
  description: string;
  descriptionI18n: {
    [key: string]: string;
  };
  displayType: string;
  order: number;
  items: MenuItem[];
  sharedItemIds: string[];
  printerIds: string[];
  orderTypes: string[];
  partnerIds: string[];
  restaurantTags: RestaurantTag[];
  color: string;
}

export interface MenuItem {
  id: string;
  creationTime: string;
  modificationTime: string;
  lastModifiedByUserId: string;
  lastModifiedByAccountId: string;
  deleted: boolean;
  restricted: boolean;
  disabled: boolean;
  ongoing: boolean;
  hidden: boolean;
  lieferando: boolean;
  favorite: boolean;
  hasHighQuantitySelector: boolean;
  restaurantId: string;
  menuId: string;
  sharedMenuId: string;
  lieferandoId: string;
  code: string;
  name: string;
  nameI18n: {
    [key: string]: string;
  };
  internalNameI18n: {
    [key: string]: string;
  };
  description: string;
  descriptionI18n: {
    [key: string]: string;
  };
  unitPrice: number;
  category: string;
  volume: string;
  thumbnailUrl: string;
  numeration: string;
  printerCategory: string;
  remarkAnnotations: any[];
  printerIds: string[];
  order: number;
  options: MenuItemOption[];
  extras: MenuItemExtra[];
  dineTaxCategory: string;
  takeawayTaxCategory: string;
  tagIds: string[];
  restaurantTags: RestaurantTag[];
  color: string;
  partnerPrices: {
    [key: string]: number;
  };
  prices: {
    [key: string]: number;
  };
  alcoholPercentage: number;
  metadata: {
    consumptions: Consumption[];
    inventoryStatus: string;
  };
  partnerIds: string[];
  ongoingExtrasQuantityMax: number;
  includedInPrice: boolean;
  preparationTime: number;
  defaultCourseNumber?: number;
  optionItems: MenuItemOptionItem[];
  extraItems: MenuItemExtraItem[];
}

export interface MenuItemOption {
  id: string;
  creationTime: string;
  modificationTime: string;
  createdByUserId: string;
  createdByAccountId: string;
  lastModifiedByUserId: string;
  lastModifiedByAccountId: string;
  restaurantId: string;
  name: string;
  nameI18n: {
    [key: string]: string;
  };
  internalNameI18n: {
    [key: string]: string;
  };
  descriptionI18n: {
    [key: string]: string;
  };
  deleted: boolean;
  restricted: boolean;
  disabled: boolean;
  hidden: boolean;
  lieferando: boolean;
  collapsed: boolean;
  order: number;
  base: boolean;
  printerIds: string[];
  qtd: number;
  rounds: number;
  items: MenuItemOptionItem[];
}

export interface MenuItemOptionItem {
  id: string;
  creationTime: string;
  modificationTime: string;
  createdByUserId: string;
  createdByAccountId: string;
  lastModifiedByUserId: string;
  lastModifiedByAccountId: string;
  restaurantId: string;
  optionId: string;
  name: string;
  nameI18n: {
    [key: string]: string;
  };
  internalName?: string;
  internalNameI18n: {
    [key: string]: string;
  };
  description?: string;
  descriptionI18n: {
    [key: string]: string;
  };
  tagIds: string[];
  deleted: boolean;
  restricted: boolean;
  disabled: boolean;
  hidden: boolean;
  order: number;
  numeration: string;
  thumbnailUrl: string;
  unitPrice: number;
  category: string;
  dineTaxCategory: string;
  takeawayTaxCategory: string;
  min: number;
  max: number;
  remarkAnnotations: any[];
  alcoholPercentage: number;
  partnerPrices?: {
    [key: string]: number;
  };
  metadata?: {
    consumptions: any[];
  };
}

export interface MenuItemExtra {
  id: string;
  creationTime: string;
  modificationTime: string;
  createdByUserId: string;
  createdByAccountId: string;
  lastModifiedByUserId: string;
  lastModifiedByAccountId: string;
  restaurantId: string;
  name: string;
  nameI18n: {
    [key: string]: string;
  };
  internalNameI18n: {
    [key: string]: string;
  };
  descriptionI18n: {
    [key: string]: string;
  };
  deleted: boolean;
  restricted: boolean;
  disabled: boolean;
  hidden: boolean;
  collapsed: boolean;
  max: number;
  rounds: number;
  base: boolean;
  printerIds: string[];
  items: MenuItemExtraItem[];
}

export interface MenuItemExtraItem {
  id: string;
  creationTime: string;
  modificationTime: string;
  createdByUserId: string;
  createdByAccountId: string;
  lastModifiedByUserId: string;
  lastModifiedByAccountId: string;
  restaurantId: string;
  extraId: string;
  name: string;
  nameI18n: {
    [key: string]: string;
  };
  internalName: string;
  internalNameI18n: {
    [key: string]: string;
  };
  description: string;
  descriptionI18n: {
    [key: string]: string;
  };
  tagIds: string[];
  thumbnailUrl: string;
  deleted: boolean;
  restricted: boolean;
  disabled: boolean;
  hidden: boolean;
  numeration: string;
  unitPrice: number;
  category: string;
  dineTaxCategory: string;
  takeawayTaxCategory: string;
  max: number;
  remarkAnnotations: any[];
  alcoholPercentage: number;
  metadata?: {
    consumptions: any[];
  };
}

export interface RestaurantTag {
  id: string;
  label: string;
}

export interface Consumption {
  categoryId: string;
  name: string;
  unit: string;
  amount: number;
  inventoryStatus: string;
}

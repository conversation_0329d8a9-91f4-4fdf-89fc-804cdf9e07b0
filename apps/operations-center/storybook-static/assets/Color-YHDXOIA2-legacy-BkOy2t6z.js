!function(){var e=["value","style"];function r(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,n)}return t}function t(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?r(Object(a),!0).forEach((function(r){n(e,r,a[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):r(Object(a)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))}))}return e}function n(e,r,t){return(r=function(e){var r=function(e,r){if("object"!=a(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==a(r)?r:r+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function o(e,r){return function(e){if(Array.isArray(e))return e}(e)||function(e,r){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var n,a,o,l,u=[],c=!0,i=!1;try{if(o=(t=t.call(e)).next,0===r){if(Object(t)!==t)return;c=!1}else for(;!(c=(n=o.call(t)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(e){i=!0,a=e}finally{try{if(!c&&null!=t.return&&(l=t.return(),Object(l)!==l))return}finally{if(i)throw a}}return u}}(e,r)||function(e,r){if(e){if("string"==typeof e)return l(e,r);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?l(e,r):void 0}}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}System.register(["./DocsRenderer-CFRXHY34-legacy-CAEo-rWd.js","./index-legacy-8Bh_oy_I.js"],(function(r,n){"use strict";var l,u,c,i,s,f,h,d,v,g,b;return{setters:[function(e){l=e.d,u=e.v,c=e.G,i=e.g,s=e.a,f=e.Z,h=e.M,d=e._,v=e.b},function(e){g=e.r,b=e.R}],execute:function(){var n=v({"../../node_modules/color-name/index.js":function(e,r){r.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}}}),p=v({"../../node_modules/color-convert/conversions.js":function(e,r){for(var t=n(),a={},l=0,u=Object.keys(t);l<u.length;l++){var c=u[l];a[t[c]]=c}var i={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};r.exports=i;for(var s=0,f=Object.keys(i);s<f.length;s++){var h=f[s];if(!("channels"in i[h]))throw new Error("missing channels property: "+h);if(!("labels"in i[h]))throw new Error("missing channel labels property: "+h);if(i[h].labels.length!==i[h].channels)throw new Error("channel and label counts mismatch: "+h);var d=i[h],v=d.channels,g=d.labels;delete i[h].channels,delete i[h].labels,Object.defineProperty(i[h],"channels",{value:v}),Object.defineProperty(i[h],"labels",{value:g})}i.rgb.hsl=function(e){var r,t=e[0]/255,n=e[1]/255,a=e[2]/255,o=Math.min(t,n,a),l=Math.max(t,n,a),u=l-o;l===o?r=0:t===l?r=(n-a)/u:n===l?r=2+(a-t)/u:a===l&&(r=4+(t-n)/u),(r=Math.min(60*r,360))<0&&(r+=360);var c=(o+l)/2;return[r,100*(l===o?0:c<=.5?u/(l+o):u/(2-l-o)),100*c]},i.rgb.hsv=function(e){var r,t,n,a,o,l=e[0]/255,u=e[1]/255,c=e[2]/255,i=Math.max(l,u,c),s=i-Math.min(l,u,c),f=function(e){return(i-e)/6/s+.5};return 0===s?(a=0,o=0):(o=s/i,r=f(l),t=f(u),n=f(c),l===i?a=n-t:u===i?a=1/3+r-n:c===i&&(a=2/3+t-r),a<0?a+=1:a>1&&(a-=1)),[360*a,100*o,100*i]},i.rgb.hwb=function(e){var r=e[0],t=e[1],n=e[2];return[i.rgb.hsl(e)[0],100*(1/255*Math.min(r,Math.min(t,n))),100*(n=1-1/255*Math.max(r,Math.max(t,n)))]},i.rgb.cmyk=function(e){var r=e[0]/255,t=e[1]/255,n=e[2]/255,a=Math.min(1-r,1-t,1-n);return[100*((1-r-a)/(1-a)||0),100*((1-t-a)/(1-a)||0),100*((1-n-a)/(1-a)||0),100*a]},i.rgb.keyword=function(e){var r=a[e];if(r)return r;for(var n,o,l,u=1/0,c=0,i=Object.keys(t);c<i.length;c++){var s=i[c],f=t[s],h=(o=e,l=f,Math.pow(o[0]-l[0],2)+Math.pow(o[1]-l[1],2)+Math.pow(o[2]-l[2],2));h<u&&(u=h,n=s)}return n},i.keyword.rgb=function(e){return t[e]},i.rgb.xyz=function(e){var r=e[0]/255,t=e[1]/255,n=e[2]/255;return[100*(.4124*(r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92)+.3576*(t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92)+.1805*(n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92)),100*(.2126*r+.7152*t+.0722*n),100*(.0193*r+.1192*t+.9505*n)]},i.rgb.lab=function(e){var r=i.rgb.xyz(e),t=r[0],n=r[1],a=r[2];return n/=100,a/=108.883,t=(t/=95.047)>.008856?Math.pow(t,1/3):7.787*t+16/116,[116*(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116)-16,500*(t-n),200*(n-(a=a>.008856?Math.pow(a,1/3):7.787*a+16/116))]},i.hsl.rgb=function(e){var r,t,n,a=e[0]/360,o=e[1]/100,l=e[2]/100;if(0===o)return[n=255*l,n,n];for(var u=2*l-(r=l<.5?l*(1+o):l+o-l*o),c=[0,0,0],i=0;i<3;i++)(t=a+1/3*-(i-1))<0&&t++,t>1&&t--,n=6*t<1?u+6*(r-u)*t:2*t<1?r:3*t<2?u+(r-u)*(2/3-t)*6:u,c[i]=255*n;return c},i.hsl.hsv=function(e){var r=e[0],t=e[1]/100,n=e[2]/100,a=t,o=Math.max(n,.01);return t*=(n*=2)<=1?n:2-n,a*=o<=1?o:2-o,[r,100*(0===n?2*a/(o+a):2*t/(n+t)),100*((n+t)/2)]},i.hsv.rgb=function(e){var r=e[0]/60,t=e[1]/100,n=e[2]/100,a=Math.floor(r)%6,o=r-Math.floor(r),l=255*n*(1-t),u=255*n*(1-t*o),c=255*n*(1-t*(1-o));switch(n*=255,a){case 0:return[n,c,l];case 1:return[u,n,l];case 2:return[l,n,c];case 3:return[l,u,n];case 4:return[c,l,n];case 5:return[n,l,u]}},i.hsv.hsl=function(e){var r,t,n=e[0],a=e[1]/100,o=e[2]/100,l=Math.max(o,.01);t=(2-a)*o;var u=(2-a)*l;return r=a*l,[n,100*(r=(r/=u<=1?u:2-u)||0),100*(t/=2)]},i.hwb.rgb=function(e){var r,t=e[0]/360,n=e[1]/100,a=e[2]/100,o=n+a;o>1&&(n/=o,a/=o);var l=Math.floor(6*t),u=1-a;r=6*t-l,1&l&&(r=1-r);var c,i,s,f=n+r*(u-n);switch(l){default:case 6:case 0:c=u,i=f,s=n;break;case 1:c=f,i=u,s=n;break;case 2:c=n,i=u,s=f;break;case 3:c=n,i=f,s=u;break;case 4:c=f,i=n,s=u;break;case 5:c=u,i=n,s=f}return[255*c,255*i,255*s]},i.cmyk.rgb=function(e){var r=e[0]/100,t=e[1]/100,n=e[2]/100,a=e[3]/100;return[255*(1-Math.min(1,r*(1-a)+a)),255*(1-Math.min(1,t*(1-a)+a)),255*(1-Math.min(1,n*(1-a)+a))]},i.xyz.rgb=function(e){var r,t,n,a=e[0]/100,o=e[1]/100,l=e[2]/100;return t=-.9689*a+1.8758*o+.0415*l,n=.0557*a+-.204*o+1.057*l,r=(r=3.2406*a+-1.5372*o+-.4986*l)>.0031308?1.055*Math.pow(r,1/2.4)-.055:12.92*r,t=t>.0031308?1.055*Math.pow(t,1/2.4)-.055:12.92*t,n=n>.0031308?1.055*Math.pow(n,1/2.4)-.055:12.92*n,[255*(r=Math.min(Math.max(0,r),1)),255*(t=Math.min(Math.max(0,t),1)),255*(n=Math.min(Math.max(0,n),1))]},i.xyz.lab=function(e){var r=e[0],t=e[1],n=e[2];return t/=100,n/=108.883,r=(r/=95.047)>.008856?Math.pow(r,1/3):7.787*r+16/116,[116*(t=t>.008856?Math.pow(t,1/3):7.787*t+16/116)-16,500*(r-t),200*(t-(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116))]},i.lab.xyz=function(e){var r,t,n,a=e[0];r=e[1]/500+(t=(a+16)/116),n=t-e[2]/200;var o=Math.pow(t,3),l=Math.pow(r,3),u=Math.pow(n,3);return t=o>.008856?o:(t-16/116)/7.787,r=l>.008856?l:(r-16/116)/7.787,n=u>.008856?u:(n-16/116)/7.787,[r*=95.047,t*=100,n*=108.883]},i.lab.lch=function(e){var r,t=e[0],n=e[1],a=e[2];return(r=360*Math.atan2(a,n)/2/Math.PI)<0&&(r+=360),[t,Math.sqrt(n*n+a*a),r]},i.lch.lab=function(e){var r=e[0],t=e[1],n=e[2]/360*2*Math.PI;return[r,t*Math.cos(n),t*Math.sin(n)]},i.rgb.ansi16=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,t=o(e,3),n=t[0],a=t[1],l=t[2],u=null===r?i.rgb.hsv(e)[2]:r;if(0===(u=Math.round(u/50)))return 30;var c=30+(Math.round(l/255)<<2|Math.round(a/255)<<1|Math.round(n/255));return 2===u&&(c+=60),c},i.hsv.ansi16=function(e){return i.rgb.ansi16(i.hsv.rgb(e),e[2])},i.rgb.ansi256=function(e){var r=e[0],t=e[1],n=e[2];return r===t&&t===n?r<8?16:r>248?231:Math.round((r-8)/247*24)+232:16+36*Math.round(r/255*5)+6*Math.round(t/255*5)+Math.round(n/255*5)},i.ansi16.rgb=function(e){var r=e%10;if(0===r||7===r)return e>50&&(r+=3.5),[r=r/10.5*255,r,r];var t=.5*(1+~~(e>50));return[(1&r)*t*255,(r>>1&1)*t*255,(r>>2&1)*t*255]},i.ansi256.rgb=function(e){if(e>=232){var r=10*(e-232)+8;return[r,r,r]}var t;return e-=16,[Math.floor(e/36)/5*255,Math.floor((t=e%36)/6)/5*255,t%6/5*255]},i.rgb.hex=function(e){var r=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(r.length)+r},i.hex.rgb=function(e){var r=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!r)return[0,0,0];var t=r[0];3===r[0].length&&(t=t.split("").map((function(e){return e+e})).join(""));var n=parseInt(t,16);return[n>>16&255,n>>8&255,255&n]},i.rgb.hcg=function(e){var r,t=e[0]/255,n=e[1]/255,a=e[2]/255,o=Math.max(Math.max(t,n),a),l=Math.min(Math.min(t,n),a),u=o-l;return r=u<=0?0:o===t?(n-a)/u%6:o===n?2+(a-t)/u:4+(t-n)/u,r/=6,[360*(r%=1),100*u,100*(u<1?l/(1-u):0)]},i.hsl.hcg=function(e){var r=e[1]/100,t=e[2]/100,n=t<.5?2*r*t:2*r*(1-t),a=0;return n<1&&(a=(t-.5*n)/(1-n)),[e[0],100*n,100*a]},i.hsv.hcg=function(e){var r=e[1]/100,t=e[2]/100,n=r*t,a=0;return n<1&&(a=(t-n)/(1-n)),[e[0],100*n,100*a]},i.hcg.rgb=function(e){var r=e[0]/360,t=e[1]/100,n=e[2]/100;if(0===t)return[255*n,255*n,255*n];var a,o=[0,0,0],l=r%1*6,u=l%1,c=1-u;switch(Math.floor(l)){case 0:o[0]=1,o[1]=u,o[2]=0;break;case 1:o[0]=c,o[1]=1,o[2]=0;break;case 2:o[0]=0,o[1]=1,o[2]=u;break;case 3:o[0]=0,o[1]=c,o[2]=1;break;case 4:o[0]=u,o[1]=0,o[2]=1;break;default:o[0]=1,o[1]=0,o[2]=c}return a=(1-t)*n,[255*(t*o[0]+a),255*(t*o[1]+a),255*(t*o[2]+a)]},i.hcg.hsv=function(e){var r=e[1]/100,t=r+e[2]/100*(1-r),n=0;return t>0&&(n=r/t),[e[0],100*n,100*t]},i.hcg.hsl=function(e){var r=e[1]/100,t=e[2]/100*(1-r)+.5*r,n=0;return t>0&&t<.5?n=r/(2*t):t>=.5&&t<1&&(n=r/(2*(1-t))),[e[0],100*n,100*t]},i.hcg.hwb=function(e){var r=e[1]/100,t=r+e[2]/100*(1-r);return[e[0],100*(t-r),100*(1-t)]},i.hwb.hcg=function(e){var r=e[1]/100,t=1-e[2]/100,n=t-r,a=0;return n<1&&(a=(t-n)/(1-n)),[e[0],100*n,100*a]},i.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},i.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},i.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},i.gray.hsl=function(e){return[0,0,e[0]]},i.gray.hsv=i.gray.hsl,i.gray.hwb=function(e){return[0,100,e[0]]},i.gray.cmyk=function(e){return[0,0,0,e[0]]},i.gray.lab=function(e){return[e[0],0,0]},i.gray.hex=function(e){var r=255&Math.round(e[0]/100*255),t=((r<<16)+(r<<8)+r).toString(16).toUpperCase();return"000000".substring(t.length)+t},i.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}}}),m=v({"../../node_modules/color-convert/route.js":function(e,r){var t=p();function n(e){var r=function(){for(var e={},r=Object.keys(t),n=r.length,a=0;a<n;a++)e[r[a]]={distance:-1,parent:null};return e}(),n=[e];for(r[e].distance=0;n.length;)for(var a=n.pop(),o=Object.keys(t[a]),l=o.length,u=0;u<l;u++){var c=o[u],i=r[c];-1===i.distance&&(i.distance=r[a].distance+1,i.parent=a,n.unshift(c))}return r}function a(e,r){return function(t){return r(e(t))}}function o(e,r){for(var n=[r[e].parent,e],o=t[r[e].parent][e],l=r[e].parent;r[l].parent;)n.unshift(r[l].parent),o=a(t[r[l].parent][l],o),l=r[l].parent;return o.conversion=n,o}r.exports=function(e){for(var r=n(e),t={},a=Object.keys(r),l=a.length,u=0;u<l;u++){var c=a[u];null!==r[c].parent&&(t[c]=o(c,r))}return t}}}),y=v({"../../node_modules/color-convert/index.js":function(e,r){var t=p(),n=m(),o={};Object.keys(t).forEach((function(e){o[e]={},Object.defineProperty(o[e],"channels",{value:t[e].channels}),Object.defineProperty(o[e],"labels",{value:t[e].labels});var r=n(e);Object.keys(r).forEach((function(t){var n=r[t];o[e][t]=function(e){var r=function(){for(var r=arguments.length,t=new Array(r),n=0;n<r;n++)t[n]=arguments[n];var o=t[0];if(null==o)return o;o.length>1&&(t=o);var l=e(t);if("object"==a(l))for(var u=l.length,c=0;c<u;c++)l[c]=Math.round(l[c]);return l};return"conversion"in e&&(r.conversion=e.conversion),r}(n),o[e][t].raw=function(e){var r=function(){for(var r=arguments.length,t=new Array(r),n=0;n<r;n++)t[n]=arguments[n];var a=t[0];return null==a?a:(a.length>1&&(t=a),e(t))};return"conversion"in e&&(r.conversion=e.conversion),r}(n)}))})),r.exports=o}}),w=d(y());function x(){return(x=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function M(e,r){if(null==e)return{};var t,n,a={},o=Object.keys(e);for(n=0;n<o.length;n++)r.indexOf(t=o[n])>=0||(a[t]=e[t]);return a}function k(e){var r=g.useRef(e),t=g.useRef((function(e){r.current&&r.current(e)}));return r.current=e,t.current}var _=function(e,r,t){return void 0===r&&(r=0),void 0===t&&(t=1),e>t?t:e<r?r:e},O=function(e){return"touches"in e},E=function(e){return e&&e.ownerDocument.defaultView||self},C=function(e,r,t){var n=e.getBoundingClientRect(),a=O(r)?function(e,r){for(var t=0;t<e.length;t++)if(e[t].identifier===r)return e[t];return e[0]}(r.touches,t):r;return{left:_((a.pageX-(n.left+E(e).pageXOffset))/n.width),top:_((a.pageY-(n.top+E(e).pageYOffset))/n.height)}},j=function(e){!O(e)&&e.preventDefault()},S=b.memo((function(e){var r=e.onMove,t=e.onKey,n=M(e,["onMove","onKey"]),a=g.useRef(null),o=k(r),l=k(t),u=g.useRef(null),c=g.useRef(!1),i=g.useMemo((function(){var e=function(e){j(e),(O(e)?e.touches.length>0:e.buttons>0)&&a.current?o(C(a.current,e,u.current)):t(!1)},r=function(){return t(!1)};function t(t){var n=c.current,o=E(a.current),l=t?o.addEventListener:o.removeEventListener;l(n?"touchmove":"mousemove",e),l(n?"touchend":"mouseup",r)}return[function(e){var r,n=e.nativeEvent,l=a.current;if(l&&(j(n),r=n,(!c.current||O(r))&&l)){if(O(n)){c.current=!0;var i=n.changedTouches||[];i.length&&(u.current=i[0].identifier)}l.focus(),o(C(l,n,u.current)),t(!0)}},function(e){var r=e.which||e.keyCode;r<37||r>40||(e.preventDefault(),l({left:39===r?.05:37===r?-.05:0,top:40===r?.05:38===r?-.05:0}))},t]}),[l,o]),s=i[0],f=i[1],h=i[2];return g.useEffect((function(){return h}),[h]),b.createElement("div",x({},n,{onTouchStart:s,onMouseDown:s,className:"react-colorful__interactive",ref:a,onKeyDown:f,tabIndex:0,role:"slider"}))})),N=function(e){return e.filter(Boolean).join(" ")},P=function(e){var r=e.color,t=e.left,n=e.top,a=void 0===n?.5:n,o=N(["react-colorful__pointer",e.className]);return b.createElement("div",{className:o,style:{top:100*a+"%",left:100*t+"%"}},b.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:r}}))},I=function(e,r,t){return void 0===r&&(r=0),void 0===t&&(t=Math.pow(10,r)),Math.round(t*e)/t},z={grad:.9,turn:360,rad:360/(2*Math.PI)},H=function(e){return"#"===e[0]&&(e=e.substring(1)),e.length<6?{r:parseInt(e[0]+e[0],16),g:parseInt(e[1]+e[1],16),b:parseInt(e[2]+e[2],16),a:4===e.length?I(parseInt(e[3]+e[3],16)/255,2):1}:{r:parseInt(e.substring(0,2),16),g:parseInt(e.substring(2,4),16),b:parseInt(e.substring(4,6),16),a:8===e.length?I(parseInt(e.substring(6,8),16)/255,2):1}},R=function(e,r){return void 0===r&&(r="deg"),Number(e)*(z[r]||1)},q=function(e){var r=e.s,t=e.l;return{h:e.h,s:(r*=(t<50?t:100-t)/100)>0?2*r/(t+r)*100:0,v:t+r,a:e.a}},D=function(e){var r=e.s,t=e.v,n=e.a,a=(200-r)*t/100;return{h:I(e.h),s:I(a>0&&a<200?r*t/100/(a<=100?a:200-a)*100:0),l:I(a/2),a:I(n,2)}},L=function(e){var r=D(e);return"hsl("+r.h+", "+r.s+"%, "+r.l+"%)"},A=function(e){var r=D(e);return"hsla("+r.h+", "+r.s+"%, "+r.l+"%, "+r.a+")"},B=function(e){var r=e.h,t=e.s,n=e.v,a=e.a;r=r/360*6,t/=100,n/=100;var o=Math.floor(r),l=n*(1-t),u=n*(1-(r-o)*t),c=n*(1-(1-r+o)*t),i=o%6;return{r:I(255*[n,u,l,l,c,n][i]),g:I(255*[c,n,n,u,l,l][i]),b:I(255*[l,l,c,n,n,u][i]),a:I(a,2)}},T=function(e){var r=e.toString(16);return r.length<2?"0"+r:r},F=function(e){var r=e.r,t=e.g,n=e.b,a=e.a,o=a<1?T(I(255*a)):"";return"#"+T(r)+T(t)+T(n)+o},K=function(e){var r=e.r,t=e.g,n=e.b,a=e.a,o=Math.max(r,t,n),l=o-Math.min(r,t,n),u=l?o===r?(t-n)/l:o===t?2+(n-r)/l:4+(r-t)/l:0;return{h:I(60*(u<0?u+6:u)),s:I(o?l/o*100:0),v:I(o/255*100),a:a}},V=b.memo((function(e){var r=e.hue,t=e.onChange,n=N(["react-colorful__hue",e.className]);return b.createElement("div",{className:n},b.createElement(S,{onMove:function(e){t({h:360*e.left})},onKey:function(e){t({h:_(r+360*e.left,0,360)})},"aria-label":"Hue","aria-valuenow":I(r),"aria-valuemax":"360","aria-valuemin":"0"},b.createElement(P,{className:"react-colorful__hue-pointer",left:r/360,color:L({h:r,s:100,v:100,a:1})})))})),$=b.memo((function(e){var r=e.hsva,t=e.onChange,n={backgroundColor:L({h:r.h,s:100,v:100,a:1})};return b.createElement("div",{className:"react-colorful__saturation",style:n},b.createElement(S,{onMove:function(e){t({s:100*e.left,v:100-100*e.top})},onKey:function(e){t({s:_(r.s+100*e.left,0,100),v:_(r.v-100*e.top,0,100)})},"aria-label":"Color","aria-valuetext":"Saturation "+I(r.s)+"%, Brightness "+I(r.v)+"%"},b.createElement(P,{className:"react-colorful__saturation-pointer",top:1-r.v/100,left:r.s/100,color:L(r)})))})),X=function(e,r){if(e===r)return!0;for(var t in e)if(e[t]!==r[t])return!1;return!0},U=function(e,r){return e.replace(/\s/g,"")===r.replace(/\s/g,"")};function W(e,r,t){var n=k(t),a=g.useState((function(){return e.toHsva(r)})),o=a[0],l=a[1],u=g.useRef({color:r,hsva:o});g.useEffect((function(){if(!e.equal(r,u.current.color)){var t=e.toHsva(r);u.current={hsva:t,color:r},l(t)}}),[r,e]),g.useEffect((function(){var r;X(o,u.current.hsva)||e.equal(r=e.fromHsva(o),u.current.color)||(u.current={hsva:o,color:r},n(r))}),[o,e,n]);var c=g.useCallback((function(e){l((function(r){return Object.assign({},r,e)}))}),[]);return[o,c]}var Y,G=("undefined"==typeof window?"undefined":a(window))<"u"?g.useLayoutEffect:g.useEffect,Z=new Map,J=function(e){G((function(){var r=e.current?e.current.ownerDocument:document;if(void 0!==r&&!Z.has(r)){var t=r.createElement("style");t.innerHTML='.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>\')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}',Z.set(r,t);var n=("undefined"==typeof __webpack_nonce__?"undefined":a(__webpack_nonce__))<"u"?__webpack_nonce__:void 0;n&&t.setAttribute("nonce",n),r.head.appendChild(t)}}),[])},Q=function(e){var r=e.className,t=e.colorModel,n=e.color,a=void 0===n?t.defaultColor:n,o=e.onChange,l=M(e,["className","colorModel","color","onChange"]),u=g.useRef(null);J(u);var c=W(t,a,o),i=c[0],s=c[1],f=N(["react-colorful",r]);return b.createElement("div",x({},l,{ref:u,className:f}),b.createElement($,{hsva:i,onChange:s}),b.createElement(V,{hue:i.h,onChange:s,className:"react-colorful__last-control"}))},ee={defaultColor:"000",toHsva:function(e){return K(H(e))},fromHsva:function(e){return function(e){return F(B(e))}({h:e.h,s:e.s,v:e.v,a:1})},equal:function(e,r){return e.toLowerCase()===r.toLowerCase()||X(H(e),H(r))}},re=function(e){var r=e.className,t=e.hsva,n=e.onChange,a={backgroundImage:"linear-gradient(90deg, "+A(Object.assign({},t,{a:0}))+", "+A(Object.assign({},t,{a:1}))+")"},o=N(["react-colorful__alpha",r]),l=I(100*t.a);return b.createElement("div",{className:o},b.createElement("div",{className:"react-colorful__alpha-gradient",style:a}),b.createElement(S,{onMove:function(e){n({a:e.left})},onKey:function(e){n({a:_(t.a+e.left)})},"aria-label":"Alpha","aria-valuetext":l+"%","aria-valuenow":l,"aria-valuemin":"0","aria-valuemax":"100"},b.createElement(P,{className:"react-colorful__alpha-pointer",left:t.a,color:A(t)})))},te=function(e){var r=e.className,t=e.colorModel,n=e.color,a=void 0===n?t.defaultColor:n,o=e.onChange,l=M(e,["className","colorModel","color","onChange"]),u=g.useRef(null);J(u);var c=W(t,a,o),i=c[0],s=c[1],f=N(["react-colorful",r]);return b.createElement("div",x({},l,{ref:u,className:f}),b.createElement($,{hsva:i,onChange:s}),b.createElement(V,{hue:i.h,onChange:s}),b.createElement(re,{hsva:i,onChange:s,className:"react-colorful__last-control"}))},ne={defaultColor:"hsla(0, 0%, 0%, 1)",toHsva:function(e){var r=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return r?q({h:R(r[1],r[2]),s:Number(r[3]),l:Number(r[4]),a:void 0===r[5]?1:Number(r[5])/(r[6]?100:1)}):{h:0,s:0,v:0,a:1}},fromHsva:A,equal:U},ae={defaultColor:"rgba(0, 0, 0, 1)",toHsva:function(e){var r=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return r?K({r:Number(r[1])/(r[2]?100/255:1),g:Number(r[3])/(r[4]?100/255:1),b:Number(r[5])/(r[6]?100/255:1),a:void 0===r[7]?1:Number(r[7])/(r[8]?100:1)}):{h:0,s:0,v:0,a:1}},fromHsva:function(e){var r=B(e);return"rgba("+r.r+", "+r.g+", "+r.b+", "+r.a+")"},equal:U},oe=u.div({position:"relative",maxWidth:250,'&[aria-readonly="true"]':{opacity:.5}}),le=u(c)({position:"absolute",zIndex:1,top:4,left:4,"[aria-readonly=true] &":{cursor:"not-allowed"}}),ue=u.div({width:200,margin:5,".react-colorful__saturation":{borderRadius:"4px 4px 0 0"},".react-colorful__hue":{boxShadow:"inset 0 0 0 1px rgb(0 0 0 / 5%)"},".react-colorful__last-control":{borderRadius:"0 0 4px 4px"}}),ce=u(s)((function(e){return{fontFamily:e.theme.typography.fonts.base}})),ie=u.div({display:"grid",gridTemplateColumns:"repeat(9, 16px)",gap:6,padding:3,marginTop:5,width:200}),se=u.div((function(e){var r=e.theme;return{width:16,height:16,boxShadow:e.active?"".concat(r.appBorderColor," 0 0 0 1px inset, ").concat(r.textMutedColor,"50 0 0 0 4px"):"".concat(r.appBorderColor," 0 0 0 1px inset"),borderRadius:r.appBorderRadius}})),fe=function(r){var n=r.value,a=r.style,o=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==r.indexOf(n))continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)t=o[n],-1===r.indexOf(t)&&{}.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(r,e),l="linear-gradient(".concat(n,", ").concat(n,"), ").concat('url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>\')',", linear-gradient(#fff, #fff)");return b.createElement(se,t(t({},o),{},{style:t(t({},a),{},{backgroundImage:l})}))},he=u(f.Input)((function(e){var r=e.theme;e.readOnly;return{width:"100%",paddingLeft:30,paddingRight:30,boxSizing:"border-box",fontFamily:r.typography.fonts.base}})),de=u(h)((function(e){return{position:"absolute",zIndex:1,top:6,right:7,width:20,height:20,padding:4,boxSizing:"border-box",cursor:"pointer",color:e.theme.input.color}})),ve=((Y=ve||{}).RGB="rgb",Y.HSL="hsl",Y.HEX="hex",Y),ge=Object.values(ve),be=/\(([0-9]+),\s*([0-9]+)%?,\s*([0-9]+)%?,?\s*([0-9.]+)?\)/,pe=/^\s*rgba?\(([0-9]+),\s*([0-9]+),\s*([0-9]+),?\s*([0-9.]+)?\)\s*$/i,me=/^\s*hsla?\(([0-9]+),\s*([0-9]+)%,\s*([0-9]+)%,?\s*([0-9.]+)?\)\s*$/i,ye=/^\s*#?([0-9a-f]{3}|[0-9a-f]{6})\s*$/i,we=/^\s*#?([0-9a-f]{3})\s*$/i,xe={hex:function(e){return b.createElement(Q,x({},e,{colorModel:ee}))},rgb:function(e){return b.createElement(te,x({},e,{colorModel:ae}))},hsl:function(e){return b.createElement(te,x({},e,{colorModel:ne}))}},Me={hex:"transparent",rgb:"rgba(0, 0, 0, 0)",hsl:"hsla(0, 0%, 0%, 0)"},ke=function(e){var r=null==e?void 0:e.match(be);if(!r)return[0,0,0,1];var t=o(r,5),n=t[1],a=t[2],l=t[3],u=t[4];return[n,a,l,void 0===u?1:u].map(Number)},_e=function(e){if(e){var r=!0;if(pe.test(e)){var t=o(ke(e),4),n=t[0],a=t[1],l=t[2],u=t[3],c=o(w.default.rgb.hsl([n,a,l])||[0,0,0],3),i=c[0],s=c[1],f=c[2];return{valid:r,value:e,keyword:w.default.rgb.keyword([n,a,l]),colorSpace:"rgb",rgb:e,hsl:"hsla(".concat(i,", ").concat(s,"%, ").concat(f,"%, ").concat(u,")"),hex:"#".concat(w.default.rgb.hex([n,a,l]).toLowerCase())}}if(me.test(e)){var h=o(ke(e),4),d=h[0],v=h[1],g=h[2],b=h[3],p=o(w.default.hsl.rgb([d,v,g])||[0,0,0],3),m=p[0],y=p[1],x=p[2];return{valid:r,value:e,keyword:w.default.hsl.keyword([d,v,g]),colorSpace:"hsl",rgb:"rgba(".concat(m,", ").concat(y,", ").concat(x,", ").concat(b,")"),hsl:e,hex:"#".concat(w.default.hsl.hex([d,v,g]).toLowerCase())}}var M=e.replace("#",""),k=w.default.keyword.rgb(M)||w.default.hex.rgb(M),_=w.default.rgb.hsl(k),O=e;if(/[^#a-f0-9]/i.test(e)?O=M:ye.test(e)&&(O="#".concat(M)),O.startsWith("#"))r=ye.test(O);else try{w.default.keyword.hex(O)}catch(E){r=!1}return{valid:r,value:O,keyword:w.default.rgb.keyword(k),colorSpace:"hex",rgb:"rgba(".concat(k[0],", ").concat(k[1],", ").concat(k[2],", 1)"),hsl:"hsla(".concat(_[0],", ").concat(_[1],"%, ").concat(_[2],"%, 1)"),hex:O}}},Oe=function(e,r){var t=o(g.useState(e||""),2),n=t[0],a=t[1],l=o(g.useState((function(){return _e(n)})),2),u=l[0],c=l[1],i=o(g.useState((null==u?void 0:u.colorSpace)||"hex"),2),s=i[0],f=i[1];g.useEffect((function(){var r=e||"",t=_e(r);a(r),c(t),f((null==t?void 0:t.colorSpace)||"hex")}),[e]);var h=g.useMemo((function(){return function(e,r,t){if(!e||null==r||!r.valid)return Me[t];if("hex"!==t)return(null==r?void 0:r[t])||Me[t];if(!r.hex.startsWith("#"))try{return"#".concat(w.default.keyword.hex(r.hex))}catch(i){return Me.hex}var n=r.hex.match(we);if(!n)return ye.test(r.hex)?r.hex:Me.hex;var a=o(n[1].split(""),3),l=a[0],u=a[1],c=a[2];return"#".concat(l).concat(l).concat(u).concat(u).concat(c).concat(c)}(n,u,s).toLowerCase()}),[n,u,s]),d=g.useCallback((function(e){var t=_e(e),n=(null==t?void 0:t.value)||e||"";a(n),""===n&&(c(void 0),r(void 0)),t&&(c(t),f(t.colorSpace),r(t.value))}),[r]),v=g.useCallback((function(){var e=ge.indexOf(s)+1;e>=ge.length&&(e=0),f(ge[e]);var t=(null==u?void 0:u[ge[e]])||"";a(t),r(t)}),[u,s,r]);return{value:n,realValue:h,updateValue:d,color:u,colorSpace:s,cycleColorSpace:v}},Ee=function(e){return e.replace(/\s*/,"").toLowerCase()},Ce=function(e){var r,n=e.name,a=e.value,u=e.onChange,s=e.onFocus,f=e.onBlur,h=e.presetColors,d=e.startOpen,v=void 0!==d&&d,p=e.argType,m=g.useCallback(l(u,200),[u]),y=Oe(a,m),w=y.value,x=y.realValue,M=y.updateValue,k=y.color,_=y.colorSpace,O=y.cycleColorSpace,E=function(e,r,n){var a=o(g.useState(null!=r&&r.valid?[r]:[]),2),l=a[0],u=a[1];g.useEffect((function(){void 0===r&&u([])}),[r]);var c=g.useMemo((function(){return(e||[]).map((function(e){return"string"==typeof e?_e(e):e.title?t(t({},_e(e.color)),{},{keyword:e.title}):_e(e.color)})).concat(l).filter(Boolean).slice(-27)}),[e,l]),i=g.useCallback((function(e){(null==e?void 0:e.valid)&&(c.some((function(r){return Ee(r[n])===Ee(e[n])}))||u((function(r){return r.concat(e)})))}),[n,c]);return{presets:c,addPreset:i}}(h,k,_),C=E.presets,j=E.addPreset,S=xe[_],N=!(null==p||null===(r=p.table)||void 0===r||!r.readonly);return b.createElement(oe,{"aria-readonly":N},b.createElement(le,{startOpen:v,trigger:N?[null]:void 0,closeOnOutsideClick:!0,onVisibleChange:function(){return j(k)},tooltip:b.createElement(ue,null,b.createElement(S,{color:"transparent"===x?"#000000":x,onChange:M,onFocus:s,onBlur:f}),C.length>0&&b.createElement(ie,null,C.map((function(e,r){return b.createElement(c,{key:"".concat(e.value,"-").concat(r),hasChrome:!1,tooltip:b.createElement(ce,{note:e.keyword||e.value})},b.createElement(fe,{value:e[_],active:k&&Ee(e[_])===Ee(k[_]),onClick:function(){return M(e.value)}}))}))))},b.createElement(fe,{value:x,style:{margin:4}})),b.createElement(he,{id:i(n),value:w,onChange:function(e){return M(e.target.value)},onFocus:function(e){return e.target.select()},readOnly:N,placeholder:"Choose color..."}),w?b.createElement(de,{onClick:O}):null)};r({ColorControl:Ce,default:Ce})}}}))}();

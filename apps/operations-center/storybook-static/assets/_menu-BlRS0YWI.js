import{j as e}from"./index-C_VjMC7g.js";import{i,t as l,q as u,L as c,v as d,O as p,s as m}from"./__federation_expose_App-DYUBmReV.js";import{u as f}from"./use-splat-hEUO4lF7.js";import{a as j,b as x,c as h,S}from"./super-folder-button-BhEwJOIW.js";import{r as v}from"./index-D4lIrffr.js";function F(){return e.jsx(e.Fragment,{children:new Array(5).fill(null).map((a,o)=>e.jsx("li",{children:e.jsx(S,{})},o))})}const _=function(){const[o]=f(),t=i(m()),n=v.useRef(null);return l(()=>{const s=n.current,r=document.getElementById(o);r&&s&&s.scrollTo({top:r.offsetTop-s.offsetTop-6,behavior:"instant"})},[]),e.jsxs("div",{className:"grid h-full grid-cols-[auto_1fr] overflow-hidden",children:[e.jsx("nav",{ref:n,className:"border-border-hard no-scrollbar overflow-y-auto border-r",children:e.jsxs("ul",{className:"space-y-1.5 p-2.5",children:[t.isPending&&e.jsx(F,{}),t.isSuccess&&t.data.items.map(s=>{const r=s.id===o;return e.jsx("li",{children:e.jsx(j,{id:s.id,asChild:!0,color:u(s.color),"aria-pressed":r,"data-state":r?"on":"off",children:e.jsxs(c,{from:d.fullPath,to:"./$",params:{_splat:s.id},children:[e.jsx(x,{children:s.emoji}),e.jsx(h,{children:s.title})]})})},s.id)})]})}),e.jsx("div",{className:"overflow-y-auto",children:e.jsx(p,{})})]})};export{_ as component};

!function(){function r(r,n){return function(r){if(Array.isArray(r))return r}(r)||function(r,e){var n=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=n){var t,o,i,l,a=[],s=!0,c=!1;try{if(i=(n=n.call(r)).next,0===e){if(Object(n)!==n)return;s=!1}else for(;!(s=(t=i.call(n)).done)&&(a.push(t.value),a.length!==e);s=!0);}catch(r){c=!0,o=r}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(c)throw o}}return a}}(r,n)||function(r,n){if(r){if("string"==typeof r)return e(r,n);var t={}.toString.call(r).slice(8,-1);return"Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?e(r,n):void 0}}(r,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function e(r,e){(null==e||e>r.length)&&(e=r.length);for(var n=0,t=Array(e);n<e;n++)t[n]=r[n];return t}System.register(["./index-legacy-C2Kr5I5h.js","./__federation_expose_App-legacy-pwn7RX54.js","./use-splat-legacy-CJVCqkki.js","./super-folder-button-legacy-DkI4bWSd.js","./index-legacy-8Bh_oy_I.js"],(function(e,n){"use strict";var t,o,i,l,a,s,c,u,f,d,y,h,j,m;return{setters:[function(r){t=r.j},function(r){o=r.i,i=r.t,l=r.q,a=r.L,s=r.v,c=r.O,u=r.s},function(r){f=r.u},function(r){d=r.a,y=r.b,h=r.c,j=r.S},function(r){m=r.r}],execute:function(){function n(){return t.jsx(t.Fragment,{children:new Array(5).fill(null).map((function(r,e){return t.jsx("li",{children:t.jsx(j,{})},e)}))})}e("component",(function(){var e=r(f(),1)[0],j=o(u()),p=m.useRef(null);return i((function(){var r=p.current,n=document.getElementById(e);if(n&&r){r.scrollTo({top:n.offsetTop-r.offsetTop-6,behavior:"instant"})}}),[]),t.jsxs("div",{className:"grid h-full grid-cols-[auto_1fr] overflow-hidden",children:[t.jsx("nav",{ref:p,className:"border-border-hard no-scrollbar overflow-y-auto border-r",children:t.jsxs("ul",{className:"space-y-1.5 p-2.5",children:[j.isPending&&t.jsx(n,{}),j.isSuccess&&j.data.items.map((function(r){var n=r.id===e;return t.jsx("li",{children:t.jsx(d,{id:r.id,asChild:!0,color:l(r.color),"aria-pressed":n,"data-state":n?"on":"off",children:t.jsxs(a,{from:s.fullPath,to:"./$",params:{_splat:r.id},children:[t.jsx(y,{children:r.emoji}),t.jsx(h,{children:r.title})]})})},r.id)}))]})}),t.jsx("div",{className:"overflow-y-auto",children:t.jsx(c,{})})]})}))}}}))}();

import{j as e}from"./index-C_VjMC7g.js";import{Z as x,i as j,Q as g,L as f,n as v,s as y}from"./__federation_expose_App-DYUBmReV.js";import{c as _,z as k,n as b}from"./x-B9YxCZbW.js";import{r as F}from"./index-D4lIrffr.js";import{u as w}from"./use-splat-hEUO4lF7.js";import{m}from"./color-Dui9lDAX.js";import{M,a as N}from"./menu-product-card-BylCxK_t.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],z=_("ArrowLeft",q),P=o=>{const r=[],n=[];for(const s of o.items)"items"in s?n.push(s):r.push(s);return{products:r,folders:n}},S=(o,r)=>F.useMemo(()=>{const[n,...s]=o,t=[],l=r.find(u=>u.id===n);if(!l)return{superFolder:null,path:t,products:[],folders:[]};t.push({id:n,name:l.title,emoji:l.emoji});let d=l;for(const u of s)for(const i of d.items)"items"in i&&i.id===u&&(d=i,t.push({id:i.id,name:i.name}));return{superFolder:l,path:t,...d?P(d):{products:[],folders:[]}}},[r,o]),c=({children:o,color:r,className:n,style:s,...t})=>e.jsx(k,{className:b("h-17 w-full px-2 text-center whitespace-normal",n),style:{...r&&{"--chunky-border-color":m(r,.25),"--chunky-shadow-bg-color":m(r,.25),"--chunky-shadow-border-color":m(r,.35)},...s!=null?s:{}},...t,children:o});try{c.displayName="FolderButton",c.__docgenInfo={description:"",displayName:"FolderButton",props:{color:{defaultValue:null,description:"",name:"color",required:!1,type:{name:"string | undefined"}},size:{defaultValue:null,description:"",name:"size",required:!1,type:{name:"enum",value:[{value:"undefined"},{value:'"sm"'},{value:'"md"'},{value:'"lg"'},{value:'"xl"'}]}},square:{defaultValue:null,description:"",name:"square",required:!1,type:{name:"boolean | undefined"}},variant:{defaultValue:null,description:"",name:"variant",required:!1,type:{name:"enum",value:[{value:"undefined"},{value:'"accent"'},{value:'"positive"'},{value:'"negative"'},{value:'"primary"'},{value:'"secondary"'},{value:'"warning"'}]}}}}}catch(o){}function B(){return e.jsx(e.Fragment,{children:new Array(2).fill(null).map((o,r)=>e.jsx("li",{children:e.jsx(v,{className:"block h-18"})},r))})}const E=function(){var i;const{order:r}=x.useParams(),n=w(),s=j(y()),{superFolder:t,path:l,products:d,folders:u}=S(n,((i=s.data)==null?void 0:i.items)||[]);return g(l.map(({name:a,emoji:p},h)=>({id:"menu-".concat(h),label:p?"".concat(p," ").concat(a):a}))),e.jsxs("div",{className:"p-2.5",children:[e.jsxs("ul",{className:"grid grid-cols-2 gap-1.5 not-empty:mb-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5",children:[n.length>1&&e.jsx("li",{children:e.jsx(c,{asChild:!0,children:e.jsxs(f,{to:n.slice(0,-1).join("/"),children:[e.jsx(z,{className:"size-4 stroke-1"}),"Back"]})})}),s.isPending&&e.jsx(B,{}),s.isSuccess&&u.map(a=>e.jsx("li",{children:e.jsx(c,{asChild:!0,color:t==null?void 0:t.color,children:e.jsx(f,{to:[...n,a.id].join("/"),children:a.name})})},a.id))]}),s.isSuccess&&e.jsx(M,{size:"tight",children:d.map(a=>e.jsx(N,{orderId:r,product:a},a.id))})]})};export{E as component};

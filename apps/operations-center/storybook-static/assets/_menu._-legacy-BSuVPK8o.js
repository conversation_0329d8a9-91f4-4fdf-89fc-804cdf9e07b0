!function(){function e(r){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(r)}var r=["children","color","className","style"];function n(e){return function(e){if(Array.isArray(e))return s(e)}(e)||l(e)||c(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function t(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),n.push.apply(n,t)}return n}function o(e){for(var r=1;r<arguments.length;r++){var n=null!=arguments[r]?arguments[r]:{};r%2?t(Object(n),!0).forEach((function(r){i(e,r,n[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):t(Object(n)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))}))}return e}function i(r,n,t){return(n=function(r){var n=function(r,n){if("object"!=e(r)||!r)return r;var t=r[Symbol.toPrimitive];if(void 0!==t){var o=t.call(r,n||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(r)}(r,"string");return"symbol"==e(n)?n:n+""}(n))in r?Object.defineProperty(r,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[n]=t,r}function a(e){return function(e){if(Array.isArray(e))return e}(e)||l(e)||c(e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function u(e,r){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=c(e))||r&&e&&"number"==typeof e.length){n&&(e=n);var t=0,o=function(){};return{s:o,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){l=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(l)throw i}}}}function c(e,r){if(e){if("string"==typeof e)return s(e,r);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,r):void 0}}function s(e,r){(null==r||r>e.length)&&(r=e.length);for(var n=0,t=Array(r);n<r;n++)t[n]=e[n];return t}System.register(["./index-legacy-C2Kr5I5h.js","./__federation_expose_App-legacy-pwn7RX54.js","./x-legacy-yM9Jk2p5.js","./index-legacy-8Bh_oy_I.js","./use-splat-legacy-CJVCqkki.js","./color-legacy-D3k4iKFx.js","./menu-product-card-legacy-CIxhdtnP.js"],(function(e,t){"use strict";var i,l,c,s,f,d,y,m,p,v,b,h,j,g,x;return{setters:[function(e){i=e.j},function(e){l=e.Z,c=e.i,s=e.Q,f=e.L,d=e.n,y=e.s},function(e){m=e.c,p=e.z,v=e.n},function(e){b=e.r},function(e){h=e.u},function(e){j=e.m},function(e){g=e.M,x=e.a}],execute:function(){
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */
var t=m("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),w=function(e,r){return b.useMemo((function(){var n=a(e),t=n[0],i=n.slice(1),l=[],c=r.find((function(e){return e.id===t}));if(!c)return{superFolder:null,path:l,products:[],folders:[]};l.push({id:t,name:c.title,emoji:c.emoji});var s,f=c,d=u(i);try{for(d.s();!(s=d.n()).done;){var y,m=s.value,p=u(f.items);try{for(p.s();!(y=p.n()).done;){var v=y.value;"items"in v&&v.id===m&&(f=v,l.push({id:v.id,name:v.name}))}}catch(b){p.e(b)}finally{p.f()}}}catch(b){d.e(b)}finally{d.f()}return o({superFolder:c,path:l},f?function(e){var r,n=[],t=[],o=u(e.items);try{for(o.s();!(r=o.n()).done;){var i=r.value;"items"in i?t.push(i):n.push(i)}}catch(b){o.e(b)}finally{o.f()}return{products:n,folders:t}}(f):{products:[],folders:[]})}),[r,e])},O=function(e){var n=e.children,t=e.color,a=e.className,l=e.style,u=function(e,r){if(null==e)return{};var n,t,o=function(e,r){if(null==e)return{};var n={};for(var t in e)if({}.hasOwnProperty.call(e,t)){if(-1!==r.indexOf(t))continue;n[t]=e[t]}return n}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(t=0;t<i.length;t++)n=i[t],-1===r.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,r);return i.jsx(p,o(o({className:v("h-17 w-full px-2 text-center whitespace-normal",a),style:o(o({},t&&{"--chunky-border-color":j(t,.25),"--chunky-shadow-bg-color":j(t,.25),"--chunky-shadow-border-color":j(t,.35)}),null!=l?l:{})},u),{},{children:n}))};try{O.displayName="FolderButton",O.__docgenInfo={description:"",displayName:"FolderButton",props:{color:{defaultValue:null,description:"",name:"color",required:!1,type:{name:"string | undefined"}},size:{defaultValue:null,description:"",name:"size",required:!1,type:{name:"enum",value:[{value:"undefined"},{value:'"sm"'},{value:'"md"'},{value:'"lg"'},{value:'"xl"'}]}},square:{defaultValue:null,description:"",name:"square",required:!1,type:{name:"boolean | undefined"}},variant:{defaultValue:null,description:"",name:"variant",required:!1,type:{name:"enum",value:[{value:"undefined"},{value:'"accent"'},{value:'"positive"'},{value:'"negative"'},{value:'"primary"'},{value:'"secondary"'},{value:'"warning"'}]}}}}}catch(P){}function S(){return i.jsx(i.Fragment,{children:new Array(2).fill(null).map((function(e,r){return i.jsx("li",{children:i.jsx(d,{className:"block h-18"})},r)}))})}e("component",(function(){var e,r=l.useParams().order,o=h(),a=c(y()),u=w(o,(null===(e=a.data)||void 0===e?void 0:e.items)||[]),d=u.superFolder,m=u.path,p=u.products,v=u.folders;return s(m.map((function(e,r){var n=e.name,t=e.emoji;return{id:"menu-".concat(r),label:t?"".concat(t," ").concat(n):n}}))),i.jsxs("div",{className:"p-2.5",children:[i.jsxs("ul",{className:"grid grid-cols-2 gap-1.5 not-empty:mb-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5",children:[o.length>1&&i.jsx("li",{children:i.jsx(O,{asChild:!0,children:i.jsxs(f,{to:o.slice(0,-1).join("/"),children:[i.jsx(t,{className:"size-4 stroke-1"}),"Back"]})})}),a.isPending&&i.jsx(S,{}),a.isSuccess&&v.map((function(e){return i.jsx("li",{children:i.jsx(O,{asChild:!0,color:null==d?void 0:d.color,children:i.jsx(f,{to:[].concat(n(o),[e.id]).join("/"),children:e.name})})},e.id)}))]}),a.isSuccess&&i.jsx(g,{size:"tight",children:p.map((function(e){return i.jsx(x,{orderId:r,product:e},e.id)}))})]})}))}}}))}();

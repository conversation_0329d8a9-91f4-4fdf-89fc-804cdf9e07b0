import{j as t}from"./index-C_VjMC7g.js";import{i as _,P as S}from"./drawer-efJps8Fr.js";import{e as R,B as T,f as w,h as Q,i as k,C as q,D as E,k as K,m as g,l as L,F as M,G as $,H as v,I as C,J as z,K as D,M as A,L as V,y as F,N as H,P as I,Q as G,O as J,S as U}from"./__federation_expose_App-DYUBmReV.js";import{c as W,e as P,n as X,z as p}from"./x-B9YxCZbW.js";import{r as c}from"./index-D4lIrffr.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y=[["path",{d:"M2 12h20",key:"9i4pu4"}],["path",{d:"M20 12v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-8",key:"u0tga0"}],["path",{d:"m4 8 16-4",key:"16g0ng"}],["path",{d:"m8.86 6.78-.45-1.81a2 2 0 0 1 1.45-2.43l1.94-.48a2 2 0 0 1 2.43 1.46l.45 1.8",key:"12cejc"}]],N=W("CookingPot",Y),Z={hidden:{height:0,opacity:0,transition:{duration:.125,ease:P["emphasized-accelerate"]}},visible:{height:"auto",opacity:1,scale:1,transition:{duration:.35,ease:P["emphasized-decelerate"]}}},h=({orderId:e,className:i})=>{const a=R(),r=T({select:n=>n.pathname}),d=c.useMemo(()=>r.includes("cart"),[r]),{toast:f}=w(),y=Q(),o=k(g(e)),u=q(e),{clearCart:b}=E(e),l=K({mutationFn:async()=>{var n;return(n=o.data)!=null&&n.assignedTo||await M(e,"Random User"),$(e,u)},onError:n=>{f({variant:"error",icon:N,message:n.message})},onSuccess:async n=>{var x;await y.invalidateQueries({queryKey:g(e).queryKey}),y.setQueryData(L().queryKey,m=>m==null?void 0:m.map(j=>j.id===e?n:j)),f({message:"Items successfully sent to the kitchen.",variant:"success",icon:N}),a({to:"/dine-in/spaces/$space",params:{space:((x=o.data)==null?void 0:x.spaceId)||""}}),b()}}),s=c.useMemo(()=>u.length>0?{num:C(u),price:v(u),status:"pending"}:o.data&&o.data.ordered.length>0?{num:C(o.data.ordered),price:v(o.data.ordered),status:"to-pay"}:null,[o.data,u]),O=()=>{a({to:"/user-testing-complete"})},B=({target:n})=>{_(n)||a(d?{to:"/dine-in/$order",params:{order:e}}:{to:"/dine-in/$order/cart",params:{order:e},search:{status:s==null?void 0:s.status}})};return t.jsx(z,{initial:!1,children:s&&t.jsxs(D.div,{className:X("flex items-center justify-end gap-1.5 overflow-hidden px-3 *:py-3",i),onClick:B,variants:Z,initial:"hidden",animate:"visible",exit:"hidden",inert:l.isPending,children:[t.jsxs("div",{className:"mr-auto text-sm",children:[s.status==="pending"?"Pending":"To pay",t.jsx(ee,{num:s.num}),t.jsx(A,{amount:s.price,className:"block"})]}),!d&&t.jsx(p,{asChild:!0,children:t.jsx(V,{to:"/dine-in/$order/cart",params:{order:e},search:{status:s.status},children:"Details"})}),s.status==="pending"&&t.jsx(p,{variant:"accent",className:"min-w-35",onClick:()=>l.mutate(),isLoading:l.isPending,children:"Send to Kitchen"}),s.status==="to-pay"&&t.jsx(p,{variant:"positive",className:"min-w-35",onClick:O,isLoading:l.isPending,children:"Payment"}),t.jsx(te,{})]})})},ee=({num:e})=>{const i=c.useRef(null),a=c.useRef(e);return c.useEffect(()=>{const r=i.current;if(r&&a.current!==e){const d=e>a.current?"increase":"decrease";r.animate([{transform:"scale(1)"},{transform:"scale(".concat(d==="increase"?1.2:.85,")")},{transform:"scale(1)"}],{duration:200,easing:"ease-out"}),a.current=e}},[e]),t.jsx(F,{ref:i,size:"xs",className:"bg-foreground/15 ml-2 min-w-5 justify-center px-1 align-bottom font-medium tabular-nums",children:e})},te=()=>{const{setTheme:e}=H();return c.useEffect(()=>(e("dark"),()=>e("light")),[e]),null};try{h.displayName="OrderStatusBar",h.__docgenInfo={description:"",displayName:"OrderStatusBar",props:{orderId:{defaultValue:null,description:"",name:"orderId",required:!0,type:{name:"string"}},className:{defaultValue:null,description:"",name:"className",required:!1,type:{name:"string | undefined"}}}}}catch(e){}const oe=function(){var r;const i=I.useParams(),a=k(g(i.order));return G([{id:"table",label:((r=a.data)==null?void 0:r.tableName)||"",loading:a.isPending,variant:"badge"}]),t.jsxs(S,{children:[t.jsx(J,{}),t.jsx(U,{id:"order-status-bar",children:t.jsx(h,{orderId:i.order,className:"-order-1"})})]})};export{oe as component};

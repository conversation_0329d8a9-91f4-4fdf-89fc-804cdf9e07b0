import{j as o}from"./index-C_VjMC7g.js";import{z as r}from"./x-B9YxCZbW.js";import"./index-D4lIrffr.js";import{E as n,a as s,b as i,c as t,L as e}from"./__federation_expose_App-DYUBmReV.js";const m=()=>o.jsxs(n,{children:[o.jsx(s,{children:"Order not found"}),o.jsx(i,{children:"The order you are looking for does not exist."}),o.jsx(t,{children:o.jsx(r,{asChild:!0,children:o.jsx(e,{to:"/dine-in",children:"Back to Tables"})})})]});export{m as notFoundComponent};

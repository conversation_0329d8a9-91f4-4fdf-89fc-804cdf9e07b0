System.register(["./index-legacy-C2Kr5I5h.js","./x-legacy-yM9Jk2p5.js","./index-legacy-8Bh_oy_I.js","./__federation_expose_App-legacy-pwn7RX54.js"],(function(e,n){"use strict";var o,t,i,r,s,c,d;return{setters:[function(e){o=e.j},function(e){t=e.z},null,function(e){i=e.E,r=e.a,s=e.b,c=e.c,d=e.L}],execute:function(){e("notFoundComponent",(function(){return o.jsxs(i,{children:[o.jsx(r,{children:"Order not found"}),o.jsx(s,{children:"The order you are looking for does not exist."}),o.jsx(c,{children:o.jsx(t,{asChild:!0,children:o.jsx(d,{to:"/dine-in",children:"Back to Tables"})})})]})}))}}}));

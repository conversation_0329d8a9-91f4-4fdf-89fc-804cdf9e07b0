!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,r,a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",o=a.toStringTag||"@@toStringTag";function s(e,a,i,o){var s=a&&a.prototype instanceof u?a:u,d=Object.create(s.prototype);return n(d,"_invoke",function(e,n,a){var i,o,s,u=0,d=a||[],l=!1,f={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,n){return i=e,o=0,s=t,f.n=n,c}};function p(e,n){for(o=e,s=n,r=0;!l&&u&&!a&&r<d.length;r++){var a,i=d[r],p=f.p,m=i[2];e>3?(a=m===n)&&(s=i[(o=i[4])?5:(o=3,3)],i[4]=i[5]=t):i[0]<=p&&((a=e<2&&p<i[1])?(o=0,f.v=n,f.n=i[1]):p<m&&(a=e<3||i[0]>n||n>m)&&(i[4]=e,i[5]=n,f.n=m,o=0))}if(a||e>1)return c;throw l=!0,n}return function(a,d,m){if(u>1)throw TypeError("Generator is already running");for(l&&1===d&&p(d,m),o=d,s=m;(r=o<2?t:s)||!l;){i||(o?o<3?(o>1&&(f.n=-1),p(o,s)):f.n=s:f.v=s);try{if(u=2,i){if(o||(a="next"),r=i[a]){if(!(r=r.call(i,s)))throw TypeError("iterator result is not an object");if(!r.done)return r;s=r.value,o<2&&(o=0)}else 1===o&&(r=i.return)&&r.call(i),o<2&&(s=TypeError("The iterator does not provide a '"+a+"' method"),o=1);i=t}else if((r=(l=f.n<0)?s:e.call(n,f))!==c)break}catch(r){i=t,o=1,s=r}finally{u=1}}return{value:r,done:l}}}(e,i,o),!0),d}var c={};function u(){}function d(){}function l(){}r=Object.getPrototypeOf;var f=[][i]?r(r([][i]())):(n(r={},i,(function(){return this})),r),p=l.prototype=u.prototype=Object.create(f);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,n(e,o,"GeneratorFunction")),e.prototype=Object.create(p),e}return d.prototype=l,n(p,"constructor",l),n(l,"constructor",d),d.displayName="GeneratorFunction",n(l,o,"GeneratorFunction"),n(p),n(p,o,"Generator"),n(p,i,(function(){return this})),n(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:s,m:m}})()}function n(e,t,r,a){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}n=function(e,t,r,a){if(t)i?i(e,t,{value:r,enumerable:!a,configurable:!a,writable:!a}):e[t]=r;else{function o(t,r){n(e,t,(function(e){return this._invoke(t,r,e)}))}o("next",0),o("throw",1),o("return",2)}},n(e,t,r,a)}function t(e,n,t,r,a,i,o){try{var s=e[i](o),c=s.value}catch(e){return void t(e)}s.done?n(c):Promise.resolve(c).then(r,a)}function r(e){return function(){var n=this,r=arguments;return new Promise((function(a,i){var o=e.apply(n,r);function s(e){t(o,a,i,s,c,"next",e)}function c(e){t(o,a,i,s,c,"throw",e)}s(void 0)}))}}System.register(["./index-legacy-C2Kr5I5h.js","./drawer-legacy-CIquJDQn.js","./__federation_expose_App-legacy-pwn7RX54.js","./x-legacy-yM9Jk2p5.js","./index-legacy-8Bh_oy_I.js"],(function(n,t){"use strict";var a,i,o,s,c,u,d,l,f,p,m,h,y,v,g,j,x,b,w,k,P,N,O,_,S,I,T,C,G,q,E,F;return{setters:[function(e){a=e.j},function(e){i=e.i,o=e.P},function(e){s=e.e,c=e.B,u=e.f,d=e.h,l=e.i,f=e.C,p=e.D,m=e.k,h=e.m,y=e.l,v=e.F,g=e.G,j=e.H,x=e.I,b=e.J,w=e.K,k=e.M,P=e.L,N=e.y,O=e.N,_=e.P,S=e.Q,I=e.O,T=e.S},function(e){C=e.c,G=e.e,q=e.n,E=e.z},function(e){F=e.r}],execute:function(){
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */
var t=C("CookingPot",[["path",{d:"M2 12h20",key:"9i4pu4"}],["path",{d:"M20 12v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-8",key:"u0tga0"}],["path",{d:"m4 8 16-4",key:"16g0ng"}],["path",{d:"m8.86 6.78-.45-1.81a2 2 0 0 1 1.45-2.43l1.94-.48a2 2 0 0 1 2.43 1.46l.45 1.8",key:"12cejc"}]]),K={hidden:{height:0,opacity:0,transition:{duration:.125,ease:G["emphasized-accelerate"]}},visible:{height:"auto",opacity:1,scale:1,transition:{duration:.35,ease:G["emphasized-decelerate"]}}},M=function(n){var o,N,O=n.orderId,_=n.className,S=s(),I=c({select:function(e){return e.pathname}}),T=F.useMemo((function(){return I.includes("cart")}),[I]),C=u().toast,G=d(),M=l(h(O)),B=f(O),D=p(O).clearCart,L=m({mutationFn:(N=r(e().m((function n(){var t;return e().w((function(e){for(;;)switch(e.n){case 0:if(null!==(t=M.data)&&void 0!==t&&t.assignedTo){e.n=1;break}return e.n=1,v(O,"Random User");case 1:return e.a(2,g(O,B))}}),n)}))),function(){return N.apply(this,arguments)}),onError:function(e){C({variant:"error",icon:t,message:e.message})},onSuccess:(o=r(e().m((function n(r){var a;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,G.invalidateQueries({queryKey:h(O).queryKey});case 1:G.setQueryData(y().queryKey,(function(e){return null==e?void 0:e.map((function(e){return e.id===O?r:e}))})),C({message:"Items successfully sent to the kitchen.",variant:"success",icon:t}),S({to:"/dine-in/spaces/$space",params:{space:(null===(a=M.data)||void 0===a?void 0:a.spaceId)||""}}),D();case 2:return e.a(2)}}),n)}))),function(e){return o.apply(this,arguments)})}),Q=F.useMemo((function(){return B.length>0?{num:x(B),price:j(B),status:"pending"}:M.data&&M.data.ordered.length>0?{num:x(M.data.ordered),price:j(M.data.ordered),status:"to-pay"}:null}),[M.data,B]);return a.jsx(b,{initial:!1,children:Q&&a.jsxs(w.div,{className:q("flex items-center justify-end gap-1.5 overflow-hidden px-3 *:py-3",_),onClick:function(e){var n=e.target;i(n)||S(T?{to:"/dine-in/$order",params:{order:O}}:{to:"/dine-in/$order/cart",params:{order:O},search:{status:null==Q?void 0:Q.status}})},variants:K,initial:"hidden",animate:"visible",exit:"hidden",inert:L.isPending,children:[a.jsxs("div",{className:"mr-auto text-sm",children:["pending"===Q.status?"Pending":"To pay",a.jsx(z,{num:Q.num}),a.jsx(k,{amount:Q.price,className:"block"})]}),!T&&a.jsx(E,{asChild:!0,children:a.jsx(P,{to:"/dine-in/$order/cart",params:{order:O},search:{status:Q.status},children:"Details"})}),"pending"===Q.status&&a.jsx(E,{variant:"accent",className:"min-w-35",onClick:function(){return L.mutate()},isLoading:L.isPending,children:"Send to Kitchen"}),"to-pay"===Q.status&&a.jsx(E,{variant:"positive",className:"min-w-35",onClick:function(){S({to:"/user-testing-complete"})},isLoading:L.isPending,children:"Payment"}),a.jsx($,{})]})})},z=function(e){var n=e.num,t=F.useRef(null),r=F.useRef(n);return F.useEffect((function(){var e=t.current;if(e&&r.current!==n){var a=n>r.current?"increase":"decrease";e.animate([{transform:"scale(1)"},{transform:"scale(".concat("increase"===a?1.2:.85,")")},{transform:"scale(1)"}],{duration:200,easing:"ease-out"}),r.current=n}}),[n]),a.jsx(N,{ref:t,size:"xs",className:"bg-foreground/15 ml-2 min-w-5 justify-center px-1 align-bottom font-medium tabular-nums",children:n})},$=function(){var e=O().setTheme;return F.useEffect((function(){return e("dark"),function(){return e("light")}}),[e]),null};try{M.displayName="OrderStatusBar",M.__docgenInfo={description:"",displayName:"OrderStatusBar",props:{orderId:{defaultValue:null,description:"",name:"orderId",required:!0,type:{name:"string"}},className:{defaultValue:null,description:"",name:"className",required:!1,type:{name:"string | undefined"}}}}}catch(B){}n("component",(function(){var e,n=_.useParams(),t=l(h(n.order));return S([{id:"table",label:(null===(e=t.data)||void 0===e?void 0:e.tableName)||"",loading:t.isPending,variant:"badge"}]),a.jsxs(o,{children:[a.jsx(I,{}),a.jsx(T,{id:"order-status-bar",children:a.jsx(M,{orderId:n.order,className:"-order-1"})})]})}))}}}))}();

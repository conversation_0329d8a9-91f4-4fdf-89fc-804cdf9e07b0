import{j as e}from"./index-C_VjMC7g.js";import{i as j,_ as i,$ as k,a0 as f,m as y,a1 as l,B as _,a2 as O,O as C,S as I,L as p}from"./__federation_expose_App-DYUBmReV.js";import{c as n,I as N,v as g,z as d,b as M,R as a,C as u}from"./x-B9YxCZbW.js";import{r as A}from"./index-D4lIrffr.js";import{P}from"./plus-BP0qVozT.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["path",{d:"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4",key:"1pf5j1"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M2 15h10",key:"jfw4w8"}],["path",{d:"m9 18 3-3-3-3",key:"112psh"}]],x=n("FileInput",R);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]],w=n("FileX",q);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F=[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]],L=n("FolderOpen",F);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]],z=n("Search",S),m=({orderId:c})=>{const s=j(y(c)),r=i("move-order",s.data),o=i("cancel-order",s.data),t=i("move-items",s.data);return e.jsxs(N,{children:[e.jsx(g,{asChild:!0,children:e.jsxs(d,{size:"sm",className:"gap-0.5",children:["Actions ",e.jsx(k,{className:"size-4"})]})}),s.isSuccess&&e.jsxs(M,{className:"[&>.lucide]:text-foreground-secondary w-50 leading-none [&>.lucide]:size-4 [&>button]:py-3",children:[e.jsxs(a,{onClick:t.open,disabled:t.disabled,children:[e.jsx(x,{}),"Move Items"]}),e.jsxs(a,{onClick:r.open,disabled:r.disabled,children:[e.jsx(x,{}),"Move order"]}),e.jsx(u,{}),e.jsxs(a,{disabled:!0,children:[e.jsx(P,{}),"Add Custom Item"]}),e.jsx(u,{}),e.jsxs(a,{disabled:!0,children:[e.jsx(f,{}),"Print Receipt"]}),e.jsxs(a,{disabled:!0,children:[e.jsx(L,{}),"Order Summary"]}),e.jsxs(a,{className:"text-negative",onClick:o.open,disabled:o.disabled,children:[e.jsx(w,{}),"Cancel Order"]})]})]})};try{m.displayName="OrderActionsDropdown",m.__docgenInfo={description:"",displayName:"OrderActionsDropdown",props:{orderId:{defaultValue:null,description:"",name:"orderId",required:!0,type:{name:"string"}}}}}catch(c){}const T=function(){var h;const{order:s,...r}=l.useParams(),o=j(y(s)),t=_({select:v=>v.pathname}),b=A.useMemo(()=>!Object.keys(r).length&&!t.includes("cart"),[r,t]);return e.jsxs(e.Fragment,{children:[e.jsx(O,{id:"order-actions",children:e.jsx(m,{orderId:s})}),e.jsx(C,{}),e.jsx(I,{id:"order-nav-bar",children:e.jsxs("div",{className:"flex items-center gap-2 p-3",children:[e.jsx(d,{asChild:!0,children:e.jsx(p,{to:"/dine-in/spaces/$space",params:{space:((h=o.data)==null?void 0:h.spaceId)||""},children:"Tables"})}),e.jsx(d,{asChild:!0,disabled:b,className:"ml-auto aria-[disabled]:pointer-events-none aria-[disabled]:opacity-60",children:e.jsx(p,{from:l.fullPath,to:".",children:"Menu List"})}),e.jsx(d,{asChild:!0,square:!0,children:e.jsx(p,{from:l.fullPath,to:"./search",children:e.jsx(z,{})})})]})})]})};export{T as component};

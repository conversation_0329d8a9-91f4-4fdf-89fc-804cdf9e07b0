!function(){var e=["order"];System.register(["./index-legacy-C2Kr5I5h.js","./__federation_expose_App-legacy-pwn7RX54.js","./x-legacy-yM9Jk2p5.js","./index-legacy-8Bh_oy_I.js","./plus-legacy-khTMZduY.js"],(function(r,s){"use strict";var a,n,d,i,t,l,c,o,u,h,p,j,x,m,f,y,b,v,g,k,O;return{setters:[function(e){a=e.j},function(e){n=e.i,d=e._,i=e.$,t=e.a0,l=e.m,c=e.a1,o=e.B,u=e.a2,h=e.O,p=e.S,j=e.L},function(e){x=e.c,m=e.I,f=e.v,y=e.z,b=e.b,v=e.R,g=e.C},function(e){k=e.r},function(e){O=e.P}],execute:function(){
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */
var s=x("FileInput",[["path",{d:"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4",key:"1pf5j1"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M2 15h10",key:"jfw4w8"}],["path",{d:"m9 18 3-3-3-3",key:"112psh"}]]),I=x("FileX",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]]),C=x("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]),M=x("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),q=function(e){var r=e.orderId,c=n(l(r)),o=d("move-order",c.data),u=d("cancel-order",c.data),h=d("move-items",c.data);return a.jsxs(m,{children:[a.jsx(f,{asChild:!0,children:a.jsxs(y,{size:"sm",className:"gap-0.5",children:["Actions ",a.jsx(i,{className:"size-4"})]})}),c.isSuccess&&a.jsxs(b,{className:"[&>.lucide]:text-foreground-secondary w-50 leading-none [&>.lucide]:size-4 [&>button]:py-3",children:[a.jsxs(v,{onClick:h.open,disabled:h.disabled,children:[a.jsx(s,{}),"Move Items"]}),a.jsxs(v,{onClick:o.open,disabled:o.disabled,children:[a.jsx(s,{}),"Move order"]}),a.jsx(g,{}),a.jsxs(v,{disabled:!0,children:[a.jsx(O,{}),"Add Custom Item"]}),a.jsx(g,{}),a.jsxs(v,{disabled:!0,children:[a.jsx(t,{}),"Print Receipt"]}),a.jsxs(v,{disabled:!0,children:[a.jsx(C,{}),"Order Summary"]}),a.jsxs(v,{className:"text-negative",onClick:u.open,disabled:u.disabled,children:[a.jsx(I,{}),"Cancel Order"]})]})]})};try{q.displayName="OrderActionsDropdown",q.__docgenInfo={description:"",displayName:"OrderActionsDropdown",props:{orderId:{defaultValue:null,description:"",name:"orderId",required:!0,type:{name:"string"}}}}}catch(w){}r("component",(function(){var r,s=c.useParams(),d=s.order,i=function(e,r){if(null==e)return{};var s,a,n=function(e,r){if(null==e)return{};var s={};for(var a in e)if({}.hasOwnProperty.call(e,a)){if(-1!==r.indexOf(a))continue;s[a]=e[a]}return s}(e,r);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(e);for(a=0;a<d.length;a++)s=d[a],-1===r.indexOf(s)&&{}.propertyIsEnumerable.call(e,s)&&(n[s]=e[s])}return n}(s,e),t=n(l(d)),x=o({select:function(e){return e.pathname}}),m=k.useMemo((function(){return!Object.keys(i).length&&!x.includes("cart")}),[i,x]);return a.jsxs(a.Fragment,{children:[a.jsx(u,{id:"order-actions",children:a.jsx(q,{orderId:d})}),a.jsx(h,{}),a.jsx(p,{id:"order-nav-bar",children:a.jsxs("div",{className:"flex items-center gap-2 p-3",children:[a.jsx(y,{asChild:!0,children:a.jsx(j,{to:"/dine-in/spaces/$space",params:{space:(null===(r=t.data)||void 0===r?void 0:r.spaceId)||""},children:"Tables"})}),a.jsx(y,{asChild:!0,disabled:m,className:"ml-auto aria-[disabled]:pointer-events-none aria-[disabled]:opacity-60",children:a.jsx(j,{from:c.fullPath,to:".",children:"Menu List"})}),a.jsx(y,{asChild:!0,square:!0,children:a.jsx(j,{from:c.fullPath,to:"./search",children:a.jsx(M,{})})})]})})]})}))}}}))}();

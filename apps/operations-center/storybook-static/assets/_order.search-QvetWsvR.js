import{j as e}from"./index-C_VjMC7g.js";import{c as f,z as c,k as j,X as g}from"./x-B9YxCZbW.js";import{r as y}from"./index-D4lIrffr.js";import{U as b,V as l,W as N,X as k,i as v,Q as C,S,L as w,Y as R}from"./__federation_expose_App-DYUBmReV.js";import{M as z,a as M}from"./menu-product-card-BylCxK_t.js";function P(){return b({select:n=>n.location.state.__TSR_index!==0})}/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B=[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]],F=f("RefreshCcw",B),D=function(){const i=l.useParams(),d=N(),u=P(),[r,o]=y.useState(""),a=k(r,64),{data:t,isSuccess:h,isError:m,isPlaceholderData:x,refetch:p}=v({queryKey:["products",a],staleTime:1/0,throwOnError:!1,placeholderData:s=>s||[],queryFn:()=>a.length===0?[]:R(a)});return C([{id:"search",label:"Search"}]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"relative h-full w-full",children:[m&&e.jsxs("div",{className:"flex h-full w-full flex-col items-center justify-center gap-4",children:[e.jsx("p",{className:"text-foreground-secondary text-sm",children:"Failed to fetch products"}),e.jsxs(c,{variant:"secondary",size:"sm",onClick:()=>p(),children:[e.jsx(F,{className:"size-3"}),"Retry"]})]}),h&&e.jsxs(e.Fragment,{children:[t.length===0&&e.jsxs("div",{className:"flex h-full w-full flex-col items-center justify-center gap-4",children:[e.jsx("img",{src:"/images/search-illustration.png",alt:"Search empty",className:"size-26"}),e.jsx("p",{className:"text-foreground-secondary text-sm",children:"Search, tap, nailed it!"})]}),t.length>0&&e.jsx("div",{className:"p-2.5 pb-12",children:e.jsx(z,{children:t.map(s=>e.jsx(M,{product:s,orderId:i.order},s.id))})}),x&&e.jsx("div",{className:"bg-background-low/66 absolute inset-0 z-3 animate-pulse"})]})]}),e.jsx(S,{id:"order-nav-bar",children:e.jsxs("div",{className:"flex items-center gap-2 p-3",children:[e.jsxs("div",{className:"relative grow",children:[e.jsx(j,{autoFocus:!0,id:"order-search-input",placeholder:"Name, code...",value:r,onChange:s=>o(s.target.value),className:"transition-none"}),r&&e.jsx("button",{className:"bg-foreground/75 absolute top-1/2 right-3 grid size-4 -translate-y-1/2 place-content-center rounded-full",onClick:()=>o(""),children:e.jsx(g,{className:"text-background size-2.5 stroke-3"})})]}),u?e.jsx(c,{onClick:()=>d.history.back(),children:"Cancel"}):e.jsx(c,{asChild:!0,children:e.jsx(w,{from:l.fullPath,to:"..",children:"Cancel"})})]})})]})};export{D as component};

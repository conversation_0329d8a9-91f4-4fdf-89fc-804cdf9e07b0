!function(){function e(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,r){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var n,a,s,l,c=[],i=!0,o=!1;try{if(s=(t=t.call(e)).next,0===r){if(Object(t)!==t)return;i=!1}else for(;!(i=(n=s.call(t)).done)&&(c.push(n.value),c.length!==r);i=!0);}catch(e){o=!0,a=e}finally{try{if(!i&&null!=t.return&&(l=t.return(),Object(l)!==l))return}finally{if(o)throw a}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}System.register(["./index-legacy-C2Kr5I5h.js","./x-legacy-yM9Jk2p5.js","./index-legacy-8Bh_oy_I.js","./__federation_expose_App-legacy-pwn7RX54.js","./menu-product-card-legacy-CIxhdtnP.js"],(function(r,t){"use strict";var n,a,s,l,c,i,o,u,d,f,h,m,x,y,p,g,j;return{setters:[function(e){n=e.j},function(e){a=e.c,s=e.z,l=e.k,c=e.X},function(e){i=e.r},function(e){o=e.U,u=e.V,d=e.W,f=e.X,h=e.i,m=e.Q,x=e.S,y=e.L,p=e.Y},function(e){g=e.M,j=e.a}],execute:function(){
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */
var t=a("RefreshCcw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]]);r("component",(function(){var r=u.useParams(),a=d(),v=o({select:function(e){return 0!==e.location.state.__TSR_index}}),b=e(i.useState(""),2),N=b[0],S=b[1],k=f(N,64),w=h({queryKey:["products",k],staleTime:1/0,throwOnError:!1,placeholderData:function(e){return e||[]},queryFn:function(){return 0===k.length?[]:p(k)}}),C=w.data,z=w.isSuccess,A=w.isError,_=w.isPlaceholderData,M=w.refetch;return m([{id:"search",label:"Search"}]),n.jsxs(n.Fragment,{children:[n.jsxs("div",{className:"relative h-full w-full",children:[A&&n.jsxs("div",{className:"flex h-full w-full flex-col items-center justify-center gap-4",children:[n.jsx("p",{className:"text-foreground-secondary text-sm",children:"Failed to fetch products"}),n.jsxs(s,{variant:"secondary",size:"sm",onClick:function(){return M()},children:[n.jsx(t,{className:"size-3"}),"Retry"]})]}),z&&n.jsxs(n.Fragment,{children:[0===C.length&&n.jsxs("div",{className:"flex h-full w-full flex-col items-center justify-center gap-4",children:[n.jsx("img",{src:"/images/search-illustration.png",alt:"Search empty",className:"size-26"}),n.jsx("p",{className:"text-foreground-secondary text-sm",children:"Search, tap, nailed it!"})]}),C.length>0&&n.jsx("div",{className:"p-2.5 pb-12",children:n.jsx(g,{children:C.map((function(e){return n.jsx(j,{product:e,orderId:r.order},e.id)}))})}),_&&n.jsx("div",{className:"bg-background-low/66 absolute inset-0 z-3 animate-pulse"})]})]}),n.jsx(x,{id:"order-nav-bar",children:n.jsxs("div",{className:"flex items-center gap-2 p-3",children:[n.jsxs("div",{className:"relative grow",children:[n.jsx(l,{autoFocus:!0,id:"order-search-input",placeholder:"Name, code...",value:N,onChange:function(e){return S(e.target.value)},className:"transition-none"}),N&&n.jsx("button",{className:"bg-foreground/75 absolute top-1/2 right-3 grid size-4 -translate-y-1/2 place-content-center rounded-full",onClick:function(){return S("")},children:n.jsx(c,{className:"text-background size-2.5 stroke-3"})})]}),v?n.jsx(s,{onClick:function(){return a.history.back()},children:"Cancel"}):n.jsx(s,{asChild:!0,children:n.jsx(y,{from:u.fullPath,to:"..",children:"Cancel"})})]})})]})}))}}}))}();

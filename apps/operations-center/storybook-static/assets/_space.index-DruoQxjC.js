import{j as a}from"./index-C_VjMC7g.js";import{r as z}from"./index-D4lIrffr.js";import{u as h,d as u,e as T,f as O,g as m,h as q,i as p,j as A,k as I,l as g,m as N,T as P,p as Q,n as S,A as k,o as B}from"./__federation_expose_App-DYUBmReV.js";import{n as E}from"./x-B9YxCZbW.js";const F=function(){var l;const r=h("tables"),y=u.useParams(),f=T(),{toast:C}=O(),x=m(s=>s.carts),b=m(s=>s.createCart),n=q(),c=p(A(y.space)),{data:t}=p(g()),j=z.useMemo(()=>(t==null?void 0:t.reduce((s,e)=>({...s,[e.tableId]:e}),{}))||{},[t]),i=I({mutationFn:async s=>Q(s),onError:s=>{C({icon:P,message:s.message,variant:"error"})},onSuccess:async s=>{f({from:u.fullPath,to:"../../$order",params:{order:s.id}}),n.invalidateQueries({queryKey:g().queryKey}),b(s.id),n.setQueryData(N(s.id).queryKey,s)}});return a.jsxs("ul",{inert:i.isPending,className:E("grid grid-cols-[repeat(auto-fill,minmax(var(--col-size),1fr))] gap-1.5 p-2.5 pb-8",r.size==="sm"&&"[--col-size:--spacing(17)]",r.size==="md"&&"[--col-size:--spacing(29)]",r.size==="lg"&&"[--col-size:--spacing(43)]"),children:[c.isPending&&Array.from({length:5}).map((s,e)=>a.jsx("li",{children:a.jsx(S,{className:"w-full pb-[100%]"})},e)),(l=c.data)==null?void 0:l.map(s=>{var d;const e=j[s.id],v=x[e==null?void 0:e.id],o=((d=i.variables)==null?void 0:d.id)===s.id&&!i.error;return a.jsx("li",{className:"*:block *:w-full",children:e&&v&&!o?a.jsx(k,{table:s,order:e}):a.jsx("button",{"aria-label":"Create Order","aria-busy":o||void 0,disabled:o,onClick:()=>i.mutate(s),children:a.jsx(B,{name:s.code,size:r.size,isLoading:o})})},s.id)})]})};export{F as component};

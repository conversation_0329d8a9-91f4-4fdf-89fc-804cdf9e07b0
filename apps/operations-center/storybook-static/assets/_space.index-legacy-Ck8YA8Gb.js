!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,n,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",u=o.toStringTag||"@@toStringTag";function c(e,o,i,u){var c=o&&o.prototype instanceof f?o:f,s=Object.create(c.prototype);return r(s,"_invoke",function(e,r,o){var i,u,c,f=0,s=o||[],l=!1,p={p:0,n:0,v:t,a:y,f:y.bind(t,4),d:function(e,r){return i=e,u=0,c=t,p.n=r,a}};function y(e,r){for(u=e,c=r,n=0;!l&&f&&!o&&n<s.length;n++){var o,i=s[n],y=p.p,d=i[2];e>3?(o=d===r)&&(c=i[(u=i[4])?5:(u=3,3)],i[4]=i[5]=t):i[0]<=y&&((o=e<2&&y<i[1])?(u=0,p.v=r,p.n=i[1]):y<d&&(o=e<3||i[0]>r||r>d)&&(i[4]=e,i[5]=r,p.n=d,u=0))}if(o||e>1)return a;throw l=!0,r}return function(o,s,d){if(f>1)throw TypeError("Generator is already running");for(l&&1===s&&y(s,d),u=s,c=d;(n=u<2?t:c)||!l;){i||(u?u<3?(u>1&&(p.n=-1),y(u,c)):p.n=c:p.v=c);try{if(f=2,i){if(u||(o="next"),n=i[o]){if(!(n=n.call(i,c)))throw TypeError("iterator result is not an object");if(!n.done)return n;c=n.value,u<2&&(u=0)}else 1===u&&(n=i.return)&&n.call(i),u<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),u=1);i=t}else if((n=(l=p.n<0)?c:e.call(r,p))!==a)break}catch(n){i=t,u=1,c=n}finally{f=1}}return{value:n,done:l}}}(e,i,u),!0),s}var a={};function f(){}function s(){}function l(){}n=Object.getPrototypeOf;var p=[][i]?n(n([][i]())):(r(n={},i,(function(){return this})),n),y=l.prototype=f.prototype=Object.create(p);function d(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,l):(t.__proto__=l,r(t,u,"GeneratorFunction")),t.prototype=Object.create(y),t}return s.prototype=l,r(y,"constructor",l),r(l,"constructor",s),s.displayName="GeneratorFunction",r(l,u,"GeneratorFunction"),r(y),r(y,u,"Generator"),r(y,i,(function(){return this})),r(y,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:c,m:d}})()}function r(t,e,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}r=function(t,e,n,o){if(e)i?i(t,e,{value:n,enumerable:!o,configurable:!o,writable:!o}):t[e]=n;else{function u(e,n){r(t,e,(function(t){return this._invoke(e,n,t)}))}u("next",0),u("throw",1),u("return",2)}},r(t,e,n,o)}function n(t,e,r,n,o,i,u){try{var c=t[i](u),a=c.value}catch(t){return void r(t)}c.done?e(a):Promise.resolve(a).then(n,o)}function o(t){return function(){var e=this,r=arguments;return new Promise((function(o,i){var u=t.apply(e,r);function c(t){n(u,o,i,c,a,"next",t)}function a(t){n(u,o,i,c,a,"throw",t)}c(void 0)}))}}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach((function(e){c(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function c(e,r,n){return(r=function(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,r||"default");if("object"!=t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==t(r)?r:r+""}(r))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}System.register(["./index-legacy-C2Kr5I5h.js","./index-legacy-8Bh_oy_I.js","./__federation_expose_App-legacy-pwn7RX54.js","./x-legacy-yM9Jk2p5.js"],(function(t,r){"use strict";var n,i,a,f,s,l,p,y,d,b,m,v,g,j,h,O,w,P,x;return{setters:[function(t){n=t.j},function(t){i=t.r},function(t){a=t.u,f=t.d,s=t.e,l=t.f,p=t.g,y=t.h,d=t.i,b=t.j,m=t.k,v=t.l,g=t.m,j=t.T,h=t.p,O=t.n,w=t.A,P=t.o},function(t){x=t.n}],execute:function(){t("component",(function(){var t,r,S,_=a("tables"),z=f.useParams(),T=s(),k=l().toast,E=p((function(t){return t.carts})),G=p((function(t){return t.createCart})),D=y(),N=d(b(z.space)),q=d(v()).data,F=i.useMemo((function(){return(null==q?void 0:q.reduce((function(t,e){return u(u({},t),{},c({},e.tableId,e))}),{}))||{}}),[q]),A=m({mutationFn:(S=o(e().m((function t(r){return e().w((function(t){for(;;)if(0===t.n)return t.a(2,h(r))}),t)}))),function(t){return S.apply(this,arguments)}),onError:function(t){k({icon:j,message:t.message,variant:"error"})},onSuccess:(r=o(e().m((function t(r){return e().w((function(t){for(;;)switch(t.n){case 0:T({from:f.fullPath,to:"../../$order",params:{order:r.id}}),D.invalidateQueries({queryKey:v().queryKey}),G(r.id),D.setQueryData(g(r.id).queryKey,r);case 1:return t.a(2)}}),t)}))),function(t){return r.apply(this,arguments)})});return n.jsxs("ul",{inert:A.isPending,className:x("grid grid-cols-[repeat(auto-fill,minmax(var(--col-size),1fr))] gap-1.5 p-2.5 pb-8","sm"===_.size&&"[--col-size:--spacing(17)]","md"===_.size&&"[--col-size:--spacing(29)]","lg"===_.size&&"[--col-size:--spacing(43)]"),children:[N.isPending&&Array.from({length:5}).map((function(t,e){return n.jsx("li",{children:n.jsx(O,{className:"w-full pb-[100%]"})},e)})),null===(t=N.data)||void 0===t?void 0:t.map((function(t){var e,r=F[t.id],o=E[null==r?void 0:r.id],i=(null===(e=A.variables)||void 0===e?void 0:e.id)===t.id&&!A.error;return n.jsx("li",{className:"*:block *:w-full",children:r&&o&&!i?n.jsx(w,{table:t,order:r}):n.jsx("button",{"aria-label":"Create Order","aria-busy":i||void 0,disabled:i,onClick:function(){return A.mutate(t)},children:n.jsx(P,{name:t.code,size:_.size,isLoading:i})})},t.id)}))]})}))}}}))}();

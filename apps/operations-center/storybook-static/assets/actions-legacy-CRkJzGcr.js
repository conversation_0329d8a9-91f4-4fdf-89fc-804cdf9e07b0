!function(){function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},r(e)}var e=["children","className"];function t(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),t.push.apply(t,n)}return t}function n(r){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?t(Object(n),!0).forEach((function(e){o(r,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):t(Object(n)).forEach((function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(n,e))}))}return r}function o(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}System.register(["./index-legacy-C2Kr5I5h.js","./x-legacy-yM9Jk2p5.js","./index-legacy-8Bh_oy_I.js"],(function(r,t){"use strict";var o,i;return{setters:[function(r){o=r.j},function(r){i=r.n},null],execute:function(){var t=r("A",(function(r){var t=r.children,c=r.className,u=function(r,e){if(null==r)return{};var t,n,o=function(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(-1!==e.indexOf(n))continue;t[n]=r[n]}return t}(r,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(r);for(n=0;n<i.length;n++)t=i[n],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(r,t)&&(o[t]=r[t])}return o}(r,e);return o.jsx("footer",n(n({className:i("border-border bg-background-highlight sticky bottom-0 z-10 mt-auto flex gap-2 border-t",c)},u),{},{children:t}))}));try{t.displayName="Actions",t.__docgenInfo={description:"",displayName:"Actions",props:{}}}catch(c){}}}}))}();

import{j as e}from"./index-C_VjMC7g.js";import{n as _,a as J,G as he,d as W,J as Z,H as ee,z as v,W as ge,Z as pe,$ as xe,f as fe,t as je,g as ye,_ as Ce,X as Se,e as X}from"./x-B9YxCZbW.js";import{r as h}from"./index-D4lIrffr.js";import{K as O,J as we,D as te,i as ne,aa as be,ab as A,ac as se,ad as S,ae,af as $,ag as ie,M as k,ah as re,s as ve,f as oe,ai as Oe,aj as ce,h as _e,k as Ie,ak as Ne,T as Me,m as le,al as H,e as Te,C as Ee,Q as qe,S as De,n as f}from"./__federation_expose_App-DYUBmReV.js";import{u as Pe,N as ke}from"./drawer-efJps8Fr.js";const de=h.createContext(null),Ue=()=>{const n=h.use(de);if(!n)throw new Error("SegmentedControlItem must be used within a SegmentedControl");return n},U=({value:n,onChange:t,className:a,children:r,...d})=>e.jsx(de,{value:{value:n,onChange:t},children:e.jsx("div",{className:_("bg-background mx-auto flex w-fit gap-2 rounded-3xl p-1",a),...d,children:r})}),T=({value:n,className:t,children:a,...r})=>{const{value:d,onChange:c}=Ue(),m=n===d;return e.jsxs("button",{type:"button",className:_("relative flex h-10 items-center justify-center rounded-xl px-4 text-xs",t),onClick:()=>c(n),...r,children:[m&&e.jsx(O.span,{"data-tab-indicator":"true",layoutId:"tab-indicator","aria-hidden":"true",className:"bg-foreground/12 absolute inset-0 z-0 rounded-xl",transition:{type:"spring",duration:.25,bounce:.1}}),e.jsx("span",{className:"relative z-10",children:a})]})};try{U.displayName="SegmentedControl",U.__docgenInfo={description:"",displayName:"SegmentedControl",props:{value:{defaultValue:null,description:"",name:"value",required:!0,type:{name:"string"}},onChange:{defaultValue:null,description:"",name:"onChange",required:!1,type:{name:"((value: string) => void) & FormEventHandler<HTMLDivElement>"}}}}}catch(n){}try{T.displayName="SegmentedControlItem",T.__docgenInfo={description:"",displayName:"SegmentedControlItem",props:{value:{defaultValue:null,description:"",name:"value",required:!0,type:{name:"string"}}}}}catch(n){}const Re="Left",Le="Right",Ve="Up",ze="Down",C={delta:10,preventScrollOnSwipe:!1,rotationAngle:0,trackMouse:!1,trackTouch:!0,swipeDuration:1/0,touchEventOptions:{passive:!0}},R={first:!0,initial:[0,0],start:0,swiping:!1,xy:[0,0]},Y="mousemove",B="mouseup",Ae="touchend",$e="touchmove",Fe="touchstart";function Qe(n,t,a,r){return n>t?a>0?Le:Re:r>0?ze:Ve}function K(n,t){if(t===0)return n;const a=Math.PI/180*t,r=n[0]*Math.cos(a)+n[1]*Math.sin(a),d=n[1]*Math.cos(a)-n[0]*Math.sin(a);return[r,d]}function Xe(n,t){const a=s=>{const i="touches"in s;i&&s.touches.length>1||n((o,u)=>{u.trackMouse&&!i&&(document.addEventListener(Y,r),document.addEventListener(B,m));const{clientX:p,clientY:y}=i?s.touches[0]:s,x=K([p,y],u.rotationAngle);return u.onTouchStartOrOnMouseDown&&u.onTouchStartOrOnMouseDown({event:s}),Object.assign(Object.assign(Object.assign({},o),R),{initial:x.slice(),xy:x,start:s.timeStamp||0})})},r=s=>{n((i,o)=>{const u="touches"in s;if(u&&s.touches.length>1)return i;if(s.timeStamp-i.start>o.swipeDuration)return i.swiping?Object.assign(Object.assign({},i),{swiping:!1}):i;const{clientX:p,clientY:y}=u?s.touches[0]:s,[x,q]=K([p,y],o.rotationAngle),I=x-i.xy[0],N=q-i.xy[1],w=Math.abs(I),b=Math.abs(N),D=(s.timeStamp||0)-i.start,ue=Math.sqrt(w*w+b*b)/(D||1),me=[I/(D||1),N/(D||1)],P=Qe(w,b,I,N),F=typeof o.delta=="number"?o.delta:o.delta[P.toLowerCase()]||C.delta;if(w<F&&b<F&&!i.swiping)return i;const M={absX:w,absY:b,deltaX:I,deltaY:N,dir:P,event:s,first:i.first,initial:i.initial,velocity:ue,vxvy:me};M.first&&o.onSwipeStart&&o.onSwipeStart(M),o.onSwiping&&o.onSwiping(M);let Q=!1;return(o.onSwiping||o.onSwiped||o["onSwiped".concat(P)])&&(Q=!0),Q&&o.preventScrollOnSwipe&&o.trackTouch&&s.cancelable&&s.preventDefault(),Object.assign(Object.assign({},i),{first:!1,eventData:M,swiping:!0})})},d=s=>{n((i,o)=>{let u;if(i.swiping&&i.eventData){if(s.timeStamp-i.start<o.swipeDuration){u=Object.assign(Object.assign({},i.eventData),{event:s}),o.onSwiped&&o.onSwiped(u);const p=o["onSwiped".concat(u.dir)];p&&p(u)}}else o.onTap&&o.onTap({event:s});return o.onTouchEndOrOnMouseUp&&o.onTouchEndOrOnMouseUp({event:s}),Object.assign(Object.assign(Object.assign({},i),R),{eventData:u})})},c=()=>{document.removeEventListener(Y,r),document.removeEventListener(B,m)},m=s=>{c(),d(s)},l=(s,i)=>{let o=()=>{};if(s&&s.addEventListener){const u=Object.assign(Object.assign({},C.touchEventOptions),i.touchEventOptions),p=[[Fe,a,u],[$e,r,Object.assign(Object.assign({},u),i.preventScrollOnSwipe?{passive:!1}:{})],[Ae,d,u]];p.forEach(([y,x,q])=>s.addEventListener(y,x,q)),o=()=>p.forEach(([y,x])=>s.removeEventListener(y,x))}return o},j={ref:s=>{s!==null&&n((i,o)=>{if(i.el===s)return i;const u={};return i.el&&i.el!==s&&i.cleanUpTouch&&(i.cleanUpTouch(),u.cleanUpTouch=void 0),o.trackTouch&&s&&(u.cleanUpTouch=l(s,o)),Object.assign(Object.assign(Object.assign({},i),{el:s}),u)})}};return t.trackMouse&&(j.onMouseDown=a),[j,l]}function He(n,t,a,r){return!t.trackTouch||!n.el?(n.cleanUpTouch&&n.cleanUpTouch(),Object.assign(Object.assign({},n),{cleanUpTouch:void 0})):n.cleanUpTouch?t.preventScrollOnSwipe!==a.preventScrollOnSwipe||t.touchEventOptions.passive!==a.touchEventOptions.passive?(n.cleanUpTouch(),Object.assign(Object.assign({},n),{cleanUpTouch:r(n.el,t)})):n:Object.assign(Object.assign({},n),{cleanUpTouch:r(n.el,t)})}function Ye(n){const{trackMouse:t}=n,a=h.useRef(Object.assign({},R)),r=h.useRef(Object.assign({},C)),d=h.useRef(Object.assign({},r.current));d.current=Object.assign({},r.current),r.current=Object.assign(Object.assign({},C),n);let c;for(c in C)r.current[c]===void 0&&(r.current[c]=C[c]);const[m,l]=h.useMemo(()=>Xe(g=>a.current=g(a.current,r.current),{trackMouse:t}),[t]);return a.current=He(a.current,r.current,d.current,l),m}function L({children:n,onSwipe:t,className:a,...r}){const{ref:d,onMouseDown:c}=Ye({swipeDuration:500,preventScrollOnSwipe:!0,trackMouse:!0,onSwipedLeft:()=>t("left"),onSwipedRight:()=>t("right")});return e.jsx("div",{ref:d,onMouseDown:c,className:_("relative grid grid-cols-1 grid-rows-1 *:col-start-1 *:row-start-1",a),...r,children:e.jsx(we,{mode:"popLayout",initial:!1,children:n})})}const E=({children:n,direction:t="left"})=>e.jsx(O.div,{initial:{x:t==="left"?"-100%":"100%",opacity:1},animate:{x:0,opacity:1},exit:{x:t==="left"?"-100%":"100%",opacity:0},transition:{duration:.175,ease:"easeOut"},children:n});try{L.displayName="Swipeable",L.__docgenInfo={description:"",displayName:"Swipeable",props:{onSwipe:{defaultValue:null,description:"",name:"onSwipe",required:!0,type:{name:'(direction: "left" | "right") => void'}}}}}catch(n){}try{E.displayName="SwipeableItem",E.__docgenInfo={description:"",displayName:"SwipeableItem",props:{direction:{defaultValue:{value:"left"},description:"",name:"direction",required:!1,type:{name:"enum",value:[{value:'"left"'},{value:'"right"'}]}}}}}catch(n){}const V=({orderId:n,item:t})=>{const{updateItem:a,removeItem:r}=te(n),{open:d}=Pe(),c=ne(ve()),m=h.useMemo(()=>{var l;return be(t,(l=c.data.items)!=null?l:[])},[t,c.data]);return e.jsx(A,{children:e.jsxs(se,{children:[e.jsxs(S,{children:[e.jsx(ae,{children:m.name}),e.jsxs(J,{min:0,quantity:t.quantity,onChange:l=>{a(t.id,{...t,quantity:l})},children:[t.quantity===1?e.jsx(he,{onClick:()=>r(t.id),size:"md"}):e.jsx(W,{size:"md"}),e.jsx(Z,{}),e.jsx(ee,{})]})]}),e.jsx($,{children:e.jsx(ie,{item:t})}),e.jsxs(S,{children:[e.jsx(v,{size:"sm",onClick:()=>d({initialCartItem:t,product:m,onSubmit:async l=>a(l.id,l)}),children:"Edit"}),e.jsx(k,{amount:re(t)})]})]})})};try{V.displayName="PendingCartItem",V.__docgenInfo={description:"",displayName:"PendingCartItem",props:{orderId:{defaultValue:null,description:"",name:"orderId",required:!0,type:{name:"string"}},item:{defaultValue:null,description:"",name:"item",required:!0,type:{name:"CartItem"}}}}}catch(n){}const Be=["Mistake","Customer changed their mind"],z=({orderId:n,item:t})=>{const{addItem:a}=te(n),{toast:r}=oe(),d=h.useMemo(()=>"".concat(t.quantity," ").concat(t.quantity===1?"item":"items"),[t.quantity]),c=h.useMemo(()=>re(t),[t]),m=()=>{a({...t,id:Oe(),quantity:1}),r({message:"1 item added to pending",variant:"success",icon:ce})};return e.jsx(A,{className:_(t.isCancelled&&"line-through"),children:e.jsxs(se,{children:[e.jsxs(S,{children:[e.jsx(ae,{children:t.name}),t.isCancelled?e.jsxs("div",{children:[d+" · ",e.jsx(k,{amount:c})]}):e.jsxs("div",{className:"flex items-center gap-2.5 text-sm",children:[e.jsxs("span",{children:["x",t.quantity]}),e.jsx(v,{onClick:m,children:"Reorder"})]})]}),!t.isCancelled&&e.jsxs(e.Fragment,{children:[e.jsx($,{children:e.jsx(ie,{item:t})}),e.jsxs(S,{children:[e.jsx(Ke,{orderId:n,item:t}),e.jsxs("div",{children:[d+" · ",e.jsx(k,{amount:c})]})]})]})]})})},Ke=({orderId:n,item:t})=>{const[a,r]=h.useState(!1),{toast:d}=oe(),[c,m]=h.useState(1),[l,g]=h.useState([]),j=_e(),s=Ie({mutationFn:()=>Ne(n,t.id,c,l),onSettled:async(i,o)=>{o?d({message:o.message,variant:"error",icon:Me,delay:200}):(await j.invalidateQueries({queryKey:le(n).queryKey}),d({message:"".concat(c," item").concat(c===1?"":"s"," cancelled"),variant:"success",icon:ce,delay:200}),r(!1))}});return e.jsxs(ge,{open:a,onOpenChange:i=>{s.isPending||(r(i),i||(m(1),g([])))},children:[e.jsx(pe,{asChild:!0,children:e.jsx(v,{size:"sm",variant:"negative",children:"Cancel"})}),e.jsx(xe,{inert:s.isPending,persistExitAnimation:!0,children:t&&e.jsxs(e.Fragment,{children:[e.jsx(fe,{children:e.jsxs(je,{children:['Cancel "',t.name,'"']})}),e.jsxs("div",{className:"bg-background-high space-y-2.5 p-2.5 text-sm",children:[e.jsx("p",{children:"Notes"}),e.jsx(ke,{customNotePlaceholder:"Type a custom notes...",presets:Be,onChange:g})]}),e.jsxs(ye,{children:[e.jsxs(J,{quantity:c,min:1,max:t.quantity,onChange:m,children:[e.jsx(W,{}),e.jsx(Z,{}),e.jsx(ee,{})]}),e.jsxs(v,{variant:"negative",className:"grow",onClick:()=>s.mutate(),isLoading:s.isPending,children:["Cancel ",c," item",c===1?"":"s"]}),e.jsx(Ce,{asChild:!0,children:e.jsx(v,{square:!0,children:e.jsx(Se,{className:"size-4"})})})]})]})})]})};try{z.displayName="OrderedCartItem",z.__docgenInfo={description:"",displayName:"OrderedCartItem",props:{orderId:{defaultValue:null,description:"",name:"orderId",required:!0,type:{name:"string"}},item:{defaultValue:null,description:"",name:"item",required:!0,type:{name:"OrderItem"}}}}}catch(n){}function Ge(){return e.jsxs(A,{children:[e.jsxs(S,{children:[e.jsxs("div",{className:"mt-2 w-full space-y-1",children:[e.jsx(f,{className:"h-3 w-52"}),e.jsx(f,{className:"h-3 w-48"})]}),e.jsx(f,{className:"h-10 w-28"})]}),e.jsxs($,{children:[e.jsx(f,{className:"h-3 w-4/4"}),e.jsx(f,{className:"h-3 w-2/3"}),e.jsx(f,{className:"h-3 w-3/4"})]}),e.jsxs(S,{children:[e.jsx(f,{className:"h-5 w-12"}),e.jsx(f,{className:"h-3 w-10"})]})]})}const Je={hidden:{y:"120%",opacity:.8,scale:.95,transition:{duration:.15,ease:X["emphasized-accelerate"]}},visible:{y:0,opacity:1,scale:1,transition:{duration:.3,ease:X["emphasized-decelerate"]}}},We=O.create(U);function Ze({value:n,onChange:t,children:a}){return e.jsx(We,{value:n,onChange:t,variants:Je,initial:"hidden",animate:"visible",exit:"hidden",children:a})}function G({className:n,children:t,...a}){return e.jsx("p",{className:_("text-foreground-secondary py-12 text-center text-xs",n),...a,children:t})}const it=function(){const{order:t}=H.useParams(),{status:a}=H.useSearch(),r=Te(),d=Ee(t),c=ne(le(t));qe([{id:"cart",label:"Details"}]);const m=h.useMemo(()=>{var g,j;return[...(j=(g=c.data)==null?void 0:g.ordered)!=null?j:[]].sort((s,i)=>s.isCancelled?1:i.isCancelled?-1:0)},[c.data]);return h.useEffect(()=>{var l;d.length===0&&((l=c.data)==null?void 0:l.ordered.length)===0&&r({to:"/dine-in/$order",params:{order:t}})},[d,c.data,r,t]),e.jsxs(e.Fragment,{children:[e.jsxs(L,{className:"bg-background-high min-h-full w-full overflow-hidden text-sm",onSwipe:l=>{r({to:".",search:{status:l==="left"?"to-pay":"pending"}})},children:[a==="pending"&&e.jsxs(E,{direction:"left",children:[d.length===0&&e.jsx(G,{children:"No pending items"}),e.jsx("ul",{children:d.map(l=>e.jsx(O.li,{layout:"position",children:e.jsx(V,{orderId:t,item:l})},l.id))})]},"pending"),a==="to-pay"&&e.jsxs(E,{direction:"right",children:[c.isPending&&e.jsx(Ge,{}),c.isSuccess&&e.jsx("ul",{children:m.map(l=>e.jsx(O.li,{layout:"position",children:e.jsx(z,{orderId:t,item:l})},l.id))}),c.isSuccess&&c.data.ordered.length===0&&e.jsx(G,{children:"No items to pay"})]},"to-pay")]}),e.jsx(De,{id:"cart-filter",children:e.jsx("div",{className:"absolute bottom-full flex w-full items-center justify-between overflow-hidden py-2",children:e.jsxs(Ze,{value:a!=null?a:"pending",onChange:l=>{r({to:".",search:{status:l}})},children:[e.jsx(T,{value:"pending",children:"Pending"},"pending"),e.jsx(T,{value:"to-pay",children:"To Pay"},"to-pay")]})})})]})};export{it as component};

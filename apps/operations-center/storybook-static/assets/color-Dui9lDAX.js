const g=(t,r=20)=>{if(!t)return t;t=t.replace(/^#/,"");let n=parseInt(t.substring(0,2),16),a=parseInt(t.substring(2,4),16),s=parseInt(t.substring(4,6),16);return n=Math.max(0,Math.floor(n*(1-r/100))),a=Math.max(0,Math.floor(a*(1-r/100))),s=Math.max(0,Math.floor(s*(1-r/100))),"#"+n.toString(16).padStart(2,"0")+a.toString(16).padStart(2,"0")+s.toString(16).padStart(2,"0")},u=(t,r=.5)=>{if(!t)return t;const n=t.replace(/^#/,""),a=parseInt(n.substring(0,2),16),s=parseInt(n.substring(2,4),16),e=parseInt(n.substring(4,6),16),d=Math.round(a*r+255*(1-r)),i=Math.round(s*r+255*(1-r)),o=Math.round(e*r+255*(1-r));return"#".concat(d.toString(16).padStart(2,"0")).concat(i.toString(16).padStart(2,"0")).concat(o.toString(16).padStart(2,"0"))};export{g as d,u as m};

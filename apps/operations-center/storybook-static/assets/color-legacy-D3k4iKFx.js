System.register([],(function(t,r){"use strict";return{execute:function(){t("d",(function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;if(!t)return t;t=t.replace(/^#/,"");var a=parseInt(t.substring(0,2),16),n=parseInt(t.substring(2,4),16),e=parseInt(t.substring(4,6),16);return a=Math.max(0,Math.floor(a*(1-r/100))),n=Math.max(0,Math.floor(n*(1-r/100))),e=Math.max(0,Math.floor(e*(1-r/100))),"#"+a.toString(16).padStart(2,"0")+n.toString(16).padStart(2,"0")+e.toString(16).padStart(2,"0")})),t("m",(function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5;if(!t)return t;var a=t.replace(/^#/,""),n=parseInt(a.substring(0,2),16),e=parseInt(a.substring(2,4),16),o=parseInt(a.substring(4,6),16),i=Math.round(n*r+255*(1-r)),s=Math.round(e*r+255*(1-r)),u=Math.round(o*r+255*(1-r));return"#".concat(i.toString(16).padStart(2,"0")).concat(s.toString(16).padStart(2,"0")).concat(u.toString(16).padStart(2,"0"))}))}}}));

import{j as r}from"./index-C_VjMC7g.js";import{A as D,n as w,h as M,a as O,d as V,J as E,H as k,z as v,X as $,G as F,W as A,$ as L,f as H,t as U,g as Q}from"./x-B9YxCZbW.js";import{r as u}from"./index-D4lIrffr.js";import{am as W,a9 as B,an as J,M as S,ah as X,a5 as R,y as q,aj as Y,ao as x,a4 as K,ap as Z}from"./__federation_expose_App-DYUBmReV.js";import{P as ee}from"./plus-BP0qVozT.js";const te=({className:e,invalid:t,variant:n,id:i,...s})=>{const a=D(),d=t||!!(a!=null&&a["aria-errormessage"])||void 0;return r.jsx("textarea",{id:i!=null?i:a==null?void 0:a.id,"aria-errormessage":a==null?void 0:a["aria-errormessage"],"aria-describedby":a==null?void 0:a["aria-describedby"],"aria-labelledby":a==null?void 0:a["aria-labelledby"],"aria-invalid":d,"data-invalid":d,className:w(M({variant:n}),"h-auto resize-none py-2 leading-snug",e),...s})},re=({ref:e,...t})=>{var l;const n=u.useRef(null),[i,s]=u.useState(t.value),a=(l=t.value)!=null?l:i,d=c=>{var o;s(c.target.value),(o=t.onChange)==null||o.call(t,c)};return u.useEffect(()=>{n.current&&(n.current.style.height="auto",n.current.style.height="".concat(n.current.scrollHeight+2,"px"))},[a]),r.jsx(te,{ref:W(e,n),...t,value:a,onChange:d})},G=(e,t)=>{const i=e.options.filter(s=>t.items.some(a=>a.id===s.id)).reduce((s,a)=>s+a.quantity,0);if(t.min>0&&i<t.min)return{code:"TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR",message:"Select at least ".concat(t.min),groupId:t.id};if(t.max&&i>t.max)return{code:"TOO_MANY_OPTIONS_SELECTED_IN_GROUP_ERROR",message:"Select up to ".concat(t.max),groupId:t.id}},ne=(e,t)=>{for(const n of t.options){const i=G(e,n);if(i)return i}},ae=(e,t)=>{switch(t.type){case"set":return{...e,options:[...e.options.filter(n=>n.id!==t.payload.id),t.payload]};case"set-initial":return t.payload;case"set-in-group":return{...e,options:[...e.options.filter(n=>n.groupId!==t.payload.groupId),t.payload]};case"remove":return{...e,options:e.options.filter(n=>n.id!==t.payload)};case"quantity":return{...e,quantity:t.payload};case"set-notes":return{...e,notes:t.payload};default:return e}},T=u.createContext(null),h=()=>{const e=u.use(T);if(!e)throw new Error("ProductConfigurationContext not found. Make sure to wrap your component in a ProductConfigurationProvider");return e};function j({product:e,initialCartItem:t,onSubmit:n,onCancel:i,children:s}){const[a,d]=u.useState(!1),[l,c]=u.useReducer(ae,t!=null?t:B(e)),[o,p]=u.useState(new Map),m=async()=>{var _;const y=ne(l,e);if(y)return(_=document.getElementById(y.groupId))==null||_.scrollIntoView({behavior:"smooth"}),p(N=>(N.set(y.groupId,Date.now()),new Map(N)));d(!0),await n(l),d(!1)},f=()=>{i==null||i()};return r.jsx(T,{value:{product:e,cartItem:l,initialCartItem:t,liveValidationGroups:o,dispatch:c,submit:m,cancel:f,isPending:a},children:s})}try{j.displayName="ProductConfigurationProvider",j.__docgenInfo={description:"",displayName:"ProductConfigurationProvider",props:{product:{defaultValue:null,description:"",name:"product",required:!0,type:{name:"Product"}},initialCartItem:{defaultValue:null,description:"",name:"initialCartItem",required:!1,type:{name:"CartItem | undefined"}},onSubmit:{defaultValue:null,description:"",name:"onSubmit",required:!0,type:{name:"(cartItem: CartItem) => unknown"}},onCancel:{defaultValue:null,description:"",name:"onCancel",required:!1,type:{name:"(() => void) | undefined"}},freeze:{defaultValue:null,description:"",name:"freeze",required:!1,type:{name:"boolean | undefined"}}}}}catch(e){}const se=()=>{const{initialCartItem:e,cartItem:t,dispatch:n,submit:i,cancel:s,isPending:a}=h(),d=u.useMemo(()=>e?J([t,e]).length!==1||e.quantity!==t.quantity:!0,[t,e]);return r.jsxs(r.Fragment,{children:[r.jsxs(O,{size:"md",min:1,quantity:t.quantity,onChange:l=>{n({type:"quantity",payload:l})},inert:a,children:[r.jsx(V,{}),r.jsx(E,{className:"text-sm font-medium"}),r.jsx(k,{})]}),r.jsxs(v,{size:"md",variant:"accent",className:"grow",onClick:i,isLoading:a,inert:a,disabled:!d,children:[r.jsx("span",{children:e?"Update":"Add"}),r.jsx("span",{children:"·"}),r.jsx(S,{amount:X(t)})]}),r.jsx(v,{size:"md",onClick:s,inert:a,children:"Cancel"})]})},ie=e=>{const t=u.useRef(e);return u.useEffect(()=>{e&&(t.current=e)},[e]),u.useCallback((...i)=>{t.current&&t.current(...i)},[])},g=(e,t)=>t.includes(e)?t.indexOf(e).toString():"custom",C=({presets:e,defaultValue:t=[],customNotePlaceholder:n="Type a custom note...",onChange:i})=>{var l,c;const[s,a]=u.useState(t.map(o=>({id:g(o,e),value:o}))),d=ie(i);return u.useEffect(()=>{d(s.map(o=>o.value))},[s,d]),r.jsxs("div",{className:"bg-border border-border flex flex-col gap-0.25 overflow-hidden rounded-xl border",children:[r.jsx(re,{rows:1,placeholder:n,className:"h-12 rounded-t-xl rounded-b-none border-0 px-3 py-3.5 text-sm !ring-transparent !ring-offset-transparent",defaultValue:(l=s.find(o=>o.id==="custom"))==null?void 0:l.value,onChange:o=>{const p=g(o.target.value,e);o.target.value?a(m=>[...m.filter(f=>f.id!==p),{id:p,value:o.target.value}]):a(m=>m.filter(f=>f.id!==p))}}),(c=e==null?void 0:e.map)==null?void 0:c.call(e,o=>{const p=g(o,e);return r.jsxs("label",{className:"bg-background-highlight flex h-12 items-center justify-between gap-1.5 px-3",children:[r.jsx("p",{children:o}),r.jsx(R,{checked:s.some(m=>m.value===o),onChange:m=>{m.target.checked?a([...s,{id:p,value:o}]):a(s.filter(f=>f.id!==p))}})]},o)})]})};try{C.displayName="NotesInput",C.__docgenInfo={description:"",displayName:"NotesInput",props:{customNotePlaceholder:{defaultValue:{value:"Type a custom note..."},description:"",name:"customNotePlaceholder",required:!1,type:{name:"string | undefined"}},defaultValue:{defaultValue:{value:"[]"},description:"",name:"defaultValue",required:!1,type:{name:"string[] | undefined"}},presets:{defaultValue:null,description:"",name:"presets",required:!0,type:{name:"string[]"}},onChange:{defaultValue:null,description:"",name:"onChange",required:!0,type:{name:"(notes: string[]) => void"}}}}}catch(e){}const oe=()=>{const{product:e,cartItem:t,dispatch:n,isPending:i}=h();return r.jsxs("div",{inert:i,children:[r.jsx("div",{className:"flex items-center gap-1.5 px-2 py-3",children:r.jsx("h2",{children:"Notes"})}),r.jsx(C,{customNotePlaceholder:"Type a custom note...",defaultValue:t.notes,presets:e.suggestedNotes,onChange:s=>{n({type:"set-notes",payload:s})}})]})},de=e=>e instanceof HTMLElement||e instanceof SVGElement?e.closest("button, a, input, textarea"):!1,le=()=>{const{product:e,liveValidationGroups:t}=h();return r.jsx(r.Fragment,{children:e.options.map(n=>r.jsx(b,{group:n,shouldValidate:t.has(n.id)},n.id))})},b=({group:e,shouldValidate:t})=>{const{cartItem:n,isPending:i}=h(),s=e.min>0,a=n.options.some(l=>l.groupId===e.id),d=u.useMemo(()=>G(n,e),[n,e]);return r.jsxs("div",{className:"text-xs",id:e.id,inert:i,children:[r.jsxs("div",{className:w("box-content flex min-h-5 items-center gap-1.5 px-2 py-3",t&&d&&"animate-shake"),children:[r.jsx("h2",{children:e.name}),t&&d&&r.jsxs(q,{size:"xs",variant:"negative",children:[r.jsx($,{})," ",d.message]}),!(t&&d)&&s&&r.jsxs(q,{size:"xs",variant:a&&!d?"positive":"default",children:[a&&!d&&r.jsx(Y,{})," Required"]})]}),r.jsx("div",{className:"bg-border border-border flex flex-col gap-0.25 overflow-hidden rounded-xl border",children:e.items.map(l=>{const o=l.max>0||l.qtd>1?l.max>1?"quantity":"checkbox":"radio";return r.jsx("div",{className:"bg-background-highlight flex items-center gap-1.5 p-3",children:r.jsx(P,{groupId:e.id,option:l,type:o})},l.id)})})]})},P=({type:e,groupId:t,option:n})=>{const{cartItem:i,dispatch:s}=h(),a=i.options.find(c=>n.id===c.id),d=({target:c})=>{if(de(c))return;const o=a?a.quantity:0;e==="quantity"&&o<(n.max||1/0)&&s({type:"set",payload:x(t,n,a?a.quantity+1:1)})},l=e==="radio"?"label":"div";return r.jsxs(l,{className:"flex w-full items-center justify-between gap-1.5",onClick:d,children:[r.jsxs("div",{className:"grow",children:[r.jsx("p",{children:n.name}),r.jsx("p",{className:"text-foreground-secondary",children:r.jsx(S,{amount:n.unitPrice})})]}),r.jsxs("div",{className:"shrink-0",children:[e==="checkbox"&&r.jsx(R,{name:t,value:n.id,checked:(a==null?void 0:a.id)===n.id,onChange:()=>{if(a)return s({type:"remove",payload:n.id});s({type:"set-in-group",payload:x(t,n,1)})}}),e==="radio"&&r.jsx(K,{name:t,value:n.id,checked:(a==null?void 0:a.id)===n.id,onChange:()=>{s({type:"set-in-group",payload:x(t,n,1)})}}),e==="quantity"&&r.jsx(r.Fragment,{children:a?r.jsxs(O,{size:"sm",quantity:a.quantity,min:n.min,max:n.max||void 0,onChange:c=>{s({type:"set",payload:x(t,n,c)})},children:[a.quantity===n.min+1?r.jsx(F,{onClick:()=>a.initialQuantity?s({type:"set",payload:x(t,n,n.min)}):s({type:"remove",payload:n.id})}):r.jsx(V,{}),r.jsx(E,{}),r.jsx(k,{})]}):r.jsx(v,{size:"sm","aria-label":"Add",square:!0,onClick:()=>{s({type:"set",payload:x(t,n,1)})},children:r.jsx(ee,{})})})]})]})};try{b.displayName="ProductOptionGroup",b.__docgenInfo={description:"",displayName:"ProductOptionGroup",props:{group:{defaultValue:null,description:"",name:"group",required:!0,type:{name:"ProductConfiguration"}},shouldValidate:{defaultValue:null,description:"",name:"shouldValidate",required:!0,type:{name:"boolean"}}}}}catch(e){}try{P.displayName="ProductOption",P.__docgenInfo={description:"",displayName:"ProductOption",props:{type:{defaultValue:null,description:"",name:"type",required:!0,type:{name:"enum",value:[{value:'"radio"'},{value:'"quantity"'}]}},groupId:{defaultValue:null,description:"",name:"groupId",required:!0,type:{name:"string"}},option:{defaultValue:null,description:"",name:"option",required:!0,type:{name:"ProductOption"}}}}}catch(e){}const z=u.createContext({open:()=>{},close:()=>{}}),xe=()=>{const e=u.use(z);if(!e)throw new Error("useProductConfigurationDrawer must be used within a ProductConfigurationDrawerProvider");return e},I=({children:e})=>{const[t,n]=u.useState(null);return r.jsxs(z,{value:{open:n,close:()=>n(null)},children:[e,r.jsx(A,{open:!!t,onOpenChange:i=>{i||n(null)},children:r.jsx(L,{className:"scroll-pt-16",children:t&&r.jsxs(j,{...t,onSubmit:i=>{const s=t.onSubmit(i);return Z(s)?s.then(()=>n(null)):n(null),s},onCancel:()=>{n(null)},children:[r.jsx(H,{children:r.jsx(U,{children:t.product.name})}),r.jsxs("div",{className:"mb-32 min-h-full space-y-2 overflow-hidden p-2 text-xs",children:[r.jsx(le,{}),r.jsx(oe,{})]}),r.jsx(Q,{children:r.jsx(se,{})})]})})})]})};try{I.displayName="ProductConfigurationDrawerProvider",I.__docgenInfo={description:"",displayName:"ProductConfigurationDrawerProvider",props:{}}}catch(e){}export{C as N,I as P,de as i,xe as u};

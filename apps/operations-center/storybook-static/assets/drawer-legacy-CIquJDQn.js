!function(){function e(n){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(n)}var n=["className","invalid","variant","id"],t=["ref"];function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,n,t="function"==typeof Symbol?Symbol:{},o=t.iterator||"@@iterator",a=t.toStringTag||"@@toStringTag";function u(t,r,o,a){var u=r&&r.prototype instanceof c?r:c,s=Object.create(u.prototype);return i(s,"_invoke",function(t,r,i){var o,a,u,c=0,s=i||[],d=!1,f={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(n,t){return o=n,a=0,u=e,f.n=t,l}};function p(t,r){for(a=t,u=r,n=0;!d&&c&&!i&&n<s.length;n++){var i,o=s[n],p=f.p,m=o[2];t>3?(i=m===r)&&(u=o[(a=o[4])?5:(a=3,3)],o[4]=o[5]=e):o[0]<=p&&((i=t<2&&p<o[1])?(a=0,f.v=r,f.n=o[1]):p<m&&(i=t<3||o[0]>r||r>m)&&(o[4]=t,o[5]=r,f.n=m,a=0))}if(i||t>1)return l;throw d=!0,r}return function(i,s,m){if(c>1)throw TypeError("Generator is already running");for(d&&1===s&&p(s,m),a=s,u=m;(n=a<2?e:u)||!d;){o||(a?a<3?(a>1&&(f.n=-1),p(a,u)):f.n=u:f.v=u);try{if(c=2,o){if(a||(i="next"),n=o[i]){if(!(n=n.call(o,u)))throw TypeError("iterator result is not an object");if(!n.done)return n;u=n.value,a<2&&(a=0)}else 1===a&&(n=o.return)&&n.call(o),a<2&&(u=TypeError("The iterator does not provide a '"+i+"' method"),a=1);o=e}else if((n=(d=f.n<0)?u:t.call(r,f))!==l)break}catch(n){o=e,a=1,u=n}finally{c=1}}return{value:n,done:d}}}(t,o,a),!0),s}var l={};function c(){}function s(){}function d(){}n=Object.getPrototypeOf;var f=[][o]?n(n([][o]())):(i(n={},o,(function(){return this})),n),p=d.prototype=c.prototype=Object.create(f);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,i(e,a,"GeneratorFunction")),e.prototype=Object.create(p),e}return s.prototype=d,i(p,"constructor",d),i(d,"constructor",s),s.displayName="GeneratorFunction",i(d,a,"GeneratorFunction"),i(p),i(p,a,"Generator"),i(p,o,(function(){return this})),i(p,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:u,m:m}})()}function i(e,n,t,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}i=function(e,n,t,r){if(n)o?o(e,n,{value:t,enumerable:!r,configurable:!r,writable:!r}):e[n]=t;else{function a(n,t){i(e,n,(function(e){return this._invoke(n,t,e)}))}a("next",0),a("throw",1),a("return",2)}},i(e,n,t,r)}function o(e,n,t,r,i,o,a){try{var u=e[o](a),l=u.value}catch(e){return void t(e)}u.done?n(l):Promise.resolve(l).then(r,i)}function a(e){return function(e){if(Array.isArray(e))return c(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||l(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,i,o,a,u=[],l=!0,c=!1;try{if(o=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;l=!1}else for(;!(l=(r=o.call(t)).done)&&(u.push(r.value),u.length!==n);l=!0);}catch(e){c=!0,i=e}finally{try{if(!l&&null!=t.return&&(a=t.return(),Object(a)!==a))return}finally{if(c)throw i}}return u}}(e,n)||l(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,n){if(e){if("string"==typeof e)return c(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?c(e,n):void 0}}function c(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function s(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function d(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?s(Object(t),!0).forEach((function(n){f(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):s(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}function f(n,t,r){return(t=function(n){var t=function(n,t){if("object"!=e(n)||!n)return n;var r=n[Symbol.toPrimitive];if(void 0!==r){var i=r.call(n,t||"default");if("object"!=e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(n)}(n,"string");return"symbol"==e(t)?t:t+""}(t))in n?Object.defineProperty(n,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[t]=r,n}function p(e,n){if(null==e)return{};var t,r,i=function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==n.indexOf(r))continue;t[r]=e[r]}return t}(e,n);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)t=o[r],-1===n.indexOf(t)&&{}.propertyIsEnumerable.call(e,t)&&(i[t]=e[t])}return i}System.register(["./index-legacy-C2Kr5I5h.js","./x-legacy-yM9Jk2p5.js","./index-legacy-8Bh_oy_I.js","./__federation_expose_App-legacy-pwn7RX54.js","./plus-legacy-khTMZduY.js"],(function(e,i){"use strict";var c,s,f,m,y,v,h,g,b,x,j,O,w,P,C,S,I,N,q,_,E,V,k,T,A,G,R,z,D;return{setters:[function(e){c=e.j},function(e){s=e.A,f=e.n,m=e.h,y=e.a,v=e.d,h=e.J,g=e.H,b=e.z,x=e.X,j=e.G,O=e.W,w=e.$,P=e.f,C=e.t,S=e.g},function(e){I=e.r},function(e){N=e.am,q=e.a9,_=e.an,E=e.M,V=e.ah,k=e.a5,T=e.y,A=e.aj,G=e.ao,R=e.a4,z=e.ap},function(e){D=e.P}],execute:function(){var i=function(e){var t=e.className,r=e.invalid,i=e.variant,o=e.id,a=p(e,n),u=s(),l=r||!(null==u||!u["aria-errormessage"])||void 0;return c.jsx("textarea",d({id:null!=o?o:null==u?void 0:u.id,"aria-errormessage":null==u?void 0:u["aria-errormessage"],"aria-describedby":null==u?void 0:u["aria-describedby"],"aria-labelledby":null==u?void 0:u["aria-labelledby"],"aria-invalid":l,"data-invalid":l,className:f(m({variant:i}),"h-auto resize-none py-2 leading-snug",t)},a))},M=function(e){var n,r=e.ref,o=p(e,t),a=I.useRef(null),l=u(I.useState(o.value),2),s=l[0],f=l[1],m=null!==(n=o.value)&&void 0!==n?n:s;return I.useEffect((function(){a.current&&(a.current.style.height="auto",a.current.style.height="".concat(a.current.scrollHeight+2,"px"))}),[m]),c.jsx(i,d(d({ref:N(r,a)},o),{},{value:m,onChange:function(e){var n;f(e.target.value),null==(n=o.onChange)||n.call(o,e)}}))},F=function(e,n){var t=e.options.filter((function(e){return n.items.some((function(n){return n.id===e.id}))})).reduce((function(e,n){return e+n.quantity}),0);return n.min>0&&t<n.min?{code:"TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR",message:"Select at least ".concat(n.min),groupId:n.id}:n.max&&t>n.max?{code:"TOO_MANY_OPTIONS_SELECTED_IN_GROUP_ERROR",message:"Select up to ".concat(n.max),groupId:n.id}:void 0},L=function(e,n){var t,r=function(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=l(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,u=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){u=!0,o=e},f:function(){try{a||null==t.return||t.return()}finally{if(u)throw o}}}}(n.options);try{for(r.s();!(t=r.n()).done;){var i=t.value,o=F(e,i);if(o)return o}}catch(a){r.e(a)}finally{r.f()}},U=function(e,n){switch(n.type){case"set":return d(d({},e),{},{options:[].concat(a(e.options.filter((function(e){return e.id!==n.payload.id}))),[n.payload])});case"set-initial":return n.payload;case"set-in-group":return d(d({},e),{},{options:[].concat(a(e.options.filter((function(e){return e.groupId!==n.payload.groupId}))),[n.payload])});case"remove":return d(d({},e),{},{options:e.options.filter((function(e){return e.id!==n.payload}))});case"quantity":return d(d({},e),{},{quantity:n.payload});case"set-notes":return d(d({},e),{},{notes:n.payload});default:return e}},H=I.createContext(null),W=function(){var e=I.use(H);if(!e)throw new Error("ProductConfigurationContext not found. Make sure to wrap your component in a ProductConfigurationProvider");return e};function $(e){var n=e.product,t=e.initialCartItem,i=e.onSubmit,a=e.onCancel,l=e.children,s=u(I.useState(!1),2),d=s[0],f=s[1],p=u(I.useReducer(U,null!=t?t:q(n)),2),m=p[0],y=p[1],v=u(I.useState(new Map),2),h=v[0],g=v[1],b=function(){var e=function(e){return function(){var n=this,t=arguments;return new Promise((function(r,i){var a=e.apply(n,t);function u(e){o(a,r,i,u,l,"next",e)}function l(e){o(a,r,i,u,l,"throw",e)}u(void 0)}))}}(r().m((function e(){var t,o;return r().w((function(e){for(;;)switch(e.n){case 0:if(!(t=L(m,n))){e.n=1;break}return null===(o=document.getElementById(t.groupId))||void 0===o||o.scrollIntoView({behavior:"smooth"}),e.a(2,g((function(e){return e.set(t.groupId,Date.now()),new Map(e)})));case 1:return f(!0),e.n=2,i(m);case 2:f(!1);case 3:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}();return c.jsx(H,{value:{product:n,cartItem:m,initialCartItem:t,liveValidationGroups:h,dispatch:y,submit:b,cancel:function(){null==a||a()},isPending:d},children:l})}try{$.displayName="ProductConfigurationProvider",$.__docgenInfo={description:"",displayName:"ProductConfigurationProvider",props:{product:{defaultValue:null,description:"",name:"product",required:!0,type:{name:"Product"}},initialCartItem:{defaultValue:null,description:"",name:"initialCartItem",required:!1,type:{name:"CartItem | undefined"}},onSubmit:{defaultValue:null,description:"",name:"onSubmit",required:!0,type:{name:"(cartItem: CartItem) => unknown"}},onCancel:{defaultValue:null,description:"",name:"onCancel",required:!1,type:{name:"(() => void) | undefined"}},freeze:{defaultValue:null,description:"",name:"freeze",required:!1,type:{name:"boolean | undefined"}}}}}catch(re){}var B=function(){var e=W(),n=e.initialCartItem,t=e.cartItem,r=e.dispatch,i=e.submit,o=e.cancel,a=e.isPending,u=I.useMemo((function(){return!n||(1!==_([t,n]).length||n.quantity!==t.quantity)}),[t,n]);return c.jsxs(c.Fragment,{children:[c.jsxs(y,{size:"md",min:1,quantity:t.quantity,onChange:function(e){r({type:"quantity",payload:e})},inert:a,children:[c.jsx(v,{}),c.jsx(h,{className:"text-sm font-medium"}),c.jsx(g,{})]}),c.jsxs(b,{size:"md",variant:"accent",className:"grow",onClick:i,isLoading:a,inert:a,disabled:!u,children:[c.jsx("span",{children:n?"Update":"Add"}),c.jsx("span",{children:"·"}),c.jsx(E,{amount:V(t)})]}),c.jsx(b,{size:"md",onClick:o,inert:a,children:"Cancel"})]})},J=function(e,n){return n.includes(e)?n.indexOf(e).toString():"custom"},Q=e("N",(function(e){var n,t,r=e.presets,i=e.defaultValue,o=void 0===i?[]:i,l=e.customNotePlaceholder,s=void 0===l?"Type a custom note...":l,d=e.onChange,f=I.useState(o.map((function(e){return{id:J(e,r),value:e}}))),p=u(f,2),m=p[0],y=p[1],v=function(e){var n=I.useRef(e);I.useEffect((function(){e&&(n.current=e)}),[e]);var t=I.useCallback((function(){n.current&&n.current.apply(n,arguments)}),[]);return t}(d);return I.useEffect((function(){v(m.map((function(e){return e.value})))}),[m,v]),c.jsxs("div",{className:"bg-border border-border flex flex-col gap-0.25 overflow-hidden rounded-xl border",children:[c.jsx(M,{rows:1,placeholder:s,className:"h-12 rounded-t-xl rounded-b-none border-0 px-3 py-3.5 text-sm !ring-transparent !ring-offset-transparent",defaultValue:null===(n=m.find((function(e){return"custom"===e.id})))||void 0===n?void 0:n.value,onChange:function(e){var n=J(e.target.value,r);e.target.value?y((function(t){return[].concat(a(t.filter((function(e){return e.id!==n}))),[{id:n,value:e.target.value}])})):y((function(e){return e.filter((function(e){return e.id!==n}))}))}}),null==r||null===(t=r.map)||void 0===t?void 0:t.call(r,(function(e){var n=J(e,r);return c.jsxs("label",{className:"bg-background-highlight flex h-12 items-center justify-between gap-1.5 px-3",children:[c.jsx("p",{children:e}),c.jsx(k,{checked:m.some((function(n){return n.value===e})),onChange:function(t){t.target.checked?y([].concat(a(m),[{id:n,value:e}])):y(m.filter((function(e){return e.id!==n})))}})]},e)}))]})}));try{Q.displayName="NotesInput",Q.__docgenInfo={description:"",displayName:"NotesInput",props:{customNotePlaceholder:{defaultValue:{value:"Type a custom note..."},description:"",name:"customNotePlaceholder",required:!1,type:{name:"string | undefined"}},defaultValue:{defaultValue:{value:"[]"},description:"",name:"defaultValue",required:!1,type:{name:"string[] | undefined"}},presets:{defaultValue:null,description:"",name:"presets",required:!0,type:{name:"string[]"}},onChange:{defaultValue:null,description:"",name:"onChange",required:!0,type:{name:"(notes: string[]) => void"}}}}}catch(re){}var X=function(){var e=W(),n=e.product,t=e.cartItem,r=e.dispatch,i=e.isPending;return c.jsxs("div",{inert:i,children:[c.jsx("div",{className:"flex items-center gap-1.5 px-2 py-3",children:c.jsx("h2",{children:"Notes"})}),c.jsx(Q,{customNotePlaceholder:"Type a custom note...",defaultValue:t.notes,presets:n.suggestedNotes,onChange:function(e){r({type:"set-notes",payload:e})}})]})},Y=e("i",(function(e){return(e instanceof HTMLElement||e instanceof SVGElement)&&e.closest("button, a, input, textarea")})),K=function(){var e=W(),n=e.product,t=e.liveValidationGroups;return c.jsx(c.Fragment,{children:n.options.map((function(e){return c.jsx(Z,{group:e,shouldValidate:t.has(e.id)},e.id)}))})},Z=function(e){var n=e.group,t=e.shouldValidate,r=W(),i=r.cartItem,o=r.isPending,a=n.min>0,u=i.options.some((function(e){return e.groupId===n.id})),l=I.useMemo((function(){return F(i,n)}),[i,n]);return c.jsxs("div",{className:"text-xs",id:n.id,inert:o,children:[c.jsxs("div",{className:f("box-content flex min-h-5 items-center gap-1.5 px-2 py-3",t&&l&&"animate-shake"),children:[c.jsx("h2",{children:n.name}),t&&l&&c.jsxs(T,{size:"xs",variant:"negative",children:[c.jsx(x,{})," ",l.message]}),!(t&&l)&&a&&c.jsxs(T,{size:"xs",variant:u&&!l?"positive":"default",children:[u&&!l&&c.jsx(A,{})," Required"]})]}),c.jsx("div",{className:"bg-border border-border flex flex-col gap-0.25 overflow-hidden rounded-xl border",children:n.items.map((function(e){var t=e.max>0||e.qtd>1?e.max>1?"quantity":"checkbox":"radio";return c.jsx("div",{className:"bg-background-highlight flex items-center gap-1.5 p-3",children:c.jsx(ee,{groupId:n.id,option:e,type:t})},e.id)}))})]})},ee=function(e){var n=e.type,t=e.groupId,r=e.option,i=W(),o=i.cartItem,a=i.dispatch,u=o.options.find((function(e){return r.id===e.id})),l="radio"===n?"label":"div";return c.jsxs(l,{className:"flex w-full items-center justify-between gap-1.5",onClick:function(e){var i=e.target;if(!Y(i)){var o=u?u.quantity:0;"quantity"===n&&o<(r.max||1/0)&&a({type:"set",payload:G(t,r,u?u.quantity+1:1)})}},children:[c.jsxs("div",{className:"grow",children:[c.jsx("p",{children:r.name}),c.jsx("p",{className:"text-foreground-secondary",children:c.jsx(E,{amount:r.unitPrice})})]}),c.jsxs("div",{className:"shrink-0",children:["checkbox"===n&&c.jsx(k,{name:t,value:r.id,checked:(null==u?void 0:u.id)===r.id,onChange:function(){if(u)return a({type:"remove",payload:r.id});a({type:"set-in-group",payload:G(t,r,1)})}}),"radio"===n&&c.jsx(R,{name:t,value:r.id,checked:(null==u?void 0:u.id)===r.id,onChange:function(){a({type:"set-in-group",payload:G(t,r,1)})}}),"quantity"===n&&c.jsx(c.Fragment,{children:u?c.jsxs(y,{size:"sm",quantity:u.quantity,min:r.min,max:r.max||void 0,onChange:function(e){a({type:"set",payload:G(t,r,e)})},children:[u.quantity===r.min+1?c.jsx(j,{onClick:function(){return u.initialQuantity?a({type:"set",payload:G(t,r,r.min)}):a({type:"remove",payload:r.id})}}):c.jsx(v,{}),c.jsx(h,{}),c.jsx(g,{})]}):c.jsx(b,{size:"sm","aria-label":"Add",square:!0,onClick:function(){a({type:"set",payload:G(t,r,1)})},children:c.jsx(D,{})})})]})]})};try{Z.displayName="ProductOptionGroup",Z.__docgenInfo={description:"",displayName:"ProductOptionGroup",props:{group:{defaultValue:null,description:"",name:"group",required:!0,type:{name:"ProductConfiguration"}},shouldValidate:{defaultValue:null,description:"",name:"shouldValidate",required:!0,type:{name:"boolean"}}}}}catch(re){}try{ee.displayName="ProductOption",ee.__docgenInfo={description:"",displayName:"ProductOption",props:{type:{defaultValue:null,description:"",name:"type",required:!0,type:{name:"enum",value:[{value:'"radio"'},{value:'"quantity"'}]}},groupId:{defaultValue:null,description:"",name:"groupId",required:!0,type:{name:"string"}},option:{defaultValue:null,description:"",name:"option",required:!0,type:{name:"ProductOption"}}}}}catch(re){}var ne=I.createContext({open:function(){},close:function(){}}),te=(e("u",(function(){var e=I.use(ne);if(!e)throw new Error("useProductConfigurationDrawer must be used within a ProductConfigurationDrawerProvider");return e})),e("P",(function(e){var n=e.children,t=u(I.useState(null),2),r=t[0],i=t[1];return c.jsxs(ne,{value:{open:i,close:function(){return i(null)}},children:[n,c.jsx(O,{open:!!r,onOpenChange:function(e){e||i(null)},children:c.jsx(w,{className:"scroll-pt-16",children:r&&c.jsxs($,d(d({},r),{},{onSubmit:function(e){var n=r.onSubmit(e);return z(n)?n.then((function(){return i(null)})):i(null),n},onCancel:function(){i(null)},children:[c.jsx(P,{children:c.jsx(C,{children:r.product.name})}),c.jsxs("div",{className:"mb-32 min-h-full space-y-2 overflow-hidden p-2 text-xs",children:[c.jsx(K,{}),c.jsx(X,{})]}),c.jsx(S,{children:c.jsx(B,{})})]}))})})]})})));try{te.displayName="ProductConfigurationDrawerProvider",te.__docgenInfo={description:"",displayName:"ProductConfigurationDrawerProvider",props:{}}}catch(re){}}}}))}();

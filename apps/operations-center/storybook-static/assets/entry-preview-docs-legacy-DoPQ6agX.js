!function(){var e=["mdxType","originalType","children"];function t(e){return function(e){if(Array.isArray(e))return n(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return(t=a(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,a(r.key),r)}}function a(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=d(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d(t)?t:t+""}function o(e,t,n){return t=h(t),function(e,t){if(t&&("object"==d(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,c()?Reflect.construct(t,n||[],h(e).constructor):t.apply(e,n))}function c(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(c=function(){return!!e})()}function p(e,t,n,r){var i=l(h(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof i?function(e){return i.apply(n,e)}:i}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=function(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=h(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},l.apply(null,arguments)}function h(e){return h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},h(e)}function f(e,t){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},f(e,t)}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}System.register(["./chunk-XP5HYGXS-legacy-C6xl9Z3R.js","./index-legacy-WIWhP2SU.js","./index-legacy-8Bh_oy_I.js"],(function(n,r){"use strict";var s,a,c,l,h,m,g,D,A,C,E,y,x,F,v,b,S,B;return{setters:[function(e){s=e._,a=e.a,c=e.c,l=e.d,h=e.b},function(e){m=e.c,g=e.n,D=e.y,A=e.g,C=e.z,E=e.o,y=e.j,x=e.l,F=e.B,v=e.a,b=e.Y},function(e){S=e.r,B=e.R}],execute:function(){var r=__STORYBOOK_MODULE_CLIENT_LOGGER__.logger,_=__STORYBOOK_MODULE_PREVIEW_API__,w=_.defaultDecorateStory,k=_.addons,P=_.useEffect,I=a({"../../node_modules/prop-types/lib/ReactPropTypesSecret.js":function(e,t){t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}}),T=a({"../../node_modules/prop-types/factoryWithThrowingShims.js":function(e,t){var n=I();function r(){}function i(){}i.resetWarningCache=r,t.exports=function(){function e(e,t,r,i,s,u){if(u!==n){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var s={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:r};return s.PropTypes=s,s}}}),L=a({"../../node_modules/prop-types/index.js":function(e,t){t.exports=T()()}}),O=a({"../../node_modules/html-tags/html-tags.json":function(e,t){t.exports=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","link","main","map","mark","math","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rb","rp","rt","rtc","ruby","s","samp","script","search","section","select","slot","small","source","span","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr"]}}),N=a({"../../node_modules/html-tags/index.js":function(e,t){t.exports=O()}}),R=a({"../../node_modules/estraverse/estraverse.js":function(e){!function e(t){var n,r,i,s,u,a;function o(e){var t,n,r={};for(t in e)e.hasOwnProperty(t)&&("object"==d(n=e[t])&&null!==n?r[t]=o(n):r[t]=n);return r}function c(e,t){this.parent=e,this.key=t}function p(e,t,n,r){this.node=e,this.path=t,this.wrap=n,this.ref=r}function l(){}function h(e){return null!=e&&("object"==d(e)&&"string"==typeof e.type)}function f(e,t){return(e===n.ObjectExpression||e===n.ObjectPattern)&&"properties"===t}function m(e,t){for(var n=e.length-1;n>=0;--n)if(e[n].node===t)return!0;return!1}function g(e,t){return(new l).traverse(e,t)}function D(e,t){var n;return n=function(e,t){var n,r,i,s;for(r=e.length,i=0;r;)t(e[s=i+(n=r>>>1)])?r=n:(i=s+1,r-=n+1);return i}(t,(function(t){return t.range[0]>e.range[0]})),e.extendedRange=[e.range[0],e.range[1]],n!==t.length&&(e.extendedRange[1]=t[n].range[0]),(n-=1)>=0&&(e.extendedRange[0]=t[n].range[1]),e}return n={AssignmentExpression:"AssignmentExpression",AssignmentPattern:"AssignmentPattern",ArrayExpression:"ArrayExpression",ArrayPattern:"ArrayPattern",ArrowFunctionExpression:"ArrowFunctionExpression",AwaitExpression:"AwaitExpression",BlockStatement:"BlockStatement",BinaryExpression:"BinaryExpression",BreakStatement:"BreakStatement",CallExpression:"CallExpression",CatchClause:"CatchClause",ChainExpression:"ChainExpression",ClassBody:"ClassBody",ClassDeclaration:"ClassDeclaration",ClassExpression:"ClassExpression",ComprehensionBlock:"ComprehensionBlock",ComprehensionExpression:"ComprehensionExpression",ConditionalExpression:"ConditionalExpression",ContinueStatement:"ContinueStatement",DebuggerStatement:"DebuggerStatement",DirectiveStatement:"DirectiveStatement",DoWhileStatement:"DoWhileStatement",EmptyStatement:"EmptyStatement",ExportAllDeclaration:"ExportAllDeclaration",ExportDefaultDeclaration:"ExportDefaultDeclaration",ExportNamedDeclaration:"ExportNamedDeclaration",ExportSpecifier:"ExportSpecifier",ExpressionStatement:"ExpressionStatement",ForStatement:"ForStatement",ForInStatement:"ForInStatement",ForOfStatement:"ForOfStatement",FunctionDeclaration:"FunctionDeclaration",FunctionExpression:"FunctionExpression",GeneratorExpression:"GeneratorExpression",Identifier:"Identifier",IfStatement:"IfStatement",ImportExpression:"ImportExpression",ImportDeclaration:"ImportDeclaration",ImportDefaultSpecifier:"ImportDefaultSpecifier",ImportNamespaceSpecifier:"ImportNamespaceSpecifier",ImportSpecifier:"ImportSpecifier",Literal:"Literal",LabeledStatement:"LabeledStatement",LogicalExpression:"LogicalExpression",MemberExpression:"MemberExpression",MetaProperty:"MetaProperty",MethodDefinition:"MethodDefinition",ModuleSpecifier:"ModuleSpecifier",NewExpression:"NewExpression",ObjectExpression:"ObjectExpression",ObjectPattern:"ObjectPattern",PrivateIdentifier:"PrivateIdentifier",Program:"Program",Property:"Property",PropertyDefinition:"PropertyDefinition",RestElement:"RestElement",ReturnStatement:"ReturnStatement",SequenceExpression:"SequenceExpression",SpreadElement:"SpreadElement",Super:"Super",SwitchStatement:"SwitchStatement",SwitchCase:"SwitchCase",TaggedTemplateExpression:"TaggedTemplateExpression",TemplateElement:"TemplateElement",TemplateLiteral:"TemplateLiteral",ThisExpression:"ThisExpression",ThrowStatement:"ThrowStatement",TryStatement:"TryStatement",UnaryExpression:"UnaryExpression",UpdateExpression:"UpdateExpression",VariableDeclaration:"VariableDeclaration",VariableDeclarator:"VariableDeclarator",WhileStatement:"WhileStatement",WithStatement:"WithStatement",YieldExpression:"YieldExpression"},i={AssignmentExpression:["left","right"],AssignmentPattern:["left","right"],ArrayExpression:["elements"],ArrayPattern:["elements"],ArrowFunctionExpression:["params","body"],AwaitExpression:["argument"],BlockStatement:["body"],BinaryExpression:["left","right"],BreakStatement:["label"],CallExpression:["callee","arguments"],CatchClause:["param","body"],ChainExpression:["expression"],ClassBody:["body"],ClassDeclaration:["id","superClass","body"],ClassExpression:["id","superClass","body"],ComprehensionBlock:["left","right"],ComprehensionExpression:["blocks","filter","body"],ConditionalExpression:["test","consequent","alternate"],ContinueStatement:["label"],DebuggerStatement:[],DirectiveStatement:[],DoWhileStatement:["body","test"],EmptyStatement:[],ExportAllDeclaration:["source"],ExportDefaultDeclaration:["declaration"],ExportNamedDeclaration:["declaration","specifiers","source"],ExportSpecifier:["exported","local"],ExpressionStatement:["expression"],ForStatement:["init","test","update","body"],ForInStatement:["left","right","body"],ForOfStatement:["left","right","body"],FunctionDeclaration:["id","params","body"],FunctionExpression:["id","params","body"],GeneratorExpression:["blocks","filter","body"],Identifier:[],IfStatement:["test","consequent","alternate"],ImportExpression:["source"],ImportDeclaration:["specifiers","source"],ImportDefaultSpecifier:["local"],ImportNamespaceSpecifier:["local"],ImportSpecifier:["imported","local"],Literal:[],LabeledStatement:["label","body"],LogicalExpression:["left","right"],MemberExpression:["object","property"],MetaProperty:["meta","property"],MethodDefinition:["key","value"],ModuleSpecifier:[],NewExpression:["callee","arguments"],ObjectExpression:["properties"],ObjectPattern:["properties"],PrivateIdentifier:[],Program:["body"],Property:["key","value"],PropertyDefinition:["key","value"],RestElement:["argument"],ReturnStatement:["argument"],SequenceExpression:["expressions"],SpreadElement:["argument"],Super:[],SwitchStatement:["discriminant","cases"],SwitchCase:["test","consequent"],TaggedTemplateExpression:["tag","quasi"],TemplateElement:[],TemplateLiteral:["quasis","expressions"],ThisExpression:[],ThrowStatement:["argument"],TryStatement:["block","handler","finalizer"],UnaryExpression:["argument"],UpdateExpression:["argument"],VariableDeclaration:["declarations"],VariableDeclarator:["id","init"],WhileStatement:["test","body"],WithStatement:["object","body"],YieldExpression:["argument"]},r={Break:s={},Skip:u={},Remove:a={}},c.prototype.replace=function(e){this.parent[this.key]=e},c.prototype.remove=function(){return Array.isArray(this.parent)?(this.parent.splice(this.key,1),!0):(this.replace(null),!1)},l.prototype.path=function(){var e,t,n,r,i;function s(e,t){if(Array.isArray(t))for(n=0,r=t.length;n<r;++n)e.push(t[n]);else e.push(t)}if(!this.__current.path)return null;for(i=[],e=2,t=this.__leavelist.length;e<t;++e)s(i,this.__leavelist[e].path);return s(i,this.__current.path),i},l.prototype.type=function(){return this.current().type||this.__current.wrap},l.prototype.parents=function(){var e,t,n;for(n=[],e=1,t=this.__leavelist.length;e<t;++e)n.push(this.__leavelist[e].node);return n},l.prototype.current=function(){return this.__current.node},l.prototype.__execute=function(e,t){var n,r;return r=void 0,n=this.__current,this.__current=t,this.__state=null,e&&(r=e.call(this,t.node,this.__leavelist[this.__leavelist.length-1].node)),this.__current=n,r},l.prototype.notify=function(e){this.__state=e},l.prototype.skip=function(){this.notify(u)},l.prototype.break=function(){this.notify(s)},l.prototype.remove=function(){this.notify(a)},l.prototype.__initialize=function(e,t){this.visitor=t,this.root=e,this.__worklist=[],this.__leavelist=[],this.__current=null,this.__state=null,this.__fallback=null,"iteration"===t.fallback?this.__fallback=Object.keys:"function"==typeof t.fallback&&(this.__fallback=t.fallback),this.__keys=i,t.keys&&(this.__keys=Object.assign(Object.create(this.__keys),t.keys))},l.prototype.traverse=function(e,t){var n,r,i,a,o,c,l,d,g,D,A,C;for(this.__initialize(e,t),C={},n=this.__worklist,r=this.__leavelist,n.push(new p(e,null,null,null)),r.push(new p(null,null,null,null));n.length;)if((i=n.pop())!==C){if(i.node){if(c=this.__execute(t.enter,i),this.__state===s||c===s)return;if(n.push(C),r.push(i),this.__state===u||c===u)continue;if(o=(a=i.node).type||i.wrap,!(D=this.__keys[o])){if(!this.__fallback)throw new Error("Unknown node type "+o+".");D=this.__fallback(a)}for(d=D.length;(d-=1)>=0;)if(A=a[l=D[d]])if(Array.isArray(A)){for(g=A.length;(g-=1)>=0;)if(A[g]&&!m(r,A[g])){if(f(o,D[d]))i=new p(A[g],[l,g],"Property",null);else{if(!h(A[g]))continue;i=new p(A[g],[l,g],null,null)}n.push(i)}}else if(h(A)){if(m(r,A))continue;n.push(new p(A,l,null,null))}}}else if(i=r.pop(),c=this.__execute(t.leave,i),this.__state===s||c===s)return},l.prototype.replace=function(e,t){var n,r,i,o,l,d,m,g,D,A,C,E,y;function x(e){var t,r,i,s;if(e.ref.remove())for(r=e.ref.key,s=e.ref.parent,t=n.length;t--;)if((i=n[t]).ref&&i.ref.parent===s){if(i.ref.key<r)break;--i.ref.key}}for(this.__initialize(e,t),C={},n=this.__worklist,r=this.__leavelist,d=new p(e,null,null,new c(E={root:e},"root")),n.push(d),r.push(d);n.length;)if((d=n.pop())!==C){if(void 0!==(l=this.__execute(t.enter,d))&&l!==s&&l!==u&&l!==a&&(d.ref.replace(l),d.node=l),(this.__state===a||l===a)&&(x(d),d.node=null),this.__state===s||l===s)return E.root;if((i=d.node)&&(n.push(C),r.push(d),this.__state!==u&&l!==u)){if(o=i.type||d.wrap,!(D=this.__keys[o])){if(!this.__fallback)throw new Error("Unknown node type "+o+".");D=this.__fallback(i)}for(m=D.length;(m-=1)>=0;)if(A=i[y=D[m]])if(Array.isArray(A)){for(g=A.length;(g-=1)>=0;)if(A[g]){if(f(o,D[m]))d=new p(A[g],[y,g],"Property",new c(A,g));else{if(!h(A[g]))continue;d=new p(A[g],[y,g],null,new c(A,g))}n.push(d)}}else h(A)&&n.push(new p(A,y,null,new c(i,y)))}}else if(d=r.pop(),void 0!==(l=this.__execute(t.leave,d))&&l!==s&&l!==u&&l!==a&&d.ref.replace(l),(this.__state===a||l===a)&&x(d),this.__state===s||l===s)return E.root;return E.root},t.Syntax=n,t.traverse=g,t.replace=function(e,t){return(new l).replace(e,t)},t.attachComments=function(e,t,n){var i,s,u,a,c=[];if(!e.range)throw new Error("attachComments needs range information");if(!n.length){if(t.length){for(u=0,s=t.length;u<s;u+=1)(i=o(t[u])).extendedRange=[0,e.range[0]],c.push(i);e.leadingComments=c}return e}for(u=0,s=t.length;u<s;u+=1)c.push(D(o(t[u]),n));return a=0,g(e,{enter:function(e){for(var t;a<c.length&&!((t=c[a]).extendedRange[1]>e.range[0]);)t.extendedRange[1]===e.range[0]?(e.leadingComments||(e.leadingComments=[]),e.leadingComments.push(t),c.splice(a,1)):a+=1;return a===c.length?r.Break:c[a].extendedRange[0]>e.range[1]?r.Skip:void 0}}),a=0,g(e,{leave:function(e){for(var t;a<c.length&&(t=c[a],!(e.range[1]<t.extendedRange[0]));)e.range[1]===t.extendedRange[0]?(e.trailingComments||(e.trailingComments=[]),e.trailingComments.push(t),c.splice(a,1)):a+=1;return a===c.length?r.Break:c[a].extendedRange[0]>e.range[1]?r.Skip:void 0}}),e},t.VisitorKeys=i,t.VisitorOption=r,t.Controller=l,t.cloneEnvironment=function(){return e({})},t}(e)}}),j=a({"../../node_modules/esutils/lib/ast.js":function(e,t){!function(){function e(e){if(null==e)return!1;switch(e.type){case"BlockStatement":case"BreakStatement":case"ContinueStatement":case"DebuggerStatement":case"DoWhileStatement":case"EmptyStatement":case"ExpressionStatement":case"ForInStatement":case"ForStatement":case"IfStatement":case"LabeledStatement":case"ReturnStatement":case"SwitchStatement":case"ThrowStatement":case"TryStatement":case"VariableDeclaration":case"WhileStatement":case"WithStatement":return!0}return!1}function n(e){switch(e.type){case"IfStatement":return null!=e.alternate?e.alternate:e.consequent;case"LabeledStatement":case"ForStatement":case"ForInStatement":case"WhileStatement":case"WithStatement":return e.body}return null}t.exports={isExpression:function(e){if(null==e)return!1;switch(e.type){case"ArrayExpression":case"AssignmentExpression":case"BinaryExpression":case"CallExpression":case"ConditionalExpression":case"FunctionExpression":case"Identifier":case"Literal":case"LogicalExpression":case"MemberExpression":case"NewExpression":case"ObjectExpression":case"SequenceExpression":case"ThisExpression":case"UnaryExpression":case"UpdateExpression":return!0}return!1},isStatement:e,isIterationStatement:function(e){if(null==e)return!1;switch(e.type){case"DoWhileStatement":case"ForInStatement":case"ForStatement":case"WhileStatement":return!0}return!1},isSourceElement:function(t){return e(t)||null!=t&&"FunctionDeclaration"===t.type},isProblematicIfStatement:function(e){var t;if("IfStatement"!==e.type||null==e.alternate)return!1;t=e.consequent;do{if("IfStatement"===t.type&&null==t.alternate)return!0;t=n(t)}while(t);return!1},trailingStatement:n}}()}}),M=a({"../../node_modules/esutils/lib/code.js":function(e,t){!function(){var e,n,r,i,s,u;function a(e){return e<=65535?String.fromCharCode(e):String.fromCharCode(Math.floor((e-65536)/1024)+55296)+String.fromCharCode((e-65536)%1024+56320)}for(n={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/,NonAsciiIdentifierPart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/},e={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/,NonAsciiIdentifierPart:/[\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/},r=[5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279],i=new Array(128),u=0;u<128;++u)i[u]=u>=97&&u<=122||u>=65&&u<=90||36===u||95===u;for(s=new Array(128),u=0;u<128;++u)s[u]=u>=97&&u<=122||u>=65&&u<=90||u>=48&&u<=57||36===u||95===u;t.exports={isDecimalDigit:function(e){return 48<=e&&e<=57},isHexDigit:function(e){return 48<=e&&e<=57||97<=e&&e<=102||65<=e&&e<=70},isOctalDigit:function(e){return e>=48&&e<=55},isWhiteSpace:function(e){return 32===e||9===e||11===e||12===e||160===e||e>=5760&&r.indexOf(e)>=0},isLineTerminator:function(e){return 10===e||13===e||8232===e||8233===e},isIdentifierStartES5:function(e){return e<128?i[e]:n.NonAsciiIdentifierStart.test(a(e))},isIdentifierPartES5:function(e){return e<128?s[e]:n.NonAsciiIdentifierPart.test(a(e))},isIdentifierStartES6:function(t){return t<128?i[t]:e.NonAsciiIdentifierStart.test(a(t))},isIdentifierPartES6:function(t){return t<128?s[t]:e.NonAsciiIdentifierPart.test(a(t))}}}()}}),V=a({"../../node_modules/esutils/lib/keyword.js":function(e,t){!function(){var e=M();function n(e,t){return!(!t&&"yield"===e)&&r(e,t)}function r(e,t){if(t&&function(e){switch(e){case"implements":case"interface":case"package":case"private":case"protected":case"public":case"static":case"let":return!0;default:return!1}}(e))return!0;switch(e.length){case 2:return"if"===e||"in"===e||"do"===e;case 3:return"var"===e||"for"===e||"new"===e||"try"===e;case 4:return"this"===e||"else"===e||"case"===e||"void"===e||"with"===e||"enum"===e;case 5:return"while"===e||"break"===e||"catch"===e||"throw"===e||"const"===e||"yield"===e||"class"===e||"super"===e;case 6:return"return"===e||"typeof"===e||"delete"===e||"switch"===e||"export"===e||"import"===e;case 7:return"default"===e||"finally"===e||"extends"===e;case 8:return"function"===e||"continue"===e||"debugger"===e;case 10:return"instanceof"===e;default:return!1}}function i(e,t){return"null"===e||"true"===e||"false"===e||n(e,t)}function s(e,t){return"null"===e||"true"===e||"false"===e||r(e,t)}function u(t){var n,r,i;if(0===t.length||(i=t.charCodeAt(0),!e.isIdentifierStartES5(i)))return!1;for(n=1,r=t.length;n<r;++n)if(i=t.charCodeAt(n),!e.isIdentifierPartES5(i))return!1;return!0}function a(t){var n,r,i,s,u;if(0===t.length)return!1;for(u=e.isIdentifierStartES6,n=0,r=t.length;n<r;++n){if(55296<=(i=t.charCodeAt(n))&&i<=56319){if(++n>=r||!(56320<=(s=t.charCodeAt(n))&&s<=57343))return!1;i=1024*(i-55296)+(s-56320)+65536}if(!u(i))return!1;u=e.isIdentifierPartES6}return!0}t.exports={isKeywordES5:n,isKeywordES6:r,isReservedWordES5:i,isReservedWordES6:s,isRestrictedWord:function(e){return"eval"===e||"arguments"===e},isIdentifierNameES5:u,isIdentifierNameES6:a,isIdentifierES5:function(e,t){return u(e)&&!i(e,t)},isIdentifierES6:function(e,t){return a(e)&&!s(e,t)}}}()}}),q=a({"../../node_modules/esutils/lib/utils.js":function(e){e.ast=j(),e.code=M(),e.keyword=V()}}),U=a({"../../node_modules/escodegen/node_modules/source-map/lib/base64.js":function(e){var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");e.encode=function(e){if(0<=e&&e<t.length)return t[e];throw new TypeError("Must be between 0 and 63: "+e)},e.decode=function(e){return 65<=e&&e<=90?e-65:97<=e&&e<=122?e-97+26:48<=e&&e<=57?e-48+52:43==e?62:47==e?63:-1}}}),W=a({"../../node_modules/escodegen/node_modules/source-map/lib/base64-vlq.js":function(e){var t=U();e.encode=function(e){var n,r="",i=function(e){return e<0?1+(-e<<1):0+(e<<1)}(e);do{n=31&i,(i>>>=5)>0&&(n|=32),r+=t.encode(n)}while(i>0);return r},e.decode=function(e,n,r){var i,s,u,a,o=e.length,c=0,p=0;do{if(n>=o)throw new Error("Expected more digits in base 64 VLQ value.");if(-1===(s=t.decode(e.charCodeAt(n++))))throw new Error("Invalid base64 digit: "+e.charAt(n-1));i=!!(32&s),c+=(s&=31)<<p,p+=5}while(i);r.value=(a=(u=c)>>1,1&~u?a:-a),r.rest=n}}}),G=a({"../../node_modules/escodegen/node_modules/source-map/lib/util.js":function(e){e.getArg=function(e,t,n){if(t in e)return e[t];if(3===arguments.length)return n;throw new Error('"'+t+'" is a required argument.')};var t=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,n=/^data:.+\,.+$/;function r(e){var n=e.match(t);return n?{scheme:n[1],auth:n[2],host:n[3],port:n[4],path:n[5]}:null}function i(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}function s(t){var n=t,s=r(t);if(s){if(!s.path)return t;n=s.path}for(var u,a=e.isAbsolute(n),o=n.split(/\/+/),c=0,p=o.length-1;p>=0;p--)"."===(u=o[p])?o.splice(p,1):".."===u?c++:c>0&&(""===u?(o.splice(p+1,c),c=0):(o.splice(p,2),c--));return""===(n=o.join("/"))&&(n=a?"/":"."),s?(s.path=n,i(s)):n}function u(e,t){""===e&&(e="."),""===t&&(t=".");var u=r(t),a=r(e);if(a&&(e=a.path||"/"),u&&!u.scheme)return a&&(u.scheme=a.scheme),i(u);if(u||t.match(n))return t;if(a&&!a.host&&!a.path)return a.host=t,i(a);var o="/"===t.charAt(0)?t:s(e.replace(/\/+$/,"")+"/"+t);return a?(a.path=o,i(a)):o}e.urlParse=r,e.urlGenerate=i,e.normalize=s,e.join=u,e.isAbsolute=function(e){return"/"===e.charAt(0)||t.test(e)},e.relative=function(e,t){""===e&&(e="."),e=e.replace(/\/$/,"");for(var n=0;0!==t.indexOf(e+"/");){var r=e.lastIndexOf("/");if(r<0||(e=e.slice(0,r)).match(/^([^\/]+:\/)?\/*$/))return t;++n}return Array(n+1).join("../")+t.substr(e.length+1)};var a=!("__proto__"in Object.create(null));function o(e){return e}function c(e){if(!e)return!1;var t=e.length;if(t<9||95!==e.charCodeAt(t-1)||95!==e.charCodeAt(t-2)||111!==e.charCodeAt(t-3)||116!==e.charCodeAt(t-4)||111!==e.charCodeAt(t-5)||114!==e.charCodeAt(t-6)||112!==e.charCodeAt(t-7)||95!==e.charCodeAt(t-8)||95!==e.charCodeAt(t-9))return!1;for(var n=t-10;n>=0;n--)if(36!==e.charCodeAt(n))return!1;return!0}function p(e,t){return e===t?0:null===e?1:null===t?-1:e>t?1:-1}e.toSetString=a?o:function(e){return c(e)?"$"+e:e},e.fromSetString=a?o:function(e){return c(e)?e.slice(1):e},e.compareByOriginalPositions=function(e,t,n){var r=p(e.source,t.source);return 0!==r||0!==(r=e.originalLine-t.originalLine)||(0!==(r=e.originalColumn-t.originalColumn)||n)||0!==(r=e.generatedColumn-t.generatedColumn)||0!==(r=e.generatedLine-t.generatedLine)?r:p(e.name,t.name)},e.compareByGeneratedPositionsDeflated=function(e,t,n){var r=e.generatedLine-t.generatedLine;return 0!==r||(0!==(r=e.generatedColumn-t.generatedColumn)||n)||0!==(r=p(e.source,t.source))||0!==(r=e.originalLine-t.originalLine)||0!==(r=e.originalColumn-t.originalColumn)?r:p(e.name,t.name)},e.compareByGeneratedPositionsInflated=function(e,t){var n=e.generatedLine-t.generatedLine;return 0!==n||0!==(n=e.generatedColumn-t.generatedColumn)||0!==(n=p(e.source,t.source))||0!==(n=e.originalLine-t.originalLine)||0!==(n=e.originalColumn-t.originalColumn)?n:p(e.name,t.name)},e.parseSourceMapInput=function(e){return JSON.parse(e.replace(/^\)]}'[^\n]*\n/,""))},e.computeSourceURL=function(e,t,n){if(t=t||"",e&&("/"!==e[e.length-1]&&"/"!==t[0]&&(e+="/"),t=e+t),n){var a=r(n);if(!a)throw new Error("sourceMapURL could not be parsed");if(a.path){var o=a.path.lastIndexOf("/");o>=0&&(a.path=a.path.substring(0,o+1))}t=u(i(a),t)}return s(t)}}}),z=a({"../../node_modules/escodegen/node_modules/source-map/lib/array-set.js":function(e){var t=G(),n=Object.prototype.hasOwnProperty,r=("undefined"==typeof Map?"undefined":d(Map))<"u";function i(){this._array=[],this._set=r?new Map:Object.create(null)}i.fromArray=function(e,t){for(var n=new i,r=0,s=e.length;r<s;r++)n.add(e[r],t);return n},i.prototype.size=function(){return r?this._set.size:Object.getOwnPropertyNames(this._set).length},i.prototype.add=function(e,i){var s=r?e:t.toSetString(e),u=r?this.has(e):n.call(this._set,s),a=this._array.length;(!u||i)&&this._array.push(e),u||(r?this._set.set(e,a):this._set[s]=a)},i.prototype.has=function(e){if(r)return this._set.has(e);var i=t.toSetString(e);return n.call(this._set,i)},i.prototype.indexOf=function(e){if(r){var i=this._set.get(e);if(i>=0)return i}else{var s=t.toSetString(e);if(n.call(this._set,s))return this._set[s]}throw new Error('"'+e+'" is not in the set.')},i.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)},i.prototype.toArray=function(){return this._array.slice()},e.ArraySet=i}}),$=a({"../../node_modules/escodegen/node_modules/source-map/lib/mapping-list.js":function(e){var t=G();function n(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}n.prototype.unsortedForEach=function(e,t){this._array.forEach(e,t)},n.prototype.add=function(e){var n,r,i,s,u,a;n=this._last,r=e,i=n.generatedLine,s=r.generatedLine,u=n.generatedColumn,a=r.generatedColumn,s>i||s==i&&a>=u||t.compareByGeneratedPositionsInflated(n,r)<=0?(this._last=e,this._array.push(e)):(this._sorted=!1,this._array.push(e))},n.prototype.toArray=function(){return this._sorted||(this._array.sort(t.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},e.MappingList=n}}),H=a({"../../node_modules/escodegen/node_modules/source-map/lib/source-map-generator.js":function(e){var t=W(),n=G(),r=z().ArraySet,i=$().MappingList;function s(e){e||(e={}),this._file=n.getArg(e,"file",null),this._sourceRoot=n.getArg(e,"sourceRoot",null),this._skipValidation=n.getArg(e,"skipValidation",!1),this._sources=new r,this._names=new r,this._mappings=new i,this._sourcesContents=null}s.prototype._version=3,s.fromSourceMap=function(e){var t=e.sourceRoot,r=new s({file:e.file,sourceRoot:t});return e.eachMapping((function(e){var i={generated:{line:e.generatedLine,column:e.generatedColumn}};null!=e.source&&(i.source=e.source,null!=t&&(i.source=n.relative(t,i.source)),i.original={line:e.originalLine,column:e.originalColumn},null!=e.name&&(i.name=e.name)),r.addMapping(i)})),e.sources.forEach((function(i){var s=i;null!==t&&(s=n.relative(t,i)),r._sources.has(s)||r._sources.add(s);var u=e.sourceContentFor(i);null!=u&&r.setSourceContent(i,u)})),r},s.prototype.addMapping=function(e){var t=n.getArg(e,"generated"),r=n.getArg(e,"original",null),i=n.getArg(e,"source",null),s=n.getArg(e,"name",null);this._skipValidation||this._validateMapping(t,r,i,s),null!=i&&(i=String(i),this._sources.has(i)||this._sources.add(i)),null!=s&&(s=String(s),this._names.has(s)||this._names.add(s)),this._mappings.add({generatedLine:t.line,generatedColumn:t.column,originalLine:null!=r&&r.line,originalColumn:null!=r&&r.column,source:i,name:s})},s.prototype.setSourceContent=function(e,t){var r=e;null!=this._sourceRoot&&(r=n.relative(this._sourceRoot,r)),null!=t?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[n.toSetString(r)]=t):this._sourcesContents&&(delete this._sourcesContents[n.toSetString(r)],0===Object.keys(this._sourcesContents).length&&(this._sourcesContents=null))},s.prototype.applySourceMap=function(e,t,i){var s=t;if(null==t){if(null==e.file)throw new Error('SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map\'s "file" property. Both were omitted.');s=e.file}var u=this._sourceRoot;null!=u&&(s=n.relative(u,s));var a=new r,o=new r;this._mappings.unsortedForEach((function(t){if(t.source===s&&null!=t.originalLine){var r=e.originalPositionFor({line:t.originalLine,column:t.originalColumn});null!=r.source&&(t.source=r.source,null!=i&&(t.source=n.join(i,t.source)),null!=u&&(t.source=n.relative(u,t.source)),t.originalLine=r.line,t.originalColumn=r.column,null!=r.name&&(t.name=r.name))}var c=t.source;null!=c&&!a.has(c)&&a.add(c);var p=t.name;null!=p&&!o.has(p)&&o.add(p)}),this),this._sources=a,this._names=o,e.sources.forEach((function(t){var r=e.sourceContentFor(t);null!=r&&(null!=i&&(t=n.join(i,t)),null!=u&&(t=n.relative(u,t)),this.setSourceContent(t,r))}),this)},s.prototype._validateMapping=function(e,t,n,r){if(t&&"number"!=typeof t.line&&"number"!=typeof t.column)throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if(!(e&&"line"in e&&"column"in e&&e.line>0&&e.column>=0)||t||n||r){if(e&&"line"in e&&"column"in e&&t&&"line"in t&&"column"in t&&e.line>0&&e.column>=0&&t.line>0&&t.column>=0&&n)return;throw new Error("Invalid mapping: "+JSON.stringify({generated:e,source:n,original:t,name:r}))}},s.prototype._serializeMappings=function(){for(var e,r,i,s,u=0,a=1,o=0,c=0,p=0,l=0,h="",f=this._mappings.toArray(),d=0,m=f.length;d<m;d++){if(e="",(r=f[d]).generatedLine!==a)for(u=0;r.generatedLine!==a;)e+=";",a++;else if(d>0){if(!n.compareByGeneratedPositionsInflated(r,f[d-1]))continue;e+=","}e+=t.encode(r.generatedColumn-u),u=r.generatedColumn,null!=r.source&&(s=this._sources.indexOf(r.source),e+=t.encode(s-l),l=s,e+=t.encode(r.originalLine-1-c),c=r.originalLine-1,e+=t.encode(r.originalColumn-o),o=r.originalColumn,null!=r.name&&(i=this._names.indexOf(r.name),e+=t.encode(i-p),p=i)),h+=e}return h},s.prototype._generateSourcesContent=function(e,t){return e.map((function(e){if(!this._sourcesContents)return null;null!=t&&(e=n.relative(t,e));var r=n.toSetString(e);return Object.prototype.hasOwnProperty.call(this._sourcesContents,r)?this._sourcesContents[r]:null}),this)},s.prototype.toJSON=function(){var e={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return null!=this._file&&(e.file=this._file),null!=this._sourceRoot&&(e.sourceRoot=this._sourceRoot),this._sourcesContents&&(e.sourcesContent=this._generateSourcesContent(e.sources,e.sourceRoot)),e},s.prototype.toString=function(){return JSON.stringify(this.toJSON())},e.SourceMapGenerator=s}}),X=a({"../../node_modules/escodegen/node_modules/source-map/lib/binary-search.js":function(e){function t(n,r,i,s,u,a){var o=Math.floor((r-n)/2)+n,c=u(i,s[o],!0);return 0===c?o:c>0?r-o>1?t(o,r,i,s,u,a):a==e.LEAST_UPPER_BOUND?r<s.length?r:-1:o:o-n>1?t(n,o,i,s,u,a):a==e.LEAST_UPPER_BOUND?o:n<0?-1:n}e.GREATEST_LOWER_BOUND=1,e.LEAST_UPPER_BOUND=2,e.search=function(n,r,i,s){if(0===r.length)return-1;var u=t(-1,r.length,n,r,i,s||e.GREATEST_LOWER_BOUND);if(u<0)return-1;for(;u-1>=0&&0===i(r[u],r[u-1],!0);)--u;return u}}}),J=a({"../../node_modules/escodegen/node_modules/source-map/lib/quick-sort.js":function(e){function t(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function n(e,r,i,s){if(i<s){var u=i-1;t(e,(p=i,l=s,Math.round(p+Math.random()*(l-p))),s);for(var a=e[s],o=i;o<s;o++)r(e[o],a)<=0&&t(e,u+=1,o);t(e,u+1,o);var c=u+1;n(e,r,i,c-1),n(e,r,c+1,s)}var p,l}e.quickSort=function(e,t){n(e,t,0,e.length-1)}}}),Y=a({"../../node_modules/escodegen/node_modules/source-map/lib/source-map-consumer.js":function(e){var t=G(),n=X(),r=z().ArraySet,i=W(),s=J().quickSort;function u(e,n){var r=e;return"string"==typeof e&&(r=t.parseSourceMapInput(e)),null!=r.sections?new c(r,n):new a(r,n)}function a(e,n){var i=e;"string"==typeof e&&(i=t.parseSourceMapInput(e));var s=t.getArg(i,"version"),u=t.getArg(i,"sources"),a=t.getArg(i,"names",[]),o=t.getArg(i,"sourceRoot",null),c=t.getArg(i,"sourcesContent",null),p=t.getArg(i,"mappings"),l=t.getArg(i,"file",null);if(s!=this._version)throw new Error("Unsupported version: "+s);o&&(o=t.normalize(o)),u=u.map(String).map(t.normalize).map((function(e){return o&&t.isAbsolute(o)&&t.isAbsolute(e)?t.relative(o,e):e})),this._names=r.fromArray(a.map(String),!0),this._sources=r.fromArray(u,!0),this._absoluteSources=this._sources.toArray().map((function(e){return t.computeSourceURL(o,e,n)})),this.sourceRoot=o,this.sourcesContent=c,this._mappings=p,this._sourceMapURL=n,this.file=l}function o(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function c(e,n){var i=e;"string"==typeof e&&(i=t.parseSourceMapInput(e));var s=t.getArg(i,"version"),a=t.getArg(i,"sections");if(s!=this._version)throw new Error("Unsupported version: "+s);this._sources=new r,this._names=new r;var o={line:-1,column:0};this._sections=a.map((function(e){if(e.url)throw new Error("Support for url field in sections not implemented.");var r=t.getArg(e,"offset"),i=t.getArg(r,"line"),s=t.getArg(r,"column");if(i<o.line||i===o.line&&s<o.column)throw new Error("Section offsets must be ordered and non-overlapping.");return o=r,{generatedOffset:{generatedLine:i+1,generatedColumn:s+1},consumer:new u(t.getArg(e,"map"),n)}}))}u.fromSourceMap=function(e,t){return a.fromSourceMap(e,t)},u.prototype._version=3,u.prototype.__generatedMappings=null,Object.defineProperty(u.prototype,"_generatedMappings",{configurable:!0,enumerable:!0,get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),u.prototype.__originalMappings=null,Object.defineProperty(u.prototype,"_originalMappings",{configurable:!0,enumerable:!0,get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),u.prototype._charIsMappingSeparator=function(e,t){var n=e.charAt(t);return";"===n||","===n},u.prototype._parseMappings=function(e,t){throw new Error("Subclasses must implement _parseMappings")},u.GENERATED_ORDER=1,u.ORIGINAL_ORDER=2,u.GREATEST_LOWER_BOUND=1,u.LEAST_UPPER_BOUND=2,u.prototype.eachMapping=function(e,n,r){var i,s=n||null;switch(r||u.GENERATED_ORDER){case u.GENERATED_ORDER:i=this._generatedMappings;break;case u.ORIGINAL_ORDER:i=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var a=this.sourceRoot;i.map((function(e){var n=null===e.source?null:this._sources.at(e.source);return{source:n=t.computeSourceURL(a,n,this._sourceMapURL),generatedLine:e.generatedLine,generatedColumn:e.generatedColumn,originalLine:e.originalLine,originalColumn:e.originalColumn,name:null===e.name?null:this._names.at(e.name)}}),this).forEach(e,s)},u.prototype.allGeneratedPositionsFor=function(e){var r=t.getArg(e,"line"),i={source:t.getArg(e,"source"),originalLine:r,originalColumn:t.getArg(e,"column",0)};if(i.source=this._findSourceIndex(i.source),i.source<0)return[];var s=[],u=this._findMapping(i,this._originalMappings,"originalLine","originalColumn",t.compareByOriginalPositions,n.LEAST_UPPER_BOUND);if(u>=0){var a=this._originalMappings[u];if(void 0===e.column)for(var o=a.originalLine;a&&a.originalLine===o;)s.push({line:t.getArg(a,"generatedLine",null),column:t.getArg(a,"generatedColumn",null),lastColumn:t.getArg(a,"lastGeneratedColumn",null)}),a=this._originalMappings[++u];else for(var c=a.originalColumn;a&&a.originalLine===r&&a.originalColumn==c;)s.push({line:t.getArg(a,"generatedLine",null),column:t.getArg(a,"generatedColumn",null),lastColumn:t.getArg(a,"lastGeneratedColumn",null)}),a=this._originalMappings[++u]}return s},e.SourceMapConsumer=u,a.prototype=Object.create(u.prototype),a.prototype.consumer=u,a.prototype._findSourceIndex=function(e){var n,r=e;if(null!=this.sourceRoot&&(r=t.relative(this.sourceRoot,r)),this._sources.has(r))return this._sources.indexOf(r);for(n=0;n<this._absoluteSources.length;++n)if(this._absoluteSources[n]==e)return n;return-1},a.fromSourceMap=function(e,n){var i=Object.create(a.prototype),u=i._names=r.fromArray(e._names.toArray(),!0),c=i._sources=r.fromArray(e._sources.toArray(),!0);i.sourceRoot=e._sourceRoot,i.sourcesContent=e._generateSourcesContent(i._sources.toArray(),i.sourceRoot),i.file=e._file,i._sourceMapURL=n,i._absoluteSources=i._sources.toArray().map((function(e){return t.computeSourceURL(i.sourceRoot,e,n)}));for(var p=e._mappings.toArray().slice(),l=i.__generatedMappings=[],h=i.__originalMappings=[],f=0,d=p.length;f<d;f++){var m=p[f],g=new o;g.generatedLine=m.generatedLine,g.generatedColumn=m.generatedColumn,m.source&&(g.source=c.indexOf(m.source),g.originalLine=m.originalLine,g.originalColumn=m.originalColumn,m.name&&(g.name=u.indexOf(m.name)),h.push(g)),l.push(g)}return s(i.__originalMappings,t.compareByOriginalPositions),i},a.prototype._version=3,Object.defineProperty(a.prototype,"sources",{get:function(){return this._absoluteSources.slice()}}),a.prototype._parseMappings=function(e,n){for(var r,u,a,c,p,l=1,h=0,f=0,d=0,m=0,g=0,D=e.length,A=0,C={},E={},y=[],x=[];A<D;)if(";"===e.charAt(A))l++,A++,h=0;else if(","===e.charAt(A))A++;else{for((r=new o).generatedLine=l,c=A;c<D&&!this._charIsMappingSeparator(e,c);c++);if(a=C[u=e.slice(A,c)])A+=u.length;else{for(a=[];A<c;)i.decode(e,A,E),p=E.value,A=E.rest,a.push(p);if(2===a.length)throw new Error("Found a source, but no line and column");if(3===a.length)throw new Error("Found a source and line, but no column");C[u]=a}r.generatedColumn=h+a[0],h=r.generatedColumn,a.length>1&&(r.source=m+a[1],m+=a[1],r.originalLine=f+a[2],f=r.originalLine,r.originalLine+=1,r.originalColumn=d+a[3],d=r.originalColumn,a.length>4&&(r.name=g+a[4],g+=a[4])),x.push(r),"number"==typeof r.originalLine&&y.push(r)}s(x,t.compareByGeneratedPositionsDeflated),this.__generatedMappings=x,s(y,t.compareByOriginalPositions),this.__originalMappings=y},a.prototype._findMapping=function(e,t,r,i,s,u){if(e[r]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+e[r]);if(e[i]<0)throw new TypeError("Column must be greater than or equal to 0, got "+e[i]);return n.search(e,t,s,u)},a.prototype.computeColumnSpans=function(){for(var e=0;e<this._generatedMappings.length;++e){var t=this._generatedMappings[e];if(e+1<this._generatedMappings.length){var n=this._generatedMappings[e+1];if(t.generatedLine===n.generatedLine){t.lastGeneratedColumn=n.generatedColumn-1;continue}}t.lastGeneratedColumn=1/0}},a.prototype.originalPositionFor=function(e){var n={generatedLine:t.getArg(e,"line"),generatedColumn:t.getArg(e,"column")},r=this._findMapping(n,this._generatedMappings,"generatedLine","generatedColumn",t.compareByGeneratedPositionsDeflated,t.getArg(e,"bias",u.GREATEST_LOWER_BOUND));if(r>=0){var i=this._generatedMappings[r];if(i.generatedLine===n.generatedLine){var s=t.getArg(i,"source",null);null!==s&&(s=this._sources.at(s),s=t.computeSourceURL(this.sourceRoot,s,this._sourceMapURL));var a=t.getArg(i,"name",null);return null!==a&&(a=this._names.at(a)),{source:s,line:t.getArg(i,"originalLine",null),column:t.getArg(i,"originalColumn",null),name:a}}}return{source:null,line:null,column:null,name:null}},a.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&(this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some((function(e){return null==e})))},a.prototype.sourceContentFor=function(e,n){if(!this.sourcesContent)return null;var r=this._findSourceIndex(e);if(r>=0)return this.sourcesContent[r];var i,s=e;if(null!=this.sourceRoot&&(s=t.relative(this.sourceRoot,s)),null!=this.sourceRoot&&(i=t.urlParse(this.sourceRoot))){var u=s.replace(/^file:\/\//,"");if("file"==i.scheme&&this._sources.has(u))return this.sourcesContent[this._sources.indexOf(u)];if((!i.path||"/"==i.path)&&this._sources.has("/"+s))return this.sourcesContent[this._sources.indexOf("/"+s)]}if(n)return null;throw new Error('"'+s+'" is not in the SourceMap.')},a.prototype.generatedPositionFor=function(e){var n=t.getArg(e,"source");if((n=this._findSourceIndex(n))<0)return{line:null,column:null,lastColumn:null};var r={source:n,originalLine:t.getArg(e,"line"),originalColumn:t.getArg(e,"column")},i=this._findMapping(r,this._originalMappings,"originalLine","originalColumn",t.compareByOriginalPositions,t.getArg(e,"bias",u.GREATEST_LOWER_BOUND));if(i>=0){var s=this._originalMappings[i];if(s.source===r.source)return{line:t.getArg(s,"generatedLine",null),column:t.getArg(s,"generatedColumn",null),lastColumn:t.getArg(s,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},e.BasicSourceMapConsumer=a,c.prototype=Object.create(u.prototype),c.prototype.constructor=u,c.prototype._version=3,Object.defineProperty(c.prototype,"sources",{get:function(){for(var e=[],t=0;t<this._sections.length;t++)for(var n=0;n<this._sections[t].consumer.sources.length;n++)e.push(this._sections[t].consumer.sources[n]);return e}}),c.prototype.originalPositionFor=function(e){var r={generatedLine:t.getArg(e,"line"),generatedColumn:t.getArg(e,"column")},i=n.search(r,this._sections,(function(e,t){return e.generatedLine-t.generatedOffset.generatedLine||e.generatedColumn-t.generatedOffset.generatedColumn})),s=this._sections[i];return s?s.consumer.originalPositionFor({line:r.generatedLine-(s.generatedOffset.generatedLine-1),column:r.generatedColumn-(s.generatedOffset.generatedLine===r.generatedLine?s.generatedOffset.generatedColumn-1:0),bias:e.bias}):{source:null,line:null,column:null,name:null}},c.prototype.hasContentsOfAllSources=function(){return this._sections.every((function(e){return e.consumer.hasContentsOfAllSources()}))},c.prototype.sourceContentFor=function(e,t){for(var n=0;n<this._sections.length;n++){var r=this._sections[n].consumer.sourceContentFor(e,!0);if(r)return r}if(t)return null;throw new Error('"'+e+'" is not in the SourceMap.')},c.prototype.generatedPositionFor=function(e){for(var n=0;n<this._sections.length;n++){var r=this._sections[n];if(-1!==r.consumer._findSourceIndex(t.getArg(e,"source"))){var i=r.consumer.generatedPositionFor(e);if(i)return{line:i.line+(r.generatedOffset.generatedLine-1),column:i.column+(r.generatedOffset.generatedLine===i.line?r.generatedOffset.generatedColumn-1:0)}}}return{line:null,column:null}},c.prototype._parseMappings=function(e,n){this.__generatedMappings=[],this.__originalMappings=[];for(var r=0;r<this._sections.length;r++)for(var i=this._sections[r],u=i.consumer._generatedMappings,a=0;a<u.length;a++){var o=u[a],c=i.consumer._sources.at(o.source);c=t.computeSourceURL(i.consumer.sourceRoot,c,this._sourceMapURL),this._sources.add(c),c=this._sources.indexOf(c);var p=null;o.name&&(p=i.consumer._names.at(o.name),this._names.add(p),p=this._names.indexOf(p));var l={source:c,generatedLine:o.generatedLine+(i.generatedOffset.generatedLine-1),generatedColumn:o.generatedColumn+(i.generatedOffset.generatedLine===o.generatedLine?i.generatedOffset.generatedColumn-1:0),originalLine:o.originalLine,originalColumn:o.originalColumn,name:p};this.__generatedMappings.push(l),"number"==typeof l.originalLine&&this.__originalMappings.push(l)}s(this.__generatedMappings,t.compareByGeneratedPositionsDeflated),s(this.__originalMappings,t.compareByOriginalPositions)},e.IndexedSourceMapConsumer=c}}),K=a({"../../node_modules/escodegen/node_modules/source-map/lib/source-node.js":function(e){var t=H().SourceMapGenerator,n=G(),r=/(\r?\n)/,i="$$$isSourceNode$$$";function s(e,t,n,r,s){this.children=[],this.sourceContents={},this.line=null!=e?e:null,this.column=null!=t?t:null,this.source=null!=n?n:null,this.name=null!=s?s:null,this[i]=!0,null!=r&&this.add(r)}s.fromStringWithSourceMap=function(e,t,i){var u=new s,a=e.split(r),o=0,c=function(){return e()+(e()||"");function e(){return o<a.length?a[o++]:void 0}},p=1,l=0,h=null;return t.eachMapping((function(e){if(null!==h){if(!(p<e.generatedLine)){var t=(n=a[o]||"").substr(0,e.generatedColumn-l);return a[o]=n.substr(e.generatedColumn-l),l=e.generatedColumn,f(h,t),void(h=e)}f(h,c()),p++,l=0}for(;p<e.generatedLine;)u.add(c()),p++;if(l<e.generatedColumn){var n=a[o]||"";u.add(n.substr(0,e.generatedColumn)),a[o]=n.substr(e.generatedColumn),l=e.generatedColumn}h=e}),this),o<a.length&&(h&&f(h,c()),u.add(a.splice(o).join(""))),t.sources.forEach((function(e){var r=t.sourceContentFor(e);null!=r&&(null!=i&&(e=n.join(i,e)),u.setSourceContent(e,r))})),u;function f(e,t){if(null===e||void 0===e.source)u.add(t);else{var r=i?n.join(i,e.source):e.source;u.add(new s(e.originalLine,e.originalColumn,r,t,e.name))}}},s.prototype.add=function(e){if(Array.isArray(e))e.forEach((function(e){this.add(e)}),this);else{if(!e[i]&&"string"!=typeof e)throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);e&&this.children.push(e)}return this},s.prototype.prepend=function(e){if(Array.isArray(e))for(var t=e.length-1;t>=0;t--)this.prepend(e[t]);else{if(!e[i]&&"string"!=typeof e)throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);this.children.unshift(e)}return this},s.prototype.walk=function(e){for(var t,n=0,r=this.children.length;n<r;n++)(t=this.children[n])[i]?t.walk(e):""!==t&&e(t,{source:this.source,line:this.line,column:this.column,name:this.name})},s.prototype.join=function(e){var t,n,r=this.children.length;if(r>0){for(t=[],n=0;n<r-1;n++)t.push(this.children[n]),t.push(e);t.push(this.children[n]),this.children=t}return this},s.prototype.replaceRight=function(e,t){var n=this.children[this.children.length-1];return n[i]?n.replaceRight(e,t):"string"==typeof n?this.children[this.children.length-1]=n.replace(e,t):this.children.push("".replace(e,t)),this},s.prototype.setSourceContent=function(e,t){this.sourceContents[n.toSetString(e)]=t},s.prototype.walkSourceContents=function(e){for(var t=0,r=this.children.length;t<r;t++)this.children[t][i]&&this.children[t].walkSourceContents(e);var s=Object.keys(this.sourceContents);for(t=0,r=s.length;t<r;t++)e(n.fromSetString(s[t]),this.sourceContents[s[t]])},s.prototype.toString=function(){var e="";return this.walk((function(t){e+=t})),e},s.prototype.toStringWithSourceMap=function(e){var n={code:"",line:1,column:0},r=new t(e),i=!1,s=null,u=null,a=null,o=null;return this.walk((function(e,t){n.code+=e,null!==t.source&&null!==t.line&&null!==t.column?((s!==t.source||u!==t.line||a!==t.column||o!==t.name)&&r.addMapping({source:t.source,original:{line:t.line,column:t.column},generated:{line:n.line,column:n.column},name:t.name}),s=t.source,u=t.line,a=t.column,o=t.name,i=!0):i&&(r.addMapping({generated:{line:n.line,column:n.column}}),s=null,i=!1);for(var c=0,p=e.length;c<p;c++)10===e.charCodeAt(c)?(n.line++,n.column=0,c+1===p?(s=null,i=!1):i&&r.addMapping({source:t.source,original:{line:t.line,column:t.column},generated:{line:n.line,column:n.column},name:t.name})):n.column++})),this.walkSourceContents((function(e,t){r.setSourceContent(e,t)})),{code:n.code,map:r}},e.SourceNode=s}}),Q=a({"../../node_modules/escodegen/node_modules/source-map/source-map.js":function(e){e.SourceMapGenerator=H().SourceMapGenerator,e.SourceMapConsumer=Y().SourceMapConsumer,e.SourceNode=K().SourceNode}}),Z=a({"../../node_modules/escodegen/package.json":function(e,t){t.exports={name:"escodegen",description:"ECMAScript code generator",homepage:"http://github.com/estools/escodegen",main:"escodegen.js",bin:{esgenerate:"./bin/esgenerate.js",escodegen:"./bin/escodegen.js"},files:["LICENSE.BSD","README.md","bin","escodegen.js","package.json"],version:"2.1.0",engines:{node:">=6.0"},maintainers:[{name:"Yusuke Suzuki",email:"<EMAIL>",web:"http://github.com/Constellation"}],repository:{type:"git",url:"http://github.com/estools/escodegen.git"},dependencies:{estraverse:"^5.2.0",esutils:"^2.0.2",esprima:"^4.0.1"},optionalDependencies:{"source-map":"~0.6.1"},devDependencies:{acorn:"^8.0.4",bluebird:"^3.4.7","bower-registry-client":"^1.0.0",chai:"^4.2.0","chai-exclude":"^2.0.2","commonjs-everywhere":"^0.9.7",gulp:"^4.0.2","gulp-eslint":"^6.0.0","gulp-mocha":"^7.0.2",minimist:"^1.2.5",optionator:"^0.9.1",semver:"^7.3.4"},license:"BSD-2-Clause",scripts:{test:"gulp travis","unit-test":"gulp test",lint:"gulp lint",release:"node tools/release.js","build-min":"./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js",build:"./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}}}}),ee=a({"../../node_modules/escodegen/escodegen.js":function(e){!function(){var t,n,r,i,s,u,a,o,c,p,l,h,f,m,g,D,A,C,E,y,x,F,v,b,S,B;function _(e){return K.Statement.hasOwnProperty(e.type)}s=R(),u=q(),t=s.Syntax,r={"??":(n={Sequence:0,Yield:1,Assignment:1,Conditional:2,ArrowFunction:2,Coalesce:3,LogicalOR:4,LogicalAND:5,BitwiseOR:6,BitwiseXOR:7,BitwiseAND:8,Equality:9,Relational:10,BitwiseSHIFT:11,Additive:12,Multiplicative:13,Exponentiation:14,Await:15,Unary:15,Postfix:16,OptionalChaining:17,Call:18,New:19,TaggedTemplate:20,Member:21,Primary:22}).Coalesce,"||":n.LogicalOR,"&&":n.LogicalAND,"|":n.BitwiseOR,"^":n.BitwiseXOR,"&":n.BitwiseAND,"==":n.Equality,"!=":n.Equality,"===":n.Equality,"!==":n.Equality,is:n.Equality,isnt:n.Equality,"<":n.Relational,">":n.Relational,"<=":n.Relational,">=":n.Relational,in:n.Relational,instanceof:n.Relational,"<<":n.BitwiseSHIFT,">>":n.BitwiseSHIFT,">>>":n.BitwiseSHIFT,"+":n.Additive,"-":n.Additive,"*":n.Multiplicative,"%":n.Multiplicative,"/":n.Multiplicative,"**":n.Exponentiation};var w=32,k=33;function P(e,t){var n="";for(t|=0;t>0;t>>>=1,e+=e)1&t&&(n+=e);return n}function I(e){var t=e.length;return t&&u.code.isLineTerminator(e.charCodeAt(t-1))}function T(e,t){var n;for(n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}function L(e,t){var n,r;function i(e){return"object"==d(e)&&e instanceof Object&&!(e instanceof RegExp)}for(n in t)t.hasOwnProperty(n)&&(i(r=t[n])?i(e[n])?L(e[n],r):e[n]=L({},r):e[n]=r);return e}function O(e,t){return 8232==(-2&e)?(t?"u":"\\u")+(8232===e?"2028":"2029"):10===e||13===e?(t?"":"\\")+(10===e?"n":"r"):String.fromCharCode(e)}function N(e,t){var n;return 8===e?"\\b":12===e?"\\f":9===e?"\\t":(n=e.toString(16).toUpperCase(),c||e>255?"\\u"+"0000".slice(n.length)+n:0!==e||u.code.isDecimalDigit(t)?11===e?"\\x0B":"\\x"+"00".slice(n.length)+n:"\\0")}function j(e){if(92===e)return"\\\\";if(10===e)return"\\n";if(13===e)return"\\r";if(8232===e)return"\\u2028";if(8233===e)return"\\u2029";throw new Error("Incorrectly classified character")}function M(e){var t,n,r,i="";for(t=0,n=e.length;t<n;++t)r=e[t],i+=Array.isArray(r)?M(r):r;return i}function V(e,t){if(!F)return Array.isArray(e)?M(e):e;if(null==t){if(e instanceof i)return e;t={}}return null==t.loc?new i(null,null,F,e,t.name||null):new i(t.loc.start.line,t.loc.start.column,!0===F?t.loc.source||null:F,e,t.name||null)}function U(){return g||" "}function W(e,t){var n,r,i,s;return 0===(n=V(e).toString()).length?[t]:0===(r=V(t).toString()).length?[e]:(i=n.charCodeAt(n.length-1),s=r.charCodeAt(0),(43===i||45===i)&&i===s||u.code.isIdentifierPartES5(i)&&u.code.isIdentifierPartES5(s)||47===i&&105===s?[e,U(),t]:u.code.isWhiteSpace(i)||u.code.isLineTerminator(i)||u.code.isWhiteSpace(s)||u.code.isLineTerminator(s)?[e,t]:[e,g,t])}function G(e){return[a,e]}function z(e){var t;t=a,e(a+=o),a=t}function $(e,t){if("Line"===e.type){if(I(e.value))return"//"+e.value;var n="//"+e.value;return b||(n+="\n"),n}return y.format.indent.adjustMultilineComment&&/[\n\r]/.test(e.value)?function(e,t){var n,r,i,s,o,c,p,l;for(n=e.split(/\r\n|[\r\n]/),c=Number.MAX_VALUE,r=1,i=n.length;r<i;++r){for(s=n[r],o=0;o<s.length&&u.code.isWhiteSpace(s.charCodeAt(o));)++o;c>o&&(c=o)}for(d(t)<"u"?(p=a,"*"===n[1][c]&&(t+=" "),a=t):(1&c&&--c,p=a),r=1,i=n.length;r<i;++r)l=V(G(n[r].slice(c))),n[r]=F?l.join(""):l;return a=p,n.join("\n")}("/*"+e.value+"*/",t):"/*"+e.value+"*/"}function H(e,n){var r,i,s,c,p,l,h,f,d,m,g,D;if(e.leadingComments&&e.leadingComments.length>0){if(c=n,b){for(n=[],f=(s=e.leadingComments[0]).extendedRange,d=s.range,(D=((g=v.substring(f[0],d[0])).match(/\n/g)||[]).length)>0?(n.push(P("\n",D)),n.push(G($(s)))):(n.push(g),n.push($(s))),m=d,r=1,i=e.leadingComments.length;r<i;r++)d=(s=e.leadingComments[r]).range,D=(v.substring(m[1],d[0]).match(/\n/g)||[]).length,n.push(P("\n",D)),n.push(G($(s))),m=d;D=(v.substring(d[1],f[1]).match(/\n/g)||[]).length,n.push(P("\n",D))}else for(s=e.leadingComments[0],n=[],C&&e.type===t.Program&&0===e.body.length&&n.push("\n"),n.push($(s)),I(V(n).toString())||n.push("\n"),r=1,i=e.leadingComments.length;r<i;++r)I(V(h=[$(s=e.leadingComments[r])]).toString())||h.push("\n"),n.push(G(h));n.push(G(c))}if(e.trailingComments)if(b)f=(s=e.trailingComments[0]).extendedRange,d=s.range,(D=((g=v.substring(f[0],d[0])).match(/\n/g)||[]).length)>0?(n.push(P("\n",D)),n.push(G($(s)))):(n.push(g),n.push($(s)));else for(p=!I(V(n).toString()),l=P(" ",function(e){var t;for(t=e.length-1;t>=0&&!u.code.isLineTerminator(e.charCodeAt(t));--t);return e.length-1-t}(V([a,n,o]).toString())),r=0,i=e.trailingComments.length;r<i;++r)s=e.trailingComments[r],p?(n=0===r?[n,o]:[n,l]).push($(s,l)):n=[n,G($(s))],r!==i-1&&!I(V(n).toString())&&(n=[n,"\n"]);return n}function X(e,t,n){var r,i=0;for(r=e;r<t;r++)"\n"===v[r]&&i++;for(r=1;r<i;r++)n.push(m)}function J(e,t,n){return t<n?["(",e,")"]:e}function Y(e){var t,n,r;for(t=1,n=(r=e.split(/\r\n|\n/)).length;t<n;t++)r[t]=m+a+r[t];return r}function K(){}function ee(e){return V(e.name,e)}function te(e,t){return e.async?"async"+(t?U():g):""}function ne(e){return e.generator&&!y.moz.starlessGenerator?"*"+g:""}function re(e){var t=e.value,n="";return t.async&&(n+=te(t,!e.computed)),t.generator&&(n+=ne(t)?"*":""),n}function ie(e){var t;if(t=new K,_(e))return t.generateStatement(e,1);if(function(e){return K.Expression.hasOwnProperty(e.type)}(e))return t.generateExpression(e,n.Sequence,7);throw new Error("Unknown node type: "+e.type)}K.prototype.maybeBlock=function(e,n){var r,i,s=this;return i=!y.comment||!e.leadingComments,e.type===t.BlockStatement&&i?[g,this.generateStatement(e,n)]:e.type===t.EmptyStatement&&i?";":(z((function(){r=[m,G(s.generateStatement(e,n))]})),r)},K.prototype.maybeBlockSuffix=function(e,n){var r=I(V(n).toString());return e.type!==t.BlockStatement||y.comment&&e.leadingComments||r?r?[n,a]:[n,m,a]:[n,g]},K.prototype.generatePattern=function(e,n,r){return e.type===t.Identifier?ee(e):this.generateExpression(e,n,r)},K.prototype.generateFunctionParams=function(e){var r,i,s,u;if(u=!1,e.type!==t.ArrowFunctionExpression||e.rest||e.defaults&&0!==e.defaults.length||1!==e.params.length||e.params[0].type!==t.Identifier){for((s=e.type===t.ArrowFunctionExpression?[te(e,!1)]:[]).push("("),e.defaults&&(u=!0),r=0,i=e.params.length;r<i;++r)u&&e.defaults[r]?s.push(this.generateAssignment(e.params[r],e.defaults[r],"=",n.Assignment,7)):s.push(this.generatePattern(e.params[r],n.Assignment,7)),r+1<i&&s.push(","+g);e.rest&&(e.params.length&&s.push(","+g),s.push("..."),s.push(ee(e.rest))),s.push(")")}else s=[te(e,!0),ee(e.params[0])];return s},K.prototype.generateFunctionBody=function(e){var r,i;return r=this.generateFunctionParams(e),e.type===t.ArrowFunctionExpression&&(r.push(g),r.push("=>")),e.expression?(r.push(g),"{"===(i=this.generateExpression(e.body,n.Assignment,7)).toString().charAt(0)&&(i=["(",i,")"]),r.push(i)):r.push(this.maybeBlock(e.body,9)),r},K.prototype.generateIterationForStatement=function(e,r,i){var s=["for"+(r.await?U()+"await":"")+g+"("],u=this;return z((function(){r.left.type===t.VariableDeclaration?z((function(){s.push(r.left.kind+U()),s.push(u.generateStatement(r.left.declarations[0],0))})):s.push(u.generateExpression(r.left,n.Call,7)),s=W(s,e),s=[W(s,u.generateExpression(r.right,n.Assignment,7)),")"]})),s.push(this.maybeBlock(r.body,i)),s},K.prototype.generatePropertyKey=function(e,t){var r=[];return t&&r.push("["),r.push(this.generateExpression(e,n.Assignment,7)),t&&r.push("]"),r},K.prototype.generateAssignment=function(e,t,r,i,s){return n.Assignment<i&&(s|=1),J([this.generateExpression(e,n.Call,s),g+r+g,this.generateExpression(t,n.Assignment,s)],n.Assignment,i)},K.prototype.semicolon=function(e){return!A&&e&w?"":";"},K.Statement={BlockStatement:function(e,t){var n,r,i=["{",m],s=this;return z((function(){var u,a,o,c;for(0===e.body.length&&b&&((n=e.range)[1]-n[0]>2&&("\n"===(r=v.substring(n[0]+1,n[1]-1))[0]&&(i=["{"]),i.push(r))),c=1,8&t&&(c|=16),u=0,a=e.body.length;u<a;++u)b&&(0===u&&(e.body[0].leadingComments&&(n=e.body[0].leadingComments[0].extendedRange,"\n"===(r=v.substring(n[0],n[1]))[0]&&(i=["{"])),e.body[0].leadingComments||X(e.range[0],e.body[0].range[0],i)),u>0&&!e.body[u-1].trailingComments&&!e.body[u].leadingComments&&X(e.body[u-1].range[1],e.body[u].range[0],i)),u===a-1&&(c|=w),o=e.body[u].leadingComments&&b?s.generateStatement(e.body[u],c):G(s.generateStatement(e.body[u],c)),i.push(o),I(V(o).toString())||b&&u<a-1&&e.body[u+1].leadingComments||i.push(m),b&&u===a-1&&(e.body[u].trailingComments||X(e.body[u].range[1],e.range[1],i))})),i.push(G("}")),i},BreakStatement:function(e,t){return e.label?"break "+e.label.name+this.semicolon(t):"break"+this.semicolon(t)},ContinueStatement:function(e,t){return e.label?"continue "+e.label.name+this.semicolon(t):"continue"+this.semicolon(t)},ClassBody:function(e,t){var r=["{",m],i=this;return z((function(t){var s,u;for(s=0,u=e.body.length;s<u;++s)r.push(t),r.push(i.generateExpression(e.body[s],n.Sequence,7)),s+1<u&&r.push(m)})),I(V(r).toString())||r.push(m),r.push(a),r.push("}"),r},ClassDeclaration:function(e,t){var r,i;return r=["class"],e.id&&(r=W(r,this.generateExpression(e.id,n.Sequence,7))),e.superClass&&(i=W("extends",this.generateExpression(e.superClass,n.Unary,7)),r=W(r,i)),r.push(g),r.push(this.generateStatement(e.body,k)),r},DirectiveStatement:function(e,t){return y.raw&&e.raw?e.raw+this.semicolon(t):function(e){var t,n,r,i;for(i="double"===h?'"':"'",t=0,n=e.length;t<n;++t){if(39===(r=e.charCodeAt(t))){i='"';break}if(34===r){i="'";break}92===r&&++t}return i+e+i}(e.directive)+this.semicolon(t)},DoWhileStatement:function(e,t){var r=W("do",this.maybeBlock(e.body,1));return W(r=this.maybeBlockSuffix(e.body,r),["while"+g+"(",this.generateExpression(e.test,n.Sequence,7),")"+this.semicolon(t)])},CatchClause:function(e,t){var r,i=this;return z((function(){var t;e.param?(r=["catch"+g+"(",i.generateExpression(e.param,n.Sequence,7),")"],e.guard&&(t=i.generateExpression(e.guard,n.Sequence,7),r.splice(2,0," if ",t))):r=["catch"]})),r.push(this.maybeBlock(e.body,1)),r},DebuggerStatement:function(e,t){return"debugger"+this.semicolon(t)},EmptyStatement:function(e,t){return";"},ExportDefaultDeclaration:function(e,t){var r,i=["export"];return r=t&w?k:1,i=W(i,"default"),i=_(e.declaration)?W(i,this.generateStatement(e.declaration,r)):W(i,this.generateExpression(e.declaration,n.Assignment,7)+this.semicolon(t))},ExportNamedDeclaration:function(e,r){var i,s=["export"],u=this;return i=r&w?k:1,e.declaration?W(s,this.generateStatement(e.declaration,i)):(e.specifiers&&(0===e.specifiers.length?s=W(s,"{"+g+"}"):e.specifiers[0].type===t.ExportBatchSpecifier?s=W(s,this.generateExpression(e.specifiers[0],n.Sequence,7)):(s=W(s,"{"),z((function(t){var r,i;for(s.push(m),r=0,i=e.specifiers.length;r<i;++r)s.push(t),s.push(u.generateExpression(e.specifiers[r],n.Sequence,7)),r+1<i&&s.push(","+m)})),I(V(s).toString())||s.push(m),s.push(a+"}")),e.source?s=W(s,["from"+g,this.generateExpression(e.source,n.Sequence,7),this.semicolon(r)]):s.push(this.semicolon(r))),s)},ExportAllDeclaration:function(e,t){return["export"+g,"*"+g,"from"+g,this.generateExpression(e.source,n.Sequence,7),this.semicolon(t)]},ExpressionStatement:function(e,r){var i,s,a,o;return 123===(s=V(i=[this.generateExpression(e.expression,n.Sequence,7)]).toString()).charCodeAt(0)||"class"===(a=s).slice(0,5)&&(123===(o=a.charCodeAt(5))||u.code.isWhiteSpace(o)||u.code.isLineTerminator(o))||function(e){var t;return"function"===e.slice(0,8)&&(40===(t=e.charCodeAt(8))||u.code.isWhiteSpace(t)||42===t||u.code.isLineTerminator(t))}(s)||function(e){var t,n,r;if("async"!==e.slice(0,5)||!u.code.isWhiteSpace(e.charCodeAt(5)))return!1;for(n=6,r=e.length;n<r&&u.code.isWhiteSpace(e.charCodeAt(n));++n);return n!==r&&"function"===e.slice(n,n+8)&&(40===(t=e.charCodeAt(n+8))||u.code.isWhiteSpace(t)||42===t||u.code.isLineTerminator(t))}(s)||E&&16&r&&e.expression.type===t.Literal&&"string"==typeof e.expression.value?i=["(",i,")"+this.semicolon(r)]:i.push(this.semicolon(r)),i},ImportDeclaration:function(e,r){var i,s,u=this;return 0===e.specifiers.length?["import",g,this.generateExpression(e.source,n.Sequence,7),this.semicolon(r)]:(i=["import"],s=0,e.specifiers[s].type===t.ImportDefaultSpecifier&&(i=W(i,[this.generateExpression(e.specifiers[s],n.Sequence,7)]),++s),e.specifiers[s]&&(0!==s&&i.push(","),e.specifiers[s].type===t.ImportNamespaceSpecifier?i=W(i,[g,this.generateExpression(e.specifiers[s],n.Sequence,7)]):(i.push(g+"{"),e.specifiers.length-s===1?(i.push(g),i.push(this.generateExpression(e.specifiers[s],n.Sequence,7)),i.push(g+"}"+g)):(z((function(t){var r,a;for(i.push(m),r=s,a=e.specifiers.length;r<a;++r)i.push(t),i.push(u.generateExpression(e.specifiers[r],n.Sequence,7)),r+1<a&&i.push(","+m)})),I(V(i).toString())||i.push(m),i.push(a+"}"+g)))),i=W(i,["from"+g,this.generateExpression(e.source,n.Sequence,7),this.semicolon(r)]))},VariableDeclarator:function(e,t){var r=1&t?7:6;return e.init?[this.generateExpression(e.id,n.Assignment,r),g,"=",g,this.generateExpression(e.init,n.Assignment,r)]:this.generatePattern(e.id,n.Assignment,r)},VariableDeclaration:function(e,t){var n,r,i,s,u,a=this;function o(){for(s=e.declarations[0],y.comment&&s.leadingComments?(n.push("\n"),n.push(G(a.generateStatement(s,u)))):(n.push(U()),n.push(a.generateStatement(s,u))),r=1,i=e.declarations.length;r<i;++r)s=e.declarations[r],y.comment&&s.leadingComments?(n.push(","+m),n.push(G(a.generateStatement(s,u)))):(n.push(","+g),n.push(a.generateStatement(s,u)))}return n=[e.kind],u=1&t?1:0,e.declarations.length>1?z(o):o(),n.push(this.semicolon(t)),n},ThrowStatement:function(e,t){return[W("throw",this.generateExpression(e.argument,n.Sequence,7)),this.semicolon(t)]},TryStatement:function(e,t){var n,r,i,s;if(n=["try",this.maybeBlock(e.block,1)],n=this.maybeBlockSuffix(e.block,n),e.handlers)for(r=0,i=e.handlers.length;r<i;++r)n=W(n,this.generateStatement(e.handlers[r],1)),(e.finalizer||r+1!==i)&&(n=this.maybeBlockSuffix(e.handlers[r].body,n));else{for(r=0,i=(s=e.guardedHandlers||[]).length;r<i;++r)n=W(n,this.generateStatement(s[r],1)),(e.finalizer||r+1!==i)&&(n=this.maybeBlockSuffix(s[r].body,n));if(e.handler)if(Array.isArray(e.handler))for(r=0,i=e.handler.length;r<i;++r)n=W(n,this.generateStatement(e.handler[r],1)),(e.finalizer||r+1!==i)&&(n=this.maybeBlockSuffix(e.handler[r].body,n));else n=W(n,this.generateStatement(e.handler,1)),e.finalizer&&(n=this.maybeBlockSuffix(e.handler.body,n))}return e.finalizer&&(n=W(n,["finally",this.maybeBlock(e.finalizer,1)])),n},SwitchStatement:function(e,t){var r,i,s,u,a,o=this;if(z((function(){r=["switch"+g+"(",o.generateExpression(e.discriminant,n.Sequence,7),")"+g+"{"+m]})),e.cases)for(a=1,s=0,u=e.cases.length;s<u;++s)s===u-1&&(a|=w),i=G(this.generateStatement(e.cases[s],a)),r.push(i),I(V(i).toString())||r.push(m);return r.push(G("}")),r},SwitchCase:function(e,r){var i,s,u,a,o,c=this;return z((function(){for(i=e.test?[W("case",c.generateExpression(e.test,n.Sequence,7)),":"]:["default:"],u=0,(a=e.consequent.length)&&e.consequent[0].type===t.BlockStatement&&(s=c.maybeBlock(e.consequent[0],1),i.push(s),u=1),u!==a&&!I(V(i).toString())&&i.push(m),o=1;u<a;++u)u===a-1&&r&w&&(o|=w),s=G(c.generateStatement(e.consequent[u],o)),i.push(s),u+1!==a&&!I(V(s).toString())&&i.push(m)})),i},IfStatement:function(e,r){var i,s,u=this;return z((function(){i=["if"+g+"(",u.generateExpression(e.test,n.Sequence,7),")"]})),s=1,r&w&&(s|=w),e.alternate?(i.push(this.maybeBlock(e.consequent,1)),i=this.maybeBlockSuffix(e.consequent,i),i=e.alternate.type===t.IfStatement?W(i,["else ",this.generateStatement(e.alternate,s)]):W(i,W("else",this.maybeBlock(e.alternate,s)))):i.push(this.maybeBlock(e.consequent,s)),i},ForStatement:function(e,r){var i,s=this;return z((function(){i=["for"+g+"("],e.init?e.init.type===t.VariableDeclaration?i.push(s.generateStatement(e.init,0)):(i.push(s.generateExpression(e.init,n.Sequence,6)),i.push(";")):i.push(";"),e.test&&(i.push(g),i.push(s.generateExpression(e.test,n.Sequence,7))),i.push(";"),e.update&&(i.push(g),i.push(s.generateExpression(e.update,n.Sequence,7))),i.push(")")})),i.push(this.maybeBlock(e.body,r&w?k:1)),i},ForInStatement:function(e,t){return this.generateIterationForStatement("in",e,t&w?k:1)},ForOfStatement:function(e,t){return this.generateIterationForStatement("of",e,t&w?k:1)},LabeledStatement:function(e,t){return[e.label.name+":",this.maybeBlock(e.body,t&w?k:1)]},Program:function(e,t){var n,r,i,s,u;for(s=e.body.length,n=[C&&s>0?"\n":""],u=17,i=0;i<s;++i)!C&&i===s-1&&(u|=w),b&&(0===i&&(e.body[0].leadingComments||X(e.range[0],e.body[i].range[0],n)),i>0&&!e.body[i-1].trailingComments&&!e.body[i].leadingComments&&X(e.body[i-1].range[1],e.body[i].range[0],n)),r=G(this.generateStatement(e.body[i],u)),n.push(r),i+1<s&&!I(V(r).toString())&&(b&&e.body[i+1].leadingComments||n.push(m)),b&&i===s-1&&(e.body[i].trailingComments||X(e.body[i].range[1],e.range[1],n));return n},FunctionDeclaration:function(e,t){return[te(e,!0),"function",ne(e)||U(),e.id?ee(e.id):"",this.generateFunctionBody(e)]},ReturnStatement:function(e,t){return e.argument?[W("return",this.generateExpression(e.argument,n.Sequence,7)),this.semicolon(t)]:["return"+this.semicolon(t)]},WhileStatement:function(e,t){var r,i=this;return z((function(){r=["while"+g+"(",i.generateExpression(e.test,n.Sequence,7),")"]})),r.push(this.maybeBlock(e.body,t&w?k:1)),r},WithStatement:function(e,t){var r,i=this;return z((function(){r=["with"+g+"(",i.generateExpression(e.object,n.Sequence,7),")"]})),r.push(this.maybeBlock(e.body,t&w?k:1)),r}},T(K.prototype,K.Statement),K.Expression={SequenceExpression:function(e,t,r){var i,s,u;for(n.Sequence<t&&(r|=1),i=[],s=0,u=e.expressions.length;s<u;++s)i.push(this.generateExpression(e.expressions[s],n.Assignment,r)),s+1<u&&i.push(","+g);return J(i,n.Sequence,t)},AssignmentExpression:function(e,t,n){return this.generateAssignment(e.left,e.right,e.operator,t,n)},ArrowFunctionExpression:function(e,t,r){return J(this.generateFunctionBody(e),n.ArrowFunction,t)},ConditionalExpression:function(e,t,r){return n.Conditional<t&&(r|=1),J([this.generateExpression(e.test,n.Coalesce,r),g+"?"+g,this.generateExpression(e.consequent,n.Assignment,r),g+":"+g,this.generateExpression(e.alternate,n.Assignment,r)],n.Conditional,t)},LogicalExpression:function(e,t,n){return"??"===e.operator&&(n|=64),this.BinaryExpression(e,t,n)},BinaryExpression:function(e,t,i){var s,a,o,c,p,l;return c=r[e.operator],a="**"===e.operator?n.Postfix:c,o="**"===e.operator?c:c+1,c<t&&(i|=1),s=47===(l=(p=this.generateExpression(e.left,a,i)).toString()).charCodeAt(l.length-1)&&u.code.isIdentifierPartES5(e.operator.charCodeAt(0))?[p,U(),e.operator]:W(p,e.operator),p=this.generateExpression(e.right,o,i),"/"===e.operator&&"/"===p.toString().charAt(0)||"<"===e.operator.slice(-1)&&"!--"===p.toString().slice(0,3)?(s.push(U()),s.push(p)):s=W(s,p),"in"!==e.operator||1&i?("||"===e.operator||"&&"===e.operator)&&64&i?["(",s,")"]:J(s,c,t):["(",s,")"]},CallExpression:function(e,t,r){var i,s,u;for(i=[this.generateExpression(e.callee,n.Call,3)],e.optional&&i.push("?."),i.push("("),s=0,u=e.arguments.length;s<u;++s)i.push(this.generateExpression(e.arguments[s],n.Assignment,7)),s+1<u&&i.push(","+g);return i.push(")"),2&r?J(i,n.Call,t):["(",i,")"]},ChainExpression:function(e,t,r){return n.OptionalChaining<t&&(r|=2),J(this.generateExpression(e.expression,n.OptionalChaining,r),n.OptionalChaining,t)},NewExpression:function(e,t,r){var i,s,u,a,o;if(s=e.arguments.length,o=4&r&&!D&&0===s?5:1,i=W("new",this.generateExpression(e.callee,n.New,o)),!(4&r)||D||s>0){for(i.push("("),u=0,a=s;u<a;++u)i.push(this.generateExpression(e.arguments[u],n.Assignment,7)),u+1<a&&i.push(","+g);i.push(")")}return J(i,n.New,t)},MemberExpression:function(e,r,i){var s,a;return s=[this.generateExpression(e.object,n.Call,2&i?3:1)],e.computed?(e.optional&&s.push("?."),s.push("["),s.push(this.generateExpression(e.property,n.Sequence,2&i?7:5)),s.push("]")):(!e.optional&&e.object.type===t.Literal&&"number"==typeof e.object.value&&((a=V(s).toString()).indexOf(".")<0&&!/[eExX]/.test(a)&&u.code.isDecimalDigit(a.charCodeAt(a.length-1))&&!(a.length>=2&&48===a.charCodeAt(0))&&s.push(" ")),s.push(e.optional?"?.":"."),s.push(ee(e.property))),J(s,n.Member,r)},MetaProperty:function(e,t,r){var i;return(i=[]).push("string"==typeof e.meta?e.meta:ee(e.meta)),i.push("."),i.push("string"==typeof e.property?e.property:ee(e.property)),J(i,n.Member,t)},UnaryExpression:function(e,t,r){var i,s,a,o,c;return s=this.generateExpression(e.argument,n.Unary,7),""===g?i=W(e.operator,s):(i=[e.operator],e.operator.length>2?i=W(i,s):(c=(o=V(i).toString()).charCodeAt(o.length-1),a=s.toString().charCodeAt(0),((43===c||45===c)&&c===a||u.code.isIdentifierPartES5(c)&&u.code.isIdentifierPartES5(a))&&i.push(U()),i.push(s))),J(i,n.Unary,t)},YieldExpression:function(e,t,r){var i;return i=e.delegate?"yield*":"yield",e.argument&&(i=W(i,this.generateExpression(e.argument,n.Yield,7))),J(i,n.Yield,t)},AwaitExpression:function(e,t,r){return J(W(e.all?"await*":"await",this.generateExpression(e.argument,n.Await,7)),n.Await,t)},UpdateExpression:function(e,t,r){return e.prefix?J([e.operator,this.generateExpression(e.argument,n.Unary,7)],n.Unary,t):J([this.generateExpression(e.argument,n.Postfix,7),e.operator],n.Postfix,t)},FunctionExpression:function(e,t,n){var r=[te(e,!0),"function"];return e.id?(r.push(ne(e)||U()),r.push(ee(e.id))):r.push(ne(e)||g),r.push(this.generateFunctionBody(e)),r},ArrayPattern:function(e,t,n){return this.ArrayExpression(e,t,n,!0)},ArrayExpression:function(e,t,r,i){var s,u,o=this;return e.elements.length?(u=!i&&e.elements.length>1,s=["[",u?m:""],z((function(t){var r,i;for(r=0,i=e.elements.length;r<i;++r)e.elements[r]?(s.push(u?t:""),s.push(o.generateExpression(e.elements[r],n.Assignment,7))):(u&&s.push(t),r+1===i&&s.push(",")),r+1<i&&s.push(","+(u?m:g))})),u&&!I(V(s).toString())&&s.push(m),s.push(u?a:""),s.push("]"),s):"[]"},RestElement:function(e,t,n){return"..."+this.generatePattern(e.argument)},ClassExpression:function(e,t,r){var i,s;return i=["class"],e.id&&(i=W(i,this.generateExpression(e.id,n.Sequence,7))),e.superClass&&(s=W("extends",this.generateExpression(e.superClass,n.Unary,7)),i=W(i,s)),i.push(g),i.push(this.generateStatement(e.body,k)),i},MethodDefinition:function(e,t,n){var r,i;return r=e.static?["static"+g]:[],i="get"===e.kind||"set"===e.kind?[W(e.kind,this.generatePropertyKey(e.key,e.computed)),this.generateFunctionBody(e.value)]:[re(e),this.generatePropertyKey(e.key,e.computed),this.generateFunctionBody(e.value)],W(r,i)},Property:function(e,t,r){return"get"===e.kind||"set"===e.kind?[e.kind,U(),this.generatePropertyKey(e.key,e.computed),this.generateFunctionBody(e.value)]:e.shorthand?"AssignmentPattern"===e.value.type?this.AssignmentPattern(e.value,n.Sequence,7):this.generatePropertyKey(e.key,e.computed):e.method?[re(e),this.generatePropertyKey(e.key,e.computed),this.generateFunctionBody(e.value)]:[this.generatePropertyKey(e.key,e.computed),":"+g,this.generateExpression(e.value,n.Assignment,7)]},ObjectExpression:function(e,t,r){var i,s,u,o,c=this;return e.properties.length?(i=e.properties.length>1,z((function(){u=c.generateExpression(e.properties[0],n.Sequence,7)})),i||(o=V(u).toString(),/[\r\n]/g.test(o))?(z((function(t){var r,a;if(s=["{",m,t,u],i)for(s.push(","+m),r=1,a=e.properties.length;r<a;++r)s.push(t),s.push(c.generateExpression(e.properties[r],n.Sequence,7)),r+1<a&&s.push(","+m)})),I(V(s).toString())||s.push(m),s.push(a),s.push("}"),s):["{",g,u,g,"}"]):"{}"},AssignmentPattern:function(e,t,n){return this.generateAssignment(e.left,e.right,"=",t,n)},ObjectPattern:function(e,r,i){var s,u,o,c,p,l=this;if(!e.properties.length)return"{}";if(c=!1,1===e.properties.length)(p=e.properties[0]).type===t.Property&&p.value.type!==t.Identifier&&(c=!0);else for(u=0,o=e.properties.length;u<o;++u)if((p=e.properties[u]).type===t.Property&&!p.shorthand){c=!0;break}return s=["{",c?m:""],z((function(t){var r,i;for(r=0,i=e.properties.length;r<i;++r)s.push(c?t:""),s.push(l.generateExpression(e.properties[r],n.Sequence,7)),r+1<i&&s.push(","+(c?m:g))})),c&&!I(V(s).toString())&&s.push(m),s.push(c?a:""),s.push("}"),s},ThisExpression:function(e,t,n){return"this"},Super:function(e,t,n){return"super"},Identifier:function(e,t,n){return ee(e)},ImportDefaultSpecifier:function(e,t,n){return ee(e.id||e.local)},ImportNamespaceSpecifier:function(e,t,n){var r=["*"],i=e.id||e.local;return i&&r.push(g+"as"+U()+ee(i)),r},ImportSpecifier:function(e,t,n){var r=e.imported,i=[r.name],s=e.local;return s&&s.name!==r.name&&i.push(U()+"as"+U()+ee(s)),i},ExportSpecifier:function(e,t,n){var r=e.local,i=[r.name],s=e.exported;return s&&s.name!==r.name&&i.push(U()+"as"+U()+ee(s)),i},Literal:function(e,n,r){var i;if(e.hasOwnProperty("raw")&&x&&y.raw)try{if((i=x(e.raw).body[0].expression).type===t.Literal&&i.value===e.value)return e.raw}catch(s){}return e.regex?"/"+e.regex.pattern+"/"+e.regex.flags:"bigint"==typeof e.value?e.value.toString()+"n":e.bigint?e.bigint+"n":null===e.value?"null":"string"==typeof e.value?function(e){var t,n,r,i,s,a="",o=0,p=0;for(t=0,n=e.length;t<n;++t){if(39===(r=e.charCodeAt(t)))++o;else if(34===r)++p;else if(47===r&&c)a+="\\";else{if(u.code.isLineTerminator(r)||92===r){a+=j(r);continue}if(!u.code.isIdentifierPartES5(r)&&(c&&r<32||!c&&!f&&(r<32||r>126))){a+=N(r,e.charCodeAt(t+1));continue}}a+=String.fromCharCode(r)}if(s=(i=!("double"===h||"auto"===h&&p<o))?"'":'"',!(i?o:p))return s+a+s;for(e=a,a=s,t=0,n=e.length;t<n;++t)(39===(r=e.charCodeAt(t))&&i||34===r&&!i)&&(a+="\\"),a+=String.fromCharCode(r);return a+s}(e.value):"number"==typeof e.value?function(e){var t,n,r,i,s;if(e!=e)throw new Error("Numeric literal whose value is NaN");if(e<0||0===e&&1/e<0)throw new Error("Numeric literal whose value is negative");if(e===1/0)return c?"null":p?"1e400":"1e+400";if(t=""+e,!p||t.length<3)return t;for(n=t.indexOf("."),!c&&48===t.charCodeAt(0)&&1===n&&(n=0,t=t.slice(1)),r=t,t=t.replace("e+","e"),i=0,(s=r.indexOf("e"))>0&&(i=+r.slice(s+1),r=r.slice(0,s)),n>=0&&(i-=r.length-n-1,r=+(r.slice(0,n)+r.slice(n+1))+""),s=0;48===r.charCodeAt(r.length+s-1);)--s;return 0!==s&&(i-=s,r=r.slice(0,s)),0!==i&&(r+="e"+i),(r.length<t.length||l&&e>1e12&&Math.floor(e)===e&&(r="0x"+e.toString(16)).length<t.length)&&+r===e&&(t=r),t}(e.value):"boolean"==typeof e.value?e.value?"true":"false":function(e){var t,n,r,i,s,u,a,o;if(n=e.toString(),e.source){if(!(t=n.match(/\/([^/]*)$/)))return n;for(r=t[1],n="",a=!1,o=!1,i=0,s=e.source.length;i<s;++i)u=e.source.charCodeAt(i),o?(n+=O(u,o),o=!1):(a?93===u&&(a=!1):47===u?n+="\\":91===u&&(a=!0),n+=O(u,o),o=92===u);return"/"+n+"/"+r}return n}(e.value)},GeneratorExpression:function(e,t,n){return this.ComprehensionExpression(e,t,n)},ComprehensionExpression:function(e,r,i){var s,u,a,o,c=this;return s=e.type===t.GeneratorExpression?["("]:["["],y.moz.comprehensionExpressionStartsWithAssignment&&(o=this.generateExpression(e.body,n.Assignment,7),s.push(o)),e.blocks&&z((function(){for(u=0,a=e.blocks.length;u<a;++u)o=c.generateExpression(e.blocks[u],n.Sequence,7),u>0||y.moz.comprehensionExpressionStartsWithAssignment?s=W(s,o):s.push(o)})),e.filter&&(s=W(s,"if"+g),o=this.generateExpression(e.filter,n.Sequence,7),s=W(s,["(",o,")"])),y.moz.comprehensionExpressionStartsWithAssignment||(o=this.generateExpression(e.body,n.Assignment,7),s=W(s,o)),s.push(e.type===t.GeneratorExpression?")":"]"),s},ComprehensionBlock:function(e,r,i){var s;return s=W(s=e.left.type===t.VariableDeclaration?[e.left.kind,U(),this.generateStatement(e.left.declarations[0],0)]:this.generateExpression(e.left,n.Call,7),e.of?"of":"in"),s=W(s,this.generateExpression(e.right,n.Sequence,7)),["for"+g+"(",s,")"]},SpreadElement:function(e,t,r){return["...",this.generateExpression(e.argument,n.Assignment,7)]},TaggedTemplateExpression:function(e,t,r){var i=3;return 2&r||(i=1),J([this.generateExpression(e.tag,n.Call,i),this.generateExpression(e.quasi,n.Primary,4)],n.TaggedTemplate,t)},TemplateElement:function(e,t,n){return e.value.raw},TemplateLiteral:function(e,t,r){var i,s,u;for(i=["`"],s=0,u=e.quasis.length;s<u;++s)i.push(this.generateExpression(e.quasis[s],n.Primary,7)),s+1<u&&(i.push("${"+g),i.push(this.generateExpression(e.expressions[s],n.Sequence,7)),i.push(g+"}"));return i.push("`"),i},ModuleSpecifier:function(e,t,n){return this.Literal(e,t,n)},ImportExpression:function(e,t,r){return J(["import(",this.generateExpression(e.source,n.Assignment,7),")"],n.Call,t)}},T(K.prototype,K.Expression),K.prototype.generateExpression=function(e,r,i){var s,u;return u=e.type||t.Property,y.verbatim&&e.hasOwnProperty(y.verbatim)?function(e,t){var r,i;return i="string"==typeof(r=e[y.verbatim])?J(Y(r),n.Sequence,t):J(i=Y(r.content),null!=r.precedence?r.precedence:n.Sequence,t),V(i,e)}(e,r):(s=this[u](e,r,i),y.comment&&(s=H(e,s)),V(s,e))},K.prototype.generateStatement=function(e,n){var r,i;return r=this[e.type](e,n),y.comment&&(r=H(e,r)),i=V(r).toString(),e.type===t.Program&&!C&&""===m&&"\n"===i.charAt(i.length-1)&&(r=F?V(r).replaceRight(/\s+$/,""):i.replace(/\s+$/,"")),V(r,e)},S={indent:{style:"",base:0},renumber:!0,hexadecimal:!0,quotes:"auto",escapeless:!0,compact:!0,parentheses:!1,semicolons:!1},B={indent:{style:"    ",base:0,adjustMultilineComment:!1},newline:"\n",space:" ",json:!1,renumber:!1,hexadecimal:!1,quotes:"single",escapeless:!1,compact:!1,parentheses:!0,semicolons:!0,safeConcatenation:!1,preserveBlankLines:!1},e.version=Z().version,e.generate=function(t,n){var r,s,u={indent:null,base:null,parse:null,comment:!1,format:{indent:{style:"    ",base:0,adjustMultilineComment:!1},newline:"\n",space:" ",json:!1,renumber:!1,hexadecimal:!1,quotes:"single",escapeless:!1,compact:!1,parentheses:!0,semicolons:!0,safeConcatenation:!1,preserveBlankLines:!1},moz:{comprehensionExpressionStartsWithAssignment:!1,starlessGenerator:!1},sourceMap:null,sourceMapRoot:null,sourceMapWithCode:!1,directive:!1,raw:!0,verbatim:null,sourceCode:null};return null!=n?("string"==typeof n.indent&&(u.format.indent.style=n.indent),"number"==typeof n.base&&(u.format.indent.base=n.base),n=L(u,n),o=n.format.indent.style,a="string"==typeof n.base?n.base:P(o,n.format.indent.base)):(o=(n=u).format.indent.style,a=P(o,n.format.indent.base)),c=n.format.json,p=n.format.renumber,l=!c&&n.format.hexadecimal,h=c?"double":n.format.quotes,f=n.format.escapeless,m=n.format.newline,g=n.format.space,n.format.compact&&(m=g=o=a=""),D=n.format.parentheses,A=n.format.semicolons,C=n.format.safeConcatenation,E=n.directive,x=c?null:n.parse,F=n.sourceMap,v=n.sourceCode,b=n.format.preserveBlankLines&&null!==v,y=n,F&&(i=e.browser?global.sourceMap.SourceNode:Q().SourceNode),r=ie(t),F?(s=r.toStringWithSourceMap({file:n.file,sourceRoot:n.sourceMapRoot}),n.sourceContent&&s.map.setSourceContent(n.sourceMap,n.sourceContent),n.sourceMapWithCode?s:s.map.toString()):(s={code:r.toString(),map:null},n.sourceMapWithCode?s:s.code)},e.attachComments=s.attachComments,e.Precedence=L({},n),e.browser=!1,e.FORMAT_MINIFY=S,e.FORMAT_DEFAULTS=B}()}}),te={};function ne(e,t){for(var n=65536,r=0;r<t.length;r+=2){if((n+=t[r])>e)return!1;if((n+=t[r+1])>=e)return!0}}function re(e,t){return e<65?36===e:e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&Ne.test(String.fromCharCode(e)):!1!==t&&ne(e,je)))}function ie(e,t){return e<48?36===e:e<58||!(e<65)&&(e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&Re.test(String.fromCharCode(e)):!1!==t&&(ne(e,je)||ne(e,Me)))))}function se(e,t){return new Ve(e,{beforeExpr:!0,binop:t})}function ue(e,t){return void 0===t&&(t={}),t.keyword=e,We[e]=new Ve(e,t)}function ae(e,t){return 10===e||13===e||!t&&(8232===e||8233===e)}function oe(e,t){return Ye.call(e,t)}function ce(e){return new RegExp("^(?:"+e.replace(/ /g,"|")+")$")}function pe(e,t){for(var n=1,r=0;;){$e.lastIndex=r;var i=$e.exec(e);if(!(i&&i.index<t))return new Ze(n,t-r);++n,r=i.index+i[0].length}}function le(e){var t={};for(var n in tt)t[n]=e&&oe(e,n)?e[n]:tt[n];if(t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),null==t.allowReserved&&(t.allowReserved=t.ecmaVersion<5),Qe(t.onToken)){var r=t.onToken;t.onToken=function(e){return r.push(e)}}return Qe(t.onComment)&&(t.onComment=function(e,t){return function(n,r,i,s,u,a){var o={type:n?"Block":"Line",value:r,start:i,end:s};e.locations&&(o.loc=new et(this,u,a)),e.ranges&&(o.range=[i,s]),t.push(o)}}(t,t.onComment)),t}function he(e,t){return nt|(e?it:0)|(t?st:0)}function fe(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1}function de(e,t,n,r){return e.type=t,e.end=n,this.options.locations&&(e.loc.end=r),this.options.ranges&&(e.range[1]=n),e}function me(e){var t=Lt[e]={binary:ce(wt[e]+" "+kt),nonBinary:{General_Category:ce(kt),Script:ce(Tt[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script,t.nonBinary.gc=t.nonBinary.General_Category,t.nonBinary.sc=t.nonBinary.Script,t.nonBinary.scx=t.nonBinary.Script_Extensions}function ge(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e)))}function De(e){return 36===e||e>=40&&e<=43||46===e||63===e||e>=91&&e<=94||e>=123&&e<=125}function Ae(e){return e>=65&&e<=90||e>=97&&e<=122}function Ce(e){return Ae(e)||95===e}function Ee(e){return Ce(e)||ye(e)}function ye(e){return e>=48&&e<=57}function xe(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function Fe(e){return e>=65&&e<=70?e-65+10:e>=97&&e<=102?e-97+10:e-48}function ve(e){return e>=48&&e<=55}function be(e){return"function"!=typeof BigInt?null:BigInt(e.replace(/_/g,""))}function Se(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e)))}function Be(e,t){return ut.parse(e,t)}function _e(e,t,n){return ut.parseExpressionAt(e,t,n)}function we(e,t){return ut.tokenizer(e,t)}h(te,{Node:function(){return xt},Parser:function(){return ut},Position:function(){return Ze},SourceLocation:function(){return et},TokContext:function(){return vt},Token:function(){return Rt},TokenType:function(){return Ve},defaultOptions:function(){return tt},getLineInfo:function(){return pe},isIdentifierChar:function(){return ie},isIdentifierStart:function(){return re},isNewLine:function(){return ae},keywordTypes:function(){return We},lineBreak:function(){return ze},lineBreakG:function(){return $e},nonASCIIwhitespace:function(){return He},parse:function(){return Be},parseExpressionAt:function(){return _e},tokContexts:function(){return bt},tokTypes:function(){return Ge},tokenizer:function(){return we},version:function(){return Vt}});var ke,Pe,Ie,Te,Le,Oe,Ne,Re,je,Me,Ve,qe,Ue,We,Ge,ze,$e,He,Xe,Je,Ye,Ke,Qe,Ze,et,tt,nt,rt,it,st,ut,at,ot,ct,pt,lt,ht,ft,dt,mt,gt,Dt,At,Ct,Et,yt,xt,Ft,vt,bt,St,Bt,_t,wt,kt,Pt,It,Tt,Lt,Ot,Nt,Rt,jt,Mt,Vt,qt=c({"../../node_modules/acorn/dist/acorn.mjs":function(){ke={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},Ie={5:Pe="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this","5module":Pe+" export import",6:Pe+" const class extends export import super"},Te=/^in(stanceof)?$/,Le="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࣇऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-鿼ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞿꟂ-ꟊꟵ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",Oe="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿᫀᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷹᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿",Ne=new RegExp("["+Le+"]"),Re=new RegExp("["+Le+Oe+"]"),Le=Oe=null,je=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,107,20,28,22,13,52,76,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,230,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,35,56,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8952,286,50,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,2357,44,11,6,17,0,370,43,1301,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42717,35,4148,12,221,3,5761,15,7472,3104,541,1507,4938],Me=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,176,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,135,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,419,13,1495,6,110,6,6,9,4759,9,787719,239],qe={beforeExpr:!0},We={},Ge={num:new(Ve=function(e,t){void 0===t&&(t={}),this.label=e,this.keyword=t.keyword,this.beforeExpr=!!t.beforeExpr,this.startsExpr=!!t.startsExpr,this.isLoop=!!t.isLoop,this.isAssign=!!t.isAssign,this.prefix=!!t.prefix,this.postfix=!!t.postfix,this.binop=t.binop||null,this.updateContext=null})("num",Ue={startsExpr:!0}),regexp:new Ve("regexp",Ue),string:new Ve("string",Ue),name:new Ve("name",Ue),eof:new Ve("eof"),bracketL:new Ve("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new Ve("]"),braceL:new Ve("{",{beforeExpr:!0,startsExpr:!0}),braceR:new Ve("}"),parenL:new Ve("(",{beforeExpr:!0,startsExpr:!0}),parenR:new Ve(")"),comma:new Ve(",",qe),semi:new Ve(";",qe),colon:new Ve(":",qe),dot:new Ve("."),question:new Ve("?",qe),questionDot:new Ve("?."),arrow:new Ve("=>",qe),template:new Ve("template"),invalidTemplate:new Ve("invalidTemplate"),ellipsis:new Ve("...",qe),backQuote:new Ve("`",Ue),dollarBraceL:new Ve("${",{beforeExpr:!0,startsExpr:!0}),eq:new Ve("=",{beforeExpr:!0,isAssign:!0}),assign:new Ve("_=",{beforeExpr:!0,isAssign:!0}),incDec:new Ve("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new Ve("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:se("||",1),logicalAND:se("&&",2),bitwiseOR:se("|",3),bitwiseXOR:se("^",4),bitwiseAND:se("&",5),equality:se("==/!=/===/!==",6),relational:se("</>/<=/>=",7),bitShift:se("<</>>/>>>",8),plusMin:new Ve("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:se("%",10),star:se("*",10),slash:se("/",10),starstar:new Ve("**",{beforeExpr:!0}),coalesce:se("??",1),_break:ue("break"),_case:ue("case",qe),_catch:ue("catch"),_continue:ue("continue"),_debugger:ue("debugger"),_default:ue("default",qe),_do:ue("do",{isLoop:!0,beforeExpr:!0}),_else:ue("else",qe),_finally:ue("finally"),_for:ue("for",{isLoop:!0}),_function:ue("function",Ue),_if:ue("if"),_return:ue("return",qe),_switch:ue("switch"),_throw:ue("throw",qe),_try:ue("try"),_var:ue("var"),_const:ue("const"),_while:ue("while",{isLoop:!0}),_with:ue("with"),_new:ue("new",{beforeExpr:!0,startsExpr:!0}),_this:ue("this",Ue),_super:ue("super",Ue),_class:ue("class",Ue),_extends:ue("extends",qe),_export:ue("export"),_import:ue("import",Ue),_null:ue("null",Ue),_true:ue("true",Ue),_false:ue("false",Ue),_in:ue("in",{beforeExpr:!0,binop:7}),_instanceof:ue("instanceof",{beforeExpr:!0,binop:7}),_typeof:ue("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:ue("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:ue("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},ze=/\r\n?|\n|\u2028|\u2029/,$e=new RegExp(ze.source,"g"),He=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,Xe=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,Je=Object.prototype,Ye=Je.hasOwnProperty,Ke=Je.toString,Qe=Array.isArray||function(e){return"[object Array]"===Ke.call(e)},(Ze=function(e,t){this.line=e,this.column=t}).prototype.offset=function(e){return new Ze(this.line,this.column+e)},et=function(e,t,n){this.start=t,this.end=n,null!==e.sourceFile&&(this.source=e.sourceFile)},tt={ecmaVersion:10,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:!1,allowHashBang:!1,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1},rt=1|(nt=2),it=4,st=8,at={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0}},(ut=function(e,t,n){this.options=e=le(e),this.sourceFile=e.sourceFile,this.keywords=ce(Ie[e.ecmaVersion>=6?6:"module"===e.sourceType?"5module":5]);var r="";if(!0!==e.allowReserved){for(var i=e.ecmaVersion;!(r=ke[i]);i--);"module"===e.sourceType&&(r+=" await")}this.reservedWords=ce(r);var s=(r?r+" ":"")+ke.strict;this.reservedWordsStrict=ce(s),this.reservedWordsStrictBind=ce(s+" "+ke.strictBind),this.input=String(t),this.containsEsc=!1,n?(this.pos=n,this.lineStart=this.input.lastIndexOf("\n",n-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(ze).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=Ge.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule="module"===e.sourceType,this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports={},0===this.pos&&e.allowHashBang&&"#!"===this.input.slice(0,2)&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(1),this.regexpState=null}).prototype.parse=function(){var e=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(e)},at.inFunction.get=function(){return(this.currentVarScope().flags&nt)>0},at.inGenerator.get=function(){return(this.currentVarScope().flags&st)>0},at.inAsync.get=function(){return(this.currentVarScope().flags&it)>0},at.allowSuper.get=function(){return(64&this.currentThisScope().flags)>0},at.allowDirectSuper.get=function(){return(128&this.currentThisScope().flags)>0},at.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},ut.prototype.inNonArrowFunction=function(){return(this.currentThisScope().flags&nt)>0},ut.extend=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];for(var n=this,r=0;r<e.length;r++)n=e[r](n);return n},ut.parse=function(e,t){return new this(t,e).parse()},ut.parseExpressionAt=function(e,t,n){var r=new this(n,e,t);return r.nextToken(),r.parseExpression()},ut.tokenizer=function(e,t){return new this(t,e)},Object.defineProperties(ut.prototype,at),ot=ut.prototype,ct=/^(?:'((?:\\.|[^'\\])*?)'|"((?:\\.|[^"\\])*?)")/,ot.strictDirective=function(e){for(;;){Xe.lastIndex=e,e+=Xe.exec(this.input)[0].length;var t=ct.exec(this.input.slice(e));if(!t)return!1;if("use strict"===(t[1]||t[2])){Xe.lastIndex=e+t[0].length;var n=Xe.exec(this.input),r=n.index+n[0].length,i=this.input.charAt(r);return";"===i||"}"===i||ze.test(n[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(i)||"!"===i&&"="===this.input.charAt(r+1))}e+=t[0].length,Xe.lastIndex=e,e+=Xe.exec(this.input)[0].length,";"===this.input[e]&&e++}},ot.eat=function(e){return this.type===e&&(this.next(),!0)},ot.isContextual=function(e){return this.type===Ge.name&&this.value===e&&!this.containsEsc},ot.eatContextual=function(e){return!!this.isContextual(e)&&(this.next(),!0)},ot.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},ot.canInsertSemicolon=function(){return this.type===Ge.eof||this.type===Ge.braceR||ze.test(this.input.slice(this.lastTokEnd,this.start))},ot.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},ot.semicolon=function(){!this.eat(Ge.semi)&&!this.insertSemicolon()&&this.unexpected()},ot.afterTrailingComma=function(e,t){if(this.type===e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},ot.expect=function(e){this.eat(e)||this.unexpected()},ot.unexpected=function(e){this.raise(null!=e?e:this.start,"Unexpected token")},ot.checkPatternErrors=function(e,t){if(e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var n=t?e.parenthesizedAssign:e.parenthesizedBind;n>-1&&this.raiseRecoverable(n,"Parenthesized pattern")}},ot.checkExpressionErrors=function(e,t){if(!e)return!1;var n=e.shorthandAssign,r=e.doubleProto;if(!t)return n>=0||r>=0;n>=0&&this.raise(n,"Shorthand property assignments are valid only in destructuring patterns"),r>=0&&this.raiseRecoverable(r,"Redefinition of __proto__ property")},ot.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},ot.isSimpleAssignTarget=function(e){return"ParenthesizedExpression"===e.type?this.isSimpleAssignTarget(e.expression):"Identifier"===e.type||"MemberExpression"===e.type},(pt=ut.prototype).parseTopLevel=function(e){var t={};for(e.body||(e.body=[]);this.type!==Ge.eof;){var n=this.parseStatement(null,!0,t);e.body.push(n)}if(this.inModule)for(var r=0,i=Object.keys(this.undefinedExports);r<i.length;r+=1){var s=i[r];this.raiseRecoverable(this.undefinedExports[s].start,"Export '"+s+"' is not defined")}return this.adaptDirectivePrologue(e.body),this.next(),e.sourceType=this.options.sourceType,this.finishNode(e,"Program")},lt={kind:"loop"},ht={kind:"switch"},pt.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;Xe.lastIndex=this.pos;var t=Xe.exec(this.input),n=this.pos+t[0].length,r=this.input.charCodeAt(n);if(91===r)return!0;if(e)return!1;if(123===r)return!0;if(re(r,!0)){for(var i=n+1;ie(this.input.charCodeAt(i),!0);)++i;var s=this.input.slice(n,i);if(!Te.test(s))return!0}return!1},pt.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;Xe.lastIndex=this.pos;var e=Xe.exec(this.input),t=this.pos+e[0].length;return!(ze.test(this.input.slice(this.pos,t))||"function"!==this.input.slice(t,t+8)||t+8!==this.input.length&&ie(this.input.charAt(t+8)))},pt.parseStatement=function(e,t,n){var r,i=this.type,s=this.startNode();switch(this.isLet(e)&&(i=Ge._var,r="let"),i){case Ge._break:case Ge._continue:return this.parseBreakContinueStatement(s,i.keyword);case Ge._debugger:return this.parseDebuggerStatement(s);case Ge._do:return this.parseDoStatement(s);case Ge._for:return this.parseForStatement(s);case Ge._function:return e&&(this.strict||"if"!==e&&"label"!==e)&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(s,!1,!e);case Ge._class:return e&&this.unexpected(),this.parseClass(s,!0);case Ge._if:return this.parseIfStatement(s);case Ge._return:return this.parseReturnStatement(s);case Ge._switch:return this.parseSwitchStatement(s);case Ge._throw:return this.parseThrowStatement(s);case Ge._try:return this.parseTryStatement(s);case Ge._const:case Ge._var:return r=r||this.value,e&&"var"!==r&&this.unexpected(),this.parseVarStatement(s,r);case Ge._while:return this.parseWhileStatement(s);case Ge._with:return this.parseWithStatement(s);case Ge.braceL:return this.parseBlock(!0,s);case Ge.semi:return this.parseEmptyStatement(s);case Ge._export:case Ge._import:if(this.options.ecmaVersion>10&&i===Ge._import){Xe.lastIndex=this.pos;var u=Xe.exec(this.input),a=this.pos+u[0].length,o=this.input.charCodeAt(a);if(40===o||46===o)return this.parseExpressionStatement(s,this.parseExpression())}return this.options.allowImportExportEverywhere||(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),i===Ge._import?this.parseImport(s):this.parseExport(s,n);default:if(this.isAsyncFunction())return e&&this.unexpected(),this.next(),this.parseFunctionStatement(s,!0,!e);var c=this.value,p=this.parseExpression();return i===Ge.name&&"Identifier"===p.type&&this.eat(Ge.colon)?this.parseLabeledStatement(s,c,p,e):this.parseExpressionStatement(s,p)}},pt.parseBreakContinueStatement=function(e,t){var n="break"===t;this.next(),this.eat(Ge.semi)||this.insertSemicolon()?e.label=null:this.type!==Ge.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var r=0;r<this.labels.length;++r){var i=this.labels[r];if((null==e.label||i.name===e.label.name)&&(null!=i.kind&&(n||"loop"===i.kind)||e.label&&n))break}return r===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,n?"BreakStatement":"ContinueStatement")},pt.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},pt.parseDoStatement=function(e){return this.next(),this.labels.push(lt),e.body=this.parseStatement("do"),this.labels.pop(),this.expect(Ge._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(Ge.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},pt.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction)&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(lt),this.enterScope(0),this.expect(Ge.parenL),this.type===Ge.semi)return t>-1&&this.unexpected(t),this.parseFor(e,null);var n=this.isLet();if(this.type===Ge._var||this.type===Ge._const||n){var r=this.startNode(),i=n?"let":this.value;return this.next(),this.parseVar(r,!0,i),this.finishNode(r,"VariableDeclaration"),(this.type===Ge._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&1===r.declarations.length?(this.options.ecmaVersion>=9&&(this.type===Ge._in?t>-1&&this.unexpected(t):e.await=t>-1),this.parseForIn(e,r)):(t>-1&&this.unexpected(t),this.parseFor(e,r))}var s=new fe,u=this.parseExpression(!0,s);return this.type===Ge._in||this.options.ecmaVersion>=6&&this.isContextual("of")?(this.options.ecmaVersion>=9&&(this.type===Ge._in?t>-1&&this.unexpected(t):e.await=t>-1),this.toAssignable(u,!1,s),this.checkLVal(u),this.parseForIn(e,u)):(this.checkExpressionErrors(s,!0),t>-1&&this.unexpected(t),this.parseFor(e,u))},pt.parseFunctionStatement=function(e,t,n){return this.next(),this.parseFunction(e,dt|(n?0:mt),!1,t)},pt.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement("if"),e.alternate=this.eat(Ge._else)?this.parseStatement("if"):null,this.finishNode(e,"IfStatement")},pt.parseReturnStatement=function(e){return!this.inFunction&&!this.options.allowReturnOutsideFunction&&this.raise(this.start,"'return' outside of function"),this.next(),this.eat(Ge.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},pt.parseSwitchStatement=function(e){this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(Ge.braceL),this.labels.push(ht),this.enterScope(0);for(var t,n=!1;this.type!==Ge.braceR;)if(this.type===Ge._case||this.type===Ge._default){var r=this.type===Ge._case;t&&this.finishNode(t,"SwitchCase"),e.cases.push(t=this.startNode()),t.consequent=[],this.next(),r?t.test=this.parseExpression():(n&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),n=!0,t.test=null),this.expect(Ge.colon)}else t||this.unexpected(),t.consequent.push(this.parseStatement(null));return this.exitScope(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},pt.parseThrowStatement=function(e){return this.next(),ze.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")},ft=[],pt.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===Ge._catch){var t=this.startNode();if(this.next(),this.eat(Ge.parenL)){t.param=this.parseBindingAtom();var n="Identifier"===t.param.type;this.enterScope(n?32:0),this.checkLVal(t.param,n?4:2),this.expect(Ge.parenR)}else this.options.ecmaVersion<10&&this.unexpected(),t.param=null,this.enterScope(0);t.body=this.parseBlock(!1),this.exitScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(Ge._finally)?this.parseBlock():null,!e.handler&&!e.finalizer&&this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},pt.parseVarStatement=function(e,t){return this.next(),this.parseVar(e,!1,t),this.semicolon(),this.finishNode(e,"VariableDeclaration")},pt.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(lt),e.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(e,"WhileStatement")},pt.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement("with"),this.finishNode(e,"WithStatement")},pt.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},pt.parseLabeledStatement=function(e,t,n,r){for(var i=0,s=this.labels;i<s.length;i+=1){s[i].name===t&&this.raise(n.start,"Label '"+t+"' is already declared")}for(var u=this.type.isLoop?"loop":this.type===Ge._switch?"switch":null,a=this.labels.length-1;a>=0;a--){var o=this.labels[a];if(o.statementStart!==e.start)break;o.statementStart=this.start,o.kind=u}return this.labels.push({name:t,kind:u,statementStart:this.start}),e.body=this.parseStatement(r?-1===r.indexOf("label")?r+"label":r:"label"),this.labels.pop(),e.label=n,this.finishNode(e,"LabeledStatement")},pt.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},pt.parseBlock=function(e,t,n){for(void 0===e&&(e=!0),void 0===t&&(t=this.startNode()),t.body=[],this.expect(Ge.braceL),e&&this.enterScope(0);this.type!==Ge.braceR;){var r=this.parseStatement(null);t.body.push(r)}return n&&(this.strict=!1),this.next(),e&&this.exitScope(),this.finishNode(t,"BlockStatement")},pt.parseFor=function(e,t){return e.init=t,this.expect(Ge.semi),e.test=this.type===Ge.semi?null:this.parseExpression(),this.expect(Ge.semi),e.update=this.type===Ge.parenR?null:this.parseExpression(),this.expect(Ge.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,"ForStatement")},pt.parseForIn=function(e,t){var n=this.type===Ge._in;return this.next(),"VariableDeclaration"===t.type&&null!=t.declarations[0].init&&(!n||this.options.ecmaVersion<8||this.strict||"var"!==t.kind||"Identifier"!==t.declarations[0].id.type)?this.raise(t.start,(n?"for-in":"for-of")+" loop variable declaration may not have an initializer"):"AssignmentPattern"===t.type&&this.raise(t.start,"Invalid left-hand side in for-loop"),e.left=t,e.right=n?this.parseExpression():this.parseMaybeAssign(),this.expect(Ge.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,n?"ForInStatement":"ForOfStatement")},pt.parseVar=function(e,t,n){for(e.declarations=[],e.kind=n;;){var r=this.startNode();if(this.parseVarId(r,n),this.eat(Ge.eq)?r.init=this.parseMaybeAssign(t):"const"!==n||this.type===Ge._in||this.options.ecmaVersion>=6&&this.isContextual("of")?"Identifier"===r.id.type||t&&(this.type===Ge._in||this.isContextual("of"))?r.init=null:this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):this.unexpected(),e.declarations.push(this.finishNode(r,"VariableDeclarator")),!this.eat(Ge.comma))break}return e},pt.parseVarId=function(e,t){e.id=this.parseBindingAtom(),this.checkLVal(e.id,"var"===t?1:2,!1)},dt=1,mt=2,pt.parseFunction=function(e,t,n,r){this.initFunction(e),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!r)&&(this.type===Ge.star&&t&mt&&this.unexpected(),e.generator=this.eat(Ge.star)),this.options.ecmaVersion>=8&&(e.async=!!r),t&dt&&(e.id=4&t&&this.type!==Ge.name?null:this.parseIdent(),e.id&&!(t&mt)&&this.checkLVal(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?1:2:3));var i=this.yieldPos,s=this.awaitPos,u=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(he(e.async,e.generator)),t&dt||(e.id=this.type===Ge.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,n,!1),this.yieldPos=i,this.awaitPos=s,this.awaitIdentPos=u,this.finishNode(e,t&dt?"FunctionDeclaration":"FunctionExpression")},pt.parseFunctionParams=function(e){this.expect(Ge.parenL),e.params=this.parseBindingList(Ge.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},pt.parseClass=function(e,t){this.next();var n=this.strict;this.strict=!0,this.parseClassId(e,t),this.parseClassSuper(e);var r=this.startNode(),i=!1;for(r.body=[],this.expect(Ge.braceL);this.type!==Ge.braceR;){var s=this.parseClassElement(null!==e.superClass);s&&(r.body.push(s),"MethodDefinition"===s.type&&"constructor"===s.kind&&(i&&this.raise(s.start,"Duplicate constructor in the same class"),i=!0))}return this.strict=n,this.next(),e.body=this.finishNode(r,"ClassBody"),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},pt.parseClassElement=function(e){var t=this;if(this.eat(Ge.semi))return null;var n=this.startNode(),r=function(e,r){void 0===r&&(r=!1);var i=t.start,s=t.startLoc;return!!t.eatContextual(e)&&(!(t.type===Ge.parenL||r&&t.canInsertSemicolon())||(n.key&&t.unexpected(),n.computed=!1,n.key=t.startNodeAt(i,s),n.key.name=e,t.finishNode(n.key,"Identifier"),!1))};n.kind="method",n.static=r("static");var i=this.eat(Ge.star),s=!1;i||(this.options.ecmaVersion>=8&&r("async",!0)?(s=!0,i=this.options.ecmaVersion>=9&&this.eat(Ge.star)):r("get")?n.kind="get":r("set")&&(n.kind="set")),n.key||this.parsePropertyName(n);var u=n.key,a=!1;return n.computed||n.static||!("Identifier"===u.type&&"constructor"===u.name||"Literal"===u.type&&"constructor"===u.value)?n.static&&"Identifier"===u.type&&"prototype"===u.name&&this.raise(u.start,"Classes may not have a static property named prototype"):("method"!==n.kind&&this.raise(u.start,"Constructor can't have get/set modifier"),i&&this.raise(u.start,"Constructor can't be a generator"),s&&this.raise(u.start,"Constructor can't be an async method"),n.kind="constructor",a=e),this.parseClassMethod(n,i,s,a),"get"===n.kind&&0!==n.value.params.length&&this.raiseRecoverable(n.value.start,"getter should have no params"),"set"===n.kind&&1!==n.value.params.length&&this.raiseRecoverable(n.value.start,"setter should have exactly one param"),"set"===n.kind&&"RestElement"===n.value.params[0].type&&this.raiseRecoverable(n.value.params[0].start,"Setter cannot use rest params"),n},pt.parseClassMethod=function(e,t,n,r){return e.value=this.parseMethod(t,n,r),this.finishNode(e,"MethodDefinition")},pt.parseClassId=function(e,t){this.type===Ge.name?(e.id=this.parseIdent(),t&&this.checkLVal(e.id,2,!1)):(!0===t&&this.unexpected(),e.id=null)},pt.parseClassSuper=function(e){e.superClass=this.eat(Ge._extends)?this.parseExprSubscripts():null},pt.parseExport=function(e,t){if(this.next(),this.eat(Ge.star))return this.options.ecmaVersion>=11&&(this.eatContextual("as")?(e.exported=this.parseIdent(!0),this.checkExport(t,e.exported.name,this.lastTokStart)):e.exported=null),this.expectContextual("from"),this.type!==Ge.string&&this.unexpected(),e.source=this.parseExprAtom(),this.semicolon(),this.finishNode(e,"ExportAllDeclaration");if(this.eat(Ge._default)){var n;if(this.checkExport(t,"default",this.lastTokStart),this.type===Ge._function||(n=this.isAsyncFunction())){var r=this.startNode();this.next(),n&&this.next(),e.declaration=this.parseFunction(r,4|dt,!1,n)}else if(this.type===Ge._class){var i=this.startNode();e.declaration=this.parseClass(i,"nullableID")}else e.declaration=this.parseMaybeAssign(),this.semicolon();return this.finishNode(e,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement())e.declaration=this.parseStatement(null),"VariableDeclaration"===e.declaration.type?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id.name,e.declaration.id.start),e.specifiers=[],e.source=null;else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))this.type!==Ge.string&&this.unexpected(),e.source=this.parseExprAtom();else{for(var s=0,u=e.specifiers;s<u.length;s+=1){var a=u[s];this.checkUnreserved(a.local),this.checkLocalExport(a.local)}e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},pt.checkExport=function(e,t,n){e&&(oe(e,t)&&this.raiseRecoverable(n,"Duplicate export '"+t+"'"),e[t]=!0)},pt.checkPatternExport=function(e,t){var n=t.type;if("Identifier"===n)this.checkExport(e,t.name,t.start);else if("ObjectPattern"===n)for(var r=0,i=t.properties;r<i.length;r+=1){var s=i[r];this.checkPatternExport(e,s)}else if("ArrayPattern"===n)for(var u=0,a=t.elements;u<a.length;u+=1){var o=a[u];o&&this.checkPatternExport(e,o)}else"Property"===n?this.checkPatternExport(e,t.value):"AssignmentPattern"===n?this.checkPatternExport(e,t.left):"RestElement"===n?this.checkPatternExport(e,t.argument):"ParenthesizedExpression"===n&&this.checkPatternExport(e,t.expression)},pt.checkVariableExport=function(e,t){if(e)for(var n=0,r=t;n<r.length;n+=1){var i=r[n];this.checkPatternExport(e,i.id)}},pt.shouldParseExportStatement=function(){return"var"===this.type.keyword||"const"===this.type.keyword||"class"===this.type.keyword||"function"===this.type.keyword||this.isLet()||this.isAsyncFunction()},pt.parseExportSpecifiers=function(e){var t=[],n=!0;for(this.expect(Ge.braceL);!this.eat(Ge.braceR);){if(n)n=!1;else if(this.expect(Ge.comma),this.afterTrailingComma(Ge.braceR))break;var r=this.startNode();r.local=this.parseIdent(!0),r.exported=this.eatContextual("as")?this.parseIdent(!0):r.local,this.checkExport(e,r.exported.name,r.exported.start),t.push(this.finishNode(r,"ExportSpecifier"))}return t},pt.parseImport=function(e){return this.next(),this.type===Ge.string?(e.specifiers=ft,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===Ge.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},pt.parseImportSpecifiers=function(){var e=[],t=!0;if(this.type===Ge.name){var n=this.startNode();if(n.local=this.parseIdent(),this.checkLVal(n.local,2),e.push(this.finishNode(n,"ImportDefaultSpecifier")),!this.eat(Ge.comma))return e}if(this.type===Ge.star){var r=this.startNode();return this.next(),this.expectContextual("as"),r.local=this.parseIdent(),this.checkLVal(r.local,2),e.push(this.finishNode(r,"ImportNamespaceSpecifier")),e}for(this.expect(Ge.braceL);!this.eat(Ge.braceR);){if(t)t=!1;else if(this.expect(Ge.comma),this.afterTrailingComma(Ge.braceR))break;var i=this.startNode();i.imported=this.parseIdent(!0),this.eatContextual("as")?i.local=this.parseIdent():(this.checkUnreserved(i.imported),i.local=i.imported),this.checkLVal(i.local,2),e.push(this.finishNode(i,"ImportSpecifier"))}return e},pt.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t)e[t].directive=e[t].expression.raw.slice(1,-1)},pt.isDirectiveCandidate=function(e){return"ExpressionStatement"===e.type&&"Literal"===e.expression.type&&"string"==typeof e.expression.value&&('"'===this.input[e.start]||"'"===this.input[e.start])},(gt=ut.prototype).toAssignable=function(e,t,n){if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&"await"===e.name&&this.raise(e.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern",n&&this.checkPatternErrors(n,!0);for(var r=0,i=e.properties;r<i.length;r+=1){var s=i[r];this.toAssignable(s,t),"RestElement"===s.type&&("ArrayPattern"===s.argument.type||"ObjectPattern"===s.argument.type)&&this.raise(s.argument.start,"Unexpected token")}break;case"Property":"init"!==e.kind&&this.raise(e.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern",n&&this.checkPatternErrors(n,!0),this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement",this.toAssignable(e.argument,t),"AssignmentPattern"===e.argument.type&&this.raise(e.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":"="!==e.operator&&this.raise(e.left.end,"Only '=' operator can be used for specifying default value."),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);case"AssignmentPattern":break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,n);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}else n&&this.checkPatternErrors(n,!0);return e},gt.toAssignableList=function(e,t){for(var n=e.length,r=0;r<n;r++){var i=e[r];i&&this.toAssignable(i,t)}if(n){var s=e[n-1];6===this.options.ecmaVersion&&t&&s&&"RestElement"===s.type&&"Identifier"!==s.argument.type&&this.unexpected(s.argument.start)}return e},gt.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},gt.parseRestBinding=function(){var e=this.startNode();return this.next(),6===this.options.ecmaVersion&&this.type!==Ge.name&&this.unexpected(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")},gt.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case Ge.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(Ge.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case Ge.braceL:return this.parseObj(!0)}return this.parseIdent()},gt.parseBindingList=function(e,t,n){for(var r=[],i=!0;!this.eat(e);)if(i?i=!1:this.expect(Ge.comma),t&&this.type===Ge.comma)r.push(null);else{if(n&&this.afterTrailingComma(e))break;if(this.type===Ge.ellipsis){var s=this.parseRestBinding();this.parseBindingListItem(s),r.push(s),this.type===Ge.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.expect(e);break}var u=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(u),r.push(u)}return r},gt.parseBindingListItem=function(e){return e},gt.parseMaybeDefault=function(e,t,n){if(n=n||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(Ge.eq))return n;var r=this.startNodeAt(e,t);return r.left=n,r.right=this.parseMaybeAssign(),this.finishNode(r,"AssignmentPattern")},gt.checkLVal=function(e,t,n){switch(void 0===t&&(t=0),e.type){case"Identifier":2===t&&"let"===e.name&&this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name"),this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(t?"Binding ":"Assigning to ")+e.name+" in strict mode"),n&&(oe(n,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),n[e.name]=!0),0!==t&&5!==t&&this.declareName(e.name,t,e.start);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":t&&this.raiseRecoverable(e.start,"Binding member expression");break;case"ObjectPattern":for(var r=0,i=e.properties;r<i.length;r+=1){var s=i[r];this.checkLVal(s,t,n)}break;case"Property":this.checkLVal(e.value,t,n);break;case"ArrayPattern":for(var u=0,a=e.elements;u<a.length;u+=1){var o=a[u];o&&this.checkLVal(o,t,n)}break;case"AssignmentPattern":this.checkLVal(e.left,t,n);break;case"RestElement":this.checkLVal(e.argument,t,n);break;case"ParenthesizedExpression":this.checkLVal(e.expression,t,n);break;default:this.raise(e.start,(t?"Binding":"Assigning to")+" rvalue")}},(Dt=ut.prototype).checkPropClash=function(e,t,n){if(!(this.options.ecmaVersion>=9&&"SpreadElement"===e.type||this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand))){var r,i=e.key;switch(i.type){case"Identifier":r=i.name;break;case"Literal":r=String(i.value);break;default:return}var s=e.kind;if(this.options.ecmaVersion>=6)return void("__proto__"===r&&"init"===s&&(t.proto&&(n?n.doubleProto<0&&(n.doubleProto=i.start):this.raiseRecoverable(i.start,"Redefinition of __proto__ property")),t.proto=!0));var u=t[r="$"+r];if(u)("init"===s?this.strict&&u.init||u.get||u.set:u.init||u[s])&&this.raiseRecoverable(i.start,"Redefinition of property");else u=t[r]={init:!1,get:!1,set:!1};u[s]=!0}},Dt.parseExpression=function(e,t){var n=this.start,r=this.startLoc,i=this.parseMaybeAssign(e,t);if(this.type===Ge.comma){var s=this.startNodeAt(n,r);for(s.expressions=[i];this.eat(Ge.comma);)s.expressions.push(this.parseMaybeAssign(e,t));return this.finishNode(s,"SequenceExpression")}return i},Dt.parseMaybeAssign=function(e,t,n){if(this.isContextual("yield")){if(this.inGenerator)return this.parseYield(e);this.exprAllowed=!1}var r=!1,i=-1,s=-1;t?(i=t.parenthesizedAssign,s=t.trailingComma,t.parenthesizedAssign=t.trailingComma=-1):(t=new fe,r=!0);var u=this.start,a=this.startLoc;(this.type===Ge.parenL||this.type===Ge.name)&&(this.potentialArrowAt=this.start);var o=this.parseMaybeConditional(e,t);if(n&&(o=n.call(this,o,u,a)),this.type.isAssign){var c=this.startNodeAt(u,a);return c.operator=this.value,c.left=this.type===Ge.eq?this.toAssignable(o,!1,t):o,r||(t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1),t.shorthandAssign>=c.left.start&&(t.shorthandAssign=-1),this.checkLVal(o),this.next(),c.right=this.parseMaybeAssign(e),this.finishNode(c,"AssignmentExpression")}return r&&this.checkExpressionErrors(t,!0),i>-1&&(t.parenthesizedAssign=i),s>-1&&(t.trailingComma=s),o},Dt.parseMaybeConditional=function(e,t){var n=this.start,r=this.startLoc,i=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return i;if(this.eat(Ge.question)){var s=this.startNodeAt(n,r);return s.test=i,s.consequent=this.parseMaybeAssign(),this.expect(Ge.colon),s.alternate=this.parseMaybeAssign(e),this.finishNode(s,"ConditionalExpression")}return i},Dt.parseExprOps=function(e,t){var n=this.start,r=this.startLoc,i=this.parseMaybeUnary(t,!1);return this.checkExpressionErrors(t)||i.start===n&&"ArrowFunctionExpression"===i.type?i:this.parseExprOp(i,n,r,-1,e)},Dt.parseExprOp=function(e,t,n,r,i){var s=this.type.binop;if(null!=s&&(!i||this.type!==Ge._in)&&s>r){var u=this.type===Ge.logicalOR||this.type===Ge.logicalAND,a=this.type===Ge.coalesce;a&&(s=Ge.logicalAND.binop);var o=this.value;this.next();var c=this.start,p=this.startLoc,l=this.parseExprOp(this.parseMaybeUnary(null,!1),c,p,s,i),h=this.buildBinary(t,n,e,l,o,u||a);return(u&&this.type===Ge.coalesce||a&&(this.type===Ge.logicalOR||this.type===Ge.logicalAND))&&this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses"),this.parseExprOp(h,t,n,r,i)}return e},Dt.buildBinary=function(e,t,n,r,i,s){var u=this.startNodeAt(e,t);return u.left=n,u.operator=i,u.right=r,this.finishNode(u,s?"LogicalExpression":"BinaryExpression")},Dt.parseMaybeUnary=function(e,t){var n,r=this.start,i=this.startLoc;if(this.isContextual("await")&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction))n=this.parseAwait(),t=!0;else if(this.type.prefix){var s=this.startNode(),u=this.type===Ge.incDec;s.operator=this.value,s.prefix=!0,this.next(),s.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(e,!0),u?this.checkLVal(s.argument):this.strict&&"delete"===s.operator&&"Identifier"===s.argument.type?this.raiseRecoverable(s.start,"Deleting local variable in strict mode"):t=!0,n=this.finishNode(s,u?"UpdateExpression":"UnaryExpression")}else{if(n=this.parseExprSubscripts(e),this.checkExpressionErrors(e))return n;for(;this.type.postfix&&!this.canInsertSemicolon();){var a=this.startNodeAt(r,i);a.operator=this.value,a.prefix=!1,a.argument=n,this.checkLVal(n),this.next(),n=this.finishNode(a,"UpdateExpression")}}return!t&&this.eat(Ge.starstar)?this.buildBinary(r,i,n,this.parseMaybeUnary(null,!1),"**",!1):n},Dt.parseExprSubscripts=function(e){var t=this.start,n=this.startLoc,r=this.parseExprAtom(e);if("ArrowFunctionExpression"===r.type&&")"!==this.input.slice(this.lastTokStart,this.lastTokEnd))return r;var i=this.parseSubscripts(r,t,n);return e&&"MemberExpression"===i.type&&(e.parenthesizedAssign>=i.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=i.start&&(e.parenthesizedBind=-1)),i},Dt.parseSubscripts=function(e,t,n,r){for(var i=this.options.ecmaVersion>=8&&"Identifier"===e.type&&"async"===e.name&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start===5&&this.potentialArrowAt===e.start,s=!1;;){var u=this.parseSubscript(e,t,n,r,i,s);if(u.optional&&(s=!0),u===e||"ArrowFunctionExpression"===u.type){if(s){var a=this.startNodeAt(t,n);a.expression=u,u=this.finishNode(a,"ChainExpression")}return u}e=u}},Dt.parseSubscript=function(e,t,n,r,i,s){var u=this.options.ecmaVersion>=11,a=u&&this.eat(Ge.questionDot);r&&a&&this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions");var o=this.eat(Ge.bracketL);if(o||a&&this.type!==Ge.parenL&&this.type!==Ge.backQuote||this.eat(Ge.dot)){var c=this.startNodeAt(t,n);c.object=e,c.property=o?this.parseExpression():this.parseIdent("never"!==this.options.allowReserved),c.computed=!!o,o&&this.expect(Ge.bracketR),u&&(c.optional=a),e=this.finishNode(c,"MemberExpression")}else if(!r&&this.eat(Ge.parenL)){var p=new fe,l=this.yieldPos,h=this.awaitPos,f=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var d=this.parseExprList(Ge.parenR,this.options.ecmaVersion>=8,!1,p);if(i&&!a&&!this.canInsertSemicolon()&&this.eat(Ge.arrow))return this.checkPatternErrors(p,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=l,this.awaitPos=h,this.awaitIdentPos=f,this.parseArrowExpression(this.startNodeAt(t,n),d,!0);this.checkExpressionErrors(p,!0),this.yieldPos=l||this.yieldPos,this.awaitPos=h||this.awaitPos,this.awaitIdentPos=f||this.awaitIdentPos;var m=this.startNodeAt(t,n);m.callee=e,m.arguments=d,u&&(m.optional=a),e=this.finishNode(m,"CallExpression")}else if(this.type===Ge.backQuote){(a||s)&&this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions");var g=this.startNodeAt(t,n);g.tag=e,g.quasi=this.parseTemplate({isTagged:!0}),e=this.finishNode(g,"TaggedTemplateExpression")}return e},Dt.parseExprAtom=function(e){this.type===Ge.slash&&this.readRegexp();var t,n=this.potentialArrowAt===this.start;switch(this.type){case Ge._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),t=this.startNode(),this.next(),this.type===Ge.parenL&&!this.allowDirectSuper&&this.raise(t.start,"super() call outside constructor of a subclass"),this.type!==Ge.dot&&this.type!==Ge.bracketL&&this.type!==Ge.parenL&&this.unexpected(),this.finishNode(t,"Super");case Ge._this:return t=this.startNode(),this.next(),this.finishNode(t,"ThisExpression");case Ge.name:var r=this.start,i=this.startLoc,s=this.containsEsc,u=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!s&&"async"===u.name&&!this.canInsertSemicolon()&&this.eat(Ge._function))return this.parseFunction(this.startNodeAt(r,i),0,!1,!0);if(n&&!this.canInsertSemicolon()){if(this.eat(Ge.arrow))return this.parseArrowExpression(this.startNodeAt(r,i),[u],!1);if(this.options.ecmaVersion>=8&&"async"===u.name&&this.type===Ge.name&&!s)return u=this.parseIdent(!1),(this.canInsertSemicolon()||!this.eat(Ge.arrow))&&this.unexpected(),this.parseArrowExpression(this.startNodeAt(r,i),[u],!0)}return u;case Ge.regexp:var a=this.value;return(t=this.parseLiteral(a.value)).regex={pattern:a.pattern,flags:a.flags},t;case Ge.num:case Ge.string:return this.parseLiteral(this.value);case Ge._null:case Ge._true:case Ge._false:return(t=this.startNode()).value=this.type===Ge._null?null:this.type===Ge._true,t.raw=this.type.keyword,this.next(),this.finishNode(t,"Literal");case Ge.parenL:var o=this.start,c=this.parseParenAndDistinguishExpression(n);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(c)&&(e.parenthesizedAssign=o),e.parenthesizedBind<0&&(e.parenthesizedBind=o)),c;case Ge.bracketL:return t=this.startNode(),this.next(),t.elements=this.parseExprList(Ge.bracketR,!0,!0,e),this.finishNode(t,"ArrayExpression");case Ge.braceL:return this.parseObj(!1,e);case Ge._function:return t=this.startNode(),this.next(),this.parseFunction(t,0);case Ge._class:return this.parseClass(this.startNode(),!1);case Ge._new:return this.parseNew();case Ge.backQuote:return this.parseTemplate();case Ge._import:return this.options.ecmaVersion>=11?this.parseExprImport():this.unexpected();default:this.unexpected()}},Dt.parseExprImport=function(){var e=this.startNode();this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword import");var t=this.parseIdent(!0);switch(this.type){case Ge.parenL:return this.parseDynamicImport(e);case Ge.dot:return e.meta=t,this.parseImportMeta(e);default:this.unexpected()}},Dt.parseDynamicImport=function(e){if(this.next(),e.source=this.parseMaybeAssign(),!this.eat(Ge.parenR)){var t=this.start;this.eat(Ge.comma)&&this.eat(Ge.parenR)?this.raiseRecoverable(t,"Trailing comma is not allowed in import()"):this.unexpected(t)}return this.finishNode(e,"ImportExpression")},Dt.parseImportMeta=function(e){this.next();var t=this.containsEsc;return e.property=this.parseIdent(!0),"meta"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for import is 'import.meta'"),t&&this.raiseRecoverable(e.start,"'import.meta' must not contain escaped characters"),"module"!==this.options.sourceType&&this.raiseRecoverable(e.start,"Cannot use 'import.meta' outside a module"),this.finishNode(e,"MetaProperty")},Dt.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),110===t.raw.charCodeAt(t.raw.length-1)&&(t.bigint=t.raw.slice(0,-1).replace(/_/g,"")),this.next(),this.finishNode(t,"Literal")},Dt.parseParenExpression=function(){this.expect(Ge.parenL);var e=this.parseExpression();return this.expect(Ge.parenR),e},Dt.parseParenAndDistinguishExpression=function(e){var t,n=this.start,r=this.startLoc,i=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var s,u=this.start,a=this.startLoc,o=[],c=!0,p=!1,l=new fe,h=this.yieldPos,f=this.awaitPos;for(this.yieldPos=0,this.awaitPos=0;this.type!==Ge.parenR;){if(c?c=!1:this.expect(Ge.comma),i&&this.afterTrailingComma(Ge.parenR,!0)){p=!0;break}if(this.type===Ge.ellipsis){s=this.start,o.push(this.parseParenItem(this.parseRestBinding())),this.type===Ge.comma&&this.raise(this.start,"Comma is not permitted after the rest element");break}o.push(this.parseMaybeAssign(!1,l,this.parseParenItem))}var d=this.start,m=this.startLoc;if(this.expect(Ge.parenR),e&&!this.canInsertSemicolon()&&this.eat(Ge.arrow))return this.checkPatternErrors(l,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=h,this.awaitPos=f,this.parseParenArrowList(n,r,o);(!o.length||p)&&this.unexpected(this.lastTokStart),s&&this.unexpected(s),this.checkExpressionErrors(l,!0),this.yieldPos=h||this.yieldPos,this.awaitPos=f||this.awaitPos,o.length>1?((t=this.startNodeAt(u,a)).expressions=o,this.finishNodeAt(t,"SequenceExpression",d,m)):t=o[0]}else t=this.parseParenExpression();if(this.options.preserveParens){var g=this.startNodeAt(n,r);return g.expression=t,this.finishNode(g,"ParenthesizedExpression")}return t},Dt.parseParenItem=function(e){return e},Dt.parseParenArrowList=function(e,t,n){return this.parseArrowExpression(this.startNodeAt(e,t),n)},At=[],Dt.parseNew=function(){this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword new");var e=this.startNode(),t=this.parseIdent(!0);if(this.options.ecmaVersion>=6&&this.eat(Ge.dot)){e.meta=t;var n=this.containsEsc;return e.property=this.parseIdent(!0),"target"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is 'new.target'"),n&&this.raiseRecoverable(e.start,"'new.target' must not contain escaped characters"),this.inNonArrowFunction()||this.raiseRecoverable(e.start,"'new.target' can only be used in functions"),this.finishNode(e,"MetaProperty")}var r=this.start,i=this.startLoc,s=this.type===Ge._import;return e.callee=this.parseSubscripts(this.parseExprAtom(),r,i,!0),s&&"ImportExpression"===e.callee.type&&this.raise(r,"Cannot use new with import()"),this.eat(Ge.parenL)?e.arguments=this.parseExprList(Ge.parenR,this.options.ecmaVersion>=8,!1):e.arguments=At,this.finishNode(e,"NewExpression")},Dt.parseTemplateElement=function(e){var t=e.isTagged,n=this.startNode();return this.type===Ge.invalidTemplate?(t||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),n.value={raw:this.value,cooked:null}):n.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value},this.next(),n.tail=this.type===Ge.backQuote,this.finishNode(n,"TemplateElement")},Dt.parseTemplate=function(e){void 0===e&&(e={});var t=e.isTagged;void 0===t&&(t=!1);var n=this.startNode();this.next(),n.expressions=[];var r=this.parseTemplateElement({isTagged:t});for(n.quasis=[r];!r.tail;)this.type===Ge.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(Ge.dollarBraceL),n.expressions.push(this.parseExpression()),this.expect(Ge.braceR),n.quasis.push(r=this.parseTemplateElement({isTagged:t}));return this.next(),this.finishNode(n,"TemplateLiteral")},Dt.isAsyncProp=function(e){return!e.computed&&"Identifier"===e.key.type&&"async"===e.key.name&&(this.type===Ge.name||this.type===Ge.num||this.type===Ge.string||this.type===Ge.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===Ge.star)&&!ze.test(this.input.slice(this.lastTokEnd,this.start))},Dt.parseObj=function(e,t){var n=this.startNode(),r=!0,i={};for(n.properties=[],this.next();!this.eat(Ge.braceR);){if(r)r=!1;else if(this.expect(Ge.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(Ge.braceR))break;var s=this.parseProperty(e,t);e||this.checkPropClash(s,i,t),n.properties.push(s)}return this.finishNode(n,e?"ObjectPattern":"ObjectExpression")},Dt.parseProperty=function(e,t){var n,r,i,s,u=this.startNode();if(this.options.ecmaVersion>=9&&this.eat(Ge.ellipsis))return e?(u.argument=this.parseIdent(!1),this.type===Ge.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.finishNode(u,"RestElement")):(this.type===Ge.parenL&&t&&(t.parenthesizedAssign<0&&(t.parenthesizedAssign=this.start),t.parenthesizedBind<0&&(t.parenthesizedBind=this.start)),u.argument=this.parseMaybeAssign(!1,t),this.type===Ge.comma&&t&&t.trailingComma<0&&(t.trailingComma=this.start),this.finishNode(u,"SpreadElement"));this.options.ecmaVersion>=6&&(u.method=!1,u.shorthand=!1,(e||t)&&(i=this.start,s=this.startLoc),e||(n=this.eat(Ge.star)));var a=this.containsEsc;return this.parsePropertyName(u),!e&&!a&&this.options.ecmaVersion>=8&&!n&&this.isAsyncProp(u)?(r=!0,n=this.options.ecmaVersion>=9&&this.eat(Ge.star),this.parsePropertyName(u,t)):r=!1,this.parsePropertyValue(u,e,n,r,i,s,t,a),this.finishNode(u,"Property")},Dt.parsePropertyValue=function(e,t,n,r,i,s,u,a){if((n||r)&&this.type===Ge.colon&&this.unexpected(),this.eat(Ge.colon))e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,u),e.kind="init";else if(this.options.ecmaVersion>=6&&this.type===Ge.parenL)t&&this.unexpected(),e.kind="init",e.method=!0,e.value=this.parseMethod(n,r);else if(t||a||!(this.options.ecmaVersion>=5)||e.computed||"Identifier"!==e.key.type||"get"!==e.key.name&&"set"!==e.key.name||this.type===Ge.comma||this.type===Ge.braceR||this.type===Ge.eq)this.options.ecmaVersion>=6&&!e.computed&&"Identifier"===e.key.type?((n||r)&&this.unexpected(),this.checkUnreserved(e.key),"await"===e.key.name&&!this.awaitIdentPos&&(this.awaitIdentPos=i),e.kind="init",t?e.value=this.parseMaybeDefault(i,s,e.key):this.type===Ge.eq&&u?(u.shorthandAssign<0&&(u.shorthandAssign=this.start),e.value=this.parseMaybeDefault(i,s,e.key)):e.value=e.key,e.shorthand=!0):this.unexpected();else{(n||r)&&this.unexpected(),e.kind=e.key.name,this.parsePropertyName(e),e.value=this.parseMethod(!1);var o="get"===e.kind?0:1;if(e.value.params.length!==o){var c=e.value.start;"get"===e.kind?this.raiseRecoverable(c,"getter should have no params"):this.raiseRecoverable(c,"setter should have exactly one param")}else"set"===e.kind&&"RestElement"===e.value.params[0].type&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")}},Dt.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(Ge.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(Ge.bracketR),e.key;e.computed=!1}return e.key=this.type===Ge.num||this.type===Ge.string?this.parseExprAtom():this.parseIdent("never"!==this.options.allowReserved)},Dt.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},Dt.parseMethod=function(e,t,n){var r=this.startNode(),i=this.yieldPos,s=this.awaitPos,u=this.awaitIdentPos;return this.initFunction(r),this.options.ecmaVersion>=6&&(r.generator=e),this.options.ecmaVersion>=8&&(r.async=!!t),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(64|he(t,r.generator)|(n?128:0)),this.expect(Ge.parenL),r.params=this.parseBindingList(Ge.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(r,!1,!0),this.yieldPos=i,this.awaitPos=s,this.awaitIdentPos=u,this.finishNode(r,"FunctionExpression")},Dt.parseArrowExpression=function(e,t,n){var r=this.yieldPos,i=this.awaitPos,s=this.awaitIdentPos;return this.enterScope(16|he(n,!1)),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!n),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0,!1),this.yieldPos=r,this.awaitPos=i,this.awaitIdentPos=s,this.finishNode(e,"ArrowFunctionExpression")},Dt.parseFunctionBody=function(e,t,n){var r=t&&this.type!==Ge.braceL,i=this.strict,s=!1;if(r)e.body=this.parseMaybeAssign(),e.expression=!0,this.checkParams(e,!1);else{var u=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);(!i||u)&&((s=this.strictDirective(this.end))&&u&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list"));var a=this.labels;this.labels=[],s&&(this.strict=!0),this.checkParams(e,!i&&!s&&!t&&!n&&this.isSimpleParamList(e.params)),this.strict&&e.id&&this.checkLVal(e.id,5),e.body=this.parseBlock(!1,void 0,s&&!i),e.expression=!1,this.adaptDirectivePrologue(e.body.body),this.labels=a}this.exitScope()},Dt.isSimpleParamList=function(e){for(var t=0,n=e;t<n.length;t+=1){if("Identifier"!==n[t].type)return!1}return!0},Dt.checkParams=function(e,t){for(var n={},r=0,i=e.params;r<i.length;r+=1){var s=i[r];this.checkLVal(s,1,t?null:n)}},Dt.parseExprList=function(e,t,n,r){for(var i=[],s=!0;!this.eat(e);){if(s)s=!1;else if(this.expect(Ge.comma),t&&this.afterTrailingComma(e))break;var u=void 0;n&&this.type===Ge.comma?u=null:this.type===Ge.ellipsis?(u=this.parseSpread(r),r&&this.type===Ge.comma&&r.trailingComma<0&&(r.trailingComma=this.start)):u=this.parseMaybeAssign(!1,r),i.push(u)}return i},Dt.checkUnreserved=function(e){var t=e.start,n=e.end,r=e.name;(this.inGenerator&&"yield"===r&&this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&"await"===r&&this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function"),this.keywords.test(r)&&this.raise(t,"Unexpected keyword '"+r+"'"),this.options.ecmaVersion<6&&-1!==this.input.slice(t,n).indexOf("\\"))||(this.strict?this.reservedWordsStrict:this.reservedWords).test(r)&&(!this.inAsync&&"await"===r&&this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(t,"The keyword '"+r+"' is reserved"))},Dt.parseIdent=function(e,t){var n=this.startNode();return this.type===Ge.name?n.name=this.value:this.type.keyword?(n.name=this.type.keyword,("class"===n.name||"function"===n.name)&&(this.lastTokEnd!==this.lastTokStart+1||46!==this.input.charCodeAt(this.lastTokStart))&&this.context.pop()):this.unexpected(),this.next(!!e),this.finishNode(n,"Identifier"),e||(this.checkUnreserved(n),"await"===n.name&&!this.awaitIdentPos&&(this.awaitIdentPos=n.start)),n},Dt.parseYield=function(e){this.yieldPos||(this.yieldPos=this.start);var t=this.startNode();return this.next(),this.type===Ge.semi||this.canInsertSemicolon()||this.type!==Ge.star&&!this.type.startsExpr?(t.delegate=!1,t.argument=null):(t.delegate=this.eat(Ge.star),t.argument=this.parseMaybeAssign(e)),this.finishNode(t,"YieldExpression")},Dt.parseAwait=function(){this.awaitPos||(this.awaitPos=this.start);var e=this.startNode();return this.next(),e.argument=this.parseMaybeUnary(null,!1),this.finishNode(e,"AwaitExpression")},(Ct=ut.prototype).raise=function(e,t){var n=pe(this.input,e);t+=" ("+n.line+":"+n.column+")";var r=new SyntaxError(t);throw r.pos=e,r.loc=n,r.raisedAt=this.pos,r},Ct.raiseRecoverable=Ct.raise,Ct.curPosition=function(){if(this.options.locations)return new Ze(this.curLine,this.pos-this.lineStart)},Et=ut.prototype,yt=function(e){this.flags=e,this.var=[],this.lexical=[],this.functions=[]},Et.enterScope=function(e){this.scopeStack.push(new yt(e))},Et.exitScope=function(){this.scopeStack.pop()},Et.treatFunctionsAsVarInScope=function(e){return e.flags&nt||!this.inModule&&1&e.flags},Et.declareName=function(e,t,n){var r=!1;if(2===t){var i=this.currentScope();r=i.lexical.indexOf(e)>-1||i.functions.indexOf(e)>-1||i.var.indexOf(e)>-1,i.lexical.push(e),this.inModule&&1&i.flags&&delete this.undefinedExports[e]}else if(4===t){this.currentScope().lexical.push(e)}else if(3===t){var s=this.currentScope();r=this.treatFunctionsAsVar?s.lexical.indexOf(e)>-1:s.lexical.indexOf(e)>-1||s.var.indexOf(e)>-1,s.functions.push(e)}else for(var u=this.scopeStack.length-1;u>=0;--u){var a=this.scopeStack[u];if(a.lexical.indexOf(e)>-1&&!(32&a.flags&&a.lexical[0]===e)||!this.treatFunctionsAsVarInScope(a)&&a.functions.indexOf(e)>-1){r=!0;break}if(a.var.push(e),this.inModule&&1&a.flags&&delete this.undefinedExports[e],a.flags&rt)break}r&&this.raiseRecoverable(n,"Identifier '"+e+"' has already been declared")},Et.checkLocalExport=function(e){-1===this.scopeStack[0].lexical.indexOf(e.name)&&-1===this.scopeStack[0].var.indexOf(e.name)&&(this.undefinedExports[e.name]=e)},Et.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},Et.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&rt)return t}},Et.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&rt&&!(16&t.flags))return t}},xt=function(e,t,n){this.type="",this.start=t,this.end=0,e.options.locations&&(this.loc=new et(e,n)),e.options.directSourceFile&&(this.sourceFile=e.options.directSourceFile),e.options.ranges&&(this.range=[t,0])},(Ft=ut.prototype).startNode=function(){return new xt(this,this.start,this.startLoc)},Ft.startNodeAt=function(e,t){return new xt(this,e,t)},Ft.finishNode=function(e,t){return de.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},Ft.finishNodeAt=function(e,t,n,r){return de.call(this,e,t,n,r)},bt={b_stat:new(vt=function(e,t,n,r,i){this.token=e,this.isExpr=!!t,this.preserveSpace=!!n,this.override=r,this.generator=!!i})("{",!1),b_expr:new vt("{",!0),b_tmpl:new vt("${",!1),p_stat:new vt("(",!1),p_expr:new vt("(",!0),q_tmpl:new vt("`",!0,!0,(function(e){return e.tryReadTemplateToken()})),f_stat:new vt("function",!1),f_expr:new vt("function",!0),f_expr_gen:new vt("function",!0,!1,null,!0),f_gen:new vt("function",!1,!1,null,!0)},(St=ut.prototype).initialContext=function(){return[bt.b_stat]},St.braceIsBlock=function(e){var t=this.curContext();return t===bt.f_expr||t===bt.f_stat||(e!==Ge.colon||t!==bt.b_stat&&t!==bt.b_expr?e===Ge._return||e===Ge.name&&this.exprAllowed?ze.test(this.input.slice(this.lastTokEnd,this.start)):e===Ge._else||e===Ge.semi||e===Ge.eof||e===Ge.parenR||e===Ge.arrow||(e===Ge.braceL?t===bt.b_stat:e!==Ge._var&&e!==Ge._const&&e!==Ge.name&&!this.exprAllowed):!t.isExpr)},St.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if("function"===t.token)return t.generator}return!1},St.updateContext=function(e){var t,n=this.type;n.keyword&&e===Ge.dot?this.exprAllowed=!1:(t=n.updateContext)?t.call(this,e):this.exprAllowed=n.beforeExpr},Ge.parenR.updateContext=Ge.braceR.updateContext=function(){if(1!==this.context.length){var e=this.context.pop();e===bt.b_stat&&"function"===this.curContext().token&&(e=this.context.pop()),this.exprAllowed=!e.isExpr}else this.exprAllowed=!0},Ge.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?bt.b_stat:bt.b_expr),this.exprAllowed=!0},Ge.dollarBraceL.updateContext=function(){this.context.push(bt.b_tmpl),this.exprAllowed=!0},Ge.parenL.updateContext=function(e){var t=e===Ge._if||e===Ge._for||e===Ge._with||e===Ge._while;this.context.push(t?bt.p_stat:bt.p_expr),this.exprAllowed=!0},Ge.incDec.updateContext=function(){},Ge._function.updateContext=Ge._class.updateContext=function(e){!e.beforeExpr||e===Ge.semi||e===Ge._else||e===Ge._return&&ze.test(this.input.slice(this.lastTokEnd,this.start))||(e===Ge.colon||e===Ge.braceL)&&this.curContext()===bt.b_stat?this.context.push(bt.f_stat):this.context.push(bt.f_expr),this.exprAllowed=!1},Ge.backQuote.updateContext=function(){this.curContext()===bt.q_tmpl?this.context.pop():this.context.push(bt.q_tmpl),this.exprAllowed=!1},Ge.star.updateContext=function(e){if(e===Ge._function){var t=this.context.length-1;this.context[t]===bt.f_expr?this.context[t]=bt.f_expr_gen:this.context[t]=bt.f_gen}this.exprAllowed=!0},Ge.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&e!==Ge.dot&&("of"===this.value&&!this.exprAllowed||"yield"===this.value&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t},wt={9:Bt="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",10:_t=Bt+" Extended_Pictographic",11:_t},kt="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",Tt={9:Pt="Adlam Adlm Ahom Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",10:It=Pt+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",11:It+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho"},Lt={},me(9),me(10),me(11),Ot=ut.prototype,(Nt=function(e){this.parser=e,this.validFlags="gim"+(e.options.ecmaVersion>=6?"uy":"")+(e.options.ecmaVersion>=9?"s":""),this.unicodeProperties=Lt[e.options.ecmaVersion>=11?11:e.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=[],this.backReferenceNames=[]}).prototype.reset=function(e,t,n){var r=-1!==n.indexOf("u");this.start=0|e,this.source=t+"",this.flags=n,this.switchU=r&&this.parser.options.ecmaVersion>=6,this.switchN=r&&this.parser.options.ecmaVersion>=9},Nt.prototype.raise=function(e){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+e)},Nt.prototype.at=function(e,t){void 0===t&&(t=!1);var n=this.source,r=n.length;if(e>=r)return-1;var i=n.charCodeAt(e);if(!t&&!this.switchU||i<=55295||i>=57344||e+1>=r)return i;var s=n.charCodeAt(e+1);return s>=56320&&s<=57343?(i<<10)+s-56613888:i},Nt.prototype.nextIndex=function(e,t){void 0===t&&(t=!1);var n=this.source,r=n.length;if(e>=r)return r;var i,s=n.charCodeAt(e);return!t&&!this.switchU||s<=55295||s>=57344||e+1>=r||(i=n.charCodeAt(e+1))<56320||i>57343?e+1:e+2},Nt.prototype.current=function(e){return void 0===e&&(e=!1),this.at(this.pos,e)},Nt.prototype.lookahead=function(e){return void 0===e&&(e=!1),this.at(this.nextIndex(this.pos,e),e)},Nt.prototype.advance=function(e){void 0===e&&(e=!1),this.pos=this.nextIndex(this.pos,e)},Nt.prototype.eat=function(e,t){return void 0===t&&(t=!1),this.current(t)===e&&(this.advance(t),!0)},Ot.validateRegExpFlags=function(e){for(var t=e.validFlags,n=e.flags,r=0;r<n.length;r++){var i=n.charAt(r);-1===t.indexOf(i)&&this.raise(e.start,"Invalid regular expression flag"),n.indexOf(i,r+1)>-1&&this.raise(e.start,"Duplicate regular expression flag")}},Ot.validateRegExpPattern=function(e){this.regexp_pattern(e),!e.switchN&&this.options.ecmaVersion>=9&&e.groupNames.length>0&&(e.switchN=!0,this.regexp_pattern(e))},Ot.regexp_pattern=function(e){e.pos=0,e.lastIntValue=0,e.lastStringValue="",e.lastAssertionIsQuantifiable=!1,e.numCapturingParens=0,e.maxBackReference=0,e.groupNames.length=0,e.backReferenceNames.length=0,this.regexp_disjunction(e),e.pos!==e.source.length&&(e.eat(41)&&e.raise("Unmatched ')'"),(e.eat(93)||e.eat(125))&&e.raise("Lone quantifier brackets")),e.maxBackReference>e.numCapturingParens&&e.raise("Invalid escape");for(var t=0,n=e.backReferenceNames;t<n.length;t+=1){var r=n[t];-1===e.groupNames.indexOf(r)&&e.raise("Invalid named capture referenced")}},Ot.regexp_disjunction=function(e){for(this.regexp_alternative(e);e.eat(124);)this.regexp_alternative(e);this.regexp_eatQuantifier(e,!0)&&e.raise("Nothing to repeat"),e.eat(123)&&e.raise("Lone quantifier brackets")},Ot.regexp_alternative=function(e){for(;e.pos<e.source.length&&this.regexp_eatTerm(e););},Ot.regexp_eatTerm=function(e){return this.regexp_eatAssertion(e)?(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)&&e.switchU&&e.raise("Invalid quantifier"),!0):!!(e.switchU?this.regexp_eatAtom(e):this.regexp_eatExtendedAtom(e))&&(this.regexp_eatQuantifier(e),!0)},Ot.regexp_eatAssertion=function(e){var t=e.pos;if(e.lastAssertionIsQuantifiable=!1,e.eat(94)||e.eat(36))return!0;if(e.eat(92)){if(e.eat(66)||e.eat(98))return!0;e.pos=t}if(e.eat(40)&&e.eat(63)){var n=!1;if(this.options.ecmaVersion>=9&&(n=e.eat(60)),e.eat(61)||e.eat(33))return this.regexp_disjunction(e),e.eat(41)||e.raise("Unterminated group"),e.lastAssertionIsQuantifiable=!n,!0}return e.pos=t,!1},Ot.regexp_eatQuantifier=function(e,t){return void 0===t&&(t=!1),!!this.regexp_eatQuantifierPrefix(e,t)&&(e.eat(63),!0)},Ot.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)},Ot.regexp_eatBracedQuantifier=function(e,t){var n=e.pos;if(e.eat(123)){var r=0,i=-1;if(this.regexp_eatDecimalDigits(e)&&(r=e.lastIntValue,e.eat(44)&&this.regexp_eatDecimalDigits(e)&&(i=e.lastIntValue),e.eat(125)))return-1!==i&&i<r&&!t&&e.raise("numbers out of order in {} quantifier"),!0;e.switchU&&!t&&e.raise("Incomplete quantifier"),e.pos=n}return!1},Ot.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)},Ot.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e))return!0;e.pos=t}return!1},Ot.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)&&e.eat(58)){if(this.regexp_disjunction(e),e.eat(41))return!0;e.raise("Unterminated group")}e.pos=t}return!1},Ot.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(e):63===e.current()&&e.raise("Invalid group"),this.regexp_disjunction(e),e.eat(41))return e.numCapturingParens+=1,!0;e.raise("Unterminated group")}return!1},Ot.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)},Ot.regexp_eatInvalidBracedQuantifier=function(e){return this.regexp_eatBracedQuantifier(e,!0)&&e.raise("Nothing to repeat"),!1},Ot.regexp_eatSyntaxCharacter=function(e){var t=e.current();return!!De(t)&&(e.lastIntValue=t,e.advance(),!0)},Ot.regexp_eatPatternCharacters=function(e){for(var t=e.pos,n=0;-1!==(n=e.current())&&!De(n);)e.advance();return e.pos!==t},Ot.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();return!(-1===t||36===t||t>=40&&t<=43||46===t||63===t||91===t||94===t||124===t)&&(e.advance(),!0)},Ot.regexp_groupSpecifier=function(e){if(e.eat(63)){if(this.regexp_eatGroupName(e))return-1!==e.groupNames.indexOf(e.lastStringValue)&&e.raise("Duplicate capture group name"),void e.groupNames.push(e.lastStringValue);e.raise("Invalid group")}},Ot.regexp_eatGroupName=function(e){if(e.lastStringValue="",e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62))return!0;e.raise("Invalid capture group name")}return!1},Ot.regexp_eatRegExpIdentifierName=function(e){if(e.lastStringValue="",this.regexp_eatRegExpIdentifierStart(e)){for(e.lastStringValue+=ge(e.lastIntValue);this.regexp_eatRegExpIdentifierPart(e);)e.lastStringValue+=ge(e.lastIntValue);return!0}return!1},Ot.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos,n=this.options.ecmaVersion>=11,r=e.current(n);return e.advance(n),92===r&&this.regexp_eatRegExpUnicodeEscapeSequence(e,n)&&(r=e.lastIntValue),function(e){return re(e,!0)||36===e||95===e}(r)?(e.lastIntValue=r,!0):(e.pos=t,!1)},Ot.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos,n=this.options.ecmaVersion>=11,r=e.current(n);return e.advance(n),92===r&&this.regexp_eatRegExpUnicodeEscapeSequence(e,n)&&(r=e.lastIntValue),function(e){return ie(e,!0)||36===e||95===e||8204===e||8205===e}(r)?(e.lastIntValue=r,!0):(e.pos=t,!1)},Ot.regexp_eatAtomEscape=function(e){return!!(this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e))||(e.switchU&&(99===e.current()&&e.raise("Invalid unicode escape"),e.raise("Invalid escape")),!1)},Ot.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var n=e.lastIntValue;if(e.switchU)return n>e.maxBackReference&&(e.maxBackReference=n),!0;if(n<=e.numCapturingParens)return!0;e.pos=t}return!1},Ot.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e))return e.backReferenceNames.push(e.lastStringValue),!0;e.raise("Invalid named reference")}return!1},Ot.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e,!1)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)},Ot.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e))return!0;e.pos=t}return!1},Ot.regexp_eatZero=function(e){return 48===e.current()&&!ye(e.lookahead())&&(e.lastIntValue=0,e.advance(),!0)},Ot.regexp_eatControlEscape=function(e){var t=e.current();return 116===t?(e.lastIntValue=9,e.advance(),!0):110===t?(e.lastIntValue=10,e.advance(),!0):118===t?(e.lastIntValue=11,e.advance(),!0):102===t?(e.lastIntValue=12,e.advance(),!0):114===t&&(e.lastIntValue=13,e.advance(),!0)},Ot.regexp_eatControlLetter=function(e){var t=e.current();return!!Ae(t)&&(e.lastIntValue=t%32,e.advance(),!0)},Ot.regexp_eatRegExpUnicodeEscapeSequence=function(e,t){void 0===t&&(t=!1);var n,r=e.pos,i=t||e.switchU;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var s=e.lastIntValue;if(i&&s>=55296&&s<=56319){var u=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var a=e.lastIntValue;if(a>=56320&&a<=57343)return e.lastIntValue=1024*(s-55296)+(a-56320)+65536,!0}e.pos=u,e.lastIntValue=s}return!0}if(i&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&((n=e.lastIntValue)>=0&&n<=1114111))return!0;i&&e.raise("Invalid unicode escape"),e.pos=r}return!1},Ot.regexp_eatIdentityEscape=function(e){if(e.switchU)return!!this.regexp_eatSyntaxCharacter(e)||!!e.eat(47)&&(e.lastIntValue=47,!0);var t=e.current();return!(99===t||e.switchN&&107===t)&&(e.lastIntValue=t,e.advance(),!0)},Ot.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do{e.lastIntValue=10*e.lastIntValue+(t-48),e.advance()}while((t=e.current())>=48&&t<=57);return!0}return!1},Ot.regexp_eatCharacterClassEscape=function(e){var t=e.current();if(function(e){return 100===e||68===e||115===e||83===e||119===e||87===e}(t))return e.lastIntValue=-1,e.advance(),!0;if(e.switchU&&this.options.ecmaVersion>=9&&(80===t||112===t)){if(e.lastIntValue=-1,e.advance(),e.eat(123)&&this.regexp_eatUnicodePropertyValueExpression(e)&&e.eat(125))return!0;e.raise("Invalid property name")}return!1},Ot.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var n=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var r=e.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(e,n,r),!0}}if(e.pos=t,this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var i=e.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(e,i),!0}return!1},Ot.regexp_validateUnicodePropertyNameAndValue=function(e,t,n){oe(e.unicodeProperties.nonBinary,t)||e.raise("Invalid property name"),e.unicodeProperties.nonBinary[t].test(n)||e.raise("Invalid property value")},Ot.regexp_validateUnicodePropertyNameOrValue=function(e,t){e.unicodeProperties.binary.test(t)||e.raise("Invalid property name")},Ot.regexp_eatUnicodePropertyName=function(e){var t=0;for(e.lastStringValue="";Ce(t=e.current());)e.lastStringValue+=ge(t),e.advance();return""!==e.lastStringValue},Ot.regexp_eatUnicodePropertyValue=function(e){var t=0;for(e.lastStringValue="";Ee(t=e.current());)e.lastStringValue+=ge(t),e.advance();return""!==e.lastStringValue},Ot.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)},Ot.regexp_eatCharacterClass=function(e){if(e.eat(91)){if(e.eat(94),this.regexp_classRanges(e),e.eat(93))return!0;e.raise("Unterminated character class")}return!1},Ot.regexp_classRanges=function(e){for(;this.regexp_eatClassAtom(e);){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var n=e.lastIntValue;e.switchU&&(-1===t||-1===n)&&e.raise("Invalid character class"),-1!==t&&-1!==n&&t>n&&e.raise("Range out of order in character class")}}},Ot.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e))return!0;if(e.switchU){var n=e.current();(99===n||ve(n))&&e.raise("Invalid class escape"),e.raise("Invalid escape")}e.pos=t}var r=e.current();return 93!==r&&(e.lastIntValue=r,e.advance(),!0)},Ot.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98))return e.lastIntValue=8,!0;if(e.switchU&&e.eat(45))return e.lastIntValue=45,!0;if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e))return!0;e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)},Ot.regexp_eatClassControlLetter=function(e){var t=e.current();return!(!ye(t)&&95!==t)&&(e.lastIntValue=t%32,e.advance(),!0)},Ot.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2))return!0;e.switchU&&e.raise("Invalid escape"),e.pos=t}return!1},Ot.regexp_eatDecimalDigits=function(e){var t=e.pos,n=0;for(e.lastIntValue=0;ye(n=e.current());)e.lastIntValue=10*e.lastIntValue+(n-48),e.advance();return e.pos!==t},Ot.regexp_eatHexDigits=function(e){var t=e.pos,n=0;for(e.lastIntValue=0;xe(n=e.current());)e.lastIntValue=16*e.lastIntValue+Fe(n),e.advance();return e.pos!==t},Ot.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var n=e.lastIntValue;t<=3&&this.regexp_eatOctalDigit(e)?e.lastIntValue=64*t+8*n+e.lastIntValue:e.lastIntValue=8*t+n}else e.lastIntValue=t;return!0}return!1},Ot.regexp_eatOctalDigit=function(e){var t=e.current();return ve(t)?(e.lastIntValue=t-48,e.advance(),!0):(e.lastIntValue=0,!1)},Ot.regexp_eatFixedHexDigits=function(e,t){var n=e.pos;e.lastIntValue=0;for(var r=0;r<t;++r){var i=e.current();if(!xe(i))return e.pos=n,!1;e.lastIntValue=16*e.lastIntValue+Fe(i),e.advance()}return!0},Rt=function(e){this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,e.options.locations&&(this.loc=new et(e,e.startLoc,e.endLoc)),e.options.ranges&&(this.range=[e.start,e.end])},(jt=ut.prototype).next=function(e){!e&&this.type.keyword&&this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword),this.options.onToken&&this.options.onToken(new Rt(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},jt.getToken=function(){return this.next(),new Rt(this)},("undefined"==typeof Symbol?"undefined":d(Symbol))<"u"&&(jt[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===Ge.eof,value:t}}}}),jt.curContext=function(){return this.context[this.context.length-1]},jt.nextToken=function(){var e=this.curContext();return(!e||!e.preserveSpace)&&this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length?this.finishToken(Ge.eof):e.override?e.override(this):void this.readToken(this.fullCharCodeAtPos())},jt.readToken=function(e){return re(e,this.options.ecmaVersion>=6)||92===e?this.readWord():this.getTokenFromCode(e)},jt.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);return e<=55295||e>=57344?e:(e<<10)+this.input.charCodeAt(this.pos+1)-56613888},jt.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition(),t=this.pos,n=this.input.indexOf("*/",this.pos+=2);if(-1===n&&this.raise(this.pos-2,"Unterminated comment"),this.pos=n+2,this.options.locations){$e.lastIndex=t;for(var r;(r=$e.exec(this.input))&&r.index<this.pos;)++this.curLine,this.lineStart=r.index+r[0].length}this.options.onComment&&this.options.onComment(!0,this.input.slice(t+2,n),t,this.pos,e,this.curPosition())},jt.skipLineComment=function(e){for(var t=this.pos,n=this.options.onComment&&this.curPosition(),r=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&!ae(r);)r=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(t+e,this.pos),t,this.pos,n,this.curPosition())},jt.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:10===this.input.charCodeAt(this.pos+1)&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(!(e>8&&e<14||e>=5760&&He.test(String.fromCharCode(e))))break e;++this.pos}}},jt.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var n=this.type;this.type=e,this.value=t,this.updateContext(n)},jt.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&46===e&&46===t?(this.pos+=3,this.finishToken(Ge.ellipsis)):(++this.pos,this.finishToken(Ge.dot))},jt.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):61===e?this.finishOp(Ge.assign,2):this.finishOp(Ge.slash,1)},jt.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),n=1,r=42===e?Ge.star:Ge.modulo;return this.options.ecmaVersion>=7&&42===e&&42===t&&(++n,r=Ge.starstar,t=this.input.charCodeAt(this.pos+2)),61===t?this.finishOp(Ge.assign,n+1):this.finishOp(r,n)},jt.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===e){if(this.options.ecmaVersion>=12)if(61===this.input.charCodeAt(this.pos+2))return this.finishOp(Ge.assign,3);return this.finishOp(124===e?Ge.logicalOR:Ge.logicalAND,2)}return 61===t?this.finishOp(Ge.assign,2):this.finishOp(124===e?Ge.bitwiseOR:Ge.bitwiseAND,1)},jt.readToken_caret=function(){return 61===this.input.charCodeAt(this.pos+1)?this.finishOp(Ge.assign,2):this.finishOp(Ge.bitwiseXOR,1)},jt.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?45!==t||this.inModule||62!==this.input.charCodeAt(this.pos+2)||0!==this.lastTokEnd&&!ze.test(this.input.slice(this.lastTokEnd,this.pos))?this.finishOp(Ge.incDec,2):(this.skipLineComment(3),this.skipSpace(),this.nextToken()):61===t?this.finishOp(Ge.assign,2):this.finishOp(Ge.plusMin,1)},jt.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),n=1;return t===e?(n=62===e&&62===this.input.charCodeAt(this.pos+2)?3:2,61===this.input.charCodeAt(this.pos+n)?this.finishOp(Ge.assign,n+1):this.finishOp(Ge.bitShift,n)):33!==t||60!==e||this.inModule||45!==this.input.charCodeAt(this.pos+2)||45!==this.input.charCodeAt(this.pos+3)?(61===t&&(n=2),this.finishOp(Ge.relational,n)):(this.skipLineComment(4),this.skipSpace(),this.nextToken())},jt.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return 61===t?this.finishOp(Ge.equality,61===this.input.charCodeAt(this.pos+2)?3:2):61===e&&62===t&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(Ge.arrow)):this.finishOp(61===e?Ge.eq:Ge.prefix,1)},jt.readToken_question=function(){var e=this.options.ecmaVersion;if(e>=11){var t=this.input.charCodeAt(this.pos+1);if(46===t){var n=this.input.charCodeAt(this.pos+2);if(n<48||n>57)return this.finishOp(Ge.questionDot,2)}if(63===t){if(e>=12)if(61===this.input.charCodeAt(this.pos+2))return this.finishOp(Ge.assign,3);return this.finishOp(Ge.coalesce,2)}}return this.finishOp(Ge.question,1)},jt.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(Ge.parenL);case 41:return++this.pos,this.finishToken(Ge.parenR);case 59:return++this.pos,this.finishToken(Ge.semi);case 44:return++this.pos,this.finishToken(Ge.comma);case 91:return++this.pos,this.finishToken(Ge.bracketL);case 93:return++this.pos,this.finishToken(Ge.bracketR);case 123:return++this.pos,this.finishToken(Ge.braceL);case 125:return++this.pos,this.finishToken(Ge.braceR);case 58:return++this.pos,this.finishToken(Ge.colon);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(Ge.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(120===t||88===t)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(111===t||79===t)return this.readRadixNumber(8);if(98===t||66===t)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 63:return this.readToken_question();case 126:return this.finishOp(Ge.prefix,1)}this.raise(this.pos,"Unexpected character '"+Se(e)+"'")},jt.finishOp=function(e,t){var n=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,n)},jt.readRegexp=function(){for(var e,t,n=this.pos;;){this.pos>=this.input.length&&this.raise(n,"Unterminated regular expression");var r=this.input.charAt(this.pos);if(ze.test(r)&&this.raise(n,"Unterminated regular expression"),e)e=!1;else{if("["===r)t=!0;else if("]"===r&&t)t=!1;else if("/"===r&&!t)break;e="\\"===r}++this.pos}var i=this.input.slice(n,this.pos);++this.pos;var s=this.pos,u=this.readWord1();this.containsEsc&&this.unexpected(s);var a=this.regexpState||(this.regexpState=new Nt(this));a.reset(n,i,u),this.validateRegExpFlags(a),this.validateRegExpPattern(a);var o=null;try{o=new RegExp(i,u)}catch(c){}return this.finishToken(Ge.regexp,{pattern:i,flags:u,value:o})},jt.readInt=function(e,t,n){for(var r=this.options.ecmaVersion>=12&&void 0===t,i=n&&48===this.input.charCodeAt(this.pos),s=this.pos,u=0,a=0,o=0,c=null!=t?t:1/0;o<c;++o,++this.pos){var p=this.input.charCodeAt(this.pos),l=void 0;if(r&&95===p)i&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals"),95===a&&this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore"),0===o&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits"),a=p;else{if((l=p>=97?p-97+10:p>=65?p-65+10:p>=48&&p<=57?p-48:1/0)>=e)break;a=p,u=u*e+l}}return r&&95===a&&this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits"),this.pos===s||null!=t&&this.pos-s!==t?null:u},jt.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var n=this.readInt(e);return null==n&&this.raise(this.start+2,"Expected number in radix "+e),this.options.ecmaVersion>=11&&110===this.input.charCodeAt(this.pos)?(n=be(this.input.slice(t,this.pos)),++this.pos):re(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(Ge.num,n)},jt.readNumber=function(e){var t=this.pos;!e&&null===this.readInt(10,void 0,!0)&&this.raise(t,"Invalid number");var n=this.pos-t>=2&&48===this.input.charCodeAt(t);n&&this.strict&&this.raise(t,"Invalid number");var r=this.input.charCodeAt(this.pos);if(!n&&!e&&this.options.ecmaVersion>=11&&110===r){var i=be(this.input.slice(t,this.pos));return++this.pos,re(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(Ge.num,i)}n&&/[89]/.test(this.input.slice(t,this.pos))&&(n=!1),46===r&&!n&&(++this.pos,this.readInt(10),r=this.input.charCodeAt(this.pos)),(69===r||101===r)&&!n&&((43===(r=this.input.charCodeAt(++this.pos))||45===r)&&++this.pos,null===this.readInt(10)&&this.raise(t,"Invalid number")),re(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var s,u=(s=this.input.slice(t,this.pos),n?parseInt(s,8):parseFloat(s.replace(/_/g,"")));return this.finishToken(Ge.num,u)},jt.readCodePoint=function(){var e;if(123===this.input.charCodeAt(this.pos)){this.options.ecmaVersion<6&&this.unexpected();var t=++this.pos;e=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,e>1114111&&this.invalidStringToken(t,"Code point out of bounds")}else e=this.readHexChar(4);return e},jt.readString=function(e){for(var t="",n=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var r=this.input.charCodeAt(this.pos);if(r===e)break;92===r?(t+=this.input.slice(n,this.pos),t+=this.readEscapedChar(!1),n=this.pos):(ae(r,this.options.ecmaVersion>=10)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return t+=this.input.slice(n,this.pos++),this.finishToken(Ge.string,t)},Mt={},jt.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(e){if(e!==Mt)throw e;this.readInvalidTemplateToken()}this.inTemplateElement=!1},jt.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw Mt;this.raise(e,t)},jt.readTmplToken=function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var n=this.input.charCodeAt(this.pos);if(96===n||36===n&&123===this.input.charCodeAt(this.pos+1))return this.pos!==this.start||this.type!==Ge.template&&this.type!==Ge.invalidTemplate?(e+=this.input.slice(t,this.pos),this.finishToken(Ge.template,e)):36===n?(this.pos+=2,this.finishToken(Ge.dollarBraceL)):(++this.pos,this.finishToken(Ge.backQuote));if(92===n)e+=this.input.slice(t,this.pos),e+=this.readEscapedChar(!0),t=this.pos;else if(ae(n)){switch(e+=this.input.slice(t,this.pos),++this.pos,n){case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:e+="\n";break;default:e+=String.fromCharCode(n)}this.options.locations&&(++this.curLine,this.lineStart=this.pos),t=this.pos}else++this.pos}},jt.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if("{"!==this.input[this.pos+1])break;case"`":return this.finishToken(Ge.invalidTemplate,this.input.slice(this.start,this.pos))}this.raise(this.start,"Unterminated template")},jt.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return Se(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";case 56:case 57:if(e){var n=this.pos-1;return this.invalidStringToken(n,"Invalid escape sequence in template string"),null}default:if(t>=48&&t<=55){var r=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],i=parseInt(r,8);return i>255&&(r=r.slice(0,-1),i=parseInt(r,8)),this.pos+=r.length-1,t=this.input.charCodeAt(this.pos),("0"!==r||56===t||57===t)&&(this.strict||e)&&this.invalidStringToken(this.pos-1-r.length,e?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(i)}return ae(t)?"":String.fromCharCode(t)}},jt.readHexChar=function(e){var t=this.pos,n=this.readInt(16,e);return null===n&&this.invalidStringToken(t,"Bad character escape sequence"),n},jt.readWord1=function(){this.containsEsc=!1;for(var e="",t=!0,n=this.pos,r=this.options.ecmaVersion>=6;this.pos<this.input.length;){var i=this.fullCharCodeAtPos();if(ie(i,r))this.pos+=i<=65535?1:2;else{if(92!==i)break;this.containsEsc=!0,e+=this.input.slice(n,this.pos);var s=this.pos;117!==this.input.charCodeAt(++this.pos)&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var u=this.readCodePoint();(t?re:ie)(u,r)||this.invalidStringToken(s,"Invalid Unicode escape"),e+=Se(u),n=this.pos}t=!1}return e+this.input.slice(n,this.pos)},jt.readWord=function(){var e=this.readWord1(),t=Ge.name;return this.keywords.test(e)&&(t=We[e]),this.finishToken(t,e)},Vt="7.4.1",ut.acorn={Parser:ut,version:Vt,defaultOptions:tt,Position:Ze,SourceLocation:et,getLineInfo:pe,Node:xt,TokenType:Ve,tokTypes:Ge,keywordTypes:We,TokContext:vt,tokContexts:bt,isIdentifierChar:ie,isIdentifierStart:re,Token:Rt,isNewLine:ae,lineBreak:ze,lineBreakG:$e,nonASCIIwhitespace:He}}}),Ut=a({"../../node_modules/acorn-jsx/xhtml.js":function(e,t){t.exports={quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"}}}),Wt=a({"../../node_modules/acorn-jsx/index.js":function(e,t){var n=Ut(),r=/^[\da-fA-F]+$/,i=/^\d+$/,s=new WeakMap;function a(e){e=e.Parser.acorn||e;var t=s.get(e);if(!t){var n=e.tokTypes,r=e.TokContext,i=e.TokenType,u=new r("<tag",!1),a=new r("</tag",!1),o=new r("<tag>...</tag>",!0,!0),c={tc_oTag:u,tc_cTag:a,tc_expr:o},p={jsxName:new i("jsxName"),jsxText:new i("jsxText",{beforeExpr:!0}),jsxTagStart:new i("jsxTagStart",{startsExpr:!0}),jsxTagEnd:new i("jsxTagEnd")};p.jsxTagStart.updateContext=function(){this.context.push(o),this.context.push(u),this.exprAllowed=!1},p.jsxTagEnd.updateContext=function(e){var t=this.context.pop();t===u&&e===n.slash||t===a?(this.context.pop(),this.exprAllowed=this.curContext()===o):this.exprAllowed=!0},t={tokContexts:c,tokTypes:p},s.set(e,t)}return t}function c(e){return e?"JSXIdentifier"===e.type?e.name:"JSXNamespacedName"===e.type?e.namespace.name+":"+e.name.name:"JSXMemberExpression"===e.type?c(e.object)+"."+c(e.property):void 0:e}t.exports=function(e){return e=e||{},function(t){return function(e,t){var s=t.acorn||(qt(),l(te)),h=a(s),d=s.tokTypes,m=h.tokTypes,g=s.tokContexts,D=h.tokContexts.tc_oTag,A=h.tokContexts.tc_cTag,C=h.tokContexts.tc_expr,E=s.isNewLine,y=s.isIdentifierStart,x=s.isIdentifierChar;return function(t){function s(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),o(this,s,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&f(e,t)}(s,t),a=s,F=[{key:"acornJsx",get:function(){return h}}],(l=[{key:"jsx_readToken",value:function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated JSX contents");var n=this.input.charCodeAt(this.pos);switch(n){case 60:case 123:return this.pos===this.start?60===n&&this.exprAllowed?(++this.pos,this.finishToken(m.jsxTagStart)):this.getTokenFromCode(n):(e+=this.input.slice(t,this.pos),this.finishToken(m.jsxText,e));case 38:e+=this.input.slice(t,this.pos),e+=this.jsx_readEntity(),t=this.pos;break;case 62:case 125:this.raise(this.pos,"Unexpected token `"+this.input[this.pos]+"`. Did you mean `"+(62===n?"&gt;":"&rbrace;")+'` or `{"'+this.input[this.pos]+'"}`?');default:E(n)?(e+=this.input.slice(t,this.pos),e+=this.jsx_readNewLine(!0),t=this.pos):++this.pos}}}},{key:"jsx_readNewLine",value:function(e){var t,n=this.input.charCodeAt(this.pos);return++this.pos,13===n&&10===this.input.charCodeAt(this.pos)?(++this.pos,t=e?"\n":"\r\n"):t=String.fromCharCode(n),this.options.locations&&(++this.curLine,this.lineStart=this.pos),t}},{key:"jsx_readString",value:function(e){for(var t="",n=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var r=this.input.charCodeAt(this.pos);if(r===e)break;38===r?(t+=this.input.slice(n,this.pos),t+=this.jsx_readEntity(),n=this.pos):E(r)?(t+=this.input.slice(n,this.pos),t+=this.jsx_readNewLine(!1),n=this.pos):++this.pos}return t+=this.input.slice(n,this.pos++),this.finishToken(d.string,t)}},{key:"jsx_readEntity",value:function(){var e,t="",s=0,u=this.input[this.pos];"&"!==u&&this.raise(this.pos,"Entity must start with an ampersand");for(var a=++this.pos;this.pos<this.input.length&&s++<10;){if(";"===(u=this.input[this.pos++])){"#"===t[0]?"x"===t[1]?(t=t.substr(2),r.test(t)&&(e=String.fromCharCode(parseInt(t,16)))):(t=t.substr(1),i.test(t)&&(e=String.fromCharCode(parseInt(t,10)))):e=n[t];break}t+=u}return e||(this.pos=a,"&")}},{key:"jsx_readWord",value:function(){var e,t=this.pos;do{e=this.input.charCodeAt(++this.pos)}while(x(e)||45===e);return this.finishToken(m.jsxName,this.input.slice(t,this.pos))}},{key:"jsx_parseIdentifier",value:function(){var e=this.startNode();return this.type===m.jsxName?e.name=this.value:this.type.keyword?e.name=this.type.keyword:this.unexpected(),this.next(),this.finishNode(e,"JSXIdentifier")}},{key:"jsx_parseNamespacedName",value:function(){var t=this.start,n=this.startLoc,r=this.jsx_parseIdentifier();if(!e.allowNamespaces||!this.eat(d.colon))return r;var i=this.startNodeAt(t,n);return i.namespace=r,i.name=this.jsx_parseIdentifier(),this.finishNode(i,"JSXNamespacedName")}},{key:"jsx_parseElementName",value:function(){if(this.type===m.jsxTagEnd)return"";var t=this.start,n=this.startLoc,r=this.jsx_parseNamespacedName();for(this.type===d.dot&&"JSXNamespacedName"===r.type&&!e.allowNamespacedObjects&&this.unexpected();this.eat(d.dot);){var i=this.startNodeAt(t,n);i.object=r,i.property=this.jsx_parseIdentifier(),r=this.finishNode(i,"JSXMemberExpression")}return r}},{key:"jsx_parseAttributeValue",value:function(){switch(this.type){case d.braceL:var e=this.jsx_parseExpressionContainer();return"JSXEmptyExpression"===e.expression.type&&this.raise(e.start,"JSX attributes must only be assigned a non-empty expression"),e;case m.jsxTagStart:case d.string:return this.parseExprAtom();default:this.raise(this.start,"JSX value should be either an expression or a quoted JSX text")}}},{key:"jsx_parseEmptyExpression",value:function(){var e=this.startNodeAt(this.lastTokEnd,this.lastTokEndLoc);return this.finishNodeAt(e,"JSXEmptyExpression",this.start,this.startLoc)}},{key:"jsx_parseExpressionContainer",value:function(){var e=this.startNode();return this.next(),e.expression=this.type===d.braceR?this.jsx_parseEmptyExpression():this.parseExpression(),this.expect(d.braceR),this.finishNode(e,"JSXExpressionContainer")}},{key:"jsx_parseAttribute",value:function(){var e=this.startNode();return this.eat(d.braceL)?(this.expect(d.ellipsis),e.argument=this.parseMaybeAssign(),this.expect(d.braceR),this.finishNode(e,"JSXSpreadAttribute")):(e.name=this.jsx_parseNamespacedName(),e.value=this.eat(d.eq)?this.jsx_parseAttributeValue():null,this.finishNode(e,"JSXAttribute"))}},{key:"jsx_parseOpeningElementAt",value:function(e,t){var n=this.startNodeAt(e,t);n.attributes=[];var r=this.jsx_parseElementName();for(r&&(n.name=r);this.type!==d.slash&&this.type!==m.jsxTagEnd;)n.attributes.push(this.jsx_parseAttribute());return n.selfClosing=this.eat(d.slash),this.expect(m.jsxTagEnd),this.finishNode(n,r?"JSXOpeningElement":"JSXOpeningFragment")}},{key:"jsx_parseClosingElementAt",value:function(e,t){var n=this.startNodeAt(e,t),r=this.jsx_parseElementName();return r&&(n.name=r),this.expect(m.jsxTagEnd),this.finishNode(n,r?"JSXClosingElement":"JSXClosingFragment")}},{key:"jsx_parseElementAt",value:function(e,t){var n=this.startNodeAt(e,t),r=[],i=this.jsx_parseOpeningElementAt(e,t),s=null;if(!i.selfClosing){e:for(;;)switch(this.type){case m.jsxTagStart:if(e=this.start,t=this.startLoc,this.next(),this.eat(d.slash)){s=this.jsx_parseClosingElementAt(e,t);break e}r.push(this.jsx_parseElementAt(e,t));break;case m.jsxText:r.push(this.parseExprAtom());break;case d.braceL:r.push(this.jsx_parseExpressionContainer());break;default:this.unexpected()}c(s.name)!==c(i.name)&&this.raise(s.start,"Expected corresponding JSX closing tag for <"+c(i.name)+">")}var u=i.name?"Element":"Fragment";return n["opening"+u]=i,n["closing"+u]=s,n.children=r,this.type===d.relational&&"<"===this.value&&this.raise(this.start,"Adjacent JSX elements must be wrapped in an enclosing tag"),this.finishNode(n,"JSX"+u)}},{key:"jsx_parseText",value:function(){var e=this.parseLiteral(this.value);return e.type="JSXText",e}},{key:"jsx_parseElement",value:function(){var e=this.start,t=this.startLoc;return this.next(),this.jsx_parseElementAt(e,t)}},{key:"parseExprAtom",value:function(e){return this.type===m.jsxText?this.jsx_parseText():this.type===m.jsxTagStart?this.jsx_parseElement():p(s,"parseExprAtom",this,3)([e])}},{key:"readToken",value:function(e){var t=this.curContext();if(t===C)return this.jsx_readToken();if(t===D||t===A){if(y(e))return this.jsx_readWord();if(62==e)return++this.pos,this.finishToken(m.jsxTagEnd);if((34===e||39===e)&&t==D)return this.jsx_readString(e)}return 60===e&&this.exprAllowed&&33!==this.input.charCodeAt(this.pos+1)?(++this.pos,this.finishToken(m.jsxTagStart)):p(s,"readToken",this,3)([e])}},{key:"updateContext",value:function(e){if(this.type==d.braceL){var t=this.curContext();t==D?this.context.push(g.b_expr):t==C?this.context.push(g.b_tmpl):p(s,"updateContext",this,3)([e]),this.exprAllowed=!0}else{if(this.type!==d.slash||e!==m.jsxTagStart)return p(s,"updateContext",this,3)([e]);this.context.length-=2,this.context.push(A),this.exprAllowed=!1}}}])&&u(a.prototype,l),F&&u(a,F),Object.defineProperty(a,"prototype",{writable:!1}),a;var a,l,F}(t)}({allowNamespaces:!1!==e.allowNamespaces,allowNamespacedObjects:!!e.allowNamespacedObjects},t)}},Object.defineProperty(t.exports,"tokTypes",{get:function(){return a((qt(),l(te))).tokTypes},configurable:!0,enumerable:!0})}}),Gt=a({"../../node_modules/@base2/pretty-print-object/dist/index.js":function(e){var t=e&&e.__assign||function(){return t=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},t.apply(this,arguments)},n=e&&e.__spreadArrays||function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),i=0;for(t=0;t<n;t++)for(var s=arguments[t],u=0,a=s.length;u<a;u++,i++)r[i]=s[u];return r};Object.defineProperty(e,"__esModule",{value:!0});var r=[];e.prettyPrint=function e(i,s,u){void 0===u&&(u="");var a,o=t(t({},{indent:"\t",singleQuotes:!0}),s);a=void 0===o.inlineCharacterLimit?{newLine:"\n",newLineOrSpace:"\n",pad:u,indent:u+o.indent}:{newLine:"@@__PRETTY_PRINT_NEW_LINE__@@",newLineOrSpace:"@@__PRETTY_PRINT_NEW_LINE_OR_SPACE__@@",pad:"@@__PRETTY_PRINT_PAD__@@",indent:"@@__PRETTY_PRINT_INDENT__@@"};var c,p,l=function(e){if(void 0===o.inlineCharacterLimit)return e;var t=e.replace(new RegExp(a.newLine,"g"),"").replace(new RegExp(a.newLineOrSpace,"g")," ").replace(new RegExp(a.pad+"|"+a.indent,"g"),"");return t.length<=o.inlineCharacterLimit?t:e.replace(new RegExp(a.newLine+"|"+a.newLineOrSpace,"g"),"\n").replace(new RegExp(a.pad,"g"),u).replace(new RegExp(a.indent,"g"),u+o.indent)};if(-1!==r.indexOf(i))return'"[Circular]"';if(null==i||"number"==typeof i||"boolean"==typeof i||"function"==typeof i||"symbol"==d(i)||(c=i,"[object RegExp]"===Object.prototype.toString.call(c)))return String(i);if(i instanceof Date)return"new Date('"+i.toISOString()+"')";if(Array.isArray(i)){if(0===i.length)return"[]";r.push(i);var h="["+a.newLine+i.map((function(t,n){var r=i.length-1===n?a.newLine:","+a.newLineOrSpace,s=e(t,o,u+o.indent);return o.transform&&(s=o.transform(i,n,s)),a.indent+s+r})).join("")+a.pad+"]";return r.pop(),l(h)}if(function(e){var t=d(e);return null!==e&&("object"===t||"function"===t)}(i)){var f=n(Object.keys(i),(p=i,Object.getOwnPropertySymbols(p).filter((function(e){return Object.prototype.propertyIsEnumerable.call(p,e)}))));if(o.filter&&(f=f.filter((function(e){return o.filter&&o.filter(i,e)}))),0===f.length)return"{}";r.push(i);h="{"+a.newLine+f.map((function(t,n){var r=f.length-1===n?a.newLine:","+a.newLineOrSpace,s="symbol"==d(t),c=!s&&/^[a-z$_][a-z$_0-9]*$/i.test(t.toString()),p=s||c?t:e(t,o),l=e(i[t],o,u+o.indent);return o.transform&&(l=o.transform(i,t,l)),a.indent+String(p)+": "+l+r})).join("")+a.pad+"}";return r.pop(),l(h)}return i=String(i).replace(/[\r\n]/g,(function(e){return"\n"===e?"\\n":"\\r"})),o.singleQuotes?"'"+(i=i.replace(/\\?'/g,"\\'"))+"'":'"'+(i=i.replace(/"/g,'\\"'))+'"'}}}),zt=a({"../../node_modules/react-element-to-jsx-string/node_modules/react-is/cjs/react-is.production.min.js":function(e){var t,n=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),o=Symbol.for("react.context"),c=Symbol.for("react.server_context"),p=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),f=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function D(e){if("object"==d(e)&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case i:case u:case s:case l:case h:return e;default:switch(e=e&&e.$$typeof){case c:case o:case p:case m:case f:case a:return e;default:return t}}case r:return t}}}t=Symbol.for("react.module.reference"),e.ContextConsumer=o,e.ContextProvider=a,e.Element=n,e.ForwardRef=p,e.Fragment=i,e.Lazy=m,e.Memo=f,e.Portal=r,e.Profiler=u,e.StrictMode=s,e.Suspense=l,e.SuspenseList=h,e.isAsyncMode=function(){return!1},e.isConcurrentMode=function(){return!1},e.isContextConsumer=function(e){return D(e)===o},e.isContextProvider=function(e){return D(e)===a},e.isElement=function(e){return"object"==d(e)&&null!==e&&e.$$typeof===n},e.isForwardRef=function(e){return D(e)===p},e.isFragment=function(e){return D(e)===i},e.isLazy=function(e){return D(e)===m},e.isMemo=function(e){return D(e)===f},e.isPortal=function(e){return D(e)===r},e.isProfiler=function(e){return D(e)===u},e.isStrictMode=function(e){return D(e)===s},e.isSuspense=function(e){return D(e)===l},e.isSuspenseList=function(e){return D(e)===h},e.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===u||e===s||e===l||e===h||e===g||"object"==d(e)&&null!==e&&(e.$$typeof===m||e.$$typeof===f||e.$$typeof===a||e.$$typeof===o||e.$$typeof===p||e.$$typeof===t||void 0!==e.getModuleId)},e.typeOf=D}}),$t=a({"../../node_modules/react-element-to-jsx-string/node_modules/react-is/index.js":function(e,t){t.exports=zt()}});h({},{applyDecorators:function(){return Mr},argTypesEnhancers:function(){return Ur},decorators:function(){return qr},parameters:function(){return Vr}});var Ht=s(L()),Xt="custom",Jt="object",Yt="array",Kt="func",Qt="element",Zt=s(N());function en(e){return Zt.default.includes(e.toLowerCase())}var tn=s(ee());var nn={format:{indent:{style:"  "},semicolons:!1}},rn=i(i({},nn),{},{format:{newline:""}}),sn=i({},nn);function un(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return(0,tn.generate)(e,t?rn:sn)}function an(e){return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?function(e){var t=un(e,!0);return t.endsWith(" }")||(t="".concat(t.slice(0,-1)," }")),t}(e):un(e)}function on(e){return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?function(e){var t=un(e,!0);return t.startsWith("[    ")&&(t=t.replace("[    ","[")),t}(e):function(e){var t=un(e);return t.endsWith("  }]")&&(t=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=Array.from("string"==typeof e?[e]:e);r[r.length-1]=r[r.length-1].replace(/\r?\n([\t ]*)$/,"");var i=r.reduce((function(e,t){var n=t.match(/\n([\t ]+|(?!\s).)/g);return n?e.concat(n.map((function(e){var t,n;return null!==(n=null===(t=e.match(/[\t ]/g))||void 0===t?void 0:t.length)&&void 0!==n?n:0}))):e}),[]);if(i.length){var s=new RegExp("\n[\t ]{"+Math.min.apply(Math,i)+"}","g");r=r.map((function(e){return e.replace(s,"\n")}))}r[0]=r[0].replace(/^\r?\n/,"");var u=r[0];return t.forEach((function(e,t){var n=u.match(/(?:^|\n)( *)$/),i=n?n[1]:"",s=e;"string"==typeof e&&e.includes("\n")&&(s=String(e).split("\n").map((function(e,t){return 0===t?e:""+i+e})).join("\n")),u+=s+r[t+1]})),u}(t)),t}(e)}var cn=function(e){return e.$$typeof===Symbol.for("react.memo")};qt();var pn=s(Wt());function ln(e,t,n,r,i){n||(n=dn),function e(r,i,s){var u=s||r.type,a=t[u];n[u](r,i,e),a&&a(r,i)}(e,r,i)}function hn(e,t,n){n(e,t)}function fn(e,t,n){}var dn={};dn.Program=dn.BlockStatement=function(e,t,n){for(var r=0,i=e.body;r<i.length;r+=1){n(i[r],t,"Statement")}},dn.Statement=hn,dn.EmptyStatement=fn,dn.ExpressionStatement=dn.ParenthesizedExpression=dn.ChainExpression=function(e,t,n){return n(e.expression,t,"Expression")},dn.IfStatement=function(e,t,n){n(e.test,t,"Expression"),n(e.consequent,t,"Statement"),e.alternate&&n(e.alternate,t,"Statement")},dn.LabeledStatement=function(e,t,n){return n(e.body,t,"Statement")},dn.BreakStatement=dn.ContinueStatement=fn,dn.WithStatement=function(e,t,n){n(e.object,t,"Expression"),n(e.body,t,"Statement")},dn.SwitchStatement=function(e,t,n){n(e.discriminant,t,"Expression");for(var r=0,i=e.cases;r<i.length;r+=1){var s=i[r];s.test&&n(s.test,t,"Expression");for(var u=0,a=s.consequent;u<a.length;u+=1){n(a[u],t,"Statement")}}},dn.SwitchCase=function(e,t,n){e.test&&n(e.test,t,"Expression");for(var r=0,i=e.consequent;r<i.length;r+=1){n(i[r],t,"Statement")}},dn.ReturnStatement=dn.YieldExpression=dn.AwaitExpression=function(e,t,n){e.argument&&n(e.argument,t,"Expression")},dn.ThrowStatement=dn.SpreadElement=function(e,t,n){return n(e.argument,t,"Expression")},dn.TryStatement=function(e,t,n){n(e.block,t,"Statement"),e.handler&&n(e.handler,t),e.finalizer&&n(e.finalizer,t,"Statement")},dn.CatchClause=function(e,t,n){e.param&&n(e.param,t,"Pattern"),n(e.body,t,"Statement")},dn.WhileStatement=dn.DoWhileStatement=function(e,t,n){n(e.test,t,"Expression"),n(e.body,t,"Statement")},dn.ForStatement=function(e,t,n){e.init&&n(e.init,t,"ForInit"),e.test&&n(e.test,t,"Expression"),e.update&&n(e.update,t,"Expression"),n(e.body,t,"Statement")},dn.ForInStatement=dn.ForOfStatement=function(e,t,n){n(e.left,t,"ForInit"),n(e.right,t,"Expression"),n(e.body,t,"Statement")},dn.ForInit=function(e,t,n){"VariableDeclaration"===e.type?n(e,t):n(e,t,"Expression")},dn.DebuggerStatement=fn,dn.FunctionDeclaration=function(e,t,n){return n(e,t,"Function")},dn.VariableDeclaration=function(e,t,n){for(var r=0,i=e.declarations;r<i.length;r+=1){n(i[r],t)}},dn.VariableDeclarator=function(e,t,n){n(e.id,t,"Pattern"),e.init&&n(e.init,t,"Expression")},dn.Function=function(e,t,n){e.id&&n(e.id,t,"Pattern");for(var r=0,i=e.params;r<i.length;r+=1){n(i[r],t,"Pattern")}n(e.body,t,e.expression?"Expression":"Statement")},dn.Pattern=function(e,t,n){"Identifier"===e.type?n(e,t,"VariablePattern"):"MemberExpression"===e.type?n(e,t,"MemberPattern"):n(e,t)},dn.VariablePattern=fn,dn.MemberPattern=hn,dn.RestElement=function(e,t,n){return n(e.argument,t,"Pattern")},dn.ArrayPattern=function(e,t,n){for(var r=0,i=e.elements;r<i.length;r+=1){var s=i[r];s&&n(s,t,"Pattern")}},dn.ObjectPattern=function(e,t,n){for(var r=0,i=e.properties;r<i.length;r+=1){var s=i[r];"Property"===s.type?(s.computed&&n(s.key,t,"Expression"),n(s.value,t,"Pattern")):"RestElement"===s.type&&n(s.argument,t,"Pattern")}},dn.Expression=hn,dn.ThisExpression=dn.Super=dn.MetaProperty=fn,dn.ArrayExpression=function(e,t,n){for(var r=0,i=e.elements;r<i.length;r+=1){var s=i[r];s&&n(s,t,"Expression")}},dn.ObjectExpression=function(e,t,n){for(var r=0,i=e.properties;r<i.length;r+=1){n(i[r],t)}},dn.FunctionExpression=dn.ArrowFunctionExpression=dn.FunctionDeclaration,dn.SequenceExpression=function(e,t,n){for(var r=0,i=e.expressions;r<i.length;r+=1){n(i[r],t,"Expression")}},dn.TemplateLiteral=function(e,t,n){for(var r=0,i=e.quasis;r<i.length;r+=1){n(i[r],t)}for(var s=0,u=e.expressions;s<u.length;s+=1){n(u[s],t,"Expression")}},dn.TemplateElement=fn,dn.UnaryExpression=dn.UpdateExpression=function(e,t,n){n(e.argument,t,"Expression")},dn.BinaryExpression=dn.LogicalExpression=function(e,t,n){n(e.left,t,"Expression"),n(e.right,t,"Expression")},dn.AssignmentExpression=dn.AssignmentPattern=function(e,t,n){n(e.left,t,"Pattern"),n(e.right,t,"Expression")},dn.ConditionalExpression=function(e,t,n){n(e.test,t,"Expression"),n(e.consequent,t,"Expression"),n(e.alternate,t,"Expression")},dn.NewExpression=dn.CallExpression=function(e,t,n){if(n(e.callee,t,"Expression"),e.arguments)for(var r=0,i=e.arguments;r<i.length;r+=1){n(i[r],t,"Expression")}},dn.MemberExpression=function(e,t,n){n(e.object,t,"Expression"),e.computed&&n(e.property,t,"Expression")},dn.ExportNamedDeclaration=dn.ExportDefaultDeclaration=function(e,t,n){e.declaration&&n(e.declaration,t,"ExportNamedDeclaration"===e.type||e.declaration.id?"Statement":"Expression"),e.source&&n(e.source,t,"Expression")},dn.ExportAllDeclaration=function(e,t,n){e.exported&&n(e.exported,t),n(e.source,t,"Expression")},dn.ImportDeclaration=function(e,t,n){for(var r=0,i=e.specifiers;r<i.length;r+=1){n(i[r],t)}n(e.source,t,"Expression")},dn.ImportExpression=function(e,t,n){n(e.source,t,"Expression")},dn.ImportSpecifier=dn.ImportDefaultSpecifier=dn.ImportNamespaceSpecifier=dn.Identifier=dn.Literal=fn,dn.TaggedTemplateExpression=function(e,t,n){n(e.tag,t,"Expression"),n(e.quasi,t,"Expression")},dn.ClassDeclaration=dn.ClassExpression=function(e,t,n){return n(e,t,"Class")},dn.Class=function(e,t,n){e.id&&n(e.id,t,"Pattern"),e.superClass&&n(e.superClass,t,"Expression"),n(e.body,t)},dn.ClassBody=function(e,t,n){for(var r=0,i=e.body;r<i.length;r+=1){n(i[r],t)}},dn.MethodDefinition=dn.Property=function(e,t,n){e.computed&&n(e.key,t,"Expression"),n(e.value,t,"Expression")};var mn=i(i({},dn),{},{JSXElement:function(){}}),gn=ut.extend((0,pn.default)());function Dn(e){return null!=e?e.name:null}function An(e){return e.filter((function(e){return"ObjectExpression"===e.type||"ArrayExpression"===e.type}))}function Cn(e){var t=[];return function(e,t,n,r,i){var s=[];n||(n=dn),function e(r,i,u){var a=u||r.type,o=t[a],c=r!==s[s.length-1];c&&s.push(r),n[a](r,i,e),o&&o(r,i||s,s),c&&s.pop()}(e,r,i)}(e,{ObjectExpression:function(e,n){t.push(An(n).length)},ArrayExpression:function(e,n){t.push(An(n).length)}},mn),Math.max.apply(Math,t)}function En(e){return{inferredType:{type:"Object",depth:Cn(e)},ast:e}}function yn(e){switch(e.type){case"Identifier":return{inferredType:{type:"Identifier",identifier:Dn(o=e)},ast:o};case"Literal":return{inferredType:{type:"Literal"},ast:e};case"FunctionExpression":case"ArrowFunctionExpression":return function(e){var t;ln(e.body,{JSXElement:function(e){t=e}},mn);var n={type:null!=t?"Element":"Function",params:e.params,hasParams:0!==e.params.length},r=Dn(e.id);return null!=r&&(n.identifier=r),{inferredType:n,ast:e}}(e);case"ClassExpression":return ln((u=e).body,{JSXElement:function(e){a=e}},mn),{inferredType:{type:null!=a?"Element":"Class",identifier:Dn(u.id)},ast:u};case"JSXElement":return i={type:"Element"},null!=(s=Dn((r=e).openingElement.name))&&(i.identifier=s),{inferredType:i,ast:r};case"CallExpression":return"shape"===Dn("MemberExpression"===(n=e).callee.type?n.callee.property:n.callee)?En(n.arguments[0]):null;case"ObjectExpression":return En(e);case"ArrayExpression":return{inferredType:{type:"Array",depth:Cn(t=e)},ast:t};default:return null}var t,n,r,i,s,u,a,o}function xn(e){try{return i({},function(e){var t=gn.parse("(".concat(e,")"),{ecmaVersion:2020}),n={inferredType:{type:"Unknown"},ast:t};if(null!=t.body[0]){var r=t.body[0];if("ExpressionStatement"===r.type){var i=yn(r.expression);null!=i&&(n=i)}}return n}(e))}catch(t){}return{inferredType:{type:"Unknown"}}}function Fn(e){var t=e.inferredType,n=e.ast;if(t.depth<=2){var r=on(n,!0);if(!v(r))return x(r)}return x(Yt,on(n))}function vn(e){var t=e.inferredType,n=e.ast;if(1===t.depth){var r=an(n,!0);if(!v(r))return x(r)}return x(Jt,an(n))}function bn(e,t){return"".concat(e,t?"( ... )":"()")}function Sn(e){return"<".concat(e," />")}function Bn(e){var t=e.type,n=e.identifier;switch(t){case"Function":return bn(n,e.hasParams);case"Element":return Sn(n);default:return n}}function _n(e){try{var t=xn(e);switch(t.inferredType.type){case"Object":return vn(t);case"Function":return function(e){var t=e.inferredType,n=e.ast;if(null!=t.identifier)return x(Bn(t),un(n));var r=un(n,!0);return v(r)?x(Kt,un(n)):x(r)}(t);case"Element":return function(e,t){var n=t.inferredType,r=n.identifier;if(null!=r&&!en(r)){var i=Bn(n);return x(i,e)}return v(e)?x(Qt,e):x(e)}(e,t);case"Array":return Fn(t);default:return null}}catch(n){console.error(n)}return null}function wn(e){return"function"==typeof e}function kn(e){return"[object Object]"===Object.prototype.toString.call(e)}var Pn=s(Gt()),In=s($t()),Tn=function(e,t){return 0===e?"":new Array(e*t).fill(" ").join("")};function Ln(e){return(Ln="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function On(e){return function(e){if(Array.isArray(e))return Nn(e)}(e)||function(e){if(("undefined"==typeof Symbol?"undefined":d(Symbol))<"u"&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Nn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Nn(e,t)}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Rn(e,t){return null===e||"object"!==Ln(e)||e instanceof Date||e instanceof RegExp||S.isValidElement(e)?e:(t.add(e),Array.isArray(e)?e.map((function(e){return Rn(e,t)})):Object.keys(e).sort().reduce((function(n,r){return"_owner"===r||("current"===r||t.has(e[r])?n[r]="[Circular]":n[r]=Rn(e[r],t)),n}),{}))}function jn(e){return Rn(e,new WeakSet)}var Mn=function(e){return{type:"string",value:e}},Vn=!!S.Fragment,qn=function(e){return e.name&&"_default"!==e.name?e.name:"No Display Name"},Un=function e(t){switch(!0){case!!t.displayName:return t.displayName;case t.$$typeof===In.Memo:return e(t.type);case t.$$typeof===In.ForwardRef:return e(t.render);default:return qn(t)}},Wn=function(e){switch(!0){case"string"==typeof e.type:return e.type;case"function"==typeof e.type:return e.type.displayName?e.type.displayName:qn(e.type);case(0,In.isForwardRef)(e):case(0,In.isMemo)(e):return Un(e.type);case(0,In.isContextConsumer)(e):return"".concat(e.type._context.displayName||"Context",".Consumer");case(0,In.isContextProvider)(e):return"".concat(e.type._context.displayName||"Context",".Provider");case(0,In.isLazy)(e):return"Lazy";case(0,In.isProfiler)(e):return"Profiler";case(0,In.isStrictMode)(e):return"StrictMode";case(0,In.isSuspense)(e):return"Suspense";default:return"UnknownElementType"}},Gn=function(e,t){return"children"!==t},zn=function(e){return!0!==e&&!1!==e&&null!==e&&""!==e},$n=function(e,t){var n={};return Object.keys(e).filter((function(n){return t(e[n],n)})).forEach((function(t){return n[t]=e[t]})),n},Hn=function e(t,n){var r=n.displayName,i=void 0===r?Wn:r;if("string"==typeof t)return Mn(t);if("number"==typeof t)return{type:"number",value:t};if(!B.isValidElement(t))throw new Error("react-element-to-jsx-string: Expected a React.Element, got `".concat(Ln(t),"`"));var s=i(t),u=$n(t.props,Gn);null!==t.ref&&(u.ref=t.ref);var a=t.key;"string"==typeof a&&a.search(/^\./)&&(u.key=a);var o=$n(t.type.defaultProps||{},Gn),c=B.Children.toArray(t.props.children).filter(zn).map((function(t){return e(t,n)}));return Vn&&t.type===S.Fragment?function(e,t){return{type:"ReactFragment",key:e,childrens:t}}(a,c):function(e,t,n,r){return{type:"ReactElement",displayName:e,props:t,defaultProps:n,childrens:r}}(s,u,o,c)};function Xn(){}var Jn=function(e){return e.toString().split("\n").map((function(e){return e.trim()})).join("")},Yn=function(e,t){var n=t.functionValue,r=void 0===n?Jn:n;return r(t.showFunctions||r!==Jn?e:Xn)},Kn=function(e,t,n,r){if("number"==typeof e)return"{".concat(String(e),"}");if("string"==typeof e)return'"'.concat(e.replace(/"/g,"&quot;"),'"');var i,s,u;if("symbol"===Ln(e)){var a=e.valueOf().toString().replace(/Symbol\((.*)\)/,"$1");return a?"{Symbol('".concat(a,"')}"):"{Symbol()}"}return"function"==typeof e?"{".concat(Yn(e,r),"}"):S.isValidElement(e)?"{".concat(sr(Hn(e,r),!0,n,r),"}"):e instanceof Date?isNaN(e.valueOf())?"{new Date(NaN)}":'{new Date("'.concat(e.toISOString(),'")}'):!1!==kn(i=e)&&(void 0===(s=i.constructor)||!1!==kn(u=s.prototype)&&!1!==u.hasOwnProperty("isPrototypeOf"))||Array.isArray(e)?"{".concat(function(e,t,n,r){var i=jn(e),s=(0,Pn.prettyPrint)(i,{transform:function(e,t,i){var s=e[t];return s&&S.isValidElement(s)?sr(Hn(s,r),!0,n,r):"function"==typeof s?Yn(s,r):i}});return t?s.replace(/\s+/g," ").replace(/{ /g,"{").replace(/ }/g,"}").replace(/\[ /g,"[").replace(/ ]/g,"]"):s.replace(/\t/g,Tn(1,r.tabStop)).replace(/\n([^$])/g,"\n".concat(Tn(n+1,r.tabStop),"$1"))}(e,t,n,r),"}"):"{".concat(String(e),"}")},Qn=function(e,t){var n=e.slice(0,e.length>0?e.length-1:0),r=e[e.length-1];return!r||"string"!==t.type&&"number"!==t.type||"string"!==r.type&&"number"!==r.type?(r&&n.push(r),n.push(t)):n.push(Mn(String(r.value)+String(t.value))),n};var Zn=function(e,t,n){return function(r){return function(e,t,n,r,i){var s=i.tabStop;return"string"===e.type?t.split("\n").map((function(e,t){return 0===t?e:"".concat(Tn(r,s)).concat(e)})).join("\n"):t}(r,sr(r,e,t,n),0,t,n)}},er=function(e,t,n,r,i){return i?Tn(n,r).length+t.length>i:e.length>1},tr=function(e,t,n,r){var i=e.type,s=e.displayName,u=void 0===s?"":s,a=e.childrens,o=e.props,c=void 0===o?{}:o,p=e.defaultProps,l=void 0===p?{}:p;if("ReactElement"!==i)throw new Error('The "formatReactElementNode" function could only format node of type "ReactElement". Given:  '.concat(i));var h=r.filterProps,f=r.maxInlineAttributesLineLength,d=r.showDefaultProps,m=r.sortProps,g=r.tabStop,D="<".concat(u),A=D,C=D,E=!1,y=[],x=function(e,t){return Array.isArray(t)?function(e){return-1===t.indexOf(e)}:function(n){return t(e[n],n)}}(c,h);Object.keys(c).filter(x).filter(function(e,t){return function(n){var r=Object.keys(e).includes(n);return!r||r&&e[n]!==t[n]}}(l,c)).forEach((function(e){return y.push(e)})),Object.keys(l).filter(x).filter((function(){return d})).filter((function(e){return!y.includes(e)})).forEach((function(e){return y.push(e)}));var F,v=(F=m,function(e){var t=e.includes("key"),n=e.includes("ref"),r=e.filter((function(e){return!["key","ref"].includes(e)})),i=On(F?r.sort():r);return n&&i.unshift("ref"),t&&i.unshift("key"),i})(y);if(v.forEach((function(e){var i=function(e,t,n,r,i,s,u,a){if(!t&&!r)throw new Error('The prop "'.concat(e,'" has no value and no default: could not be formatted'));var o=t?n:i,c=a.useBooleanShorthandSyntax,p=a.tabStop,l=Kn(o,s,u,a),h=" ",f="\n".concat(Tn(u+1,p)),d=l.includes("\n");return c&&"{false}"===l&&!r?(h="",f=""):c&&"{true}"===l?(h+="".concat(e),f+="".concat(e)):(h+="".concat(e,"=").concat(l),f+="".concat(e,"=").concat(l)),{attributeFormattedInline:h,attributeFormattedMultiline:f,isMultilineAttribute:d}}(e,Object.keys(c).includes(e),c[e],Object.keys(l).includes(e),l[e],t,n,r),s=i.attributeFormattedInline,u=i.attributeFormattedMultiline;i.isMultilineAttribute&&(E=!0),A+=s,C+=u})),C+="\n".concat(Tn(n,g)),D=function(e,t,n,r,i,s,u){return(er(e,t,i,s,u)||n)&&!r}(v,A,E,t,n,g,f)?C:A,a&&a.length>0){var b=n+1;D+=">",t||(D+="\n",D+=Tn(b,g)),D+=a.reduce(Qn,[]).map(Zn(t,b,r)).join(t?"":"\n".concat(Tn(b,g))),t||(D+="\n",D+=Tn(b-1,g)),D+="</".concat(u,">")}else er(v,A,n,g,f)||(D+=" "),D+="/>";return D},nr="React.Fragment",rr=["<",">","{","}"],ir=function(e){return function(e){return rr.some((function(t){return e.includes(t)}))}(e)?"{`".concat(e,"`}"):e},sr=function(e,t,n,r){if("number"===e.type)return String(e.value);if("string"===e.type)return e.value?"".concat((i=ir(String(e.value)),(s=i).endsWith(" ")&&(s=s.replace(/^(.*?)(\s+)$/,"$1{'$2'}")),s.startsWith(" ")&&(s=s.replace(/^(\s+)(.*)$/,"{'$1'}$2")),s)):"";var i,s;if("ReactElement"===e.type)return tr(e,t,n,r);if("ReactFragment"===e.type)return function(e,t,n,r){var i,s=e.type,u=e.key,a=e.childrens;if("ReactFragment"!==s)throw new Error('The "formatReactFragmentNode" function could only format node of type "ReactFragment". Given: '.concat(s));return i=r.useFragmentShortSyntax?0===e.childrens.length||e.key?nr:"":nr,tr(function(e,t,n){var r={};return t&&(r={key:t}),{type:"ReactElement",displayName:e,props:r,defaultProps:{},childrens:n}}(i,u,a),t,n,r)}(e,t,n,r);throw new TypeError('Unknow format type "'.concat(e.type,'"'))},ur=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.filterProps,r=void 0===n?[]:n,i=t.showDefaultProps,s=void 0===i||i,u=t.showFunctions,a=void 0!==u&&u,o=t.functionValue,c=t.tabStop,p=void 0===c?2:c,l=t.useBooleanShorthandSyntax,h=void 0===l||l,f=t.useFragmentShortSyntax,d=void 0===f||f,m=t.sortProps,g=void 0===m||m,D=t.maxInlineAttributesLineLength,A=t.displayName;if(!e)throw new Error("react-element-to-jsx-string: Expected a ReactElement");var C={filterProps:r,showDefaultProps:s,showFunctions:a,functionValue:o,tabStop:p,useBooleanShorthandSyntax:h,useFragmentShortSyntax:d,sortProps:g,maxInlineAttributesLineLength:D,displayName:A};return function(e,t){return sr(e,!1,0,t)}(Hn(e,C),C)},ar=ur;function or(e){return null!=e.$$typeof}function cr(e,t){var n=e.name;return""!==n&&"anonymous"!==n&&n!==t?n:null}function pr(e){var t,n=e.type,r=n.displayName,i=ar(e,{});if(null!=r){var s=Sn(r);return x(s,i)}if(("string"==typeof(t=n)||t instanceof String)&&en(n)){var u=ar(e,{tabStop:0}).replace(/\r?\n|\r/g,"");if(!v(u))return x(u)}return x(Qt,i)}var lr={string:function(e){return x(JSON.stringify(e))},object:function(e){return or(e)&&null!=e.type?pr(e):function(e){if("object"!=d(e)||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){var t,n=e[Symbol.toStringTag];return!(null==n||null===(t=Object.getOwnPropertyDescriptor(e,Symbol.toStringTag))||void 0===t||!t.writable)&&e.toString()==="[object ".concat(n,"]")}for(var r=e;null!==Object.getPrototypeOf(r);)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(e)===r}(e)?vn(xn(JSON.stringify(e))):Array.isArray(e)?Fn(xn(JSON.stringify(e))):x(Jt)},function:function(e,t){var n,r=!1;if(wn(e.render))r=!0;else if(null!=e.prototype&&wn(e.prototype.render))r=!0;else{var i;try{var s=(n=xn(e.toString())).inferredType,u=s.hasParams,a=s.params;u?1===a.length&&"ObjectPattern"===a[0].type&&(i=e({})):i=e(),null!=i&&or(i)&&(r=!0)}catch(p){}}var o=cr(e,t.name);if(null!=o){if(r)return x(Sn(o));null!=n&&(n=xn(e.toString()));var c=n.inferredType.hasParams;return x(bn(o,c))}return x(r?Qt:Kt)},default:function(e){return x(e.toString())}};function hr(e,t){var n=null!=e,r=null!=t;if(!n&&!r)return"";var i=[];if(n){var s=e.map((function(e){var t=e.getPrettyName(),n=e.getTypeName();return null!=n?"".concat(t,": ").concat(n):t}));i.push("(".concat(s.join(", "),")"))}else i.push("()");return r&&i.push("=> ".concat(t.getTypeName())),i.join(" ")}function fr(e,t){var n=null!=e,r=null!=t;if(!n&&!r)return"";var i=[];return n?i.push("( ... )"):i.push("()"),r&&i.push("=> ".concat(t.getTypeName())),i.join(" ")}function dr(e){var t=e.name,n=e.short,r=e.compact,i=e.full;return{name:t,short:n,compact:r,full:null!=i?i:n,inferredType:e.inferredType}}function mr(e){return e.replace(/PropTypes./g,"").replace(/.isRequired/g,"")}function gr(e){return e.split(/\r?\n/)}function Dr(e){return mr(an(e,arguments.length>1&&void 0!==arguments[1]&&arguments[1]))}function Ar(e){return mr(un(e,arguments.length>1&&void 0!==arguments[1]&&arguments[1]))}function Cr(e,t){var n,r,i,s=xn(e),u=s.inferredType,a=s.ast,o=u.type;switch(o){case"Identifier":case"Literal":n=e,r=e;break;case"Object":var c=u.depth;n=Jt,r=1===c?Dr(a,!0):null,i=Dr(a);break;case"Element":var p=u.identifier;n=null==p||en(p)?Qt:p,r=1===gr(e).length?e:null,i=e;break;case"Array":var l=u.depth;n=Yt,r=l<=2?Ar(a,!0):null,i=Ar(a);break;default:n=function(e){switch(e){case"Object":return Jt;case"Array":return Yt;case"Class":return"class";case"Function":return Kt;case"Element":return Qt;default:return Xt}}(o),r=1===gr(e).length?e:null,i=e}return dr({name:t,short:n,compact:r,full:i,inferredType:o})}function Er(e){return"objectOf(".concat(e,")")}function yr(e){if(Array.isArray(e.value)){var t=e.value.reduce((function(e,t){var n,r,i=(r=(n=t).value,n.computed?Cr(r,"enumvalue"):dr({name:"enumvalue",short:r,compact:r})),s=i.short,u=i.compact,a=i.full;return e.short.push(s),e.compact.push(u),e.full.push(a),e}),{short:[],compact:[],full:[]});return dr({name:"enum",short:t.short.join(" | "),compact:t.compact.every((function(e){return null!=e}))?t.compact.join(" | "):null,full:t.full.join(" | ")})}return dr({name:"enum",short:e.value,compact:e.value})}function xr(e){return"".concat(e,"[]")}function Fr(e){return"[".concat(e,"]")}function vr(e,t,n){return dr({name:"arrayOf",short:xr(e),compact:null!=t?Fr(t):null,full:n&&Fr(n)})}function br(e,t){try{switch(e.name){case"custom":return null!=(n=e.raw)?Cr(n,"custom"):dr({name:"custom",short:Xt,compact:Xt});case"func":return function(e){var t=e.jsDocTags;return null==t||null==t.params&&null==t.returns?dr({name:"func",short:Kt,compact:Kt}):dr({name:"func",short:fr(t.params,t.returns),compact:null,full:hr(t.params,t.returns)})}(t);case"shape":return function(e,t){var n=Object.keys(e.value).map((function(n){return"".concat(n,": ").concat(br(e.value[n],t).full)})).join(", "),r=xn("{ ".concat(n," }")),i=r.inferredType,s=r.ast,u=i.depth;return dr({name:"shape",short:Jt,compact:1===u&&s?Dr(s,!0):null,full:s?Dr(s):null})}(e,t);case"instanceOf":return dr({name:"instanceOf",short:e.value,compact:e.value});case"objectOf":return function(e,t){var n=br(e.value,t),r=n.short,i=n.compact,s=n.full;return dr({name:"objectOf",short:Er(r),compact:null!=i?Er(i):null,full:s&&Er(s)})}(e,t);case"union":return function(e,t){if(Array.isArray(e.value)){var n=e.value.reduce((function(e,n){var r=br(n,t),i=r.short,s=r.compact,u=r.full;return e.short.push(i),e.compact.push(s),e.full.push(u),e}),{short:[],compact:[],full:[]});return dr({name:"union",short:n.short.join(" | "),compact:n.compact.every((function(e){return null!=e}))?n.compact.join(" | "):null,full:n.full.join(" | ")})}return dr({name:"union",short:e.value,compact:null})}(e,t);case"enum":return yr(e);case"arrayOf":return function(e,t){var n=br(e.value,t),r=n.name,i=n.short,s=n.compact,u=n.full,a=n.inferredType;if("custom"===r){if("Object"===a)return vr(i,s,u)}else if("shape"===r)return vr(i,s,u);return dr({name:"arrayOf",short:xr(i),compact:xr(i)})}(e,t);default:return dr({name:e.name,short:e.name,compact:e.name})}}catch(r){console.error(r)}var n;return dr({name:"unknown",short:"unknown",compact:"unknown"})}var Sr=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return i(i({},lr),e)}({function:function(e,t){var n=t.name,r=t.type,i="element"===(null==r?void 0:r.summary)||"elementType"===(null==r?void 0:r.summary),s=cr(e,n);if(null!=s){if(i)return x(Sn(s));var u=xn(e.toString()).inferredType.hasParams;return x(bn(s,u))}return x(i?Qt:Kt)}});function Br(e,t){var n=e.propDef,r=function(e){var t=e.docgenInfo.type;if(null==t)return null;try{switch(t.name){case"custom":case"shape":case"instanceOf":case"objectOf":case"union":case"enum":case"arrayOf":var n=br(t,e),r=n.short,i=n.compact,s=n.full;return null==i||F(i)?s?x(r,s):x(r):x(i);case"func":var u,a=br(t,e),o=a.short,c=a.full,p=o;return c&&c.length<150?p=c:c&&(u=c.replace(/,/g,",\r\n")),x(p,u);default:return null}}catch(l){console.error(l)}return null}(e);null!=r&&(n.type=r);var i=e.docgenInfo.defaultValue;if(null!=i&&null!=i.value){var s=_n(i.value);null!=s&&(n.defaultValue=s)}else if(null!=t){var u=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:lr;try{switch(d(e)){case"string":return n.string(e,t);case"object":return n.object(e,t);case"function":return n.function(e,t);default:return n.default(e,t)}}catch(r){console.error(r)}return null}(t,n,Sr);null!=u&&(n.defaultValue=u)}return n}function _r(e,t){var n=null!=t.defaultProps?t.defaultProps:{};return function(e,t){var n=t.propTypes;return null!=n?Object.keys(n).map((function(t){return e.find((function(e){return e.name===t}))})).filter(Boolean):e}(e.map((function(e){return Br(e,n[e.propDef.name])})),t)}function wr(e){return e.map((function(e){return function(e){var t=e.propDef,n=e.docgenInfo.defaultValue;if(null!=n&&null!=n.value){var r=_n(n.value);null!=r&&(t.defaultValue=r)}return t}(e)}))}var kr=new Map;function Pr(e,t){var n=e;!C(e)&&!e.propTypes&&cn(e)&&(n=e.type);var r=E(n,t);if(0===r.length)return[];switch(r[0].typeSystem){case y.JAVASCRIPT:return _r(r,e);case y.TYPESCRIPT:return wr(r);default:return r.map((function(e){return e.propDef}))}}Object.keys(Ht.default).forEach((function(e){var t=Ht.default[e];kr.set(t,e),kr.set(t.isRequired,e)}));var Ir=ur,Tr=function(e){return e.charAt(0).toUpperCase()+e.slice(1)};function Lr(e){if(S.isValidElement(e)){var t=Object.keys(e.props).reduce((function(t,n){return t[n]=Lr(e.props[n]),t}),{});return i(i({},e),{},{props:t,_owner:null})}return Array.isArray(e)?e.map(Lr):e}var Or=function(e,t){if(d(e)>"u")return r.warn("Too many skip or undefined component"),null;for(var n=e,s=n.type,u=0;u<(null==t?void 0:t.skip);u+=1){if(d(n)>"u")return r.warn("Cannot skip undefined element"),null;if(B.Children.count(n)>1)return r.warn("Trying to skip an array of elements"),null;d(n.props.children)>"u"?(r.warn("Not enough children to skip elements."),"function"==typeof n.type&&""===n.type.name&&(n=B.createElement(s,i({},n.props)))):n="function"==typeof n.props.children?n.props.children():n.props.children}var a=i(i(i({},"string"==typeof(null==t?void 0:t.displayName)?{showFunctions:!0,displayName:function(){return t.displayName}}:{displayName:function(e){var t,n;return e.type.displayName?e.type.displayName:b(e.type,"displayName")?b(e.type,"displayName"):null!==(t=e.type.render)&&void 0!==t&&t.displayName?e.type.render.displayName:"symbol"==d(e.type)||e.type.$$typeof&&"symbol"==d(e.type.$$typeof)?((n=e.type).$$typeof||n).toString().replace(/^Symbol\((.*)\)$/,"$1").split(".").map((function(e){return e.split("_").map(Tr).join("")})).join("."):e.type.name&&"_default"!==e.type.name?e.type.name:"function"==typeof e.type?"No Display Name":e.type.$$typeof===Symbol.for("react.forward_ref")?e.type.render.name:cn(e.type)?e.type.type.name:e.type}}),{filterProps:function(e,t){return void 0!==e}}),t);return B.Children.map(e,(function(e){var t="number"==typeof e?e.toString():e,n=("function"==typeof Ir?Ir:Ir.default)(Lr(t),a);if(n.indexOf("&quot;")>-1){var r=n.match(/\S+=\\"([^"]*)\\"/g);r&&r.forEach((function(e){n=n.replace(e,e.replace(/&quot;/g,"'"))}))}return n})).join("\n").replace(/function\s+noRefCheck\(\)\s*\{\}/g,"() => {}")},Nr={skip:0,showFunctions:!1,enableBeautify:!0,showDefaultProps:!1},Rr=function(n){var r;if(!function(e){var t,n;return"MDXCreateElement"===(null===(t=e.type)||void 0===t?void 0:t.displayName)&&!(null===(n=e.props)||void 0===n||!n.mdxType)}(n))return n;var i=n.props,s=(i.mdxType,i.originalType),u=i.children,a=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(i,e),o=[];return u&&(o=(Array.isArray(u)?u:[u]).map(Rr)),(r=S).createElement.apply(r,[s,a].concat(t(o)))},jr=function(e,t){var n,r=k.getChannel(),s=function(e){var t,n=null==e||null===(t=e.parameters.docs)||void 0===t?void 0:t.source,r=null==e?void 0:e.parameters.__isArgsStory;return(null==n?void 0:n.type)!==A.DYNAMIC&&(!r||(null==n?void 0:n.code)||(null==n?void 0:n.type)===A.CODE)}(t),u="";P((function(){if(!s){var e=t.id,n=t.unmappedArgs;r.emit(D,{id:e,source:u,args:n})}}));var a=e();if(s)return a;var o=i(i({},Nr),(null==t?void 0:t.parameters.jsx)||{}),c=null!=t&&null!==(n=t.parameters.docs)&&void 0!==n&&null!==(n=n.source)&&void 0!==n&&n.excludeDecorators?t.originalStoryFn(t.args,t):a,p=Rr(c),l=Or(p,o);return l&&(u=l),a},Mr=n("applyDecorators",(function(e,n){var r=n.findIndex((function(e){return e.originalFn===jr})),i=-1===r?n:[].concat(t(n.splice(r,1)),t(n));return w(e,i)})),Vr={docs:{story:{inline:!0},extractArgTypes:function(e){if(e){var t=function(e){return{rows:Pr(e,"props")}}(e),n=t.rows;if(n)return n.reduce((function(e,t){var n=t.name,r=t.description,s=t.type,u=t.sbType,a=t.defaultValue,o=t.jsDocTags,c=t.required;return e[n]={name:n,description:r,type:i({required:c},u),table:{type:null!=s?s:void 0,jsDocTags:o,defaultValue:null!=a?a:void 0}},e}),{})}return null},extractComponentDescription:g}},qr=[jr],Ur=[m];n({parameters:Vr,decorators:qr,argTypesEnhancers:Ur})}}}))}();

import{j as e}from"./index-C_VjMC7g.js";import{c as O,F as r,M as R,X as U}from"./x-B9YxCZbW.js";import{P as X}from"./plus-BP0qVozT.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],H=O("Settings",G),Y={title:"UI/FlatButton",component:r,parameters:{layout:"centered"},tags:["autodocs"],argTypes:{children:{control:"text",description:"Button content"},square:{control:"boolean",description:"Makes the button square-shaped"},isLoading:{control:"boolean",description:"Shows loading spinner"},asChild:{control:"boolean",description:"Renders as child component using Slot"},disabled:{control:"boolean",description:"Disables the button"},onClick:{action:"clicked",description:"Click handler"}}},a={args:{children:"Button"}},s={args:{children:"Click me"}},n={args:{children:e.jsx(R,{}),square:!0}},t={args:{children:e.jsxs(e.Fragment,{children:[e.jsx(X,{}),"Add Item"]})}},o={args:{children:e.jsx(U,{}),square:!0}},c={args:{children:"Loading...",isLoading:!0}},i={args:{children:e.jsx(H,{}),square:!0,isLoading:!0}},l={args:{children:"Disabled",disabled:!0}},d={args:{children:"Custom Style",className:"bg-blue-500 text-white hover:bg-blue-600"}},u={render:()=>e.jsxs("div",{className:"flex flex-wrap gap-4 items-center",children:[e.jsx(r,{children:"Default"}),e.jsx(r,{square:!0,children:e.jsx(R,{})}),e.jsx(r,{isLoading:!0,children:"Loading"}),e.jsx(r,{disabled:!0,children:"Disabled"}),e.jsxs(r,{children:[e.jsx(X,{}),"With Icon"]})]}),parameters:{controls:{disable:!0}}};var m,p,g;a.parameters={...a.parameters,docs:{...(m=a.parameters)==null?void 0:m.docs,source:{originalSource:"{\n  args: {\n    children: 'Button'\n  }\n}",...(g=(p=a.parameters)==null?void 0:p.docs)==null?void 0:g.source}}};var h,x,b;s.parameters={...s.parameters,docs:{...(h=s.parameters)==null?void 0:h.docs,source:{originalSource:"{\n  args: {\n    children: 'Click me'\n  }\n}",...(b=(x=s.parameters)==null?void 0:x.docs)==null?void 0:b.source}}};var S,L,j;n.parameters={...n.parameters,docs:{...(S=n.parameters)==null?void 0:S.docs,source:{originalSource:"{\n  args: {\n    children: <MenuIcon />,\n    square: true\n  }\n}",...(j=(L=n.parameters)==null?void 0:L.docs)==null?void 0:j.source}}};var q,B,F;t.parameters={...t.parameters,docs:{...(q=t.parameters)==null?void 0:q.docs,source:{originalSource:"{\n  args: {\n    children: <>\n        <Plus />\n        Add Item\n      </>\n  }\n}",...(F=(B=t.parameters)==null?void 0:B.docs)==null?void 0:F.source}}};var f,I,D;o.parameters={...o.parameters,docs:{...(f=o.parameters)==null?void 0:f.docs,source:{originalSource:"{\n  args: {\n    children: <X />,\n    square: true\n  }\n}",...(D=(I=o.parameters)==null?void 0:I.docs)==null?void 0:D.source}}};var v,y,C;c.parameters={...c.parameters,docs:{...(v=c.parameters)==null?void 0:v.docs,source:{originalSource:"{\n  args: {\n    children: 'Loading...',\n    isLoading: true\n  }\n}",...(C=(y=c.parameters)==null?void 0:y.docs)==null?void 0:C.source}}};var k,W,A;i.parameters={...i.parameters,docs:{...(k=i.parameters)==null?void 0:k.docs,source:{originalSource:"{\n  args: {\n    children: <Settings />,\n    square: true,\n    isLoading: true\n  }\n}",...(A=(W=i.parameters)==null?void 0:W.docs)==null?void 0:A.source}}};var M,w,N;l.parameters={...l.parameters,docs:{...(M=l.parameters)==null?void 0:M.docs,source:{originalSource:"{\n  args: {\n    children: 'Disabled',\n    disabled: true\n  }\n}",...(N=(w=l.parameters)==null?void 0:w.docs)==null?void 0:N.source}}};var T,P,V;d.parameters={...d.parameters,docs:{...(T=d.parameters)==null?void 0:T.docs,source:{originalSource:"{\n  args: {\n    children: 'Custom Style',\n    className: 'bg-blue-500 text-white hover:bg-blue-600'\n  }\n}",...(V=(P=d.parameters)==null?void 0:P.docs)==null?void 0:V.source}}};var _,z,E;u.parameters={...u.parameters,docs:{...(_=u.parameters)==null?void 0:_.docs,source:{originalSource:'{\n  render: () => <div className="flex flex-wrap gap-4 items-center">\n      <FlatButton>Default</FlatButton>\n      <FlatButton square>\n        <MenuIcon />\n      </FlatButton>\n      <FlatButton isLoading>Loading</FlatButton>\n      <FlatButton disabled>Disabled</FlatButton>\n      <FlatButton>\n        <Plus />\n        With Icon\n      </FlatButton>\n    </div>,\n  parameters: {\n    controls: {\n      disable: true\n    }\n  }\n}',...(E=(z=u.parameters)==null?void 0:z.docs)==null?void 0:E.source}}};const Z=["Default","WithText","WithIcon","WithIconAndText","Square","Loading","LoadingSquare","Disabled","CustomStyle","AllVariants"];export{u as AllVariants,d as CustomStyle,a as Default,l as Disabled,c as Loading,i as LoadingSquare,o as Square,n as WithIcon,t as WithIconAndText,s as WithText,Z as __namedExportsOrder,Y as default};

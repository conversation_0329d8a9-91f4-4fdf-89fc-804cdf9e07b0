!function(){function e(r){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(r)}function r(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),n.push.apply(n,o)}return n}function n(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?r(Object(t),!0).forEach((function(r){o(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):r(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function o(r,n,o){return(n=function(r){var n=function(r,n){if("object"!=e(r)||!r)return r;var o=r[Symbol.toPrimitive];if(void 0!==o){var t=o.call(r,n||"default");if("object"!=e(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(r)}(r,"string");return"symbol"==e(n)?n:n+""}(n))in r?Object.defineProperty(r,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[n]=o,r}System.register(["./index-legacy-C2Kr5I5h.js","./x-legacy-yM9Jk2p5.js","./plus-legacy-khTMZduY.js"],(function(e,r){"use strict";var o,t,a,i,s,l;return{setters:[function(e){o=e.j},function(e){t=e.c,a=e.F,i=e.M,s=e.X},function(e){l=e.P}],execute:function(){var r,c,d,u,p,m,v,g,b,h,f,y,S,j,x,O,q,D,L,w,B=t("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),P=(e("default",{title:"UI/FlatButton",component:a,parameters:{layout:"centered"},tags:["autodocs"],argTypes:{children:{control:"text",description:"Button content"},square:{control:"boolean",description:"Makes the button square-shaped"},isLoading:{control:"boolean",description:"Shows loading spinner"},asChild:{control:"boolean",description:"Renders as child component using Slot"},disabled:{control:"boolean",description:"Disables the button"},onClick:{action:"clicked",description:"Click handler"}}}),e("Default",{args:{children:"Button"}})),F=e("WithText",{args:{children:"Click me"}}),I=e("WithIcon",{args:{children:o.jsx(i,{}),square:!0}}),k=e("WithIconAndText",{args:{children:o.jsxs(o.Fragment,{children:[o.jsx(l,{}),"Add Item"]})}}),C=e("Square",{args:{children:o.jsx(s,{}),square:!0}}),W=e("Loading",{args:{children:"Loading...",isLoading:!0}}),A=e("LoadingSquare",{args:{children:o.jsx(B,{}),square:!0,isLoading:!0}}),T=e("Disabled",{args:{children:"Disabled",disabled:!0}}),M=e("CustomStyle",{args:{children:"Custom Style",className:"bg-blue-500 text-white hover:bg-blue-600"}}),N=e("AllVariants",{render:function(){return o.jsxs("div",{className:"flex flex-wrap gap-4 items-center",children:[o.jsx(a,{children:"Default"}),o.jsx(a,{square:!0,children:o.jsx(i,{})}),o.jsx(a,{isLoading:!0,children:"Loading"}),o.jsx(a,{disabled:!0,children:"Disabled"}),o.jsxs(a,{children:[o.jsx(l,{}),"With Icon"]})]})},parameters:{controls:{disable:!0}}});
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */P.parameters=n(n({},P.parameters),{},{docs:n(n({},null===(r=P.parameters)||void 0===r?void 0:r.docs),{},{source:n({originalSource:"{\n  args: {\n    children: 'Button'\n  }\n}"},null===(c=P.parameters)||void 0===c||null===(c=c.docs)||void 0===c?void 0:c.source)})}),F.parameters=n(n({},F.parameters),{},{docs:n(n({},null===(d=F.parameters)||void 0===d?void 0:d.docs),{},{source:n({originalSource:"{\n  args: {\n    children: 'Click me'\n  }\n}"},null===(u=F.parameters)||void 0===u||null===(u=u.docs)||void 0===u?void 0:u.source)})}),I.parameters=n(n({},I.parameters),{},{docs:n(n({},null===(p=I.parameters)||void 0===p?void 0:p.docs),{},{source:n({originalSource:"{\n  args: {\n    children: <MenuIcon />,\n    square: true\n  }\n}"},null===(m=I.parameters)||void 0===m||null===(m=m.docs)||void 0===m?void 0:m.source)})}),k.parameters=n(n({},k.parameters),{},{docs:n(n({},null===(v=k.parameters)||void 0===v?void 0:v.docs),{},{source:n({originalSource:"{\n  args: {\n    children: <>\n        <Plus />\n        Add Item\n      </>\n  }\n}"},null===(g=k.parameters)||void 0===g||null===(g=g.docs)||void 0===g?void 0:g.source)})}),C.parameters=n(n({},C.parameters),{},{docs:n(n({},null===(b=C.parameters)||void 0===b?void 0:b.docs),{},{source:n({originalSource:"{\n  args: {\n    children: <X />,\n    square: true\n  }\n}"},null===(h=C.parameters)||void 0===h||null===(h=h.docs)||void 0===h?void 0:h.source)})}),W.parameters=n(n({},W.parameters),{},{docs:n(n({},null===(f=W.parameters)||void 0===f?void 0:f.docs),{},{source:n({originalSource:"{\n  args: {\n    children: 'Loading...',\n    isLoading: true\n  }\n}"},null===(y=W.parameters)||void 0===y||null===(y=y.docs)||void 0===y?void 0:y.source)})}),A.parameters=n(n({},A.parameters),{},{docs:n(n({},null===(S=A.parameters)||void 0===S?void 0:S.docs),{},{source:n({originalSource:"{\n  args: {\n    children: <Settings />,\n    square: true,\n    isLoading: true\n  }\n}"},null===(j=A.parameters)||void 0===j||null===(j=j.docs)||void 0===j?void 0:j.source)})}),T.parameters=n(n({},T.parameters),{},{docs:n(n({},null===(x=T.parameters)||void 0===x?void 0:x.docs),{},{source:n({originalSource:"{\n  args: {\n    children: 'Disabled',\n    disabled: true\n  }\n}"},null===(O=T.parameters)||void 0===O||null===(O=O.docs)||void 0===O?void 0:O.source)})}),M.parameters=n(n({},M.parameters),{},{docs:n(n({},null===(q=M.parameters)||void 0===q?void 0:q.docs),{},{source:n({originalSource:"{\n  args: {\n    children: 'Custom Style',\n    className: 'bg-blue-500 text-white hover:bg-blue-600'\n  }\n}"},null===(D=M.parameters)||void 0===D||null===(D=D.docs)||void 0===D?void 0:D.source)})}),N.parameters=n(n({},N.parameters),{},{docs:n(n({},null===(L=N.parameters)||void 0===L?void 0:L.docs),{},{source:n({originalSource:'{\n  render: () => <div className="flex flex-wrap gap-4 items-center">\n      <FlatButton>Default</FlatButton>\n      <FlatButton square>\n        <MenuIcon />\n      </FlatButton>\n      <FlatButton isLoading>Loading</FlatButton>\n      <FlatButton disabled>Disabled</FlatButton>\n      <FlatButton>\n        <Plus />\n        With Icon\n      </FlatButton>\n    </div>,\n  parameters: {\n    controls: {\n      disable: true\n    }\n  }\n}'},null===(w=N.parameters)||void 0===w||null===(w=w.docs)||void 0===w?void 0:w.source)})});e("__namedExportsOrder",["Default","WithText","WithIcon","WithIconAndText","Square","Loading","LoadingSquare","Disabled","CustomStyle","AllVariants"])}}}))}();

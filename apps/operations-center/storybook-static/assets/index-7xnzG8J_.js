import{j as s}from"./index-C_VjMC7g.js";import{i as l,q as n,L as i,r as t,s as a}from"./__federation_expose_App-DYUBmReV.js";import{S as d,a as u,b as c,c as m}from"./super-folder-button-BhEwJOIW.js";const h=function(){const r=l(a());return s.jsxs("ul",{className:"grid grid-cols-3 gap-1.5 p-2.5 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-7",children:[r.isPending&&Array.from({length:14}).map((e,o)=>s.jsx("li",{children:s.jsx(d,{className:"w-full"})},o)),r.isSuccess&&r.data.items.map(e=>s.jsx("li",{children:s.jsx(u,{asChild:!0,color:n(e.color),className:"w-full",children:s.jsxs(i,{from:t.fullPath,to:e.id,children:[s.jsx(c,{children:e.emoji}),s.jsx(m,{children:e.title})]})})},e.id))]})};export{h as component};

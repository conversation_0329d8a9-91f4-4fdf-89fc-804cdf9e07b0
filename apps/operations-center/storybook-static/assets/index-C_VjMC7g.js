import{g as v}from"./index-D4lIrffr.js";import{r as m}from"./index-DsJinFGm.js";var u={exports:{}},e={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var i;function E(){if(i)return e;i=1;var x=Symbol.for("react.transitional.element"),R=Symbol.for("react.fragment");function n(p,r,t){var s=null;if(t!==void 0&&(s=""+t),r.key!==void 0&&(s=""+r.key),"key"in r){t={};for(var o in r)o!=="key"&&(t[o]=r[o])}else t=r;return r=t.ref,{$$typeof:x,type:p,key:s,ref:r!==void 0?r:null,props:t}}return e.Fragment=R,e.jsx=n,e.jsxs=n,e}var a;function l(){return a||(a=1,u.exports=E()),u.exports}var T=l(),d=m();const _=v(d);export{_ as I,T as j,d as r};

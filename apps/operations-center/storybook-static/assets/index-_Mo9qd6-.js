import{j as e}from"./index-C_VjMC7g.js";import{x as i,y as l,L as t,z as o}from"./__federation_expose_App-DYUBmReV.js";import{c as r,z as n}from"./x-B9YxCZbW.js";import"./index-D4lIrffr.js";import{A as c}from"./actions--Pb8QTI7.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d=[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]],m=r("Pencil",d),g=function(){const a=i(s=>s.views);return e.jsxs("div",{className:"flex h-full flex-col [&_:is(section,header,footer)]:p-4",children:[e.jsxs("section",{children:[e.jsx("h2",{children:"Tables"}),e.jsx("p",{className:"text-foreground-secondary mt-1",children:"Create up to 4 views to help you optimize the floor management"}),e.jsx("ul",{className:"mt-4 space-y-2",children:a.tables.map(s=>e.jsxs("li",{className:"bg-background-highlight border-border relative flex w-full items-center gap-1 rounded-2xl border p-3 text-left",children:[e.jsx("h3",{className:"leading-none",children:s.title}),e.jsx(l,{size:"xs",children:"Default view"}),e.jsxs(t,{from:o.id,to:"./tables/$view",params:{view:s.id},className:"ml-auto flex items-center gap-1 after:absolute after:inset-0",children:[e.jsx(m,{}),"Edit"]})]},s.id))})]}),e.jsx(c,{children:e.jsx(n,{asChild:!0,className:"w-full",children:e.jsx(t,{to:"/dine-in",children:"Back to Tables"})})})]})};export{g as component};

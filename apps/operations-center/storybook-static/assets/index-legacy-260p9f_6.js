System.register(["./index-legacy-C2Kr5I5h.js","./__federation_expose_App-legacy-pwn7RX54.js","./super-folder-button-legacy-DkI4bWSd.js"],(function(e,s){"use strict";var i,n,r,c,l,t,o,a,d,u;return{setters:[function(e){i=e.j},function(e){n=e.i,r=e.q,c=e.L,l=e.r,t=e.s},function(e){o=e.S,a=e.a,d=e.b,u=e.c}],execute:function(){e("component",(function(){var e=n(t());return i.jsxs("ul",{className:"grid grid-cols-3 gap-1.5 p-2.5 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-7",children:[e.isPending&&Array.from({length:14}).map((function(e,s){return i.jsx("li",{children:i.jsx(o,{className:"w-full"})},s)})),e.isSuccess&&e.data.items.map((function(e){return i.jsx("li",{children:i.jsx(a,{asChild:!0,color:r(e.color),className:"w-full",children:i.jsxs(c,{from:l.fullPath,to:e.id,children:[i.jsx(d,{children:e.emoji}),i.jsx(u,{children:e.title})]})})},e.id)}))]})}))}}}));

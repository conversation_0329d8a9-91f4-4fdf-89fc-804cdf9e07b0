System.register(["./index-legacy-8Bh_oy_I.js","./index-legacy-c2BWqqFK.js"],(function(e,r){"use strict";var t,n;return{setters:[function(e){t=e.g},function(e){n=e.r}],execute:function(){var r,o,i={exports:{}},s={};e("j",(o||(o=1,i.exports=function(){if(r)return s;r=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function n(r,t,n){var o=null;if(void 0!==n&&(o=""+n),void 0!==t.key&&(o=""+t.key),"key"in t)for(var i in n={},t)"key"!==i&&(n[i]=t[i]);else n=t;return t=n.ref,{$$typeof:e,type:r,key:o,ref:void 0!==t?t:null,props:n}}return s.Fragment=t,s.jsx=n,s.jsxs=n,s}()),i.exports));var a=e("r",n());e("I",t(a))}}}));

!function(){function e(e,u){return function(e){if(Array.isArray(e))return e}(e)||function(e,u){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var n,r,o,a,D=[],i=!0,c=!1;try{if(o=(t=t.call(e)).next,0===u){if(Object(t)!==t)return;i=!1}else for(;!(i=(n=o.call(t)).done)&&(D.push(n.value),D.length!==u);i=!0);}catch(e){c=!0,r=e}finally{try{if(!i&&null!=t.return&&(a=t.return(),Object(a)!==a))return}finally{if(c)throw r}}return D}}(e,u)||D(e,u)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e,u){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);u&&(n=n.filter((function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable}))),t.push.apply(t,n)}return t}function t(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(u){n(e,u,r[u])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(r,u))}))}return e}function n(e,u,t){return(u=p(u))in e?Object.defineProperty(e,u,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[u]=t,e}function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e){return function(e){if(Array.isArray(e))return i(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||D(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e,u){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=D(e))||u&&e&&"number"==typeof e.length){t&&(e=t);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,i=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){i=!0,o=e},f:function(){try{a||null==t.return||t.return()}finally{if(i)throw o}}}}function D(e,u){if(e){if("string"==typeof e)return i(e,u);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?i(e,u):void 0}}function i(e,u){(null==u||u>e.length)&&(u=e.length);for(var t=0,n=Array(u);t<u;t++)n[t]=e[t];return n}function c(e,u){if(!(e instanceof u))throw new TypeError("Cannot call a class as a function")}function s(e,u){for(var t=0;t<u.length;t++){var n=u[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,p(n.key),n)}}function l(e,u,t){return u&&s(e.prototype,u),t&&s(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function p(e){var u=function(e,u){if("object"!=r(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,u||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===u?String:Number)(e)}(e,"string");return"symbol"==r(u)?u:u+""}function F(e,u,t){return u=E(u),function(e,u){if(u&&("object"==r(u)||"function"==typeof u))return u;if(void 0!==u)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,d()?Reflect.construct(u,t||[],E(e).constructor):u.apply(e,t))}function y(e,u){if("function"!=typeof u&&null!==u)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(u&&u.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),u&&m(e,u)}function f(e){var u="function"==typeof Map?new Map:void 0;return f=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(u){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==u){if(u.has(e))return u.get(e);u.set(e,t)}function t(){return function(e,u,t){if(d())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,u);var r=new(e.bind.apply(e,n));return t&&m(r,t.prototype),r}(e,arguments,E(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),m(t,e)},f(e)}function d(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(d=function(){return!!e})()}function m(e,u){return m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,u){return e.__proto__=u,e},m(e,u)}function E(e){return E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},E(e)}System.register([],(function(u,n){"use strict";return{execute:function(){u({B:Ne,Y:U,a:Pe,l:xe,n:ou,z:_});var n,D={exports:{}};var i=(n||(n=1,function(e,u){!function(e){function u(e){return void 0!==e.text&&""!==e.text?"'".concat(e.type,"' with value '").concat(e.text,"'"):"'".concat(e.type,"'")}var t=function(e){function t(e){var n;return c(this,t),(n=F(this,t,["No parslet found for token: ".concat(u(e))])).token=e,Object.setPrototypeOf(n,t.prototype),n}return y(t,e),l(t,[{key:"getToken",value:function(){return this.token}}])}(f(Error)),n=function(e){function t(e){var n;return c(this,t),(n=F(this,t,["The parsing ended early. The next token was: ".concat(u(e))])).token=e,Object.setPrototypeOf(n,t.prototype),n}return y(t,e),l(t,[{key:"getToken",value:function(){return this.token}}])}(f(Error)),r=function(e){function u(e,t){var n;c(this,u);var r="Unexpected type: '".concat(e.type,"'.");return void 0!==t&&(r+=" Message: ".concat(t)),n=F(this,u,[r]),Object.setPrototypeOf(n,u.prototype),n}return y(u,e),l(u)}(f(Error));function D(e){return function(u){return u.startsWith(e)?{type:e,text:e}:null}}function i(e){var u,t=0,n=e[0],r=!1;if("'"!==n&&'"'!==n)return null;for(;t<e.length;){if(u=e[++t],!r&&u===n){t++;break}r=!r&&"\\"===u}if(u!==n)throw new Error("Unterminated String");return e.slice(0,t)}var s=/(?:[\$A-Z_a-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C8A\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CD\uA7D0\uA7D1\uA7D3\uA7D5-\uA7DC\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDDC0-\uDDF3\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDD4A-\uDD65\uDD6F-\uDD85\uDE80-\uDEA9\uDEB0\uDEB1\uDEC2-\uDEC4\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE3F\uDE40\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61\uDF80-\uDF89\uDF8B\uDF8E\uDF90-\uDFB5\uDFB7\uDFD1\uDFD3]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8\uDFC0-\uDFE0]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDF02\uDF04-\uDF10\uDF12-\uDF33\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD80E\uD80F\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC41-\uDC46\uDC60-\uDFFF]|\uD810[\uDC00-\uDFFA]|\uD811[\uDC00-\uDE46]|\uD818[\uDD00-\uDD1D]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDD40-\uDD6C\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDCFF-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC30-\uDC6D\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDCD0-\uDCEB\uDDD0-\uDDED\uDDF0\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0\uDFF0-\uDFFF]|\uD87B[\uDC00-\uDE5D]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF])|\\u[0-9A-Fa-f\uFF10-\uFF19\uFF21-\uFF26\uFF41-\uFF46]{4}|\\u\{0*(?:[0-9A-Fa-f\uFF10-\uFF19\uFF21-\uFF26\uFF41-\uFF46]{1,5}|10[0-9A-Fa-f\uFF10-\uFF19\uFF21-\uFF26\uFF41-\uFF46]{4})\}/,p=/(?:[\$\x2D0-9A-Z_a-z\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05EF-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u07FD\u0800-\u082D\u0840-\u085B\u0860-\u086A\u0870-\u0887\u0889-\u088E\u0897-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u09FE\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3C-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C5D\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDD\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1-\u0CF3\u0D00-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D81-\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECE\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1715\u171F-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u1820-\u1878\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B4C\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C8A\u1C90-\u1CBA\u1CBD-\u1CBF\u1CD0-\u1CD2\u1CD4-\u1CFA\u1D00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CD\uA7D0\uA7D1\uA7D3\uA7D5-\uA7DC\uA7F2-\uA827\uA82C\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF65-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDDC0-\uDDF3\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD27\uDD30-\uDD39\uDD40-\uDD65\uDD69-\uDD6D\uDD6F-\uDD85\uDE80-\uDEA9\uDEAB\uDEAC\uDEB0\uDEB1\uDEC2-\uDEC4\uDEFC-\uDF1C\uDF27\uDF30-\uDF50\uDF70-\uDF85\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC00-\uDC46\uDC66-\uDC75\uDC7F-\uDCBA\uDCC2\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD44-\uDD47\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDC9-\uDDCC\uDDCE-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E-\uDE41\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3B-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74\uDF80-\uDF89\uDF8B\uDF8E\uDF90-\uDFB5\uDFB7-\uDFC0\uDFC2\uDFC5\uDFC7-\uDFCA\uDFCC-\uDFD3\uDFE1\uDFE2]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC5E-\uDC61\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB8\uDEC0-\uDEC9\uDED0-\uDEE3\uDF00-\uDF1A\uDF1D-\uDF2B\uDF30-\uDF39\uDF40-\uDF46]|\uD806[\uDC00-\uDC3A\uDCA0-\uDCE9\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD35\uDD37\uDD38\uDD3B-\uDD43\uDD50-\uDD59\uDDA0-\uDDA7\uDDAA-\uDDD7\uDDDA-\uDDE1\uDDE3\uDDE4\uDE00-\uDE3E\uDE47\uDE50-\uDE99\uDE9D\uDEB0-\uDEF8\uDFC0-\uDFE0\uDFF0-\uDFF9]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD8E\uDD90\uDD91\uDD93-\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF6\uDF00-\uDF10\uDF12-\uDF3A\uDF3E-\uDF42\uDF50-\uDF5A\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD80E\uD80F\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC40-\uDC55\uDC60-\uDFFF]|\uD810[\uDC00-\uDFFA]|\uD811[\uDC00-\uDE46]|\uD818[\uDD00-\uDD39]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDE70-\uDEBE\uDEC0-\uDEC9\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDD40-\uDD6C\uDD70-\uDD79\uDE40-\uDE7F\uDF00-\uDF4A\uDF4F-\uDF87\uDF8F-\uDF9F\uDFE0\uDFE1\uDFE3\uDFE4\uDFF0\uDFF1]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDCFF-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD833[\uDCF0-\uDCF9\uDF00-\uDF2D\uDF30-\uDF46]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A\uDC30-\uDC6D\uDC8F\uDD00-\uDD2C\uDD30-\uDD3D\uDD40-\uDD49\uDD4E\uDE90-\uDEAE\uDEC0-\uDEF9]|\uD839[\uDCD0-\uDCF9\uDDD0-\uDDFA\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4B\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD83E[\uDFF0-\uDFF9]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0\uDFF0-\uDFFF]|\uD87B[\uDC00-\uDE5D]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF]|\uDB40[\uDD00-\uDDEF])|\\u[0-9A-Fa-f\uFF10-\uFF19\uFF21-\uFF26\uFF41-\uFF46]{4}|\\u\{0*(?:[0-9A-Fa-f\uFF10-\uFF19\uFF21-\uFF26\uFF41-\uFF46]{1,5}|10[0-9A-Fa-f\uFF10-\uFF19\uFF21-\uFF26\uFF41-\uFF46]{4})\}/;function d(e){var u=e[0];if(!s.test(u))return null;var t=1;do{if(u=e[t],!p.test(u))break;t++}while(t<e.length);return e.slice(0,t)}var m=/^(NaN|-?((\d*\.\d+|\d+)([Ee][+-]?\d+)?|Infinity))/;function E(e){var u,t;return null!==(t=null===(u=m.exec(e))||void 0===u?void 0:u[0])&&void 0!==t?t:null}var A=function(e){var u=d(e);return null==u?null:{type:"Identifier",text:u}};function C(e){return function(u){if(!u.startsWith(e))return null;var t=u[e.length];return void 0!==t&&p.test(t)?null:{type:e,text:e}}}var T,v=function(e){var u=i(e);return null==u?null:{type:"StringValue",text:u}},B=function(e){var u=E(e);return null===u?null:{type:"Number",text:u}},h=[function(e){return e.length>0?null:{type:"EOF",text:""}},D("=>"),D("("),D(")"),D("{"),D("}"),D("["),D("]"),D("|"),D("&"),D("<"),D(">"),D(","),D(";"),D("*"),D("?"),D("!"),D("="),D(":"),D("..."),D("."),D("#"),D("~"),D("/"),D("@"),C("undefined"),C("null"),C("function"),C("this"),C("new"),C("module"),C("event"),C("external"),C("typeof"),C("keyof"),C("readonly"),C("import"),C("is"),C("in"),C("asserts"),B,A,v],g=/^\s*\n\s*/,w=function(){function e(u,t,n,r){c(this,e),this.text="",this.text=u,this.previous=t,this.current=n,this.next=r}return l(e,[{key:"advance",value:function(){var u=e.read(this.text);return new e(u.text,this.current,this.next,u.token)}}],[{key:"create",value:function(u){var t=this.read(u);u=t.text;var n=this.read(u);return new e(u=n.text,void 0,t.token,n.token)}},{key:"read",value:function(e){var u=arguments.length>1&&void 0!==arguments[1]&&arguments[1];u=u||g.test(e),e=e.trim();for(var t=0,n=h;t<n.length;t++){var r=(0,n[t])(e);if(null!==r){var o=Object.assign(Object.assign({},r),{startOfLine:u});return{text:e=e.slice(o.text.length),token:o}}}throw new Error("Unexpected Token "+e)}}])}();function J(e){if(void 0===e)throw new Error("Unexpected undefined");if("JsdocTypeKeyValue"===e.type||"JsdocTypeParameterList"===e.type||"JsdocTypeProperty"===e.type||"JsdocTypeReadonlyProperty"===e.type||"JsdocTypeObjectField"===e.type||"JsdocTypeJsdocObjectField"===e.type||"JsdocTypeIndexSignature"===e.type||"JsdocTypeMappedType"===e.type)throw new r(e);return e}function b(e){return"JsdocTypeKeyValue"===e.type?P(e):J(e)}function N(e){return"JsdocTypeName"===e.type?e:P(e)}function P(e){if("JsdocTypeKeyValue"!==e.type)throw new r(e);return e}function x(e){var u;if("JsdocTypeVariadic"===e.type){if("JsdocTypeName"===(null===(u=e.element)||void 0===u?void 0:u.type))return e;throw new r(e)}if("JsdocTypeNumber"!==e.type&&"JsdocTypeName"!==e.type)throw new r(e);return e}function O(e){return"JsdocTypeIndexSignature"===e.type||"JsdocTypeMappedType"===e.type}!function(e){e[e.ALL=0]="ALL",e[e.PARAMETER_LIST=1]="PARAMETER_LIST",e[e.OBJECT=2]="OBJECT",e[e.KEY_VALUE=3]="KEY_VALUE",e[e.INDEX_BRACKETS=4]="INDEX_BRACKETS",e[e.UNION=5]="UNION",e[e.INTERSECTION=6]="INTERSECTION",e[e.PREFIX=7]="PREFIX",e[e.INFIX=8]="INFIX",e[e.TUPLE=9]="TUPLE",e[e.SYMBOL=10]="SYMBOL",e[e.OPTIONAL=11]="OPTIONAL",e[e.NULLABLE=12]="NULLABLE",e[e.KEY_OF_TYPE_OF=13]="KEY_OF_TYPE_OF",e[e.FUNCTION=14]="FUNCTION",e[e.ARROW=15]="ARROW",e[e.ARRAY_BRACKETS=16]="ARRAY_BRACKETS",e[e.GENERIC=17]="GENERIC",e[e.NAME_PATH=18]="NAME_PATH",e[e.PARENTHESIS=19]="PARENTHESIS",e[e.SPECIAL_TYPES=20]="SPECIAL_TYPES"}(T||(T={}));var S=function(){function e(u,t,n){c(this,e),this.grammar=u,this._lexer="string"==typeof t?w.create(t):t,this.baseParser=n}return l(e,[{key:"lexer",get:function(){return this._lexer}},{key:"parse",value:function(){var e=this.parseType(T.ALL);if("EOF"!==this.lexer.current.type)throw new n(this.lexer.current);return e}},{key:"parseType",value:function(e){return J(this.parseIntermediateType(e))}},{key:"parseIntermediateType",value:function(e){var u=this.tryParslets(null,e);if(null===u)throw new t(this.lexer.current);return this.parseInfixIntermediateType(u,e)}},{key:"parseInfixIntermediateType",value:function(e,u){for(var t=this.tryParslets(e,u);null!==t;)e=t,t=this.tryParslets(e,u);return e}},{key:"tryParslets",value:function(e,u){var t,n=a(this.grammar);try{for(n.s();!(t=n.n()).done;){var r=(0,t.value)(this,u,e);if(null!==r)return r}}catch(o){n.e(o)}finally{n.f()}return null}},{key:"consume",value:function(e){return Array.isArray(e)||(e=[e]),!!e.includes(this.lexer.current.type)&&(this._lexer=this.lexer.advance(),!0)}},{key:"acceptLexerState",value:function(e){this._lexer=e.lexer}}])}();function k(e){return"EOF"===e||"|"===e||","===e||")"===e||">"===e}var I=function(e,u,t){var n=e.lexer.current.type,r=e.lexer.next.type;return null==t&&"?"===n&&!k(r)||null!=t&&"?"===n?(e.consume("?"),null==t?{type:"JsdocTypeNullable",element:e.parseType(T.NULLABLE),meta:{position:"prefix"}}:{type:"JsdocTypeNullable",element:J(t),meta:{position:"suffix"}}):null};function j(e){var u=function(u,t,n){var r=u.lexer.current.type,o=u.lexer.next.type;if(null===n){if("parsePrefix"in e&&e.accept(r,o))return e.parsePrefix(u)}else if("parseInfix"in e&&e.precedence>t&&e.accept(r,o))return e.parseInfix(u,n);return null};return Object.defineProperty(u,"name",{value:e.name}),u}var _=j({name:"optionalParslet",accept:function(e){return"="===e},precedence:T.OPTIONAL,parsePrefix:function(e){return e.consume("="),{type:"JsdocTypeOptional",element:e.parseType(T.OPTIONAL),meta:{position:"prefix"}}},parseInfix:function(e,u){return e.consume("="),{type:"JsdocTypeOptional",element:J(u),meta:{position:"suffix"}}}}),L=j({name:"numberParslet",accept:function(e){return"Number"===e},parsePrefix:function(e){var u=parseFloat(e.lexer.current.text);return e.consume("Number"),{type:"JsdocTypeNumber",value:u}}}),U=j({name:"parenthesisParslet",accept:function(e){return"("===e},parsePrefix:function(e){if(e.consume("("),e.consume(")"))return{type:"JsdocTypeParameterList",elements:[]};var u=e.parseIntermediateType(T.ALL);if(!e.consume(")"))throw new Error("Unterminated parenthesis");return"JsdocTypeParameterList"===u.type?u:"JsdocTypeKeyValue"===u.type?{type:"JsdocTypeParameterList",elements:[u]}:{type:"JsdocTypeParenthesis",element:J(u)}}}),V=j({name:"specialTypesParslet",accept:function(e,u){return"?"===e&&k(u)||"null"===e||"undefined"===e||"*"===e},parsePrefix:function(e){if(e.consume("null"))return{type:"JsdocTypeNull"};if(e.consume("undefined"))return{type:"JsdocTypeUndefined"};if(e.consume("*"))return{type:"JsdocTypeAny"};if(e.consume("?"))return{type:"JsdocTypeUnknown"};throw new Error("Unacceptable token: "+e.lexer.current.text)}}),K=j({name:"notNullableParslet",accept:function(e){return"!"===e},precedence:T.NULLABLE,parsePrefix:function(e){return e.consume("!"),{type:"JsdocTypeNotNullable",element:e.parseType(T.NULLABLE),meta:{position:"prefix"}}},parseInfix:function(e,u){return e.consume("!"),{type:"JsdocTypeNotNullable",element:J(u),meta:{position:"suffix"}}}});function q(e){return e.allowTrailingComma,j({name:"parameterListParslet",accept:function(e){return","===e},precedence:T.PARAMETER_LIST,parseInfix:function(e,u){var n=[b(u)];e.consume(",");do{try{var r=e.parseIntermediateType(T.PARAMETER_LIST);n.push(b(r))}catch(R){if(R instanceof t)break;throw R}}while(e.consume(","));if(n.length>0&&n.slice(0,-1).some((function(e){return"JsdocTypeVariadic"===e.type})))throw new Error("Only the last parameter may be a rest parameter");return{type:"JsdocTypeParameterList",elements:n}}})}var M=j({name:"genericParslet",accept:function(e,u){return"<"===e||"."===e&&"<"===u},precedence:T.GENERIC,parseInfix:function(e,u){var t=e.consume(".");e.consume("<");var n=[];do{n.push(e.parseType(T.PARAMETER_LIST))}while(e.consume(","));if(!e.consume(">"))throw new Error("Unterminated generic parameter list");return{type:"JsdocTypeGeneric",left:J(u),elements:n,meta:{brackets:"angle",dot:t}}}}),Y=j({name:"unionParslet",accept:function(e){return"|"===e},precedence:T.UNION,parseInfix:function(e,u){e.consume("|");var t=[];do{t.push(e.parseType(T.UNION))}while(e.consume("|"));return{type:"JsdocTypeUnion",elements:[J(u)].concat(t)}}}),G=[I,_,L,U,V,K,q({allowTrailingComma:!0}),M,Y,_];function W(e){var u=e.allowSquareBracketsOnAnyType,t=e.allowJsdocNamePaths,n=e.pathGrammar;return function(e,o,a){if(null==a||o>=T.NAME_PATH)return null;var D,i=e.lexer.current.type,c=e.lexer.next.type;if(!("."===i&&"<"!==c||"["===i&&(u||"JsdocTypeName"===a.type)||t&&("~"===i||"#"===i)))return null;var s=!1;e.consume(".")?D="property":e.consume("[")?(D="property-brackets",s=!0):e.consume("~")?D="inner":(e.consume("#"),D="instance");var l,p=null!==n?new S(n,e.lexer,e):e,F=p.parseIntermediateType(T.NAME_PATH);switch(e.acceptLexerState(p),F.type){case"JsdocTypeName":l={type:"JsdocTypeProperty",value:F.value,meta:{quote:void 0}};break;case"JsdocTypeNumber":l={type:"JsdocTypeProperty",value:F.value.toString(10),meta:{quote:void 0}};break;case"JsdocTypeStringValue":l={type:"JsdocTypeProperty",value:F.value,meta:{quote:F.meta.quote}};break;case"JsdocTypeSpecialNamePath":if("event"!==F.specialType)throw new r(F,"Type 'JsdocTypeSpecialNamePath' is only allowed with specialType 'event'");l=F;break;default:throw new r(F,"Expecting 'JsdocTypeName', 'JsdocTypeNumber', 'JsdocStringValue' or 'JsdocTypeSpecialNamePath'")}if(s&&!e.consume("]")){var y=e.lexer.current;throw new Error("Unterminated square brackets. Next token is '".concat(y.type,"' ")+"with text '".concat(y.text,"'"))}return{type:"JsdocTypeNamePath",left:J(a),right:l,pathType:D}}}function X(e){var u=e.allowedAdditionalTokens;return j({name:"nameParslet",accept:function(e){return"Identifier"===e||"this"===e||"new"===e||u.includes(e)},parsePrefix:function(e){var u=e.lexer.current,t=u.type,n=u.text;return e.consume(t),{type:"JsdocTypeName",value:n}}})}var Q=j({name:"stringValueParslet",accept:function(e){return"StringValue"===e},parsePrefix:function(e){var u=e.lexer.current.text;return e.consume("StringValue"),{type:"JsdocTypeStringValue",value:u.slice(1,-1),meta:{quote:"'"===u[0]?"single":"double"}}}});function z(e){var u=e.pathGrammar,t=e.allowedTypes;return j({name:"specialNamePathParslet",accept:function(e){return t.includes(e)},parsePrefix:function(e){var t,n=e.lexer.current.type;if(e.consume(n),!e.consume(":"))return{type:"JsdocTypeName",value:n};var r=e.lexer.current;if(e.consume("StringValue"))t={type:"JsdocTypeSpecialNamePath",value:r.text.slice(1,-1),specialType:n,meta:{quote:"'"===r.text[0]?"single":"double"}};else{for(var o="",a=["Identifier","@","/"];a.some((function(u){return e.consume(u)}));)o+=r.text,r=e.lexer.current;t={type:"JsdocTypeSpecialNamePath",value:o,specialType:n,meta:{quote:void 0}}}var D=new S(u,e.lexer,e),i=D.parseInfixIntermediateType(t,T.ALL);return e.acceptLexerState(D),J(i)}})}var H=[X({allowedAdditionalTokens:["external","module"]}),Q,L,W({allowSquareBracketsOnAnyType:!1,allowJsdocNamePaths:!0,pathGrammar:null})],$=[].concat(H,[z({allowedTypes:["event"],pathGrammar:H})]);function Z(e){var u;if("JsdocTypeParameterList"===e.type)u=e.elements;else{if("JsdocTypeParenthesis"!==e.type)throw new r(e);u=[e.element]}return u.map((function(e){return b(e)}))}function ee(e){var u=Z(e);if(u.some((function(e){return"JsdocTypeKeyValue"===e.type})))throw new Error("No parameter should be named");return u}function ue(e){var u=e.allowNamedParameters,t=e.allowNoReturnType,n=e.allowWithoutParenthesis,r=e.allowNewAsFunctionKeyword;return j({name:"functionParslet",accept:function(e,u){return"function"===e||r&&"new"===e&&"("===u},parsePrefix:function(e){var r=e.consume("new");e.consume("function");var o="("===e.lexer.current.type;if(!o){if(!n)throw new Error("function is missing parameter list");return{type:"JsdocTypeName",value:"function"}}var D={type:"JsdocTypeFunction",parameters:[],arrow:!1,constructor:r,parenthesis:o},i=e.parseIntermediateType(T.FUNCTION);if(void 0===u)D.parameters=ee(i);else{if(r&&"JsdocTypeFunction"===i.type&&i.arrow)return(D=i).constructor=!0,D;D.parameters=Z(i);var c,s=a(D.parameters);try{for(s.s();!(c=s.n()).done;){var l=c.value;if("JsdocTypeKeyValue"===l.type&&!u.includes(l.key))throw new Error("only allowed named parameters are ".concat(u.join(", ")," but got ").concat(l.type))}}catch(p){s.e(p)}finally{s.f()}}if(e.consume(":"))D.returnType=e.parseType(T.PREFIX);else if(!t)throw new Error("function is missing return type");return D}})}function te(e){var u=e.allowPostfix,n=e.allowEnclosingBrackets;return j({name:"variadicParslet",accept:function(e){return"..."===e},precedence:T.PREFIX,parsePrefix:function(e){e.consume("...");var u=n&&e.consume("[");try{var r=e.parseType(T.PREFIX);if(u&&!e.consume("]"))throw new Error("Unterminated variadic type. Missing ']'");return{type:"JsdocTypeVariadic",element:J(r),meta:{position:"prefix",squareBrackets:u}}}catch(R){if(R instanceof t){if(u)throw new Error("Empty square brackets for variadic are not allowed.");return{type:"JsdocTypeVariadic",meta:{position:void 0,squareBrackets:!1}}}throw R}},parseInfix:u?function(e,u){return e.consume("..."),{type:"JsdocTypeVariadic",element:J(u),meta:{position:"suffix",squareBrackets:!1}}}:void 0})}var ne=j({name:"symbolParslet",accept:function(e){return"("===e},precedence:T.SYMBOL,parseInfix:function(e,u){if("JsdocTypeName"!==u.type)throw new Error("Symbol expects a name on the left side. (Reacting on '(')");e.consume("(");var t={type:"JsdocTypeSymbol",value:u.value};if(!e.consume(")")){var n=e.parseIntermediateType(T.SYMBOL);if(t.element=x(n),!e.consume(")"))throw new Error("Symbol does not end after value")}return t}}),re=j({name:"arrayBracketsParslet",precedence:T.ARRAY_BRACKETS,accept:function(e,u){return"["===e&&"]"===u},parseInfix:function(e,u){return e.consume("["),e.consume("]"),{type:"JsdocTypeGeneric",left:{type:"JsdocTypeName",value:"Array"},elements:[J(u)],meta:{brackets:"square",dot:!1}}}});function oe(e){var u=e.objectFieldGrammar,t=e.allowKeyTypes;return j({name:"objectParslet",accept:function(e){return"{"===e},parsePrefix:function(e){e.consume("{");var n={type:"JsdocTypeObject",meta:{separator:"comma"},elements:[]};if(!e.consume("}")){for(var o,a=new S(u,e.lexer,e);;){a.acceptLexerState(e);var D=a.parseIntermediateType(T.OBJECT);e.acceptLexerState(a),void 0===D&&t&&(D=e.parseIntermediateType(T.OBJECT));var i=!1;if("JsdocTypeNullable"===D.type&&(i=!0,D=D.element),"JsdocTypeNumber"===D.type||"JsdocTypeName"===D.type||"JsdocTypeStringValue"===D.type){var c=void 0;"JsdocTypeStringValue"===D.type&&(c=D.meta.quote),n.elements.push({type:"JsdocTypeObjectField",key:D.value.toString(),right:void 0,optional:i,readonly:!1,meta:{quote:c}})}else{if("JsdocTypeObjectField"!==D.type&&"JsdocTypeJsdocObjectField"!==D.type)throw new r(D);n.elements.push(D)}if(e.lexer.current.startOfLine)o="linebreak";else if(e.consume(","))o="comma";else{if(!e.consume(";"))break;o="semicolon"}if("}"===e.lexer.current.type)break}if(n.meta.separator=null!=o?o:"comma",!e.consume("}"))throw new Error("Unterminated record type. Missing '}'")}return n}})}function ae(e){var u=e.allowSquaredProperties,t=e.allowKeyTypes,n=e.allowReadonly,o=e.allowOptional;return j({name:"objectFieldParslet",precedence:T.KEY_VALUE,accept:function(e){return":"===e},parseInfix:function(e,a){var D,i=!1,c=!1;o&&"JsdocTypeNullable"===a.type&&(i=!0,a=a.element),n&&"JsdocTypeReadonlyProperty"===a.type&&(c=!0,a=a.element);var s=null!==(D=e.baseParser)&&void 0!==D?D:e;if(s.acceptLexerState(e),"JsdocTypeNumber"===a.type||"JsdocTypeName"===a.type||"JsdocTypeStringValue"===a.type||O(a)){if(O(a)&&!u)throw new r(a);var l;s.consume(":"),"JsdocTypeStringValue"===a.type&&(l=a.meta.quote);var p=s.parseType(T.KEY_VALUE);return e.acceptLexerState(s),{type:"JsdocTypeObjectField",key:O(a)?a:a.value.toString(),right:p,optional:i,readonly:c,meta:{quote:l}}}if(!t)throw new r(a);s.consume(":");var F=s.parseType(T.KEY_VALUE);return e.acceptLexerState(s),{type:"JsdocTypeJsdocObjectField",left:J(a),right:F}}})}function De(e){var u=e.allowOptional,t=e.allowVariadic;return j({name:"keyValueParslet",precedence:T.KEY_VALUE,accept:function(e){return":"===e},parseInfix:function(e,n){var o=!1,a=!1;if(u&&"JsdocTypeNullable"===n.type&&(o=!0,n=n.element),t&&"JsdocTypeVariadic"===n.type&&void 0!==n.element&&(a=!0,n=n.element),"JsdocTypeName"!==n.type)throw new r(n);e.consume(":");var D=e.parseType(T.KEY_VALUE);return{type:"JsdocTypeKeyValue",key:n.value,right:D,optional:o,variadic:a}}})}var ie=[].concat(G,[ue({allowWithoutParenthesis:!0,allowNamedParameters:["this","new"],allowNoReturnType:!0,allowNewAsFunctionKeyword:!1}),Q,z({allowedTypes:["module","external","event"],pathGrammar:$}),te({allowEnclosingBrackets:!0,allowPostfix:!0}),X({allowedAdditionalTokens:["keyof"]}),ne,re,W({allowSquareBracketsOnAnyType:!1,allowJsdocNamePaths:!0,pathGrammar:$})]),ce=[].concat(o(ie),[oe({objectFieldGrammar:[X({allowedAdditionalTokens:["module","in"]}),ae({allowSquaredProperties:!1,allowKeyTypes:!0,allowOptional:!1,allowReadonly:!1})].concat(o(ie)),allowKeyTypes:!0}),De({allowOptional:!0,allowVariadic:!0})]),se=j({name:"typeOfParslet",accept:function(e){return"typeof"===e},parsePrefix:function(e){return e.consume("typeof"),{type:"JsdocTypeTypeof",element:J(e.parseType(T.KEY_OF_TYPE_OF))}}}),le=[X({allowedAdditionalTokens:["module","keyof","event","external","in"]}),I,_,Q,L,ae({allowSquaredProperties:!1,allowKeyTypes:!1,allowOptional:!1,allowReadonly:!1})],pe=[].concat(G,[oe({allowKeyTypes:!1,objectFieldGrammar:le}),X({allowedAdditionalTokens:["event","external","in"]}),se,ue({allowWithoutParenthesis:!1,allowNamedParameters:["this","new"],allowNoReturnType:!0,allowNewAsFunctionKeyword:!1}),te({allowEnclosingBrackets:!1,allowPostfix:!1}),X({allowedAdditionalTokens:["keyof"]}),z({allowedTypes:["module"],pathGrammar:$}),W({allowSquareBracketsOnAnyType:!1,allowJsdocNamePaths:!0,pathGrammar:$}),De({allowOptional:!1,allowVariadic:!1}),ne]),Fe=j({name:"assertsParslet",accept:function(e){return"asserts"===e},parsePrefix:function(e){e.consume("asserts");var u=e.parseIntermediateType(T.SYMBOL);if("JsdocTypeName"!==u.type)throw new r(u,"A typescript asserts always has to have a name on the left side.");return e.consume("is"),{type:"JsdocTypeAsserts",left:u,right:J(e.parseIntermediateType(T.INFIX))}}});function ye(e){return e.allowQuestionMark,j({name:"tupleParslet",accept:function(e){return"["===e},parsePrefix:function(e){e.consume("[");var u={type:"JsdocTypeTuple",elements:[]};if(e.consume("]"))return u;var t=e.parseIntermediateType(T.ALL);if("JsdocTypeParameterList"===t.type?"JsdocTypeKeyValue"===t.elements[0].type?u.elements=t.elements.map(P):u.elements=t.elements.map(J):"JsdocTypeKeyValue"===t.type?u.elements=[P(t)]:u.elements=[J(t)],!e.consume("]"))throw new Error("Unterminated '['");if(u.elements.some((function(e){return"JsdocTypeUnknown"===e.type})))throw new Error("Question mark in tuple not allowed");return u}})}var fe=j({name:"keyOfParslet",accept:function(e){return"keyof"===e},parsePrefix:function(e){return e.consume("keyof"),{type:"JsdocTypeKeyof",element:J(e.parseType(T.KEY_OF_TYPE_OF))}}}),de=j({name:"importParslet",accept:function(e){return"import"===e},parsePrefix:function(e){if(e.consume("import"),!e.consume("("))throw new Error("Missing parenthesis after import keyword");var u=e.parseType(T.PREFIX);if("JsdocTypeStringValue"!==u.type)throw new Error("Only string values are allowed as paths for imports");if(!e.consume(")"))throw new Error("Missing closing parenthesis after import keyword");return{type:"JsdocTypeImport",element:u}}}),me=j({name:"readonlyPropertyParslet",accept:function(e){return"readonly"===e},parsePrefix:function(e){return e.consume("readonly"),{type:"JsdocTypeReadonlyProperty",element:e.parseType(T.KEY_VALUE)}}}),Ee=j({name:"arrowFunctionParslet",precedence:T.ARROW,accept:function(e){return"=>"===e},parseInfix:function(e,u){return e.consume("=>"),{type:"JsdocTypeFunction",parameters:Z(u).map(N),arrow:!0,constructor:!1,parenthesis:!0,returnType:e.parseType(T.OBJECT)}}}),Ae=j({name:"intersectionParslet",accept:function(e){return"&"===e},precedence:T.INTERSECTION,parseInfix:function(e,u){e.consume("&");var t=[];do{t.push(e.parseType(T.INTERSECTION))}while(e.consume("&"));return{type:"JsdocTypeIntersection",elements:[J(u)].concat(t)}}}),Ce=j({name:"predicateParslet",precedence:T.INFIX,accept:function(e){return"is"===e},parseInfix:function(e,u){if("JsdocTypeName"!==u.type)throw new r(u,"A typescript predicate always has to have a name on the left side.");return e.consume("is"),{type:"JsdocTypePredicate",left:u,right:J(e.parseIntermediateType(T.INFIX))}}}),Te=j({name:"objectSquareBracketPropertyParslet",accept:function(e){return"["===e},parsePrefix:function(e){if(void 0===e.baseParser)throw new Error("Only allowed inside object grammar");e.consume("[");var u,t=e.lexer.current.text;if(e.consume("Identifier"),e.consume(":")){var n=e.baseParser;n.acceptLexerState(e),u={type:"JsdocTypeIndexSignature",key:t,right:n.parseType(T.INDEX_BRACKETS)},e.acceptLexerState(n)}else{if(!e.consume("in"))throw new Error("Missing ':' or 'in' inside square bracketed property.");var r=e.baseParser;r.acceptLexerState(e),u={type:"JsdocTypeMappedType",key:t,right:r.parseType(T.ARRAY_BRACKETS)},e.acceptLexerState(r)}if(!e.consume("]"))throw new Error("Unterminated square brackets");return u}}),ve=[me,X({allowedAdditionalTokens:["module","event","keyof","event","external","in"]}),I,_,Q,L,ae({allowSquaredProperties:!0,allowKeyTypes:!1,allowOptional:!0,allowReadonly:!0}),Te],Be=[].concat(G,[oe({allowKeyTypes:!1,objectFieldGrammar:ve}),se,fe,de,Q,ue({allowWithoutParenthesis:!0,allowNoReturnType:!1,allowNamedParameters:["this","new","args"],allowNewAsFunctionKeyword:!0}),ye({allowQuestionMark:!1}),te({allowEnclosingBrackets:!1,allowPostfix:!1}),Fe,X({allowedAdditionalTokens:["event","external","in"]}),z({allowedTypes:["module"],pathGrammar:$}),re,Ee,W({allowSquareBracketsOnAnyType:!0,allowJsdocNamePaths:!1,pathGrammar:$}),Ae,Ce,De({allowVariadic:!0,allowOptional:!0})]);function he(e,u){switch(u){case"closure":return new S(pe,e).parse();case"jsdoc":return new S(ce,e).parse();case"typescript":return new S(Be,e).parse()}}function ge(e){var u,t,n=a(arguments.length>1&&void 0!==arguments[1]?arguments[1]:["typescript","closure","jsdoc"]);try{for(n.s();!(t=n.n()).done;){var r=t.value;try{return he(e,r)}catch(R){u=R}}}catch(o){n.e(o)}finally{n.f()}throw u}function we(e,u){var t=e[u.type];if(void 0===t)throw new Error("In this set of transform rules exists no rule for type ".concat(u.type,"."));return t(u,(function(u){return we(e,u)}))}function Je(e){throw new Error("This transform is not available. Are you trying the correct parsing mode?")}function be(e){var u,t={params:[]},n=a(e.parameters);try{for(n.s();!(u=n.n()).done;){var r=u.value;"JsdocTypeKeyValue"===r.type?"this"===r.key?t.this=r.right:"new"===r.key?t.new=r.right:t.params.push(r):t.params.push(r)}}catch(o){n.e(o)}finally{n.f()}return t}function Ne(e,u,t){return"prefix"===e?t+u:u+t}function Pe(e,u){switch(u){case"double":return'"'.concat(e,'"');case"single":return"'".concat(e,"'");case void 0:return e}}function xe(){return{JsdocTypeParenthesis:function(e,u){return"(".concat(void 0!==e.element?u(e.element):"",")")},JsdocTypeKeyof:function(e,u){return"keyof ".concat(u(e.element))},JsdocTypeFunction:function(e,u){if(e.arrow){if(void 0===e.returnType)throw new Error("Arrow function needs a return type.");var t="(".concat(e.parameters.map(u).join(", "),") => ").concat(u(e.returnType));return e.constructor&&(t="new "+t),t}var n=e.constructor?"new":"function";return e.parenthesis?(n+="(".concat(e.parameters.map(u).join(", "),")"),void 0!==e.returnType&&(n+=": ".concat(u(e.returnType))),n):n},JsdocTypeName:function(e){return e.value},JsdocTypeTuple:function(e,u){return"[".concat(e.elements.map(u).join(", "),"]")},JsdocTypeVariadic:function(e,u){return void 0===e.meta.position?"...":Ne(e.meta.position,u(e.element),"...")},JsdocTypeNamePath:function(e,u){var t=u(e.left),n=u(e.right);switch(e.pathType){case"inner":return"".concat(t,"~").concat(n);case"instance":return"".concat(t,"#").concat(n);case"property":return"".concat(t,".").concat(n);case"property-brackets":return"".concat(t,"[").concat(n,"]")}},JsdocTypeStringValue:function(e){return Pe(e.value,e.meta.quote)},JsdocTypeAny:function(){return"*"},JsdocTypeGeneric:function(e,u){if("square"===e.meta.brackets){var t=e.elements[0],n=u(t);return"JsdocTypeUnion"===t.type||"JsdocTypeIntersection"===t.type?"(".concat(n,")[]"):"".concat(n,"[]")}return"".concat(u(e.left)).concat(e.meta.dot?".":"","<").concat(e.elements.map(u).join(", "),">")},JsdocTypeImport:function(e,u){return"import(".concat(u(e.element),")")},JsdocTypeObjectField:function(e,u){var t="";return e.readonly&&(t+="readonly "),"string"==typeof e.key?t+=Pe(e.key,e.meta.quote):t+=u(e.key),e.optional&&(t+="?"),void 0===e.right?t:t+": ".concat(u(e.right))},JsdocTypeJsdocObjectField:function(e,u){return"".concat(u(e.left),": ").concat(u(e.right))},JsdocTypeKeyValue:function(e,u){var t=e.key;return e.optional&&(t+="?"),e.variadic&&(t="..."+t),void 0===e.right?t:t+": ".concat(u(e.right))},JsdocTypeSpecialNamePath:function(e){return"".concat(e.specialType,":").concat(Pe(e.value,e.meta.quote))},JsdocTypeNotNullable:function(e,u){return Ne(e.meta.position,u(e.element),"!")},JsdocTypeNull:function(){return"null"},JsdocTypeNullable:function(e,u){return Ne(e.meta.position,u(e.element),"?")},JsdocTypeNumber:function(e){return e.value.toString()},JsdocTypeObject:function(e,u){return"{".concat(e.elements.map(u).join(("comma"===e.meta.separator?",":";")+" "),"}")},JsdocTypeOptional:function(e,u){return Ne(e.meta.position,u(e.element),"=")},JsdocTypeSymbol:function(e,u){return"".concat(e.value,"(").concat(void 0!==e.element?u(e.element):"",")")},JsdocTypeTypeof:function(e,u){return"typeof ".concat(u(e.element))},JsdocTypeUndefined:function(){return"undefined"},JsdocTypeUnion:function(e,u){return e.elements.map(u).join(" | ")},JsdocTypeUnknown:function(){return"?"},JsdocTypeIntersection:function(e,u){return e.elements.map(u).join(" & ")},JsdocTypeProperty:function(e){return Pe(e.value,e.meta.quote)},JsdocTypePredicate:function(e,u){return"".concat(u(e.left)," is ").concat(u(e.right))},JsdocTypeIndexSignature:function(e,u){return"[".concat(e.key,": ").concat(u(e.right),"]")},JsdocTypeMappedType:function(e,u){return"[".concat(e.key," in ").concat(u(e.right),"]")},JsdocTypeAsserts:function(e,u){return"asserts ".concat(u(e.left)," is ").concat(u(e.right))}}}var Oe=xe();function Se(e){return we(Oe,e)}var ke=["null","true","false","break","case","catch","class","const","continue","debugger","default","delete","do","else","export","extends","finally","for","function","if","import","in","instanceof","new","return","super","switch","this","throw","try","typeof","var","void","while","with","yield"];function Ie(e){var u={type:"NameExpression",name:e};return ke.includes(e)&&(u.reservedWord=!0),u}var Re={JsdocTypeOptional:function(e,u){var t=u(e.element);return t.optional=!0,t},JsdocTypeNullable:function(e,u){var t=u(e.element);return t.nullable=!0,t},JsdocTypeNotNullable:function(e,u){var t=u(e.element);return t.nullable=!1,t},JsdocTypeVariadic:function(e,u){if(void 0===e.element)throw new Error("dots without value are not allowed in catharsis mode");var t=u(e.element);return t.repeatable=!0,t},JsdocTypeAny:function(){return{type:"AllLiteral"}},JsdocTypeNull:function(){return{type:"NullLiteral"}},JsdocTypeStringValue:function(e){return Ie(Pe(e.value,e.meta.quote))},JsdocTypeUndefined:function(){return{type:"UndefinedLiteral"}},JsdocTypeUnknown:function(){return{type:"UnknownLiteral"}},JsdocTypeFunction:function(e,u){var t=be(e),n={type:"FunctionType",params:t.params.map(u)};return void 0!==t.this&&(n.this=u(t.this)),void 0!==t.new&&(n.new=u(t.new)),void 0!==e.returnType&&(n.result=u(e.returnType)),n},JsdocTypeGeneric:function(e,u){return{type:"TypeApplication",applications:e.elements.map((function(e){return u(e)})),expression:u(e.left)}},JsdocTypeSpecialNamePath:function(e){return Ie(e.specialType+":"+Pe(e.value,e.meta.quote))},JsdocTypeName:function(e){return"function"!==e.value?Ie(e.value):{type:"FunctionType",params:[]}},JsdocTypeNumber:function(e){return Ie(e.value.toString())},JsdocTypeObject:function(e,u){var t,n={type:"RecordType",fields:[]},r=a(e.elements);try{for(r.s();!(t=r.n()).done;){var o=t.value;"JsdocTypeObjectField"!==o.type&&"JsdocTypeJsdocObjectField"!==o.type?n.fields.push({type:"FieldType",key:u(o),value:void 0}):n.fields.push(u(o))}}catch(D){r.e(D)}finally{r.f()}return n},JsdocTypeObjectField:function(e,u){if("string"!=typeof e.key)throw new Error("Index signatures and mapped types are not supported");return{type:"FieldType",key:Ie(Pe(e.key,e.meta.quote)),value:void 0===e.right?void 0:u(e.right)}},JsdocTypeJsdocObjectField:function(e,u){return{type:"FieldType",key:u(e.left),value:u(e.right)}},JsdocTypeUnion:function(e,u){return{type:"TypeUnion",elements:e.elements.map((function(e){return u(e)}))}},JsdocTypeKeyValue:function(e,u){return{type:"FieldType",key:Ie(e.key),value:void 0===e.right?void 0:u(e.right)}},JsdocTypeNamePath:function(e,u){var t,n=u(e.left);t="JsdocTypeSpecialNamePath"===e.right.type?u(e.right).name:Pe(e.right.value,e.right.meta.quote);var r="inner"===e.pathType?"~":"instance"===e.pathType?"#":".";return Ie("".concat(n.name).concat(r).concat(t))},JsdocTypeSymbol:function(e){var u="",t=e.element,n=!1;return"JsdocTypeVariadic"===(null==t?void 0:t.type)&&("prefix"===t.meta.position?u="...":n=!0,t=t.element),"JsdocTypeName"===(null==t?void 0:t.type)?u+=t.value:"JsdocTypeNumber"===(null==t?void 0:t.type)&&(u+=t.value.toString()),n&&(u+="..."),Ie("".concat(e.value,"(").concat(u,")"))},JsdocTypeParenthesis:function(e,u){return u(J(e.element))},JsdocTypeMappedType:Je,JsdocTypeIndexSignature:Je,JsdocTypeImport:Je,JsdocTypeKeyof:Je,JsdocTypeTuple:Je,JsdocTypeTypeof:Je,JsdocTypeIntersection:Je,JsdocTypeProperty:Je,JsdocTypePredicate:Je,JsdocTypeAsserts:Je};function je(e){return we(Re,e)}function _e(e){switch(e){case void 0:return"none";case"single":return"single";case"double":return"double"}}function Le(e){switch(e){case"inner":return"INNER_MEMBER";case"instance":return"INSTANCE_MEMBER";case"property":case"property-brackets":return"MEMBER"}}function Ue(e,u){return 2===u.length?{type:e,left:u[0],right:u[1]}:{type:e,left:u[0],right:Ue(e,u.slice(1))}}var Ve={JsdocTypeOptional:function(e,u){return{type:"OPTIONAL",value:u(e.element),meta:{syntax:"prefix"===e.meta.position?"PREFIX_EQUAL_SIGN":"SUFFIX_EQUALS_SIGN"}}},JsdocTypeNullable:function(e,u){return{type:"NULLABLE",value:u(e.element),meta:{syntax:"prefix"===e.meta.position?"PREFIX_QUESTION_MARK":"SUFFIX_QUESTION_MARK"}}},JsdocTypeNotNullable:function(e,u){return{type:"NOT_NULLABLE",value:u(e.element),meta:{syntax:"prefix"===e.meta.position?"PREFIX_BANG":"SUFFIX_BANG"}}},JsdocTypeVariadic:function(e,u){var t={type:"VARIADIC",meta:{syntax:"prefix"===e.meta.position?"PREFIX_DOTS":"suffix"===e.meta.position?"SUFFIX_DOTS":"ONLY_DOTS"}};return void 0!==e.element&&(t.value=u(e.element)),t},JsdocTypeName:function(e){return{type:"NAME",name:e.value}},JsdocTypeTypeof:function(e,u){return{type:"TYPE_QUERY",name:u(e.element)}},JsdocTypeTuple:function(e,u){return{type:"TUPLE",entries:e.elements.map(u)}},JsdocTypeKeyof:function(e,u){return{type:"KEY_QUERY",value:u(e.element)}},JsdocTypeImport:function(e){return{type:"IMPORT",path:{type:"STRING_VALUE",quoteStyle:_e(e.element.meta.quote),string:e.element.value}}},JsdocTypeUndefined:function(){return{type:"NAME",name:"undefined"}},JsdocTypeAny:function(){return{type:"ANY"}},JsdocTypeFunction:function(e,u){var t=be(e),n={type:e.arrow?"ARROW":"FUNCTION",params:t.params.map((function(e){if("JsdocTypeKeyValue"===e.type){if(void 0===e.right)throw new Error("Function parameter without ':' is not expected to be 'KEY_VALUE'");return{type:"NAMED_PARAMETER",name:e.key,typeName:u(e.right)}}return u(e)})),new:null,returns:null};return void 0!==t.this?n.this=u(t.this):e.arrow||(n.this=null),void 0!==t.new&&(n.new=u(t.new)),void 0!==e.returnType&&(n.returns=u(e.returnType)),n},JsdocTypeGeneric:function(e,u){var t={type:"GENERIC",subject:u(e.left),objects:e.elements.map(u),meta:{syntax:"square"===e.meta.brackets?"SQUARE_BRACKET":e.meta.dot?"ANGLE_BRACKET_WITH_DOT":"ANGLE_BRACKET"}};return"square"!==e.meta.brackets||"JsdocTypeFunction"!==e.elements[0].type||e.elements[0].parenthesis||(t.objects[0]={type:"NAME",name:"function"}),t},JsdocTypeObjectField:function(e,u){if("string"!=typeof e.key)throw new Error("Index signatures and mapped types are not supported");if(void 0===e.right)return{type:"RECORD_ENTRY",key:e.key,quoteStyle:_e(e.meta.quote),value:null,readonly:!1};var t=u(e.right);return e.optional&&(t={type:"OPTIONAL",value:t,meta:{syntax:"SUFFIX_KEY_QUESTION_MARK"}}),{type:"RECORD_ENTRY",key:e.key.toString(),quoteStyle:_e(e.meta.quote),value:t,readonly:!1}},JsdocTypeJsdocObjectField:function(){throw new Error("Keys may not be typed in jsdoctypeparser.")},JsdocTypeKeyValue:function(e,u){if(void 0===e.right)return{type:"RECORD_ENTRY",key:e.key,quoteStyle:"none",value:null,readonly:!1};var t=u(e.right);return e.optional&&(t={type:"OPTIONAL",value:t,meta:{syntax:"SUFFIX_KEY_QUESTION_MARK"}}),{type:"RECORD_ENTRY",key:e.key,quoteStyle:"none",value:t,readonly:!1}},JsdocTypeObject:function(e,u){var t,n=[],r=a(e.elements);try{for(r.s();!(t=r.n()).done;){var o=t.value;"JsdocTypeObjectField"!==o.type&&"JsdocTypeJsdocObjectField"!==o.type||n.push(u(o))}}catch(D){r.e(D)}finally{r.f()}return{type:"RECORD",entries:n}},JsdocTypeSpecialNamePath:function(e){if("module"!==e.specialType)throw new Error("jsdoctypeparser does not support type ".concat(e.specialType," at this point."));return{type:"MODULE",value:{type:"FILE_PATH",quoteStyle:_e(e.meta.quote),path:e.value}}},JsdocTypeNamePath:function(e,u){var t,n,r=!1;"JsdocTypeSpecialNamePath"===e.right.type&&"event"===e.right.specialType?(r=!0,t=e.right.value,n=_e(e.right.meta.quote)):(t=e.right.value,n=_e(e.right.meta.quote));var o={type:Le(e.pathType),owner:u(e.left),name:t,quoteStyle:n,hasEventPrefix:r};if("MODULE"===o.owner.type){var a=o.owner;return o.owner=o.owner.value,a.value=o,a}return o},JsdocTypeUnion:function(e,u){return Ue("UNION",e.elements.map(u))},JsdocTypeParenthesis:function(e,u){return{type:"PARENTHESIS",value:u(J(e.element))}},JsdocTypeNull:function(){return{type:"NAME",name:"null"}},JsdocTypeUnknown:function(){return{type:"UNKNOWN"}},JsdocTypeStringValue:function(e){return{type:"STRING_VALUE",quoteStyle:_e(e.meta.quote),string:e.value}},JsdocTypeIntersection:function(e,u){return Ue("INTERSECTION",e.elements.map(u))},JsdocTypeNumber:function(e){return{type:"NUMBER_VALUE",number:e.value.toString()}},JsdocTypeSymbol:Je,JsdocTypeProperty:Je,JsdocTypePredicate:Je,JsdocTypeMappedType:Je,JsdocTypeIndexSignature:Je,JsdocTypeAsserts:Je};function Ke(e){return we(Ve,e)}function qe(){return{JsdocTypeIntersection:function(e,u){return{type:"JsdocTypeIntersection",elements:e.elements.map(u)}},JsdocTypeGeneric:function(e,u){return{type:"JsdocTypeGeneric",left:u(e.left),elements:e.elements.map(u),meta:{dot:e.meta.dot,brackets:e.meta.brackets}}},JsdocTypeNullable:function(e){return e},JsdocTypeUnion:function(e,u){return{type:"JsdocTypeUnion",elements:e.elements.map(u)}},JsdocTypeUnknown:function(e){return e},JsdocTypeUndefined:function(e){return e},JsdocTypeTypeof:function(e,u){return{type:"JsdocTypeTypeof",element:u(e.element)}},JsdocTypeSymbol:function(e,u){var t={type:"JsdocTypeSymbol",value:e.value};return void 0!==e.element&&(t.element=u(e.element)),t},JsdocTypeOptional:function(e,u){return{type:"JsdocTypeOptional",element:u(e.element),meta:{position:e.meta.position}}},JsdocTypeObject:function(e,u){return{type:"JsdocTypeObject",meta:{separator:"comma"},elements:e.elements.map(u)}},JsdocTypeNumber:function(e){return e},JsdocTypeNull:function(e){return e},JsdocTypeNotNullable:function(e,u){return{type:"JsdocTypeNotNullable",element:u(e.element),meta:{position:e.meta.position}}},JsdocTypeSpecialNamePath:function(e){return e},JsdocTypeObjectField:function(e,u){return{type:"JsdocTypeObjectField",key:e.key,right:void 0===e.right?void 0:u(e.right),optional:e.optional,readonly:e.readonly,meta:e.meta}},JsdocTypeJsdocObjectField:function(e,u){return{type:"JsdocTypeJsdocObjectField",left:u(e.left),right:u(e.right)}},JsdocTypeKeyValue:function(e,u){return{type:"JsdocTypeKeyValue",key:e.key,right:void 0===e.right?void 0:u(e.right),optional:e.optional,variadic:e.variadic}},JsdocTypeImport:function(e,u){return{type:"JsdocTypeImport",element:u(e.element)}},JsdocTypeAny:function(e){return e},JsdocTypeStringValue:function(e){return e},JsdocTypeNamePath:function(e){return e},JsdocTypeVariadic:function(e,u){var t={type:"JsdocTypeVariadic",meta:{position:e.meta.position,squareBrackets:e.meta.squareBrackets}};return void 0!==e.element&&(t.element=u(e.element)),t},JsdocTypeTuple:function(e,u){return{type:"JsdocTypeTuple",elements:e.elements.map(u)}},JsdocTypeName:function(e){return e},JsdocTypeFunction:function(e,u){var t={type:"JsdocTypeFunction",arrow:e.arrow,parameters:e.parameters.map(u),constructor:e.constructor,parenthesis:e.parenthesis};return void 0!==e.returnType&&(t.returnType=u(e.returnType)),t},JsdocTypeKeyof:function(e,u){return{type:"JsdocTypeKeyof",element:u(e.element)}},JsdocTypeParenthesis:function(e,u){return{type:"JsdocTypeParenthesis",element:u(e.element)}},JsdocTypeProperty:function(e){return e},JsdocTypePredicate:function(e,u){return{type:"JsdocTypePredicate",left:u(e.left),right:u(e.right)}},JsdocTypeIndexSignature:function(e,u){return{type:"JsdocTypeIndexSignature",key:e.key,right:u(e.right)}},JsdocTypeMappedType:function(e,u){return{type:"JsdocTypeMappedType",key:e.key,right:u(e.right)}},JsdocTypeAsserts:function(e,u){return{type:"JsdocTypeAsserts",left:u(e.left),right:u(e.right)}}}}var Me={JsdocTypeAny:[],JsdocTypeFunction:["parameters","returnType"],JsdocTypeGeneric:["left","elements"],JsdocTypeImport:[],JsdocTypeIndexSignature:["right"],JsdocTypeIntersection:["elements"],JsdocTypeKeyof:["element"],JsdocTypeKeyValue:["right"],JsdocTypeMappedType:["right"],JsdocTypeName:[],JsdocTypeNamePath:["left","right"],JsdocTypeNotNullable:["element"],JsdocTypeNull:[],JsdocTypeNullable:["element"],JsdocTypeNumber:[],JsdocTypeObject:["elements"],JsdocTypeObjectField:["right"],JsdocTypeJsdocObjectField:["left","right"],JsdocTypeOptional:["element"],JsdocTypeParenthesis:["element"],JsdocTypeSpecialNamePath:[],JsdocTypeStringValue:[],JsdocTypeSymbol:["element"],JsdocTypeTuple:["elements"],JsdocTypeTypeof:["element"],JsdocTypeUndefined:[],JsdocTypeUnion:["elements"],JsdocTypeUnknown:[],JsdocTypeVariadic:["element"],JsdocTypeProperty:[],JsdocTypePredicate:["left","right"],JsdocTypeAsserts:["left","right"]};function Ye(e,u,t,n,r){null==n||n(e,u,t);var o,D=a(Me[e.type]);try{for(D.s();!(o=D.n()).done;){var i=o.value,c=e[i];if(void 0!==c)if(Array.isArray(c)){var s,l=a(c);try{for(l.s();!(s=l.n()).done;)Ye(s.value,e,i,n,r)}catch(p){l.e(p)}finally{l.f()}}else Ye(c,e,i,n,r)}}catch(p){D.e(p)}finally{D.f()}null==r||r(e,u,t)}function Ge(e,u,t){Ye(e,void 0,void 0,u,t)}e.catharsisTransform=je,e.identityTransformRules=qe,e.jtpTransform=Ke,e.parse=he,e.stringify=Se,e.stringifyRules=xe,e.transform=we,e.traverse=Ge,e.tryParse=ge,e.visitorKeys=Me}(u)}(0,D.exports)),D.exports),s=Object.defineProperty,p=function(e,u){return s(e,"name",{value:u,configurable:!0})},d=__STORYBOOK_MODULE_CORE_EVENTS_PREVIEW_ERRORS__.UnknownArgTypesError,m=p((function(e){return"literal"===e.name}),"isLiteral"),E=p((function(e){return e.value.replace(/['|"]/g,"")}),"toEnumOption"),A=p((function(e){switch(e.type){case"function":return{name:"function"};case"object":var u={};return e.signature.properties.forEach((function(e){u[e.key]=C(e.value)})),{name:"object",value:u};default:throw new d({type:e,language:"Flow"})}}),"convertSig"),C=p((function(e){var u,n,o,a,D=e.name,i=e.raw,c={};switch(r(i)<"u"&&(c.raw=i),e.name){case"literal":return t(t({},c),{},{name:"other",value:e.value});case"string":case"number":case"symbol":case"boolean":return t(t({},c),{},{name:D});case"Array":return t(t({},c),{},{name:"array",value:e.elements.map(C)});case"signature":return t(t({},c),A(e));case"union":return null!==(u=e.elements)&&void 0!==u&&u.every(m)?t(t({},c),{},{name:"enum",value:null===(n=e.elements)||void 0===n?void 0:n.map(E)}):t(t({},c),{},{name:D,value:null===(o=e.elements)||void 0===o?void 0:o.map(C)});case"intersection":return t(t({},c),{},{name:D,value:null===(a=e.elements)||void 0===a?void 0:a.map(C)});default:return t(t({},c),{},{name:"other",value:D})}}),"convert");function T(e,u){for(var t={},n=Object.keys(e),r=0;r<n.length;r++){var o=n[r],a=e[o];t[o]=u(a,o,e)}return t}p(T,"mapValues");var v=/^['"]|['"]$/g,B=p((function(e){return e.replace(v,"")}),"trimQuotes"),h=p((function(e){return v.test(e)}),"includesQuotes"),g=p((function(e){var u=B(e);return h(e)||Number.isNaN(Number(u))?u:Number(u)}),"parseLiteral"),w=/^\(.*\) => /,J=p((function(e){var u=e.name,n=e.raw,o=e.computed,a=e.value,D={};switch(r(n)<"u"&&(D.raw=n),u){case"enum":var i=o?a:a.map((function(e){return g(e.value)}));return t(t({},D),{},{name:u,value:i});case"string":case"number":case"symbol":case"object":return t(t({},D),{},{name:u});case"func":return t(t({},D),{},{name:"function"});case"bool":case"boolean":return t(t({},D),{},{name:"boolean"});case"arrayOf":case"array":return t(t({},D),{},{name:"array",value:a&&J(a)});case"objectOf":return t(t({},D),{},{name:u,value:J(a)});case"shape":case"exact":var c=T(a,(function(e){return J(e)}));return t(t({},D),{},{name:"object",value:c});case"union":return t(t({},D),{},{name:"union",value:a.map((function(e){return J(e)}))});default:if((null==u?void 0:u.indexOf("|"))>0)try{var s=u.split("|").map((function(e){return JSON.parse(e)}));return t(t({},D),{},{name:"enum",value:s})}catch(F){}var l=a?"".concat(u,"(").concat(a,")"):u,p=w.test(u)?"function":"other";return t(t({},D),{},{name:p,value:l})}}),"convert"),b=__STORYBOOK_MODULE_CORE_EVENTS_PREVIEW_ERRORS__.UnknownArgTypesError,N=p((function(e){switch(e.type){case"function":return{name:"function"};case"object":var u={};return e.signature.properties.forEach((function(e){u[e.key]=P(e.value)})),{name:"object",value:u};default:throw new b({type:e,language:"Typescript"})}}),"convertSig"),P=p((function(e){var u,n,o,a,D=e.name,i=e.raw,c={};switch(r(i)<"u"&&(c.raw=i),e.name){case"string":case"number":case"symbol":case"boolean":return t(t({},c),{},{name:D});case"Array":return t(t({},c),{},{name:"array",value:e.elements.map(P)});case"signature":return t(t({},c),N(e));case"union":return null!==(u=e.elements)&&void 0!==u&&u.every((function(e){return"literal"===e.name}))?t(t({},c),{},{name:"enum",value:null===(n=e.elements)||void 0===n?void 0:n.map((function(e){return g(e.value)}))}):t(t({},c),{},{name:D,value:null===(o=e.elements)||void 0===o?void 0:o.map(P)});case"intersection":return t(t({},c),{},{name:D,value:null===(a=e.elements)||void 0===a?void 0:a.map(P)});default:return t(t({},c),{},{name:"other",value:D})}}),"convert"),x=p((function(e){var u=e.type,t=e.tsType,n=e.flowType;try{if(null!=u)return J(u);if(null!=t)return P(t);if(null!=n)return C(n)}catch(r){console.error(r)}return null}),"convert"),O=u("j",function(e){return e.JAVASCRIPT="JavaScript",e.FLOW="Flow",e.TYPESCRIPT="TypeScript",e.UNKNOWN="Unknown",e}(O||{})),S=["null","undefined"];function k(e){return S.some((function(u){return u===e}))}p(k,"isDefaultValueBlacklisted");var I,R,j=p((function(e){if(!e)return"";if("string"==typeof e)return e;throw new Error("Description: expected string, got: ".concat(JSON.stringify(e)))}),"str");function _(e){return!!e.__docgenInfo}function L(e){return null!=e&&Object.keys(e).length>0}function U(e,u){return _(e)?e.__docgenInfo[u]:null}function V(e){return _(e)?j(e.__docgenInfo.description):""}function K(e){return/^\s+$/.test(e)}function q(e){var u=e.match(/\r+$/);return null==u?["",e]:[e.slice(-u[0].length),e.slice(0,-u[0].length)]}function M(e){var u=e.match(/^\s+/);return null==u?["",e]:[e.slice(0,u[0].length),e.slice(u[0].length)]}function Y(e){return e.split(/\n/)}function G(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.assign({tag:"",name:"",type:"",optional:!1,description:"",problems:[],source:[]},e)}function W(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.assign({start:"",delimiter:"",postDelimiter:"",tag:"",postTag:"",name:"",postName:"",type:"",postType:"",description:"",end:"",lineEnd:""},e)}p(_,"hasDocgen"),p(L,"isValidDocgenSection"),p(U,"getDocgenSection"),p(V,"getDocgenDescription"),(R=I=I||(I={})).start="/**",R.nostart="/***",R.delim="*",R.end="*/",p(K,"isSpace"),p(q,"splitCR"),p(M,"splitSpace"),p(Y,"splitLines"),p(G,"seedSpec"),p(W,"seedTokens");var X=/^@\S+/;function Q(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).fence,u=z(void 0===e?"```":e),t=p((function(e,t){return u(e)?!t:t}),"toggleFence");return p((function(e){var u,n=[[]],r=!1,o=a(e);try{for(o.s();!(u=o.n()).done;){var D=u.value;X.test(D.tokens.description)&&!r?n.push([D]):n[n.length-1].push(D),r=t(D.tokens.description,r)}}catch(i){o.e(i)}finally{o.f()}return n}),"parseBlock")}function z(e){return"string"==typeof e?function(u){return u.split(e).length%2==0}:e}function H(){var u=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=u.startLine,n=void 0===t?0:t,r=u.markers,o=void 0===r?I:r,a=null,D=n;return p((function(u){var t,n,r,i,c=u,s=W();if(t=e(q(c),2),s.lineEnd=t[0],n=e(M(c=t[1]),2),s.start=n[0],c=n[1],null===a&&c.startsWith(o.start)&&!c.startsWith(o.nostart)&&(a=[],s.delimiter=c.slice(0,o.start.length),r=e(M(c=c.slice(o.start.length)),2),s.postDelimiter=r[0],c=r[1]),null===a)return D++,null;var l=c.trimRight().endsWith(o.end);if(""===s.delimiter&&c.startsWith(o.delim)&&!c.startsWith(o.end)&&(s.delimiter=o.delim,i=e(M(c=c.slice(o.delim.length)),2),s.postDelimiter=i[0],c=i[1]),l){var p=c.trimRight();s.end=c.slice(p.length-o.end.length),c=p.slice(0,-o.end.length)}if(s.description=c,a.push({number:D,source:u,tokens:s}),D++,l){var F=a.slice();return a=null,F}return null}),"parseSource")}function $(e){var u=e.tokenizers;return p((function(e){var t,n,r=G({source:e}),o=a(u);try{for(o.s();!(n=o.n()).done;){if(null!==(t=(r=(0,n.value)(r)).problems[r.problems.length-1])&&void 0!==t&&t.critical)break}}catch(D){o.e(D)}finally{o.f()}return r}),"parseSpec")}function Z(){return function(e){var u=e.source[0].tokens,t=u.description.match(/\s*(@(\S+))(\s*)/);return null===t?(e.problems.push({code:"spec:tag:prefix",message:'tag should start with "@" symbol',line:e.source[0].number,critical:!0}),e):(u.tag=t[1],u.postTag=t[3],u.description=u.description.slice(t[0].length),e.tag=t[2],e)}}function ee(){var u=te(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"compact");return function(t){var n,r=0,o=[],D=a(t.source.entries());try{for(D.s();!(n=D.n()).done;){var i=e(n.value,2),c=i[0],s=i[1].tokens,l="";if(0===c&&"{"!==s.description[0])return t;var p,F=a(s.description);try{for(F.s();!(p=F.n()).done;){var y=p.value;if("{"===y&&r++,"}"===y&&r--,l+=y,0===r)break}}catch(g){F.e(g)}finally{F.f()}if(o.push([s,l]),0===r)break}}catch(g){D.e(g)}finally{D.f()}if(0!==r)return t.problems.push({code:"spec:type:unpaired-curlies",message:"unpaired curlies",line:t.source[0].number,critical:!0}),t;var f,d=[],m=o[0][0].postDelimiter.length,E=a(o.entries());try{for(E.s();!(f=E.n()).done;){var A,C=e(f.value,2),T=C[0],v=e(C[1],2),B=v[0],h=v[1];B.type=h,T>0&&(B.type=B.postDelimiter.slice(m)+h,B.postDelimiter=B.postDelimiter.slice(0,m)),A=e(M(B.description.slice(h.length)),2),B.postType=A[0],B.description=A[1],d.push(B.type)}}catch(g){E.e(g)}finally{E.f()}return d[0]=d[0].slice(1),d[d.length-1]=d[d.length-1].slice(0,-1),t.type=u(d),t}}p(Q,"getParser"),p(z,"getFencer"),p(H,"getParser"),p($,"getParser"),p(Z,"tagTokenizer"),p(ee,"typeTokenizer");var ue=p((function(e){return e.trim()}),"trim");function te(e){return"compact"===e?function(e){return e.map(ue).join("")}:"preserve"===e?function(e){return e.join("\n")}:e}p(te,"getJoiner");var ne=p((function(e){return e&&e.startsWith('"')&&e.endsWith('"')}),"isQuoted");function re(){var u=p((function(e,u,t){return""===u.tokens.type?e:t}),"typeEnd");return function(t){var n,r,o=t.source[t.source.reduce(u,0)].tokens,D=o.description.trimLeft(),i=D.split('"');if(i.length>1&&""===i[0]&&i.length%2==1)return t.name=i[1],o.name='"'.concat(i[1],'"'),n=e(M(D.slice(o.name.length)),2),o.postName=n[0],o.description=n[1],t;var c,s,l=0,p="",F=!1,y=a(D);try{for(y.s();!(s=y.n()).done;){var f=s.value;if(0===l&&K(f))break;"["===f&&l++,"]"===f&&l--,p+=f}}catch(E){y.e(E)}finally{y.f()}if(0!==l)return t.problems.push({code:"spec:name:unpaired-brackets",message:"unpaired brackets",line:t.source[0].number,critical:!0}),t;var d=p;if("["===p[0]&&"]"===p[p.length-1]){F=!0;var m=(p=p.slice(1,-1)).split("=");if(p=m[0].trim(),void 0!==m[1]&&(c=m.slice(1).join("=").trim()),""===p)return t.problems.push({code:"spec:name:empty-name",message:"empty name",line:t.source[0].number,critical:!0}),t;if(""===c)return t.problems.push({code:"spec:name:empty-default",message:"empty default value",line:t.source[0].number,critical:!0}),t;if(!ne(c)&&/=(?!>)/.test(c))return t.problems.push({code:"spec:name:invalid-default",message:"invalid default value syntax",line:t.source[0].number,critical:!0}),t}return t.optional=F,t.name=p,o.name=d,void 0!==c&&(t.default=c),r=e(M(D.slice(o.name.length)),2),o.postName=r[0],o.description=r[1],t}}function oe(){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:I,u=ae(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"compact");return function(t){return t.description=u(t.source,e),t}}function ae(e){return"compact"===e?De:"preserve"===e?se:e}function De(e){return e.map((function(e){return e.tokens.description.trim()})).filter((function(e){return""!==e})).join(" ")}p(re,"nameTokenizer"),p(oe,"descriptionTokenizer"),p(ae,"getJoiner"),p(De,"compactJoiner");var ie=p((function(e,u,t){return""===u.tokens.type?e:t}),"lineNo"),ce=p((function(e){var u=e.tokens;return(""===u.delimiter?u.start:u.postDelimiter.slice(1))+u.description}),"getDescription");function se(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:I;if(0===e.length)return"";""===e[0].tokens.description&&e[0].tokens.delimiter===u.start&&(e=e.slice(1));var t=e[e.length-1];return void 0!==t&&""===t.tokens.description&&t.tokens.end.endsWith(u.end)&&(e=e.slice(0,-1)),(e=e.slice(e.reduce(ie,0))).map(ce).join("\n")}function le(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},u=e.startLine,t=void 0===u?0:u,n=e.fence,r=void 0===n?"```":n,o=e.spacing,D=void 0===o?"compact":o,i=e.markers,c=void 0===i?I:i,s=e.tokenizers,l=void 0===s?[function(e){var u=e.source[0].tokens,t=u.description.match(/\s*(@(\S+))(\s*)/);return null===t?(e.problems.push({code:"spec:tag:prefix",message:'tag should start with "@" symbol',line:e.source[0].number,critical:!0}),e):(u.tag=t[1],u.postTag=t[3],u.description=u.description.slice(t[0].length),e.tag=t[2],e)},ee(D),re(),oe(D)]:s;if(t<0||t%1>0)throw new Error("Invalid startLine");var p=H({startLine:t,markers:c}),F=Q({fence:r}),y=$({tokenizers:l}),f=ae(D);return function(e){var u,t=[],n=a(Y(e));try{for(n.s();!(u=n.n()).done;){var r=u.value,o=p(r);if(null!==o){var D=F(o),i=D.slice(1).map(y);t.push({description:f(D[0],c),tags:i,source:o,problems:i.reduce((function(e,u){return e.concat(u.problems)}),[])})}}}catch(s){n.e(s)}finally{n.f()}return t}}function pe(e){return e.start+e.delimiter+e.postDelimiter+e.tag+e.postTag+e.type+e.postType+e.name+e.postName+e.description+e.end+e.lineEnd}function Fe(e){return le(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})(e)}function ye(e){return null!=e&&e.includes("@")}function fe(e){var u=Fe("/**\n"+(null!=e?e:"").split("\n").map((function(e){return" * ".concat(e)})).join("\n")+"\n*/",{spacing:"preserve"});if(!u||0===u.length)throw new Error("Cannot parse JSDoc tags.");return u[0]}p(se,"preserveJoiner"),p(le,"getParser"),p(pe,"join"),p((function(){return function(e){return e.source.map((function(e){return pe(e.tokens)})).join("\n")}}),"getStringifier"),p(Fe,"parse"),p(ye,"containsJsDoc"),p(fe,"parse");var de={tags:["param","arg","argument","returns","ignore","deprecated"]},me=p((function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:de;if(!ye(e))return{includesJsDoc:!1,ignore:!1};var t=fe(e),n=Ee(t,u.tags);return n.ignore?{includesJsDoc:!0,ignore:!0}:{includesJsDoc:!0,ignore:!1,description:t.description.trim(),extractedTags:n}}),"parseJsDoc");function Ee(e,u){var t,n={params:null,deprecated:null,returns:null,ignore:!1},r=a(e.tags);try{for(r.s();!(t=r.n()).done;){var o=t.value;if(void 0===u||u.includes(o.tag)){if("ignore"===o.tag){n.ignore=!0;break}switch(o.tag){case"param":case"arg":case"argument":var D=Ce(o);null!=D&&(null==n.params&&(n.params=[]),n.params.push(D));break;case"deprecated":var i=Te(o);null!=i&&(n.deprecated=i);break;case"returns":var c=he(o);null!=c&&(n.returns=c)}}}}catch(s){r.e(s)}finally{r.f()}return n}function Ae(e){return e.replace(/[\.-]$/,"")}function Ce(e){if(!e.name||"-"===e.name)return null;var u=Je(e.type);return{name:e.name,type:u,description:Be(e.description),getPrettyName:p((function(){return Ae(e.name)}),"getPrettyName"),getTypeName:p((function(){return u?be(u):null}),"getTypeName")}}function Te(e){return e.name?ve(e.name,e.description):null}function ve(e,u){return Be(""===e?u:"".concat(e," ").concat(u))}function Be(e){var u=e.replace(/^- /g,"").trim();return""===u?null:u}function he(e){var u=Je(e.type);return u?{type:u,description:ve(e.name,e.description),getTypeName:p((function(){return be(u)}),"getTypeName")}:null}p(Ee,"extractJsDocTags"),p(Ae,"normaliseParamName"),p(Ce,"extractParam"),p(Te,"extractDeprecated"),p(ve,"joinNameAndDescription"),p(Be,"normaliseDescription"),p(he,"extractReturns");var ge=i.stringifyRules(),we=ge.JsdocTypeObject;function Je(e){try{return i.parse(e,"typescript")}catch(u){return null}}function be(e){return i.transform(ge,e)}function Ne(e){return e.length>90}function Pe(e){return e.length>50}function xe(e,u){return e===u?{summary:e}:{summary:e,detail:u}}function Oe(e,u){if(null!=e){var t=e.value;if(!k(t))return Pe(t)?xe(null==u?void 0:u.name,t):xe(t)}return null}function Se(e){var u=e.name,t=e.value,n=e.elements,r=e.raw;return null!=t?t:null!=n?n.map(Se).join(" | "):null!=r?r:u}function ke(e){var u=e.name,t=e.raw,n=e.elements;return xe(null!=n?n.map(Se).join(" | "):null!=t?t.replace(/^\|\s*/,""):u)}function Ie(e){var u=e.type,t=e.raw;return xe(null!=t?t:u)}function Re(e){var u=e.type,t=e.raw;return null!=t?Ne(t)?xe(u,t):xe(t):xe(u)}function je(e){return"object"===e.type?Re(e):Ie(e)}function _e(e){var u=e.name,t=e.raw;return null!=t?Ne(t)?xe(u,t):xe(t):xe(u)}function Le(e){if(null==e)return null;switch(e.name){case"union":return ke(e);case"signature":return je(e);default:return _e(e)}}ge.JsdocTypeAny=function(){return"any"},ge.JsdocTypeObject=function(e,u){return"(".concat(we(e,u),")")},ge.JsdocTypeOptional=function(e,u){return u(e.element)},ge.JsdocTypeNullable=function(e,u){return u(e.element)},ge.JsdocTypeNotNullable=function(e,u){return u(e.element)},ge.JsdocTypeUnion=function(e,u){return e.elements.map(u).join("|")},p(Je,"extractType"),p(be,"extractTypeName"),p(Ne,"isTooLongForTypeSummary"),p(Pe,"isTooLongForDefaultValueSummary"),p(xe,"createSummaryValue"),p(Oe,"createDefaultValue"),p(Se,"generateUnionElement"),p(ke,"generateUnion"),p(Ie,"generateFuncSignature"),p(Re,"generateObjectSignature"),p(je,"generateSignature"),p(_e,"generateDefault"),p(Le,"createType");var Ue=p((function(e,u){var t=u.flowType,n=u.description,r=u.required,o=u.defaultValue;return{name:e,type:Le(t),required:r,description:n,defaultValue:Oe(null!=o?o:null,null!=t?t:null)}}),"createFlowPropDef");function Ve(e){var u=e.defaultValue;if(null!=u){var t=u.value;if(!k(t))return xe(t)}return null}function Ke(e){var u=e.tsType,t=e.required;if(null==u)return null;var n=u.name;return t||(n=n.replace(" | undefined","")),xe(["Array","Record","signature"].includes(u.name)?u.raw:n)}p(Ve,"createDefaultValue"),p(Ke,"createType");var qe=p((function(e,u){var t=u.description,n=u.required;return{name:e,type:Ke(u),required:n,description:t,defaultValue:Ve(u)}}),"createTsPropDef");function Me(e){return null!=e?xe(e.name):null}function Ye(e){var u=e.computed,t=e.func;return r(u)>"u"&&r(t)>"u"}function Ge(e){return!!e&&("string"===e.name||"enum"===e.name&&(Array.isArray(e.value)&&e.value.every((function(e){var u=e.value;return"string"==typeof u&&'"'===u[0]&&'"'===u[u.length-1]}))))}function We(e,u){if(null!=e){var t=e.value;if(!k(t))return Ye(e)&&Ge(u)?xe(JSON.stringify(t)):xe(t)}return null}function Xe(e,u,t){var n=t.description,r=t.required,o=t.defaultValue;return{name:e,type:Me(u),required:r,description:n,defaultValue:We(o,u)}}function Qe(e,u){if(null!=u&&u.includesJsDoc){var n,r=u.description,o=u.extractedTags;null!=r&&(e.description=u.description);var a=t(t({},o),{},{params:null==o||null===(n=o.params)||void 0===n?void 0:n.map((function(e){return{name:e.getPrettyName(),description:e.description}}))});Object.values(a).filter(Boolean).length>0&&(e.jsDocTags=a)}return e}p(Me,"createType"),p(Ye,"isReactDocgenTypescript"),p(Ge,"isStringValued"),p(We,"createDefaultValue"),p(Xe,"createBasicPropDef"),p(Qe,"applyJsDocResult");var ze=p((function(e,u,t){var n=Xe(e,u.type,u);return n.sbType=x(u),Qe(n,t)}),"javaScriptFactory"),He=p((function(e,u,t){var n=qe(e,u);return n.sbType=x(u),Qe(n,t)}),"tsFactory"),$e=p((function(e,u,t){var n=Ue(e,u);return n.sbType=x(u),Qe(n,t)}),"flowFactory"),Ze=p((function(e,u,t){return Qe(Xe(e,{name:"unknown"},u),t)}),"unknownFactory"),eu=p((function(e){switch(e){case"JavaScript":return ze;case"TypeScript":return He;case"Flow":return $e;default:return Ze}}),"getPropDefFactory"),uu=p((function(e){return null!=e.type?"JavaScript":null!=e.flowType?"Flow":null!=e.tsType?"TypeScript":"Unknown"}),"getTypeSystem"),tu=p((function(e){var u=uu(e[0]),n=eu(u);return e.map((function(e){var r,o=e;return null!==(r=e.type)&&void 0!==r&&r.elements&&(o=t(t({},e),{},{type:t(t({},e.type),{},{value:e.type.elements})})),ru(o.name,o,u,n)}))}),"extractComponentSectionArray"),nu=p((function(e){var u=Object.keys(e),t=uu(e[u[0]]),n=eu(t);return u.map((function(u){var r=e[u];return null!=r?ru(u,r,t,n):null})).filter(Boolean)}),"extractComponentSectionObject");u("o",p((function(e,u){var t=U(e,u);return L(t)?Array.isArray(t)?tu(t):nu(t):[]}),"extractComponentProps"));function ru(e,u,t,n){var r=me(u.description);return r.includesJsDoc&&r.ignore?null:{propDef:n(e,u,r),jsDocTags:r.extractedTags,docgenInfo:u,typeSystem:t}}function ou(e){return null!=e?V(e):""}p(ru,"extractProp"),p(ou,"extractComponentDescription");var au=__STORYBOOK_MODULE_PREVIEW_API__.combineParameters,Du=(u("c",p((function(e){var u=e.component,t=e.argTypes,n=e.parameters.docs,r=(void 0===n?{}:n).extractArgTypes,o=r&&u?r(u):{};return o?au(o,t):t}),"enhanceArgTypes")),"".concat("storybook/docs","/snippet-rendered")),iu=function(e){return e.AUTO="auto",e.CODE="code",e.DYNAMIC="dynamic",e}(iu||{});u({y:Du,g:iu})}}}))}();

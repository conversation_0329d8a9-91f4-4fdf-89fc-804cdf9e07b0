!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}System.register(["./index-legacy-8Bh_oy_I.js"],(function(e,r){"use strict";var n;return{setters:[function(t){n=t.b}],execute:function(){e("r",(function(){if(i)return o.exports;return i=1,function t(){if("undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__||"function"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)return;try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(e){console.error(e)}}(),o.exports=function(){if(r)return s;r=1;var e=n();function i(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)e+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var f={d:{f:o,r:function(){throw Error(i(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},c=Symbol.for("react.portal");var u=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function y(t,e){return"font"===t?"":"string"==typeof e?"use-credentials"===e?e:"":void 0}return s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=f,s.createPortal=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)throw Error(i(299));return function(t,e,r){var n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:c,key:null==n?null:""+n,children:t,containerInfo:e,implementation:r}}(t,e,null,r)},s.flushSync=function(t){var e=u.T,r=f.p;try{if(u.T=null,f.p=2,t)return t()}finally{u.T=e,f.p=r,f.d.f()}},s.preconnect=function(t,e){"string"==typeof t&&(e?e="string"==typeof(e=e.crossOrigin)?"use-credentials"===e?e:"":void 0:e=null,f.d.C(t,e))},s.prefetchDNS=function(t){"string"==typeof t&&f.d.D(t)},s.preinit=function(t,e){if("string"==typeof t&&e&&"string"==typeof e.as){var r=e.as,n=y(r,e.crossOrigin),i="string"==typeof e.integrity?e.integrity:void 0,o="string"==typeof e.fetchPriority?e.fetchPriority:void 0;"style"===r?f.d.S(t,"string"==typeof e.precedence?e.precedence:void 0,{crossOrigin:n,integrity:i,fetchPriority:o}):"script"===r&&f.d.X(t,{crossOrigin:n,integrity:i,fetchPriority:o,nonce:"string"==typeof e.nonce?e.nonce:void 0})}},s.preinitModule=function(e,r){if("string"==typeof e)if("object"===t(r)&&null!==r){if(null==r.as||"script"===r.as){var n=y(r.as,r.crossOrigin);f.d.M(e,{crossOrigin:n,integrity:"string"==typeof r.integrity?r.integrity:void 0,nonce:"string"==typeof r.nonce?r.nonce:void 0})}}else null==r&&f.d.M(e)},s.preload=function(e,r){if("string"==typeof e&&"object"===t(r)&&null!==r&&"string"==typeof r.as){var n=r.as,i=y(n,r.crossOrigin);f.d.L(e,n,{crossOrigin:i,integrity:"string"==typeof r.integrity?r.integrity:void 0,nonce:"string"==typeof r.nonce?r.nonce:void 0,type:"string"==typeof r.type?r.type:void 0,fetchPriority:"string"==typeof r.fetchPriority?r.fetchPriority:void 0,referrerPolicy:"string"==typeof r.referrerPolicy?r.referrerPolicy:void 0,imageSrcSet:"string"==typeof r.imageSrcSet?r.imageSrcSet:void 0,imageSizes:"string"==typeof r.imageSizes?r.imageSizes:void 0,media:"string"==typeof r.media?r.media:void 0})}},s.preloadModule=function(t,e){if("string"==typeof t)if(e){var r=y(e.as,e.crossOrigin);f.d.m(t,{as:"string"==typeof e.as&&"script"!==e.as?e.as:void 0,crossOrigin:r,integrity:"string"==typeof e.integrity?e.integrity:void 0})}else f.d.m(t)},s.requestFormReset=function(t){f.d.r(t)},s.unstable_batchedUpdates=function(t,e){return t(e)},s.useFormState=function(t,e,r){return u.H.useFormState(t,e,r)},s.useFormStatus=function(){return u.H.useHostTransitionStatus()},s.version="19.1.0",s}(),o.exports}));var r,i,o={exports:{}},s={}}}}))}();

System.register(["./index-legacy-C2Kr5I5h.js","./__federation_expose_App-legacy-pwn7RX54.js","./x-legacy-yM9Jk2p5.js","./index-legacy-8Bh_oy_I.js","./actions-legacy-CRkJzGcr.js"],(function(e,s){"use strict";var t,n,l,a,i,r,c,o;return{setters:[function(e){t=e.j},function(e){n=e.x,l=e.y,a=e.L,i=e.z},function(e){r=e.c,c=e.z},null,function(e){o=e.A}],execute:function(){
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */
var s=r("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]]);e("component",(function(){var e=n((function(e){return e.views}));return t.jsxs("div",{className:"flex h-full flex-col [&_:is(section,header,footer)]:p-4",children:[t.jsxs("section",{children:[t.jsx("h2",{children:"Tables"}),t.jsx("p",{className:"text-foreground-secondary mt-1",children:"Create up to 4 views to help you optimize the floor management"}),t.jsx("ul",{className:"mt-4 space-y-2",children:e.tables.map((function(e){return t.jsxs("li",{className:"bg-background-highlight border-border relative flex w-full items-center gap-1 rounded-2xl border p-3 text-left",children:[t.jsx("h3",{className:"leading-none",children:e.title}),t.jsx(l,{size:"xs",children:"Default view"}),t.jsxs(a,{from:i.id,to:"./tables/$view",params:{view:e.id},className:"ml-auto flex items-center gap-1 after:absolute after:inset-0",children:[t.jsx(s,{}),"Edit"]})]},e.id)}))})]}),t.jsx(o,{children:t.jsx(c,{asChild:!0,className:"w-full",children:t.jsx(a,{to:"/dine-in",children:"Back to Tables"})})})]})}))}}}));

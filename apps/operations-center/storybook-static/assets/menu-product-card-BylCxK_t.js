import{j as r}from"./index-C_VjMC7g.js";import{r as c}from"./index-D4lIrffr.js";import{c as p,n as u,z as x}from"./x-B9YxCZbW.js";import{u as v,i as N}from"./drawer-efJps8Fr.js";import{C as M,D as P,i as k,a8 as g,y as I,M as q,a9 as w,m as V}from"./__federation_expose_App-DYUBmReV.js";import{P as z}from"./plus-BP0qVozT.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]],O=p("Flame",D);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=[["path",{d:"M5 12h14",key:"1ays0h"}]],L=p("Minus",S);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],A=p("Trash2",R),G=(e,a,t)=>{const s=[],d=e>=a-t,i=a%t===0,l=e>=a-t&&e<a-(a%t||t);return e===0&&s.push("tl"),e===a-1&&s.push("br"),e===Math.min(t-1,a-1)&&s.push("tr"),d&&e%t===0&&s.push("bl"),d&&e%t===0&&s.push("bl"),!i&&l&&e%t===t-1&&s.push("br"),s},T=(e,a,t)=>{const s={};for(const d of t){const i=G(e,a,d);for(const l of i)s["data-".concat(d,"c-").concat(l)]=""}return s},y=({children:e,className:a,size:t="full",...s})=>r.jsx("ul",{...s,className:u("grid grid-cols-1 gap-0.25",t==="full"&&"grid-cols-2",a),children:c.Children.map(e,(d,i)=>r.jsx("li",{...T(i,c.Children.count(e),[1,2]),className:u("rounded-none *:rounded-[inherit]",t==="tight"&&["data-[1c-tl]:rounded-tl-xl","data-[1c-tr]:rounded-tr-xl","data-[1c-bl]:rounded-bl-xl","data-[1c-br]:rounded-br-xl"],t==="full"&&["rounded-none","data-[2c-tl]:rounded-tl-xl","data-[2c-tr]:rounded-tr-xl","data-[2c-bl]:rounded-bl-xl","data-[2c-br]:rounded-br-xl"]),children:d},i))});try{y.displayName="MenuProductCardGrid",y.__docgenInfo={description:"",displayName:"MenuProductCardGrid",props:{size:{defaultValue:{value:"full"},description:"",name:"size",required:!1,type:{name:"enum",value:[{value:"undefined"},{value:'"full"'},{value:'"tight"'}]}}}}}catch(e){}const b=({product:e,orderId:a,className:t})=>{var h;const{open:s}=v(),d=M(a),{addItem:i,updateItem:l,removeItem:j}=P(a),m=k(V(a)),o=c.useMemo(()=>{var n;return{pending:g(e.id,d),ordered:g(e.id,((n=m.data)==null?void 0:n.ordered)||[])}},[e.id,d,(h=m.data)==null?void 0:h.ordered]),f=()=>{e.options.length>0?s({product:e,onSubmit:n=>i(n)}):i(w(e))},_=()=>{const n=d.find(({productId:C})=>C===e.id);n&&(n.quantity===1?j(n.id):l(n.id,{...n,quantity:n.quantity-1}))};return r.jsxs("div",{className:u("bg-background-highlight outline-border flex h-24 cursor-pointer flex-col justify-between p-2 text-sm outline",t),onClick:n=>{N(n.target)||(n.preventDefault(),f())},inert:e.isSoldOut,children:[r.jsxs("p",{className:"flex justify-between gap-1",children:[r.jsx("span",{className:"line-clamp-2",children:e.name}),!!o.ordered&&r.jsxs("span",{className:"bg-background-low flex h-fit gap-1 rounded-sm px-1.5 py-1.25 text-xs [font-size:--spacing(2.5)] leading-none",children:[r.jsx(O,{}),o.ordered]})]}),r.jsxs("div",{className:"flex items-end justify-between gap-1",children:[e.isSoldOut?r.jsx(I,{size:"xs",className:"font-medium",children:"Sold Out"}):r.jsx(q,{amount:e.unitPrice,className:"text-foreground-secondary"}),r.jsxs("div",{className:"flex items-center gap-2",children:[o.pending>0&&r.jsx(x,{size:"sm",square:!0,"aria-label":"Remove Product",onClick:_,children:o.pending>1?r.jsx(L,{}):r.jsx(A,{})}),r.jsx(x,{variant:o.pending>0?"accent":"primary",size:"sm",square:!0,"aria-label":"Add Product",onClick:f,disabled:e.isSoldOut,children:o.pending>0?o.pending:r.jsx(z,{})})]})]})]})};try{b.displayName="MenuProductCard",b.__docgenInfo={description:"",displayName:"MenuProductCard",props:{product:{defaultValue:null,description:"",name:"product",required:!0,type:{name:"Product"}},orderId:{defaultValue:null,description:"",name:"orderId",required:!0,type:{name:"string"}},className:{defaultValue:null,description:"",name:"className",required:!1,type:{name:"string | undefined"}}}}}catch(e){}export{y as M,b as a};

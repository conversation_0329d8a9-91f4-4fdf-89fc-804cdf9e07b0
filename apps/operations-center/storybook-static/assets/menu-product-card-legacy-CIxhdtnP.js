!function(){function e(r){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(r)}var r=["children","className","size"];function n(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),n.push.apply(n,t)}return n}function t(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?n(Object(t),!0).forEach((function(r){i(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):n(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function i(r,n,t){return(n=function(r){var n=function(r,n){if("object"!=e(r)||!r)return r;var t=r[Symbol.toPrimitive];if(void 0!==t){var i=t.call(r,n||"default");if("object"!=e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(r)}(r,"string");return"symbol"==e(n)?n:n+""}(n))in r?Object.defineProperty(r,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[n]=t,r}function a(e,r){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,r){if(e){if("string"==typeof e)return o(e,r);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,r):void 0}}(e))||r&&e&&"number"==typeof e.length){n&&(e=n);var t=0,i=function(){};return{s:i,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return u=e.done,e},e:function(e){l=!0,a=e},f:function(){try{u||null==n.return||n.return()}finally{if(l)throw a}}}}function o(e,r){(null==r||r>e.length)&&(r=e.length);for(var n=0,t=Array(r);n<r;n++)t[n]=e[n];return t}System.register(["./index-legacy-C2Kr5I5h.js","./index-legacy-8Bh_oy_I.js","./x-legacy-yM9Jk2p5.js","./drawer-legacy-CIquJDQn.js","./__federation_expose_App-legacy-pwn7RX54.js","./plus-legacy-khTMZduY.js"],(function(e,n){"use strict";var i,o,u,l,c,d,s,f,p,y,m,b,h,v,g,x;return{setters:[function(e){i=e.j},function(e){o=e.r},function(e){u=e.c,l=e.n,c=e.z},function(e){d=e.u,s=e.i},function(e){f=e.C,p=e.D,y=e.i,m=e.a8,b=e.y,h=e.M,v=e.a9,g=e.m},function(e){x=e.P}],execute:function(){
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */
var n=u("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]]),j=u("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),O=u("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),P=function(e,r,n){var t=[],i=e>=r-n,a=r%n===0,o=e>=r-n&&e<r-(r%n||n);return 0===e&&t.push("tl"),e===r-1&&t.push("br"),e===Math.min(n-1,r-1)&&t.push("tr"),i&&e%n===0&&t.push("bl"),i&&e%n===0&&t.push("bl"),!a&&o&&e%n===n-1&&t.push("br"),t},w=e("M",(function(e){var n=e.children,u=e.className,c=e.size,d=void 0===c?"full":c,s=function(e,r){if(null==e)return{};var n,t,i=function(e,r){if(null==e)return{};var n={};for(var t in e)if({}.hasOwnProperty.call(e,t)){if(-1!==r.indexOf(t))continue;n[t]=e[t]}return n}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(t=0;t<a.length;t++)n=a[t],-1===r.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,r);return i.jsx("ul",t(t({},s),{},{className:l("grid grid-cols-1 gap-0.25","full"===d&&"grid-cols-2",u),children:o.Children.map(n,(function(e,r){return i.jsx("li",t(t({},function(e,r,n){var t,i={},o=a(n);try{for(o.s();!(t=o.n()).done;){var u,l=t.value,c=a(P(e,r,l));try{for(c.s();!(u=c.n()).done;){var d=u.value;i["data-".concat(l,"c-").concat(d)]=""}}catch(s){c.e(s)}finally{c.f()}}}catch(s){o.e(s)}finally{o.f()}return i}(r,o.Children.count(n),[1,2])),{},{className:l("rounded-none *:rounded-[inherit]","tight"===d&&["data-[1c-tl]:rounded-tl-xl","data-[1c-tr]:rounded-tr-xl","data-[1c-bl]:rounded-bl-xl","data-[1c-br]:rounded-br-xl"],"full"===d&&["rounded-none","data-[2c-tl]:rounded-tl-xl","data-[2c-tr]:rounded-tr-xl","data-[2c-bl]:rounded-bl-xl","data-[2c-br]:rounded-br-xl"]),children:e}),r)}))}))}));try{w.displayName="MenuProductCardGrid",w.__docgenInfo={description:"",displayName:"MenuProductCardGrid",props:{size:{defaultValue:{value:"full"},description:"",name:"size",required:!1,type:{name:"enum",value:[{value:"undefined"},{value:'"full"'},{value:'"tight"'}]}}}}}catch(N){}var S=e("a",(function(e){var r,a=e.product,u=e.orderId,P=e.className,w=d().open,S=f(u),N=p(u),M=N.addItem,k=N.updateItem,I=N.removeItem,C=y(g(u)),q=o.useMemo((function(){var e;return{pending:m(a.id,S),ordered:m(a.id,(null===(e=C.data)||void 0===e?void 0:e.ordered)||[])}}),[a.id,S,null===(r=C.data)||void 0===r?void 0:r.ordered]),z=function(){a.options.length>0?w({product:a,onSubmit:function(e){return M(e)}}):M(v(a))};return i.jsxs("div",{className:l("bg-background-highlight outline-border flex h-24 cursor-pointer flex-col justify-between p-2 text-sm outline",P),onClick:function(e){s(e.target)||(e.preventDefault(),z())},inert:a.isSoldOut,children:[i.jsxs("p",{className:"flex justify-between gap-1",children:[i.jsx("span",{className:"line-clamp-2",children:a.name}),!!q.ordered&&i.jsxs("span",{className:"bg-background-low flex h-fit gap-1 rounded-sm px-1.5 py-1.25 text-xs [font-size:--spacing(2.5)] leading-none",children:[i.jsx(n,{}),q.ordered]})]}),i.jsxs("div",{className:"flex items-end justify-between gap-1",children:[a.isSoldOut?i.jsx(b,{size:"xs",className:"font-medium",children:"Sold Out"}):i.jsx(h,{amount:a.unitPrice,className:"text-foreground-secondary"}),i.jsxs("div",{className:"flex items-center gap-2",children:[q.pending>0&&i.jsx(c,{size:"sm",square:!0,"aria-label":"Remove Product",onClick:function(){var e=S.find((function(e){return e.productId===a.id}));e&&(1===e.quantity?I(e.id):k(e.id,t(t({},e),{},{quantity:e.quantity-1})))},children:q.pending>1?i.jsx(j,{}):i.jsx(O,{})}),i.jsx(c,{variant:q.pending>0?"accent":"primary",size:"sm",square:!0,"aria-label":"Add Product",onClick:z,disabled:a.isSoldOut,children:q.pending>0?q.pending:i.jsx(x,{})})]})]})]})}));try{S.displayName="MenuProductCard",S.__docgenInfo={description:"",displayName:"MenuProductCard",props:{product:{defaultValue:null,description:"",name:"product",required:!0,type:{name:"Product"}},orderId:{defaultValue:null,description:"",name:"orderId",required:!0,type:{name:"string"}},className:{defaultValue:null,description:"",name:"className",required:!1,type:{name:"string | undefined"}}}}}catch(N){}}}}))}();

var R=Object.freeze,N=Object.defineProperty;var C=(r,e)=>R(N(r,"raw",{value:R(e||r.slice())}));import{d as W}from"./index-D-tao7Fd.js";const{useEffect:B,useMemo:A}=__STORYBOOK_MODULE_PREVIEW_API__,{global:q}=__STORYBOOK_MODULE_GLOBAL__,{logger:J}=__STORYBOOK_MODULE_CLIENT_LOGGER__;var p="backgrounds",H={light:{name:"light",value:"#F8F8F8"},dark:{name:"dark",value:"#333"}},{document:b,window:G}=q,K=()=>{var r;return!!((r=G==null?void 0:G.matchMedia("(prefers-reduced-motion: reduce)"))!=null&&r.matches)},T=r=>{(Array.isArray(r)?r:[r]).forEach(Q)},Q=r=>{var t;let e=b.getElementById(r);e&&((t=e.parentElement)==null||t.removeChild(e))},P=(r,e)=>{let t=b.getElementById(r);if(t)t.innerHTML!==e&&(t.innerHTML=e);else{let o=b.createElement("style");o.setAttribute("id",r),o.innerHTML=e,b.head.appendChild(o)}},j=(r,e,t)=>{var a;let o=b.getElementById(r);if(o)o.innerHTML!==e&&(o.innerHTML=e);else{let d=b.createElement("style");d.setAttribute("id",r),d.innerHTML=e;let i="addon-backgrounds-grid".concat(t?"-docs-".concat(t):""),n=b.getElementById(i);n?(a=n.parentElement)==null||a.insertBefore(d,n):b.head.appendChild(d)}},Z={cellSize:100,cellAmount:10,opacity:.8},I="addon-backgrounds",z="addon-backgrounds-grid",V=K()?"":"transition: background-color 0.3s;",ee=(r,e)=>{let{globals:t,parameters:o,viewMode:a,id:d}=e,{options:i=H,disable:n,grid:s=Z}=o[p]||{},c=t[p]||{},u=c.value,l=u?i[u]:void 0,$=(l==null?void 0:l.value)||"transparent",f=c.grid||!1,y=!!l&&!n,k=a==="docs"?"#anchor--".concat(d," .docs-story"):".sb-show-main",x=a==="docs"?"#anchor--".concat(d," .docs-story"):".sb-show-main",m=o.layout===void 0||o.layout==="padded",_=a==="docs"?20:m?16:0,{cellAmount:v,cellSize:g,opacity:S,offsetX:E=_,offsetY:h=_}=s,M=a==="docs"?"".concat(I,"-docs-").concat(d):"".concat(I,"-color"),w=a==="docs"?d:null;B(()=>{let L="\n    ".concat(k," {\n      background: ").concat($," !important;\n      ").concat(V,"\n      }");if(!y){T(M);return}j(M,L,w)},[k,M,w,y,$]);let O=a==="docs"?"".concat(z,"-docs-").concat(d):"".concat(z);return B(()=>{if(!f){T(O);return}let L=["".concat(g*v,"px ").concat(g*v,"px"),"".concat(g*v,"px ").concat(g*v,"px"),"".concat(g,"px ").concat(g,"px"),"".concat(g,"px ").concat(g,"px")].join(", "),X="\n        ".concat(x," {\n          background-size: ").concat(L," !important;\n          background-position: ").concat(E,"px ").concat(h,"px, ").concat(E,"px ").concat(h,"px, ").concat(E,"px ").concat(h,"px, ").concat(E,"px ").concat(h,"px !important;\n          background-blend-mode: difference !important;\n          background-image: linear-gradient(rgba(130, 130, 130, ").concat(S,") 1px, transparent 1px),\n           linear-gradient(90deg, rgba(130, 130, 130, ").concat(S,") 1px, transparent 1px),\n           linear-gradient(rgba(130, 130, 130, ").concat(S/2,") 1px, transparent 1px),\n           linear-gradient(90deg, rgba(130, 130, 130, ").concat(S/2,") 1px, transparent 1px) !important;\n        }\n      ");P(O,X)},[v,g,x,O,f,E,h,S]),r()},U,re=(r,e=[],t)=>{if(r==="transparent")return"transparent";if(e.find(a=>a.value===r)||r)return r;let o=e.find(a=>a.name===t);if(o)return o.value;if(t){let a=e.map(d=>d.name).join(", ");J.warn(W(U||(U=C(['\n        Backgrounds Addon: could not find the default color "','".\n        These are the available colors for your story based on your configuration:\n        ',".\n      "])),t,a))}return"transparent"},ae=(r,e)=>{var u;let{globals:t,parameters:o}=e,a=(u=t[p])==null?void 0:u.value,d=o[p],i=A(()=>d.disable?"transparent":re(a,d.values,d.default),[d,a]),n=A(()=>i&&i!=="transparent",[i]),s=e.viewMode==="docs"?"#anchor--".concat(e.id," .docs-story"):".sb-show-main",c=A(()=>"\n      ".concat(s," {\n        background: ").concat(i," !important;\n        ").concat(K()?"":"transition: background-color 0.3s;","\n      }\n    "),[i,s]);return B(()=>{let l=e.viewMode==="docs"?"addon-backgrounds-docs-".concat(e.id):"addon-backgrounds-color";if(!n){T(l);return}j(l,c,e.viewMode==="docs"?e.id:null)},[n,c,e]),r()},oe=(r,e)=>{var y,k,x;let{globals:t,parameters:o}=e,a=o[p].grid,d=((y=t[p])==null?void 0:y.grid)===!0&&a.disable!==!0,{cellAmount:i,cellSize:n,opacity:s}=a,c=e.viewMode==="docs",u=o.layout===void 0||o.layout==="padded"?16:0,l=(k=a.offsetX)!=null?k:c?20:u,$=(x=a.offsetY)!=null?x:c?20:u,f=A(()=>{let m=e.viewMode==="docs"?"#anchor--".concat(e.id," .docs-story"):".sb-show-main",_=["".concat(n*i,"px ").concat(n*i,"px"),"".concat(n*i,"px ").concat(n*i,"px"),"".concat(n,"px ").concat(n,"px"),"".concat(n,"px ").concat(n,"px")].join(", ");return"\n      ".concat(m," {\n        background-size: ").concat(_," !important;\n        background-position: ").concat(l,"px ").concat($,"px, ").concat(l,"px ").concat($,"px, ").concat(l,"px ").concat($,"px, ").concat(l,"px ").concat($,"px !important;\n        background-blend-mode: difference !important;\n        background-image: linear-gradient(rgba(130, 130, 130, ").concat(s,") 1px, transparent 1px),\n         linear-gradient(90deg, rgba(130, 130, 130, ").concat(s,") 1px, transparent 1px),\n         linear-gradient(rgba(130, 130, 130, ").concat(s/2,") 1px, transparent 1px),\n         linear-gradient(90deg, rgba(130, 130, 130, ").concat(s/2,") 1px, transparent 1px) !important;\n      }\n    ")},[n]);return B(()=>{let m=e.viewMode==="docs"?"addon-backgrounds-grid-docs-".concat(e.id):"addon-backgrounds-grid";if(!d){T(m);return}P(m,f)},[d,f,e]),r()},D,ie=(D=globalThis.FEATURES)!=null&&D.backgroundsStoryGlobals?[ee]:[oe,ae],F,le={[p]:{grid:{cellSize:20,opacity:.5,cellAmount:5},disable:!1,...!((F=globalThis.FEATURES)!=null&&F.backgroundsStoryGlobals)&&{values:Object.values(H)}}},de={[p]:{value:void 0,grid:!1}},Y,se=(Y=globalThis.FEATURES)!=null&&Y.backgroundsStoryGlobals?de:{[p]:null};export{ie as decorators,se as initialGlobals,le as parameters};

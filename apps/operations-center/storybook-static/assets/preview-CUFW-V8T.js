const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DocsRenderer-CFRXHY34-hKBcQ9ay.js","./preload-helper-Bj_ofs9N.js","./index-D4lIrffr.js","./index-C_VjMC7g.js","./index-DsJinFGm.js","./index-DrMxZ_hi.js","./index-ca7qGdJg.js","./index-D-tao7Fd.js","./react-18-CrtzkFTW.js"])))=>i.map(i=>d[i]);
import{_ as i}from"./preload-helper-Bj_ofs9N.js";var s=Object.defineProperty,_=(e,r)=>{for(var t in r)s(e,t,{get:r[t],enumerable:!0})},p={};_(p,{parameters:()=>n});var o,d=Object.entries((o=globalThis.TAGS_OPTIONS)!=null?o:{}).reduce((e,r)=>{let[t,a]=r;return a.excludeFromDocsStories&&(e[t]=!0),e},{}),n={docs:{renderer:async()=>{let{DocsRenderer:e}=await i(()=>import("./DocsRenderer-CFRXHY34-hKBcQ9ay.js").then(r=>r.D),__vite__mapDeps([0,1,2,3,4,5,6,7,8]),import.meta.url);return new e},stories:{filter:e=>{var r;return(e.tags||[]).filter(t=>d[t]).length===0&&!((r=e.parameters.docs)!=null&&r.disable)}}}};export{n as parameters};

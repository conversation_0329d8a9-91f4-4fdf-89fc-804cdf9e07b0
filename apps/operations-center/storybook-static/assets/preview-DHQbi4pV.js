const{makeDecorator:O,addons:_}=__STORYBOOK_MODULE_PREVIEW_API__,{STORY_CHANGED:l,SELECT_STORY:E}=__STORYBOOK_MODULE_CORE_EVENTS__,{global:L}=__STORYBOOK_MODULE_GLOBAL__;var c="links",{document:s,HTMLElement:v}=L,d=e=>_.getChannel().emit(E,e),i=e=>{let{target:t}=e;if(!(t instanceof v))return;let o=t,{sbKind:a,sbStory:r}=o.dataset;(a||r)&&(e.preventDefault(),d({kind:a,story:r}))},n=!1,m=()=>{n||(n=!0,s.addEventListener("click",i))},k=()=>{n&&(n=!1,s.removeEventListener("click",i))},R=O({name:"withLinks",parameterName:c,wrapper:(e,t)=>(m(),_.getChannel().once(l,k),e(t))}),S=[R];export{S as decorators};

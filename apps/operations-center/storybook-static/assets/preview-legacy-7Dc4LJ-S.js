!function(){function t(r){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(r)}function r(r,e,o){return(e=function(r){var e=function(r,e){if("object"!=t(r)||!r)return r;var o=r[Symbol.toPrimitive];if(void 0!==o){var i=o.call(r,e||"default");if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(r)}(r,"string");return"symbol"==t(e)?e:e+""}(e))in r?Object.defineProperty(r,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[e]=o,r}System.register([],(function(t,e){"use strict";return{execute:function(){var e,o=r({},"viewport",{value:void 0,isRotated:!1});t("initialGlobals",null!==(e=globalThis.FEATURES)&&void 0!==e&&e.viewportStoryGlobals?o:{viewport:"reset",viewportRotated:!1})}}}))}();

System.register([],(function(e,t){"use strict";return{execute:function(){var t=__STORYBOOK_MODULE_PREVIEW_API__,n=t.makeDecorator,r=t.addons,_=__STORYBOOK_MODULE_CORE_EVENTS__,a=_.STORY_CHANGED,i=_.SELECT_STORY,o=__STORYBOOK_MODULE_GLOBAL__.global,c=o.document,O=o.HTMLElement,s=function(e){var t=e.target;if(t instanceof O){var n,_=t.dataset,a=_.sbKind,o=_.sbStory;(a||o)&&(e.preventDefault(),n={kind:a,story:o},r.getChannel().emit(i,n))}},E=!1,u=function(){E&&(E=!1,c.removeEventListener("click",s))},L=n({name:"withLinks",parameterName:"links",wrapper:function(e,t){return E||(E=!0,c.addEventListener("click",s)),r.getChannel().once(a,u),e(t)}});e("decorators",[L])}}}));

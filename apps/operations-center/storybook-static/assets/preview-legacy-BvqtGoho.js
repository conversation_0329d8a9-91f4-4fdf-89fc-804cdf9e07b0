!function(){function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},n(t)}var t;function o(n,t){var o=Object.keys(n);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(n);t&&(e=e.filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),o.push.apply(o,e)}return o}function e(t,o,e){return(o=function(t){var o=function(t,o){if("object"!=n(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,o||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(t)}(t,"string");return"symbol"==n(o)?o:o+""}(o))in t?Object.defineProperty(t,o,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[o]=e,t}System.register(["./index-legacy-Dvg014dY.js"],(function(n,r){"use strict";var a;return{setters:[function(n){a=n.d}],execute:function(){var r,c,i,d=__STORYBOOK_MODULE_PREVIEW_API__,l=d.useEffect,u=d.useMemo,s=__STORYBOOK_MODULE_GLOBAL__.global,p=__STORYBOOK_MODULE_CLIENT_LOGGER__.logger,b="backgrounds",f={light:{name:"light",value:"#F8F8F8"},dark:{name:"dark",value:"#333"}},g=s.document,v=s.window,m=function(){var n;return!(null==v||null===(n=v.matchMedia("(prefers-reduced-motion: reduce)"))||void 0===n||!n.matches)},y=function(n){(Array.isArray(n)?n:[n]).forEach(x)},x=function(n){var t,o=g.getElementById(n);o&&(null===(t=o.parentElement)||void 0===t||t.removeChild(o))},O=function(n,t){var o=g.getElementById(n);if(o)o.innerHTML!==t&&(o.innerHTML=t);else{var e=g.createElement("style");e.setAttribute("id",n),e.innerHTML=t,g.head.appendChild(e)}},h=function(n,t,o){var e=g.getElementById(n);if(e)e.innerHTML!==t&&(e.innerHTML=t);else{var r,a=g.createElement("style");a.setAttribute("id",n),a.innerHTML=t;var c="addon-backgrounds-grid".concat(o?"-docs-".concat(o):""),i=g.getElementById(c);i?null===(r=i.parentElement)||void 0===r||r.insertBefore(a,i):g.head.appendChild(a)}},k={cellSize:100,cellAmount:10,opacity:.8},E="addon-backgrounds",w="addon-backgrounds-grid",S=m()?"":"transition: background-color 0.3s;",j=function(n){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],e=arguments.length>2?arguments[2]:void 0;if("transparent"===n)return"transparent";if(o.find((function(t){return t.value===n}))||n)return n;var r,c,i=o.find((function(n){return n.name===e}));if(i)return i.value;if(e){var d=o.map((function(n){return n.name})).join(", ");p.warn(a(t||(r=['\n        Backgrounds Addon: could not find the default color "','".\n        These are the available colors for your story based on your configuration:\n        ',".\n      "],c||(c=r.slice(0)),t=Object.freeze(Object.defineProperties(r,{raw:{value:Object.freeze(c)}}))),e,d))}return"transparent"},_=null!==(r=globalThis.FEATURES)&&void 0!==r&&r.backgroundsStoryGlobals?[function(n,t){var o=t.globals,e=t.parameters,r=t.viewMode,a=t.id,c=e[b]||{},i=c.options,d=void 0===i?f:i,u=c.disable,s=c.grid,p=void 0===s?k:s,g=o[b]||{},v=g.value,m=v?d[v]:void 0,x=(null==m?void 0:m.value)||"transparent",j=g.grid||!1,_=!!m&&!u,M="docs"===r?"#anchor--".concat(a," .docs-story"):".sb-show-main",T="docs"===r?"#anchor--".concat(a," .docs-story"):".sb-show-main",A=void 0===e.layout||"padded"===e.layout,P="docs"===r?20:A?16:0,L=p.cellAmount,B=p.cellSize,z=p.opacity,R=p.offsetX,D=void 0===R?P:R,G=p.offsetY,I=void 0===G?P:G,F="docs"===r?"".concat(E,"-docs-").concat(a):"".concat(E,"-color"),H="docs"===r?a:null;l((function(){var n="\n    ".concat(M," {\n      background: ").concat(x," !important;\n      ").concat(S,"\n      }");_?h(F,n,H):y(F)}),[M,F,H,_,x]);var U="docs"===r?"".concat(w,"-docs-").concat(a):"".concat(w);return l((function(){if(j){var n=["".concat(B*L,"px ").concat(B*L,"px"),"".concat(B*L,"px ").concat(B*L,"px"),"".concat(B,"px ").concat(B,"px"),"".concat(B,"px ").concat(B,"px")].join(", "),t="\n        ".concat(T," {\n          background-size: ").concat(n," !important;\n          background-position: ").concat(D,"px ").concat(I,"px, ").concat(D,"px ").concat(I,"px, ").concat(D,"px ").concat(I,"px, ").concat(D,"px ").concat(I,"px !important;\n          background-blend-mode: difference !important;\n          background-image: linear-gradient(rgba(130, 130, 130, ").concat(z,") 1px, transparent 1px),\n           linear-gradient(90deg, rgba(130, 130, 130, ").concat(z,") 1px, transparent 1px),\n           linear-gradient(rgba(130, 130, 130, ").concat(z/2,") 1px, transparent 1px),\n           linear-gradient(90deg, rgba(130, 130, 130, ").concat(z/2,") 1px, transparent 1px) !important;\n        }\n      ");O(U,t)}else y(U)}),[L,B,T,U,j,D,I,z]),n()}]:[function(n,t){var o,e,r,a=t.globals,c=t.parameters,i=c[b].grid,d=!0===(null===(o=a[b])||void 0===o?void 0:o.grid)&&!0!==i.disable,s=i.cellAmount,p=i.cellSize,f=i.opacity,g="docs"===t.viewMode,v=void 0===c.layout||"padded"===c.layout?16:0,m=null!==(e=i.offsetX)&&void 0!==e?e:g?20:v,x=null!==(r=i.offsetY)&&void 0!==r?r:g?20:v,h=u((function(){var n="docs"===t.viewMode?"#anchor--".concat(t.id," .docs-story"):".sb-show-main",o=["".concat(p*s,"px ").concat(p*s,"px"),"".concat(p*s,"px ").concat(p*s,"px"),"".concat(p,"px ").concat(p,"px"),"".concat(p,"px ").concat(p,"px")].join(", ");return"\n      ".concat(n," {\n        background-size: ").concat(o," !important;\n        background-position: ").concat(m,"px ").concat(x,"px, ").concat(m,"px ").concat(x,"px, ").concat(m,"px ").concat(x,"px, ").concat(m,"px ").concat(x,"px !important;\n        background-blend-mode: difference !important;\n        background-image: linear-gradient(rgba(130, 130, 130, ").concat(f,") 1px, transparent 1px),\n         linear-gradient(90deg, rgba(130, 130, 130, ").concat(f,") 1px, transparent 1px),\n         linear-gradient(rgba(130, 130, 130, ").concat(f/2,") 1px, transparent 1px),\n         linear-gradient(90deg, rgba(130, 130, 130, ").concat(f/2,") 1px, transparent 1px) !important;\n      }\n    ")}),[p]);return l((function(){var n="docs"===t.viewMode?"addon-backgrounds-grid-docs-".concat(t.id):"addon-backgrounds-grid";d?O(n,h):y(n)}),[d,h,t]),n()},function(n,t){var o,e=t.globals,r=t.parameters,a=null===(o=e[b])||void 0===o?void 0:o.value,c=r[b],i=u((function(){return c.disable?"transparent":j(a,c.values,c.default)}),[c,a]),d=u((function(){return i&&"transparent"!==i}),[i]),s="docs"===t.viewMode?"#anchor--".concat(t.id," .docs-story"):".sb-show-main",p=u((function(){return"\n      ".concat(s," {\n        background: ").concat(i," !important;\n        ").concat(m()?"":"transition: background-color 0.3s;","\n      }\n    ")}),[i,s]);return l((function(){var n="docs"===t.viewMode?"addon-backgrounds-docs-".concat(t.id):"addon-backgrounds-color";d?h(n,p,"docs"===t.viewMode?t.id:null):y(n)}),[d,p,t]),n()}],M=e({},b,function(n){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){e(n,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(r,t))}))}return n}({grid:{cellSize:20,opacity:.5,cellAmount:5},disable:!1},!(null!==(c=globalThis.FEATURES)&&void 0!==c&&c.backgroundsStoryGlobals)&&{values:Object.values(f)})),T=e({},b,{value:void 0,grid:!1}),A=null!==(i=globalThis.FEATURES)&&void 0!==i&&i.backgroundsStoryGlobals?T:e({},b,null);n({decorators:_,parameters:M,initialGlobals:A})}}}))}();

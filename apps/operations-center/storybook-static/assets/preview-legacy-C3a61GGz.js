!function(){function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,n,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",u=o.toStringTag||"@@toStringTag";function c(r,o,i,u){var c=o&&o.prototype instanceof f?o:f,l=Object.create(c.prototype);return t(l,"_invoke",function(r,t,o){var i,u,c,f=0,l=o||[],s=!1,y={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(r,t){return i=r,u=0,c=e,y.n=t,a}};function p(r,t){for(u=r,c=t,n=0;!s&&f&&!o&&n<l.length;n++){var o,i=l[n],p=y.p,v=i[2];r>3?(o=v===t)&&(c=i[(u=i[4])?5:(u=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=r<2&&p<i[1])?(u=0,y.v=t,y.n=i[1]):p<v&&(o=r<3||i[0]>t||t>v)&&(i[4]=r,i[5]=t,y.n=v,u=0))}if(o||r>1)return a;throw s=!0,t}return function(o,l,v){if(f>1)throw TypeError("Generator is already running");for(s&&1===l&&p(l,v),u=l,c=v;(n=u<2?e:c)||!s;){i||(u?u<3?(u>1&&(y.n=-1),p(u,c)):y.n=c:y.v=c);try{if(f=2,i){if(u||(o="next"),n=i[o]){if(!(n=n.call(i,c)))throw TypeError("iterator result is not an object");if(!n.done)return n;c=n.value,u<2&&(u=0)}else 1===u&&(n=i.return)&&n.call(i),u<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),u=1);i=e}else if((n=(s=y.n<0)?c:r.call(t,y))!==a)break}catch(n){i=e,u=1,c=n}finally{f=1}}return{value:n,done:s}}}(r,i,u),!0),l}var a={};function f(){}function l(){}function s(){}n=Object.getPrototypeOf;var y=[][i]?n(n([][i]())):(t(n={},i,(function(){return this})),n),p=s.prototype=f.prototype=Object.create(y);function v(r){return Object.setPrototypeOf?Object.setPrototypeOf(r,s):(r.__proto__=s,t(r,u,"GeneratorFunction")),r.prototype=Object.create(p),r}return l.prototype=s,t(p,"constructor",s),t(s,"constructor",l),l.displayName="GeneratorFunction",t(s,u,"GeneratorFunction"),t(p),t(p,u,"Generator"),t(p,i,(function(){return this})),t(p,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:c,m:v}})()}function t(r,e,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(r){i=0}t=function(r,e,n,o){if(e)i?i(r,e,{value:n,enumerable:!o,configurable:!o,writable:!o}):r[e]=n;else{function u(e,n){t(r,e,(function(r){return this._invoke(e,n,r)}))}u("next",0),u("throw",1),u("return",2)}},t(r,e,n,o)}function e(r,t,e,n,o,i,u){try{var c=r[i](u),a=c.value}catch(r){return void e(r)}c.done?t(a):Promise.resolve(a).then(n,o)}function n(r,t){return function(r){if(Array.isArray(r))return r}(r)||function(r,t){var e=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=e){var n,o,i,u,c=[],a=!0,f=!1;try{if(i=(e=e.call(r)).next,0===t){if(Object(e)!==e)return;a=!1}else for(;!(a=(n=i.call(e)).done)&&(c.push(n.value),c.length!==t);a=!0);}catch(r){f=!0,o=r}finally{try{if(!a&&null!=e.return&&(u=e.return(),Object(u)!==u))return}finally{if(f)throw o}}return c}}(r,t)||function(r,t){if(r){if("string"==typeof r)return o(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?o(r,t):void 0}}(r,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}System.register(["./preload-helper-legacy-CZHHxkdF.js"],(function(t,o){"use strict";var i;return{setters:[function(r){i=r._}],execute:function(){var u,c=Object.defineProperty;!function(r,t){for(var e in t)c(r,e,{get:t[e],enumerable:!0})}({},{parameters:function(){return s}});var a,f,l=Object.entries(null!==(u=globalThis.TAGS_OPTIONS)&&void 0!==u?u:{}).reduce((function(r,t){var e=n(t,2),o=e[0];return e[1].excludeFromDocsStories&&(r[o]=!0),r}),{}),s=t("parameters",{docs:{renderer:(a=r().m((function t(){var e,n;return r().w((function(r){for(;;)switch(r.n){case 0:return r.n=1,i((function(){return o.import("./DocsRenderer-CFRXHY34-legacy-CAEo-rWd.js").then((function(r){return r.D}))}),void 0,o.meta.url);case 1:return e=r.v,n=e.DocsRenderer,r.a(2,new n)}}),t)})),f=function(){var r=this,t=arguments;return new Promise((function(n,o){var i=a.apply(r,t);function u(r){e(i,n,o,u,c,"next",r)}function c(r){e(i,n,o,u,c,"throw",r)}u(void 0)}))},function(){return f.apply(this,arguments)}),stories:{filter:function(r){var t;return 0===(r.tags||[]).filter((function(r){return l[r]})).length&&!(null!==(t=r.parameters.docs)&&void 0!==t&&t.disable)}}}})}}}))}();

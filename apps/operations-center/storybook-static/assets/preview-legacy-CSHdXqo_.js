!function(){function t(o){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(o)}function o(o,e,n){return(e=function(o){var e=function(o,e){if("object"!=t(o)||!o)return o;var n=o[Symbol.toPrimitive];if(void 0!==n){var i=n.call(o,e||"default");if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(o)}(o,"string");return"symbol"==t(e)?e:e+""}(e))in o?Object.defineProperty(o,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):o[e]=n,o}function e(t){return function(t){if(Array.isArray(t))return n(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,o){if(t){if("string"==typeof t)return n(t,o);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?n(t,o):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(t,o){(null==o||o>t.length)&&(o=t.length);for(var e=0,n=Array(o);e<o;e++)n[e]=t[e];return n}System.register(["./tiny-invariant-legacy-CsyeKX8A.js"],(function(t,n){"use strict";var i;return{setters:[function(t){i=t.i}],execute:function(){var n=__STORYBOOK_MODULE_PREVIEW_API__.useEffect,r=__STORYBOOK_MODULE_GLOBAL__.global;function l(){var t=r.document.documentElement,o=Math.max(t.scrollHeight,t.offsetHeight);return{width:Math.max(t.scrollWidth,t.offsetWidth),height:o}}function a(t,o,e){var n=e.width,i=e.height;t.style.width="".concat(n,"px"),t.style.height="".concat(i,"px");var l=r.window.devicePixelRatio;t.width=Math.floor(n*l),t.height=Math.floor(i*l),o.scale(l,l)}var f={};function c(){f.canvas||(f=function(){var t=r.document.createElement("canvas");t.id="storybook-addon-measure";var o=t.getContext("2d");i(null!=o);var e=l(),n=e.width,f=e.height;return a(t,o,{width:n,height:f}),t.style.position="absolute",t.style.left="0",t.style.top="0",t.style.zIndex="2147483647",t.style.pointerEvents="none",r.document.body.appendChild(t),{canvas:t,context:o,width:n,height:f}}())}function u(){var t,o;f.context&&f.context.clearRect(0,0,null!==(t=f.width)&&void 0!==t?t:0,null!==(o=f.height)&&void 0!==o?o:0)}var d={margin:"#f6b26b",border:"#ffe599",padding:"#93c47d",content:"#6fa8dc",text:"#232020"};function h(t,o){var e=o.x,n=o.y,i=o.w,r=o.h,l=o.r;e-=i/2,n-=r/2,i<2*l&&(l=i/2),r<2*l&&(l=r/2),t.beginPath(),t.moveTo(e+l,n),t.arcTo(e+i,n,e+i,n+r,l),t.arcTo(e+i,n+r,e,n+r,l),t.arcTo(e,n+r,e,n,l),t.arcTo(e,n,e+i,n,l),t.closePath()}function p(t,o,e,n){var i=e.x,r=e.y,l=e.w,a=e.h;return h(t,{x:i,y:r,w:l,h:a,r:3}),t.fillStyle="".concat(d[o],"dd"),t.fill(),t.strokeStyle=d[o],t.stroke(),t.fillStyle=d.text,t.fillText(n,i,r),h(t,{x:i,y:r,w:l,h:a,r:3}),t.fillStyle="".concat(d[o],"dd"),t.fill(),t.strokeStyle=d[o],t.stroke(),t.fillStyle=d.text,t.fillText(n,i,r),{x:i,y:r,w:l,h:a}}function s(t,o){t.font="600 12px monospace",t.textBaseline="middle",t.textAlign="center";var e=t.measureText(o),n=e.actualBoundingBoxAscent+e.actualBoundingBoxDescent;return{w:e.width+12,h:n+12}}function m(t,o,e,n){var i=e.type,r=e.position,l=void 0===r?"center":r,a=e.text,f=arguments.length>4&&void 0!==arguments[4]&&arguments[4],c=function(t,o){var e=o.padding,n=o.border,i=o.width,r=o.height,l=o.top,a=o.left,f=i-n.left-n.right-e.left-e.right,c=r-e.top-e.bottom-n.top-n.bottom,u=a+n.left+e.left,d=l+n.top+e.top;return"top"===t?u+=f/2:"right"===t?(u+=f,d+=c/2):"bottom"===t?(u+=f/2,d+=c):"left"===t?d+=c/2:"center"===t&&(u+=f/2,d+=c/2),{x:u,y:d}}(l,o),u=c.x,d=c.y,h=function(t,o,e,n,i){var r=e.margin,l=e.border,a=e.padding,f=function(t){return 0},c=0,u=0,d=i?1:.5,h=i?2*n:0;return"padding"===t?f=function(t){return a[t]*d+h}:"border"===t?f=function(t){return a[t]+l[t]*d+h}:"margin"===t&&(f=function(t){return a[t]+l[t]+r[t]*d+h}),"top"===o?u=-f("top"):"right"===o?c=f("right"):"bottom"===o?u=f("bottom"):"left"===o&&(c=-f("left")),{offsetX:c,offsetY:u}}(i,l,o,7,f);u+=h.offsetX,d+=h.offsetY;var m,g,b=s(t,a),y=b.w,v=b.h;if(n&&(m={x:u,y:d,w:y,h:v},g=n,Math.abs(m.x-g.x)<Math.abs(m.w+g.w)/2&&Math.abs(m.y-g.y)<Math.abs(m.h+g.h)/2)){var w=function(t,o,e){return"top"===t?o.y=e.y-e.h-6:"right"===t?o.x=e.x+e.w/2+6+o.w/2:"bottom"===t?o.y=e.y+e.h+6:"left"===t&&(o.x=e.x-e.w/2-6-o.w/2),{x:o.x,y:o.y}}(l,{x:u,y:d,w:y},n);u=w.x,d=w.y}return p(t,i,{x:u,y:d,w:y,h:v},a)}function g(t,o,e,n){var i=[];e.forEach((function(e,r){var l=n&&"center"===e.position?function(t,o,e){var n,i,r,l,a=e.type,f=e.text,c=o.floatingAlignment,u=o.extremities,d=u[c.x],h=u[c.y],m=s(t,f),g=m.w,b=m.h,y=(n=c,r=.5*(i={w:g,h:b}).w+6,l=.5*i.h+6,{offsetX:("left"===n.x?-1:1)*r,offsetY:("top"===n.y?-1:1)*l});return p(t,a,{x:d+=y.offsetX,y:h+=y.offsetY,w:g,h:b},f)}(t,o,e):m(t,o,e,i[r-1],n);i[r]=l}))}var b="#f6b26ba8",y="#ffe599a8",v="#93c47d8c",w="#6fa8dca8";function x(t){return parseInt(t.replace("px",""),10)}function S(t){return Number.isInteger(t)?t:t.toFixed(2)}function R(t){return t.filter((function(t){return 0!==t.text&&"0"!==t.text}))}function E(t){var o=r.window.scrollY,e=r.window.scrollY+r.window.innerHeight,n=r.window.scrollX,i=r.window.scrollX+r.window.innerWidth,l=Math.abs(o-t.top),a=Math.abs(e-t.bottom);return{x:Math.abs(n-t.left)>Math.abs(i-t.right)?"left":"right",y:l>a?"top":"bottom"}}function M(t){return function(o){if(t&&o){var n=function(t){var o=r.getComputedStyle(t),e=t.getBoundingClientRect(),n=e.top,i=e.left,l=e.right,a=e.bottom,f=e.width,c=e.height,u=o.marginTop,d=o.marginBottom,h=o.marginLeft,p=o.marginRight,s=o.paddingTop,m=o.paddingBottom,g=o.paddingLeft,b=o.paddingRight,y=o.borderBottomWidth,v=o.borderTopWidth,w=o.borderLeftWidth,S=o.borderRightWidth;n+=r.window.scrollY,i+=r.window.scrollX,a+=r.window.scrollY,l+=r.window.scrollX;var R={top:x(u),bottom:x(d),left:x(h),right:x(p)},M={top:x(s),bottom:x(m),left:x(g),right:x(b)},A={top:x(v),bottom:x(y),left:x(w),right:x(S)},T={top:n-R.top,bottom:a+R.bottom,left:i-R.left,right:l+R.right};return{margin:R,padding:M,border:A,top:n,left:i,bottom:a,right:l,width:f,height:c,extremities:T,floatingAlignment:E(T)}}(t),i=function(t,o){var e=o.margin,n=o.width,i=o.height,r=o.top,l=o.left,a=o.bottom,f=o.right,c=i+e.bottom+e.top;return t.fillStyle=b,t.fillRect(l,r-e.top,n,e.top),t.fillRect(f,r-e.top,e.right,c),t.fillRect(l,a,n,e.bottom),t.fillRect(l-e.left,r-e.top,e.left,c),R([{type:"margin",text:S(e.top),position:"top"},{type:"margin",text:S(e.right),position:"right"},{type:"margin",text:S(e.bottom),position:"bottom"},{type:"margin",text:S(e.left),position:"left"}])}(o,n),l=function(t,o){var e=o.padding,n=o.border,i=o.width,r=o.height,l=o.top,a=o.left,f=o.bottom,c=o.right,u=i-n.left-n.right,d=r-e.top-e.bottom-n.top-n.bottom;return t.fillStyle=v,t.fillRect(a+n.left,l+n.top,u,e.top),t.fillRect(c-e.right-n.right,l+e.top+n.top,e.right,d),t.fillRect(a+n.left,f-e.bottom-n.bottom,u,e.bottom),t.fillRect(a+n.left,l+e.top+n.top,e.left,d),R([{type:"padding",text:e.top,position:"top"},{type:"padding",text:e.right,position:"right"},{type:"padding",text:e.bottom,position:"bottom"},{type:"padding",text:e.left,position:"left"}])}(o,n),a=function(t,o){var e=o.border,n=o.width,i=o.height,r=o.top,l=o.left,a=o.bottom,f=o.right,c=i-e.top-e.bottom;return t.fillStyle=y,t.fillRect(l,r,n,e.top),t.fillRect(l,a-e.bottom,n,e.bottom),t.fillRect(l,r+e.top,e.left,c),t.fillRect(f-e.right,r+e.top,e.right,c),R([{type:"border",text:e.top,position:"top"},{type:"border",text:e.right,position:"right"},{type:"border",text:e.bottom,position:"bottom"},{type:"border",text:e.left,position:"left"}])}(o,n),f=function(t,o){var e=o.padding,n=o.border,i=o.width,r=o.height,l=o.top,a=o.left,f=i-n.left-n.right-e.left-e.right,c=r-e.top-e.bottom-n.top-n.bottom;return t.fillStyle=w,t.fillRect(a+n.left+e.left,l+n.top+e.top,f,c),[{type:"content",position:"center",text:"".concat(S(f)," x ").concat(S(c))}]}(o,n),c=n.width<=90||n.height<=30;!function(t,o,e,n){var i=e.reduce((function(t,o){var e;return Object.prototype.hasOwnProperty.call(t,o.position)||(t[o.position]=[]),null!==(e=t[o.position])&&void 0!==e&&e.push(o),t}),{});i.top&&g(t,o,i.top,n),i.right&&g(t,o,i.right,n),i.bottom&&g(t,o,i.bottom,n),i.left&&g(t,o,i.left,n),i.center&&g(t,o,i.center,n)}(o,n,[].concat(e(f),e(l),e(a),e(i)),c)}}}function A(t){var o;o=M(t),u(),o(f.context)}var T,O={x:0,y:0};function P(t,o){T=function(t,o){var e=r.document.elementFromPoint(t,o),n=function(e){if(e&&e.shadowRoot){var i=e.shadowRoot.elementFromPoint(t,o);return e.isEqualNode(i)?e:i.shadowRoot?n(i):i}return e};return n(e)||e}(t,o),A(T)}var _=[function(t,o){var e=o.globals.measureEnabled;return n((function(){var t=function(t){window.requestAnimationFrame((function(){t.stopPropagation(),O.x=t.clientX,O.y=t.clientY}))};return document.addEventListener("pointermove",t),function(){document.removeEventListener("pointermove",t)}}),[]),n((function(){var t=function(){window.requestAnimationFrame((function(){!function(){i(f.canvas),i(f.context),a(f.canvas,f.context,{width:0,height:0});var t=l(),o=t.width,e=t.height;a(f.canvas,f.context,{width:o,height:e}),f.width=o,f.height=e}()}))};return"story"===o.viewMode&&e&&(document.addEventListener("pointerover",(function(t){window.requestAnimationFrame((function(){t.stopPropagation(),P(t.clientX,t.clientY)}))})),c(),window.addEventListener("resize",t),P(O.x,O.y)),function(){var o;window.removeEventListener("resize",t),f.canvas&&(u(),null!==(o=f.canvas.parentNode)&&void 0!==o&&o.removeChild(f.canvas),f={})}}),[e,o.viewMode]),t()}],B=o({},"measureEnabled",!1);t({decorators:_,initialGlobals:B})}}}))}();

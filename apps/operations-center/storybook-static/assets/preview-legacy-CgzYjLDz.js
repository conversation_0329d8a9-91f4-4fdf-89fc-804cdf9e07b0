System.register([],(function(n,t){"use strict";return{execute:function(){var n=__STORYBOOK_MODULE_CORE_EVENTS__.STORY_CHANGED,t=__STORYBOOK_MODULE_PREVIEW_API__.addons,e=__STORYBOOK_MODULE_GLOBAL__.global,o="storybook/highlight",r="storybookHighlight",a="".concat(o,"/add"),c="".concat(o,"/reset"),i=e.document,_=t.getChannel(),l=function(){var n,t=r,e=i.getElementById(t);e&&(null===(n=e.parentNode)||void 0===n||n.removeChild(e))};_.on(n,l),_.on(c,l),_.on(a,(function(n){var t=r;l();var e=Array.from(new Set(n.elements)),o=i.createElement("style");o.setAttribute("id",t),o.innerHTML=e.map((function(t){return"".concat(t,"{\n          ").concat(function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"#FF4785";return"\n  outline: 2px ".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"dashed"," ").concat(n,";\n  outline-offset: 2px;\n  box-shadow: 0 0 0 6px rgba(255,255,255,0.6);\n")}(n.color,n.style),"\n         }")})).join(" "),i.head.appendChild(o)}))}}}));

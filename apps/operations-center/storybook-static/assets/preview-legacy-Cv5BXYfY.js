!function(){function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},n(t)}var t;function o(t,o,i){return(o=function(t){var o=function(t,o){if("object"!=n(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var e=i.call(t,o||"default");if("object"!=n(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(t)}(t,"string");return"symbol"==n(o)?o:o+""}(o))in t?Object.defineProperty(t,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[o]=i,t}System.register(["./index-legacy-Dvg014dY.js"],(function(n,i){"use strict";var e;return{setters:[function(n){e=n.d}],execute:function(){var i=__STORYBOOK_MODULE_PREVIEW_API__,l=i.useMemo,p=i.useEffect,r=__STORYBOOK_MODULE_GLOBAL__.global,a="outline",d=function(n){(Array.isArray(n)?n:[n]).forEach(u)},u=function(n){var t="string"==typeof n?n:n.join(""),o=r.document.getElementById(t);o&&o.parentElement&&o.parentElement.removeChild(o)};function s(n){return e(t||(o=["\n    "," body {\n      outline: 1px solid #2980b9 !important;\n    }\n\n    "," article {\n      outline: 1px solid #3498db !important;\n    }\n\n    "," nav {\n      outline: 1px solid #0088c3 !important;\n    }\n\n    "," aside {\n      outline: 1px solid #33a0ce !important;\n    }\n\n    "," section {\n      outline: 1px solid #66b8da !important;\n    }\n\n    "," header {\n      outline: 1px solid #99cfe7 !important;\n    }\n\n    "," footer {\n      outline: 1px solid #cce7f3 !important;\n    }\n\n    "," h1 {\n      outline: 1px solid #162544 !important;\n    }\n\n    "," h2 {\n      outline: 1px solid #314e6e !important;\n    }\n\n    "," h3 {\n      outline: 1px solid #3e5e85 !important;\n    }\n\n    "," h4 {\n      outline: 1px solid #449baf !important;\n    }\n\n    "," h5 {\n      outline: 1px solid #c7d1cb !important;\n    }\n\n    "," h6 {\n      outline: 1px solid #4371d0 !important;\n    }\n\n    "," main {\n      outline: 1px solid #2f4f90 !important;\n    }\n\n    "," address {\n      outline: 1px solid #1a2c51 !important;\n    }\n\n    "," div {\n      outline: 1px solid #036cdb !important;\n    }\n\n    "," p {\n      outline: 1px solid #ac050b !important;\n    }\n\n    "," hr {\n      outline: 1px solid #ff063f !important;\n    }\n\n    "," pre {\n      outline: 1px solid #850440 !important;\n    }\n\n    "," blockquote {\n      outline: 1px solid #f1b8e7 !important;\n    }\n\n    "," ol {\n      outline: 1px solid #ff050c !important;\n    }\n\n    "," ul {\n      outline: 1px solid #d90416 !important;\n    }\n\n    "," li {\n      outline: 1px solid #d90416 !important;\n    }\n\n    "," dl {\n      outline: 1px solid #fd3427 !important;\n    }\n\n    "," dt {\n      outline: 1px solid #ff0043 !important;\n    }\n\n    "," dd {\n      outline: 1px solid #e80174 !important;\n    }\n\n    "," figure {\n      outline: 1px solid #ff00bb !important;\n    }\n\n    "," figcaption {\n      outline: 1px solid #bf0032 !important;\n    }\n\n    "," table {\n      outline: 1px solid #00cc99 !important;\n    }\n\n    "," caption {\n      outline: 1px solid #37ffc4 !important;\n    }\n\n    "," thead {\n      outline: 1px solid #98daca !important;\n    }\n\n    "," tbody {\n      outline: 1px solid #64a7a0 !important;\n    }\n\n    "," tfoot {\n      outline: 1px solid #22746b !important;\n    }\n\n    "," tr {\n      outline: 1px solid #86c0b2 !important;\n    }\n\n    "," th {\n      outline: 1px solid #a1e7d6 !important;\n    }\n\n    "," td {\n      outline: 1px solid #3f5a54 !important;\n    }\n\n    "," col {\n      outline: 1px solid #6c9a8f !important;\n    }\n\n    "," colgroup {\n      outline: 1px solid #6c9a9d !important;\n    }\n\n    "," button {\n      outline: 1px solid #da8301 !important;\n    }\n\n    "," datalist {\n      outline: 1px solid #c06000 !important;\n    }\n\n    "," fieldset {\n      outline: 1px solid #d95100 !important;\n    }\n\n    "," form {\n      outline: 1px solid #d23600 !important;\n    }\n\n    "," input {\n      outline: 1px solid #fca600 !important;\n    }\n\n    "," keygen {\n      outline: 1px solid #b31e00 !important;\n    }\n\n    "," label {\n      outline: 1px solid #ee8900 !important;\n    }\n\n    "," legend {\n      outline: 1px solid #de6d00 !important;\n    }\n\n    "," meter {\n      outline: 1px solid #e8630c !important;\n    }\n\n    "," optgroup {\n      outline: 1px solid #b33600 !important;\n    }\n\n    "," option {\n      outline: 1px solid #ff8a00 !important;\n    }\n\n    "," output {\n      outline: 1px solid #ff9619 !important;\n    }\n\n    "," progress {\n      outline: 1px solid #e57c00 !important;\n    }\n\n    "," select {\n      outline: 1px solid #e26e0f !important;\n    }\n\n    "," textarea {\n      outline: 1px solid #cc5400 !important;\n    }\n\n    "," details {\n      outline: 1px solid #33848f !important;\n    }\n\n    "," summary {\n      outline: 1px solid #60a1a6 !important;\n    }\n\n    "," command {\n      outline: 1px solid #438da1 !important;\n    }\n\n    "," menu {\n      outline: 1px solid #449da6 !important;\n    }\n\n    "," del {\n      outline: 1px solid #bf0000 !important;\n    }\n\n    "," ins {\n      outline: 1px solid #400000 !important;\n    }\n\n    "," img {\n      outline: 1px solid #22746b !important;\n    }\n\n    "," iframe {\n      outline: 1px solid #64a7a0 !important;\n    }\n\n    "," embed {\n      outline: 1px solid #98daca !important;\n    }\n\n    "," object {\n      outline: 1px solid #00cc99 !important;\n    }\n\n    "," param {\n      outline: 1px solid #37ffc4 !important;\n    }\n\n    "," video {\n      outline: 1px solid #6ee866 !important;\n    }\n\n    "," audio {\n      outline: 1px solid #027353 !important;\n    }\n\n    "," source {\n      outline: 1px solid #012426 !important;\n    }\n\n    "," canvas {\n      outline: 1px solid #a2f570 !important;\n    }\n\n    "," track {\n      outline: 1px solid #59a600 !important;\n    }\n\n    "," map {\n      outline: 1px solid #7be500 !important;\n    }\n\n    "," area {\n      outline: 1px solid #305900 !important;\n    }\n\n    "," a {\n      outline: 1px solid #ff62ab !important;\n    }\n\n    "," em {\n      outline: 1px solid #800b41 !important;\n    }\n\n    "," strong {\n      outline: 1px solid #ff1583 !important;\n    }\n\n    "," i {\n      outline: 1px solid #803156 !important;\n    }\n\n    "," b {\n      outline: 1px solid #cc1169 !important;\n    }\n\n    "," u {\n      outline: 1px solid #ff0430 !important;\n    }\n\n    "," s {\n      outline: 1px solid #f805e3 !important;\n    }\n\n    "," small {\n      outline: 1px solid #d107b2 !important;\n    }\n\n    "," abbr {\n      outline: 1px solid #4a0263 !important;\n    }\n\n    "," q {\n      outline: 1px solid #240018 !important;\n    }\n\n    "," cite {\n      outline: 1px solid #64003c !important;\n    }\n\n    "," dfn {\n      outline: 1px solid #b4005a !important;\n    }\n\n    "," sub {\n      outline: 1px solid #dba0c8 !important;\n    }\n\n    "," sup {\n      outline: 1px solid #cc0256 !important;\n    }\n\n    "," time {\n      outline: 1px solid #d6606d !important;\n    }\n\n    "," code {\n      outline: 1px solid #e04251 !important;\n    }\n\n    "," kbd {\n      outline: 1px solid #5e001f !important;\n    }\n\n    "," samp {\n      outline: 1px solid #9c0033 !important;\n    }\n\n    "," var {\n      outline: 1px solid #d90047 !important;\n    }\n\n    "," mark {\n      outline: 1px solid #ff0053 !important;\n    }\n\n    "," bdi {\n      outline: 1px solid #bf3668 !important;\n    }\n\n    "," bdo {\n      outline: 1px solid #6f1400 !important;\n    }\n\n    "," ruby {\n      outline: 1px solid #ff7b93 !important;\n    }\n\n    "," rt {\n      outline: 1px solid #ff2f54 !important;\n    }\n\n    "," rp {\n      outline: 1px solid #803e49 !important;\n    }\n\n    "," span {\n      outline: 1px solid #cc2643 !important;\n    }\n\n    "," br {\n      outline: 1px solid #db687d !important;\n    }\n\n    "," wbr {\n      outline: 1px solid #db175b !important;\n    }"],i||(i=o.slice(0)),t=Object.freeze(Object.defineProperties(o,{raw:{value:Object.freeze(i)}}))),n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n);var o,i}var m=[function(n,t){var o=t.globals,i=[!0,"true"].includes(o[a]),e="docs"===t.viewMode,u=l((function(){return s(e?'[data-story-block="true"]':".sb-show-main")}),[t]);return p((function(){var n=e?"addon-outline-docs-".concat(t.id):"addon-outline";return i?function(n,t){var o=r.document.getElementById(n);if(o)o.innerHTML!==t&&(o.innerHTML=t);else{var i=r.document.createElement("style");i.setAttribute("id",n),i.innerHTML=t,r.document.head.appendChild(i)}}(n,u):d(n),function(){d(n)}}),[i,u,t]),n()}],x=o({},a,!1);n({decorators:m,initialGlobals:x})}}}))}();

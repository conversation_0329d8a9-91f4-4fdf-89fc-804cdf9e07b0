function A(){import.meta.url,import("_").catch(()=>1),async function*(){}().next()}const _={},E=new Set(["Module","__esModule","default","_export_sfc"]);let g={"./App":()=>(w(["__federation_expose_App-C9Wrai2r.css","globals-DkV8MmcH.css"],!1,"./App"),m("./assets/__federation_expose_App-DYUBmReV.js").then(e=>Object.keys(e).every(n=>E.has(n))?()=>e.default:()=>e))};const b={},w=(e,n,c)=>{const r=import.meta.url;if(typeof r>"u"){console.warn('The remote style takes effect only when the build.target option in the vite.config.ts file is higher than that of "es2020".');return}const i=r.substring(0,r.lastIndexOf("remoteEntry-legacy.js")),a='./';'assets',e.forEach(l=>{let s="";const f=a||i;if(f){const o={trailing:t=>t.endsWith("/")?t.slice(0,-1):t,leading:t=>t.startsWith("/")?t.slice(1):t},y=t=>t.startsWith("http")||t.startsWith("//"),h=o.trailing(f),p=o.leading(l),u=o.trailing(i);y(f)?s=[h,p].filter(Boolean).join("/"):u.includes(h)?s=[u,p].filter(Boolean).join("/"):s=[u+h,p].filter(Boolean).join("/")}else s=l;if(n){const o="css__operations__"+c;window[o]=window[o]||[],window[o].push(s);return}if(s in b)return;b[s]=!0;const d=document.createElement("link");d.rel="stylesheet",d.href=s,document.head.appendChild(d)})};async function m(e){var n;return(n=_[e])!=null||(_[e]=import(e)),_[e]}const T=e=>{if(!g[e])throw new Error("Can not find remote module "+e);return g[e]()},x=e=>{globalThis.__federation_shared__=globalThis.__federation_shared__||{},Object.entries(e).forEach(([n,c])=>{for(const[r,i]of Object.entries(c)){const a=i.scope||"default";globalThis.__federation_shared__[a]=globalThis.__federation_shared__[a]||{};const l=globalThis.__federation_shared__[a];(l[n]=l[n]||{})[r]=i}})};export{A as __vite_legacy_guard,w as dynamicLoadingCss,T as get,x as init};

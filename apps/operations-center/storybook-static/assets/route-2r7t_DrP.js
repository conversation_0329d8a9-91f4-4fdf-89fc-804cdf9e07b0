import{j as e}from"./index-C_VjMC7g.js";import{O as t}from"./__federation_expose_App-DYUBmReV.js";const i=function(){return e.jsxs("div",{className:"mx-auto flex h-full max-w-xl flex-col text-sm",children:[e.jsx("header",{className:"border-border-soft bg-background-highlight sticky top-0 z-10 flex h-11 shrink-0 items-center border-b px-2.5",children:e.jsx("h1",{children:"Settings"})}),e.jsx("div",{className:"bg-background-high grow",children:e.jsx(t,{})})]})};export{i as component};

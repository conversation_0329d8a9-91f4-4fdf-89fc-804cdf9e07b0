import{j as a}from"./index-C_VjMC7g.js";import{d as i}from"./color-Dui9lDAX.js";import{n as o,z as p}from"./x-B9YxCZbW.js";import"./index-D4lIrffr.js";import{n as c}from"./__federation_expose_App-DYUBmReV.js";function n({color:e,children:r,className:t,...d}){return a.jsx(p,{...d,className:o("text-background h-auto min-h-17 w-30 p-2","flex-col items-start justify-between text-left text-sm","[--chunky-bg-color:var(--chunky-bg-color-base)]","[&[aria-pressed][data-state=on]]:[--chunky-bg-color:var(--chunky-bg-color-active)]",t),style:{"--chunky-bg-color-base":e,"--chunky-bg-color-active":i(e,33),"--chunky-border-color":"rgba(0, 0, 0, 0.16)","--chunky-depth":"4px"},children:r})}const l=({className:e,children:r,...t})=>a.jsx("div",{className:o("text-xl leading-none empty:hidden",e),...t,children:r!=null&&r.toString().trim()?r:null}),u=({className:e,children:r,...t})=>a.jsx("div",{className:o("whitespace-no-wrap mt-auto w-full truncate",e),...t,children:r}),s=({className:e,...r})=>a.jsx(c,{className:o("block h-17 w-24 rounded-md",e),...r});try{n.displayName="SuperFolderButton",n.__docgenInfo={description:"",displayName:"SuperFolderButton",props:{color:{defaultValue:null,description:"",name:"color",required:!0,type:{name:"string"}},size:{defaultValue:null,description:"",name:"size",required:!1,type:{name:"enum",value:[{value:"undefined"},{value:'"sm"'},{value:'"md"'},{value:'"lg"'},{value:'"xl"'}]}},square:{defaultValue:null,description:"",name:"square",required:!1,type:{name:"boolean | undefined"}},variant:{defaultValue:null,description:"",name:"variant",required:!1,type:{name:"enum",value:[{value:"undefined"},{value:'"accent"'},{value:'"positive"'},{value:'"negative"'},{value:'"primary"'},{value:'"secondary"'},{value:'"warning"'}]}}}}}catch(e){}try{l.displayName="SuperFolderButtonEmoji",l.__docgenInfo={description:"",displayName:"SuperFolderButtonEmoji",props:{}}}catch(e){}try{u.displayName="SuperFolderButtonLabel",u.__docgenInfo={description:"",displayName:"SuperFolderButtonLabel",props:{}}}catch(e){}try{s.displayName="SuperFolderButtonSkeleton",s.__docgenInfo={description:"",displayName:"SuperFolderButtonSkeleton",props:{}}}catch(e){}export{s as S,n as a,l as b,u as c};

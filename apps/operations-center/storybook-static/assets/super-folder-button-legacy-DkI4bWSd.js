!function(){function e(r){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(r)}var r=["color","children","className"],t=["className","children"],n=["className","children"],o=["className"];function a(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,n)}return t}function l(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?a(Object(t),!0).forEach((function(r){i(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function i(r,t,n){return(t=function(r){var t=function(r,t){if("object"!=e(r)||!r)return r;var n=r[Symbol.toPrimitive];if(void 0!==n){var o=n.call(r,t||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==e(t)?t:t+""}(t))in r?Object.defineProperty(r,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[t]=n,r}function u(e,r){if(null==e)return{};var t,n,o=function(e,r){if(null==e)return{};var t={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==r.indexOf(n))continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)t=a[n],-1===r.indexOf(t)&&{}.propertyIsEnumerable.call(e,t)&&(o[t]=e[t])}return o}System.register(["./index-legacy-C2Kr5I5h.js","./color-legacy-D3k4iKFx.js","./x-legacy-yM9Jk2p5.js","./index-legacy-8Bh_oy_I.js","./__federation_expose_App-legacy-pwn7RX54.js"],(function(e,a){"use strict";var i,c,s,p,d;return{setters:[function(e){i=e.j},function(e){c=e.d},function(e){s=e.n,p=e.z},null,function(e){d=e.n}],execute:function(){function a(e){var t=e.color,n=e.children,o=e.className,a=u(e,r);return i.jsx(p,l(l({},a),{},{className:s("text-background h-auto min-h-17 w-30 p-2","flex-col items-start justify-between text-left text-sm","[--chunky-bg-color:var(--chunky-bg-color-base)]","[&[aria-pressed][data-state=on]]:[--chunky-bg-color:var(--chunky-bg-color-active)]",o),style:{"--chunky-bg-color-base":t,"--chunky-bg-color-active":c(t,33),"--chunky-border-color":"rgba(0, 0, 0, 0.16)","--chunky-depth":"4px"},children:n}))}e("a",a);var f=e("b",(function(e){var r=e.className,n=e.children,o=u(e,t);return i.jsx("div",l(l({className:s("text-xl leading-none empty:hidden",r)},o),{},{children:null!=n&&n.toString().trim()?n:null}))})),y=e("c",(function(e){var r=e.className,t=e.children,o=u(e,n);return i.jsx("div",l(l({className:s("whitespace-no-wrap mt-auto w-full truncate",r)},o),{},{children:t}))})),m=e("S",(function(e){var r=e.className,t=u(e,o);return i.jsx(d,l({className:s("block h-17 w-24 rounded-md",r)},t))}));try{a.displayName="SuperFolderButton",a.__docgenInfo={description:"",displayName:"SuperFolderButton",props:{color:{defaultValue:null,description:"",name:"color",required:!0,type:{name:"string"}},size:{defaultValue:null,description:"",name:"size",required:!1,type:{name:"enum",value:[{value:"undefined"},{value:'"sm"'},{value:'"md"'},{value:'"lg"'},{value:'"xl"'}]}},square:{defaultValue:null,description:"",name:"square",required:!1,type:{name:"boolean | undefined"}},variant:{defaultValue:null,description:"",name:"variant",required:!1,type:{name:"enum",value:[{value:"undefined"},{value:'"accent"'},{value:'"positive"'},{value:'"negative"'},{value:'"primary"'},{value:'"secondary"'},{value:'"warning"'}]}}}}}catch(b){}try{f.displayName="SuperFolderButtonEmoji",f.__docgenInfo={description:"",displayName:"SuperFolderButtonEmoji",props:{}}}catch(b){}try{y.displayName="SuperFolderButtonLabel",y.__docgenInfo={description:"",displayName:"SuperFolderButtonLabel",props:{}}}catch(b){}try{m.displayName="SuperFolderButtonSkeleton",m.__docgenInfo={description:"",displayName:"SuperFolderButtonSkeleton",props:{}}}catch(b){}}}}))}();

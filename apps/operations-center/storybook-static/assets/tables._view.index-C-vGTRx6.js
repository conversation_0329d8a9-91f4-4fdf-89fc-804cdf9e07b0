import{j as s}from"./index-C_VjMC7g.js";import{a3 as N,a4 as O,a5 as F,y as S,x as V,e as E,a6 as I,a7 as f,L as b}from"./__federation_expose_App-DYUBmReV.js";import{A as w,n as _,X as R,B as q,k as U,z as y}from"./x-B9YxCZbW.js";import{r as u}from"./index-D4lIrffr.js";import{A as k}from"./actions--Pb8QTI7.js";const T=({id:e,children:i,className:t,required:a,...n})=>{const l=u.useId(),c=e!=null?e:l,d=w();return u.useEffect(()=>d?d.registerElement("label",c):void 0,[d,c]),s.jsxs("label",{id:c,"aria-required":a,htmlFor:d==null?void 0:d.id,className:_("text-foreground inline-block text-sm",t),...n,children:[i,a&&s.jsx("span",{className:"text-accent",children:" *"})]})},j=(e,i)=>{switch(e.type){case"select":if(!i)return{code:"TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR",message:"Select at least one option",fieldId:e.id};break;case"multi-select":if(e.min&&i.length<e.min)return{code:"TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR",message:"Select at least ".concat(e.min," options"),fieldId:e.id};if(e.max&&i.length>e.max)return{code:"TOO_MANY_OPTIONS_SELECTED_IN_GROUP_ERROR",message:"Select up to ".concat(e.max," options"),fieldId:e.id};break}},M=(e,i)=>{for(const t of e){const a=t.id,n=j(t,i[a]);if(n)return n}},p=({value:e,onChange:i,field:t})=>s.jsx(N,{children:t.options.map(a=>s.jsxs("label",{className:"flex items-center gap-2 py-1.5",children:[s.jsx(O,{value:a.id,checked:e===a.id,disabled:a.readOnly,readOnly:a.readOnly,onChange:n=>{n&&i(a.id)}}),s.jsxs("span",{children:[s.jsx("div",{children:a.title}),a.description&&s.jsx("div",{className:"text-foreground-secondary",children:a.description})]})]},a.id))});try{p.displayName="SelectField",p.__docgenInfo={description:"",displayName:"SelectField",props:{field:{defaultValue:null,description:"",name:"field",required:!0,type:{name:"UserConfigSelectField"}},value:{defaultValue:null,description:"",name:"value",required:!0,type:{name:"string"}},onChange:{defaultValue:null,description:"",name:"onChange",required:!0,type:{name:"(value: string) => void"}}}}}catch(e){}const g=({value:e,onChange:i,field:t})=>s.jsx("ul",{className:"grid grid-cols-2 gap-1",children:t.options.map(a=>s.jsx("li",{children:s.jsxs("label",{className:"flex items-center gap-2 py-1.5",children:[s.jsx(F,{checked:e.includes(a.id),disabled:a.readOnly,readOnly:a.readOnly,onChange:n=>{i(n.target.checked?[...e,a.id]:e.filter(l=>l!==a.id))}}),s.jsx("span",{children:a.title})]})},a.id))});try{g.displayName="MultiSelectField",g.__docgenInfo={description:"",displayName:"MultiSelectField",props:{value:{defaultValue:null,description:"",name:"value",required:!0,type:{name:"string[]"}},onChange:{defaultValue:null,description:"",name:"onChange",required:!0,type:{name:"(value: string[]) => void"}},field:{defaultValue:null,description:"",name:"field",required:!0,type:{name:"UserConfigMultiSelectField"}}}}}catch(e){}function h({field:e,value:i,onChange:t,shouldValidate:a}){const n=u.useMemo(()=>j(e,i),[e,i]);return s.jsxs("div",{children:[s.jsxs("div",{className:"mb-3",children:[s.jsxs("div",{className:_("flex flex-wrap items-center gap-2",a&&"animate-shake"),children:[s.jsx("h3",{children:e.title}),a&&n&&s.jsxs(S,{size:"xs",variant:"negative",children:[s.jsx(R,{})," ",n.message]})]}),e.description&&s.jsx("p",{className:"text-foreground-secondary mt-1 text-xs",children:e.description})]}),e.type==="select"&&s.jsx(p,{field:e,value:i,onChange:l=>t(l)}),e.type==="multi-select"&&s.jsx(g,{field:e,value:i,onChange:l=>t(l)})]})}try{h.displayName="ConfigField",h.__docgenInfo={description:"",displayName:"ConfigField",props:{field:{defaultValue:null,description:"",name:"field",required:!0,type:{name:"UserConfigField"}},value:{defaultValue:null,description:"",name:"value",required:!0,type:{name:"InferUserConfigFieldValue<UserConfigField>"}},onChange:{defaultValue:null,description:"",name:"onChange",required:!0,type:{name:"(value: InferUserConfigFieldValue<UserConfigField>) => void"}},shouldValidate:{defaultValue:null,description:"",name:"shouldValidate",required:!1,type:{name:"boolean | undefined"}}}}}catch(e){}const z=function(){const i=V(r=>r.views),t=E(),{initialValue:a}=I.useLoaderData(),n=u.useRef(null),[l,c]=u.useState(a),[d,C]=u.useState(new Map),v=()=>{var o;const r=M(f.views.tables,l);if(r){const m=(o=n.current)==null?void 0:o.querySelector("#".concat(r.fieldId));m==null||m.scrollIntoView({behavior:"smooth"}),C(x=>(x.set(r.fieldId,Date.now()),new Map(x)))}else i.update("tables",l.id,l),t({to:"/dine-in/settings"})};return s.jsxs("div",{ref:n,className:"flex h-full flex-col *:p-4",children:[s.jsxs("div",{children:[s.jsxs(q,{className:"space-y-2",children:[s.jsx(T,{children:"View name"}),s.jsx(U,{placeholder:"View name",value:l.title,onChange:r=>{c(o=>({...o,title:r.target.value}))}})]}),s.jsx("ul",{className:"my-6 space-y-6",children:f.views.tables.map(r=>s.jsx("li",{className:"space-y-2",id:r.id,children:s.jsx(h,{shouldValidate:d.has(r.id),field:r,value:l[r.id],onChange:o=>{c(m=>({...m,[r.id]:o}))}},d.get(r.id))},r.id))})]}),s.jsxs(k,{children:[s.jsx(y,{asChild:!0,children:s.jsx(b,{to:"/dine-in/settings",children:"Cancel"})}),s.jsx(y,{variant:"accent",className:"grow",onClick:v,children:"Save view"})]})]})};export{z as component};

!function(){function e(n){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(n)}var n=["id","children","className","required"];function t(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,i,l,a,o=[],u=!0,c=!1;try{if(l=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;u=!1}else for(;!(u=(r=l.call(t)).done)&&(o.push(r.value),o.length!==n);u=!0);}catch(e){c=!0,i=e}finally{try{if(!u&&null!=t.return&&(a=t.return(),Object(a)!==a))return}finally{if(c)throw i}}return o}}(e,n)||i(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e){return function(e){if(Array.isArray(e))return l(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||i(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,n){if(e){if("string"==typeof e)return l(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?l(e,n):void 0}}function l(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function a(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function o(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?a(Object(t),!0).forEach((function(n){u(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}function u(n,t,r){return(t=function(n){var t=function(n,t){if("object"!=e(n)||!n)return n;var r=n[Symbol.toPrimitive];if(void 0!==r){var i=r.call(n,t||"default");if("object"!=e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(n)}(n,"string");return"symbol"==e(t)?t:t+""}(t))in n?Object.defineProperty(n,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[t]=r,n}System.register(["./index-legacy-C2Kr5I5h.js","./__federation_expose_App-legacy-pwn7RX54.js","./x-legacy-yM9Jk2p5.js","./index-legacy-8Bh_oy_I.js","./actions-legacy-CRkJzGcr.js"],(function(e,l){"use strict";var a,c,s,d,f,p,m,y,h,v,g,b,j,x,O,S,w,C;return{setters:[function(e){a=e.j},function(e){c=e.a3,s=e.a4,d=e.a5,f=e.y,p=e.x,m=e.e,y=e.a6,h=e.a7,v=e.L},function(e){g=e.A,b=e.n,j=e.X,x=e.B,O=e.k,S=e.z},function(e){w=e.r},function(e){C=e.A}],execute:function(){var l=function(e){var t=e.id,r=e.children,i=e.className,l=e.required,u=function(e,n){if(null==e)return{};var t,r,i=function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==n.indexOf(r))continue;t[r]=e[r]}return t}(e,n);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)t=l[r],-1===n.indexOf(t)&&{}.propertyIsEnumerable.call(e,t)&&(i[t]=e[t])}return i}(e,n),c=w.useId(),s=null!=t?t:c,d=g();return w.useEffect((function(){return d?d.registerElement("label",s):void 0}),[d,s]),a.jsxs("label",o(o({id:s,"aria-required":l,htmlFor:null==d?void 0:d.id,className:b("text-foreground inline-block text-sm",i)},u),{},{children:[r,l&&a.jsx("span",{className:"text-accent",children:" *"})]}))},N=function(e,n){switch(e.type){case"select":if(!n)return{code:"TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR",message:"Select at least one option",fieldId:e.id};break;case"multi-select":if(e.min&&n.length<e.min)return{code:"TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR",message:"Select at least ".concat(e.min," options"),fieldId:e.id};if(e.max&&n.length>e.max)return{code:"TOO_MANY_OPTIONS_SELECTED_IN_GROUP_ERROR",message:"Select up to ".concat(e.max," options"),fieldId:e.id}}},_=function(e,n){var t,r=function(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=i(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var r=0,l=function(){};return{s:l,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:l}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==t.return||t.return()}finally{if(u)throw a}}}}(e);try{for(r.s();!(t=r.n()).done;){var l=t.value,a=l.id,o=N(l,n[a]);if(o)return o}}catch(u){r.e(u)}finally{r.f()}},I=function(e){var n=e.value,t=e.onChange,r=e.field;return a.jsx(c,{children:r.options.map((function(e){return a.jsxs("label",{className:"flex items-center gap-2 py-1.5",children:[a.jsx(s,{value:e.id,checked:n===e.id,disabled:e.readOnly,readOnly:e.readOnly,onChange:function(n){n&&t(e.id)}}),a.jsxs("span",{children:[a.jsx("div",{children:e.title}),e.description&&a.jsx("div",{className:"text-foreground-secondary",children:e.description})]})]},e.id)}))})};try{I.displayName="SelectField",I.__docgenInfo={description:"",displayName:"SelectField",props:{field:{defaultValue:null,description:"",name:"field",required:!0,type:{name:"UserConfigSelectField"}},value:{defaultValue:null,description:"",name:"value",required:!0,type:{name:"string"}},onChange:{defaultValue:null,description:"",name:"onChange",required:!0,type:{name:"(value: string) => void"}}}}}catch(V){}var E=function(e){var n=e.value,t=e.onChange,i=e.field;return a.jsx("ul",{className:"grid grid-cols-2 gap-1",children:i.options.map((function(e){return a.jsx("li",{children:a.jsxs("label",{className:"flex items-center gap-2 py-1.5",children:[a.jsx(d,{checked:n.includes(e.id),disabled:e.readOnly,readOnly:e.readOnly,onChange:function(i){t(i.target.checked?[].concat(r(n),[e.id]):n.filter((function(n){return n!==e.id})))}}),a.jsx("span",{children:e.title})]})},e.id)}))})};try{E.displayName="MultiSelectField",E.__docgenInfo={description:"",displayName:"MultiSelectField",props:{value:{defaultValue:null,description:"",name:"value",required:!0,type:{name:"string[]"}},onChange:{defaultValue:null,description:"",name:"onChange",required:!0,type:{name:"(value: string[]) => void"}},field:{defaultValue:null,description:"",name:"field",required:!0,type:{name:"UserConfigMultiSelectField"}}}}}catch(V){}function P(e){var n=e.field,t=e.value,r=e.onChange,i=e.shouldValidate,l=w.useMemo((function(){return N(n,t)}),[n,t]);return a.jsxs("div",{children:[a.jsxs("div",{className:"mb-3",children:[a.jsxs("div",{className:b("flex flex-wrap items-center gap-2",i&&"animate-shake"),children:[a.jsx("h3",{children:n.title}),i&&l&&a.jsxs(f,{size:"xs",variant:"negative",children:[a.jsx(j,{})," ",l.message]})]}),n.description&&a.jsx("p",{className:"text-foreground-secondary mt-1 text-xs",children:n.description})]}),"select"===n.type&&a.jsx(I,{field:n,value:t,onChange:function(e){return r(e)}}),"multi-select"===n.type&&a.jsx(E,{field:n,value:t,onChange:function(e){return r(e)}})]})}try{P.displayName="ConfigField",P.__docgenInfo={description:"",displayName:"ConfigField",props:{field:{defaultValue:null,description:"",name:"field",required:!0,type:{name:"UserConfigField"}},value:{defaultValue:null,description:"",name:"value",required:!0,type:{name:"InferUserConfigFieldValue<UserConfigField>"}},onChange:{defaultValue:null,description:"",name:"onChange",required:!0,type:{name:"(value: InferUserConfigFieldValue<UserConfigField>) => void"}},shouldValidate:{defaultValue:null,description:"",name:"shouldValidate",required:!1,type:{name:"boolean | undefined"}}}}}catch(V){}e("component",(function(){var e=p((function(e){return e.views})),n=m(),r=y.useLoaderData().initialValue,i=w.useRef(null),c=t(w.useState(r),2),s=c[0],d=c[1],f=t(w.useState(new Map),2),g=f[0],b=f[1];return a.jsxs("div",{ref:i,className:"flex h-full flex-col *:p-4",children:[a.jsxs("div",{children:[a.jsxs(x,{className:"space-y-2",children:[a.jsx(l,{children:"View name"}),a.jsx(O,{placeholder:"View name",value:s.title,onChange:function(e){d((function(n){return o(o({},n),{},{title:e.target.value})}))}})]}),a.jsx("ul",{className:"my-6 space-y-6",children:h.views.tables.map((function(e){return a.jsx("li",{className:"space-y-2",id:e.id,children:a.jsx(P,{shouldValidate:g.has(e.id),field:e,value:s[e.id],onChange:function(n){d((function(t){return o(o({},t),{},u({},e.id,n))}))}},g.get(e.id))},e.id)}))})]}),a.jsxs(C,{children:[a.jsx(S,{asChild:!0,children:a.jsx(v,{to:"/dine-in/settings",children:"Cancel"})}),a.jsx(S,{variant:"accent",className:"grow",onClick:function(){var t=_(h.views.tables,s);if(t){var r,l=null===(r=i.current)||void 0===r?void 0:r.querySelector("#".concat(t.fieldId));null==l||l.scrollIntoView({behavior:"smooth"}),b((function(e){return e.set(t.fieldId,Date.now()),new Map(e)}))}else e.update("tables",s.id,s),n({to:"/dine-in/settings"})},children:"Save view"})]})]})}))}}}))}();

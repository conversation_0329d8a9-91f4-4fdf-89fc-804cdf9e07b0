System.register(["./index-legacy-8Bh_oy_I.js"],(function(t,e){"use strict";var r,n;return{setters:[function(t){r=t.b,n=t.g}],execute:function(){function e(t,e){for(var r=function(){var r=e[n];if("string"!=typeof r&&!Array.isArray(r)){var o=function(e){if("default"!==e&&!(e in t)){var n=Object.getOwnPropertyDescriptor(r,e);n&&Object.defineProperty(t,e,n.get?n:{enumerable:!0,get:function(){return r[e]}})}};for(var a in r)o(a)}},n=0;n<e.length;n++)r();return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}var o,a,i={exports:{}},c={},f=(a||(a=1,i.exports=function(){if(o)return c;o=1;var t=r(),e=!1;return c.act=function(r){return!1===e&&(e=!0,console.error("`ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.")),t.act(r)},c}()),i.exports);t("t",e({__proto__:null,default:n(f)},[f]))}}}));

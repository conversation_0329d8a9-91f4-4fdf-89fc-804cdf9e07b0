import{j as e}from"./index-C_VjMC7g.js";import{z as s}from"./x-B9YxCZbW.js";import"./index-D4lIrffr.js";const o=function(){return e.jsx("div",{className:"bg-background-low fixed inset-0 z-999",children:e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:e.jsxs("div",{className:"text-center text-base",children:[e.jsx("h1",{children:"User testing session completed."}),e.jsx("p",{children:"Thank you for your time."}),e.jsx(s,{asChild:!0,className:"mt-4",children:e.jsx("a",{href:"/dine-in",children:"Restart"})})]})})})};export{o as component};

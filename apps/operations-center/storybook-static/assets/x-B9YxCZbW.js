import{j as C,r as Pa}from"./index-C_VjMC7g.js";import{r as p,a as Hc}from"./index-D4lIrffr.js";function Ra(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Ra(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function kr(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Ra(e))&&(r&&(r+=" "),r+=t);return r}const ks=e=>typeof e=="boolean"?"".concat(e):e===0?"0":e,Kc=e=>{const t=function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];var s,i;return typeof(e==null||(s=e.hooks)===null||s===void 0?void 0:s["cx:done"])<"u"?e==null?void 0:e.hooks["cx:done"](kr(r)):typeof(e==null||(i=e.hooks)===null||i===void 0?void 0:i.onComplete)<"u"?e==null?void 0:e.hooks.onComplete(kr(r)):kr(r)};return{compose:function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return s=>{const i=Object.fromEntries(Object.entries(s||{}).filter(a=>{let[l]=a;return!["class","className"].includes(l)}));return t(r.map(a=>a(i)),s==null?void 0:s.class,s==null?void 0:s.className)}},cva:n=>r=>{var o;if((n==null?void 0:n.variants)==null)return t(n==null?void 0:n.base,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:s,defaultVariants:i}=n,a=Object.keys(s).map(c=>{const d=r==null?void 0:r[c],f=i==null?void 0:i[c],h=ks(d)||ks(f);return s[c][h]}),l={...i,...r&&Object.entries(r).reduce((c,d)=>{let[f,h]=d;return typeof h>"u"?c:{...c,[f]:h}},{})},u=n==null||(o=n.compoundVariants)===null||o===void 0?void 0:o.reduce((c,d)=>{let{class:f,className:h,...m}=d;return Object.entries(m).every(g=>{let[y,v]=g;const b=l[y];return Array.isArray(v)?v.includes(b):b===v})?[...c,f,h]:c},[]);return t(n==null?void 0:n.base,a,u,r==null?void 0:r.class,r==null?void 0:r.className)},cx:t}},So="-",Gc=e=>{const t=_c(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:o=>{const s=o.split(So);return s[0]===""&&s.length!==1&&s.shift(),Aa(s,t)||Xc(o)},getConflictingClassGroupIds:(o,s)=>{const i=n[o]||[];return s&&r[o]?[...i,...r[o]]:i}}},Aa=(e,t)=>{var n;if(e.length===0)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),s=o?Aa(e.slice(1),o):void 0;if(s)return s;if(t.validators.length===0)return;const i=e.join(So);return(n=t.validators.find(({validator:a})=>a(i)))==null?void 0:n.classGroupId},Ss=/^\[(.+)\]$/,Xc=e=>{if(Ss.test(e)){const t=Ss.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},_c=e=>{const{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(const o in n)Xr(n[o],r,o,t);return r},Xr=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:Es(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(Yc(o)){Xr(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{Xr(i,Es(t,s),n,r)})})},Es=(e,t)=>{let n=e;return t.split(So).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Yc=e=>e.isThemeGetter,Zc=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},_r="!",Yr=":",Jc=Yr.length,Qc=e=>{const{prefix:t,experimentalParseClassName:n}=e;let r=o=>{const s=[];let i=0,a=0,l=0,u;for(let m=0;m<o.length;m++){let g=o[m];if(i===0&&a===0){if(g===Yr){s.push(o.slice(l,m)),l=m+Jc;continue}if(g==="/"){u=m;continue}}g==="["?i++:g==="]"?i--:g==="("?a++:g===")"&&a--}const c=s.length===0?o:o.substring(l),d=ed(c),f=d!==c,h=u&&u>l?u-l:void 0;return{modifiers:s,hasImportantModifier:f,baseClassName:d,maybePostfixModifierPosition:h}};if(t){const o=t+Yr,s=r;r=i=>i.startsWith(o)?s(i.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(n){const o=r;r=s=>n({className:s,parseClassName:o})}return r},ed=e=>e.endsWith(_r)?e.substring(0,e.length-1):e.startsWith(_r)?e.substring(1):e,td=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const r=[];let o=[];return n.forEach(s=>{s[0]==="["||t[s]?(r.push(...o.sort(),s),o=[]):o.push(s)}),r.push(...o.sort()),r}},nd=e=>({cache:Zc(e.cacheSize),parseClassName:Qc(e),sortModifiers:td(e),...Gc(e)}),rd=/\s+/,od=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o,sortModifiers:s}=t,i=[],a=e.trim().split(rd);let l="";for(let u=a.length-1;u>=0;u-=1){const c=a[u],{isExternal:d,modifiers:f,hasImportantModifier:h,baseClassName:m,maybePostfixModifierPosition:g}=n(c);if(d){l=c+(l.length>0?" "+l:l);continue}let y=!!g,v=r(y?m.substring(0,g):m);if(!v){if(!y){l=c+(l.length>0?" "+l:l);continue}if(v=r(m),!v){l=c+(l.length>0?" "+l:l);continue}y=!1}const b=s(f).join(":"),x=h?b+_r:b,k=x+v;if(i.includes(k))continue;i.push(k);const w=o(v,y);for(let $=0;$<w.length;++$){const R=w[$];i.push(x+R)}l=c+(l.length>0?" "+l:l)}return l};function sd(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Ma(t))&&(r&&(r+=" "),r+=n);return r}const Ma=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Ma(e[r]))&&(n&&(n+=" "),n+=t);return n};function id(e,...t){let n,r,o,s=i;function i(l){const u=t.reduce((c,d)=>d(c),e());return n=nd(u),r=n.cache.get,o=n.cache.set,s=a,a(l)}function a(l){const u=r(l);if(u)return u;const c=od(l,n);return o(l,c),c}return function(){return s(sd.apply(null,arguments))}}const me=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Da=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ia=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ad=/^\d+\/\d+$/,ld=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ud=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,cd=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,dd=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fd=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Et=e=>ad.test(e),X=e=>!!e&&!Number.isNaN(Number(e)),ct=e=>!!e&&Number.isInteger(Number(e)),Ts=e=>e.endsWith("%")&&X(e.slice(0,-1)),Qe=e=>ld.test(e),hd=()=>!0,pd=e=>ud.test(e)&&!cd.test(e),Eo=()=>!1,md=e=>dd.test(e),gd=e=>fd.test(e),vd=e=>!N(e)&&!F(e),yd=e=>Ft(e,La,Eo),N=e=>Da.test(e),dt=e=>Ft(e,Oa,pd),Sr=e=>Ft(e,Rd,X),bd=e=>Ft(e,ja,Eo),xd=e=>Ft(e,Va,gd),$d=e=>Ft(e,Eo,md),F=e=>Ia.test(e),Sn=e=>Bt(e,Oa),wd=e=>Bt(e,Ad),kd=e=>Bt(e,ja),Sd=e=>Bt(e,La),Ed=e=>Bt(e,Va),Td=e=>Bt(e,Md,!0),Ft=(e,t,n)=>{const r=Da.exec(e);return r?r[1]?t(r[1]):n(r[2]):!1},Bt=(e,t,n=!1)=>{const r=Ia.exec(e);return r?r[1]?t(r[1]):n:!1},ja=e=>e==="position",Cd=new Set(["image","url"]),Va=e=>Cd.has(e),Pd=new Set(["length","size","percentage"]),La=e=>Pd.has(e),Oa=e=>e==="length",Rd=e=>e==="number",Ad=e=>e==="family-name",Md=e=>e==="shadow",Dd=()=>{const e=me("color"),t=me("font"),n=me("text"),r=me("font-weight"),o=me("tracking"),s=me("leading"),i=me("breakpoint"),a=me("container"),l=me("spacing"),u=me("radius"),c=me("shadow"),d=me("inset-shadow"),f=me("drop-shadow"),h=me("blur"),m=me("perspective"),g=me("aspect"),y=me("ease"),v=me("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],k=()=>["auto","hidden","clip","visible","scroll"],w=()=>["auto","contain","none"],$=()=>[F,N,l],R=()=>[Et,"full","auto",...$()],A=()=>[ct,"none","subgrid",F,N],D=()=>["auto",{span:["full",ct,F,N]},F,N],T=()=>[ct,"auto",F,N],V=()=>["auto","min","max","fr",F,N],j=()=>["start","end","center","between","around","evenly","stretch","baseline"],E=()=>["start","end","center","stretch"],H=()=>["auto",...$()],M=()=>[Et,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...$()],S=()=>[e,F,N],B=()=>[Ts,dt],P=()=>["","none","full",u,F,N],I=()=>["",X,Sn,dt],L=()=>["solid","dashed","dotted","double"],Y=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Z=()=>["","none",h,F,N],xe=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",F,N],he=()=>["none",X,F,N],ve=()=>["none",X,F,N],Q=()=>[X,F,N],te=()=>[Et,"full",...$()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Qe],breakpoint:[Qe],color:[hd],container:[Qe],"drop-shadow":[Qe],ease:["in","out","in-out"],font:[vd],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Qe],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Qe],shadow:[Qe],spacing:["px",X],text:[Qe],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Et,N,F,g]}],container:["container"],columns:[{columns:[X,N,F,a]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...x(),N,F]}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:w()}],"overscroll-x":[{"overscroll-x":w()}],"overscroll-y":[{"overscroll-y":w()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:R()}],"inset-x":[{"inset-x":R()}],"inset-y":[{"inset-y":R()}],start:[{start:R()}],end:[{end:R()}],top:[{top:R()}],right:[{right:R()}],bottom:[{bottom:R()}],left:[{left:R()}],visibility:["visible","invisible","collapse"],z:[{z:[ct,"auto",F,N]}],basis:[{basis:[Et,"full","auto",a,...$()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[X,Et,"auto","initial","none",N]}],grow:[{grow:["",X,F,N]}],shrink:[{shrink:["",X,F,N]}],order:[{order:[ct,"first","last","none",F,N]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:D()}],"col-start":[{"col-start":T()}],"col-end":[{"col-end":T()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:D()}],"row-start":[{"row-start":T()}],"row-end":[{"row-end":T()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":V()}],"auto-rows":[{"auto-rows":V()}],gap:[{gap:$()}],"gap-x":[{"gap-x":$()}],"gap-y":[{"gap-y":$()}],"justify-content":[{justify:[...j(),"normal"]}],"justify-items":[{"justify-items":[...E(),"normal"]}],"justify-self":[{"justify-self":["auto",...E()]}],"align-content":[{content:["normal",...j()]}],"align-items":[{items:[...E(),"baseline"]}],"align-self":[{self:["auto",...E(),"baseline"]}],"place-content":[{"place-content":j()}],"place-items":[{"place-items":[...E(),"baseline"]}],"place-self":[{"place-self":["auto",...E()]}],p:[{p:$()}],px:[{px:$()}],py:[{py:$()}],ps:[{ps:$()}],pe:[{pe:$()}],pt:[{pt:$()}],pr:[{pr:$()}],pb:[{pb:$()}],pl:[{pl:$()}],m:[{m:H()}],mx:[{mx:H()}],my:[{my:H()}],ms:[{ms:H()}],me:[{me:H()}],mt:[{mt:H()}],mr:[{mr:H()}],mb:[{mb:H()}],ml:[{ml:H()}],"space-x":[{"space-x":$()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":$()}],"space-y-reverse":["space-y-reverse"],size:[{size:M()}],w:[{w:[a,"screen",...M()]}],"min-w":[{"min-w":[a,"screen","none",...M()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[i]},...M()]}],h:[{h:["screen",...M()]}],"min-h":[{"min-h":["screen","none",...M()]}],"max-h":[{"max-h":["screen",...M()]}],"font-size":[{text:["base",n,Sn,dt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,F,Sr]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Ts,N]}],"font-family":[{font:[wd,N,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,F,N]}],"line-clamp":[{"line-clamp":[X,"none",F,Sr]}],leading:[{leading:[s,...$()]}],"list-image":[{"list-image":["none",F,N]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",F,N]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:S()}],"text-color":[{text:S()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...L(),"wavy"]}],"text-decoration-thickness":[{decoration:[X,"from-font","auto",F,dt]}],"text-decoration-color":[{decoration:S()}],"underline-offset":[{"underline-offset":[X,"auto",F,N]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:$()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F,N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F,N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...x(),kd,bd]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",Sd,yd]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ct,F,N],radial:["",F,N],conic:[ct,F,N]},Ed,xd]}],"bg-color":[{bg:S()}],"gradient-from-pos":[{from:B()}],"gradient-via-pos":[{via:B()}],"gradient-to-pos":[{to:B()}],"gradient-from":[{from:S()}],"gradient-via":[{via:S()}],"gradient-to":[{to:S()}],rounded:[{rounded:P()}],"rounded-s":[{"rounded-s":P()}],"rounded-e":[{"rounded-e":P()}],"rounded-t":[{"rounded-t":P()}],"rounded-r":[{"rounded-r":P()}],"rounded-b":[{"rounded-b":P()}],"rounded-l":[{"rounded-l":P()}],"rounded-ss":[{"rounded-ss":P()}],"rounded-se":[{"rounded-se":P()}],"rounded-ee":[{"rounded-ee":P()}],"rounded-es":[{"rounded-es":P()}],"rounded-tl":[{"rounded-tl":P()}],"rounded-tr":[{"rounded-tr":P()}],"rounded-br":[{"rounded-br":P()}],"rounded-bl":[{"rounded-bl":P()}],"border-w":[{border:I()}],"border-w-x":[{"border-x":I()}],"border-w-y":[{"border-y":I()}],"border-w-s":[{"border-s":I()}],"border-w-e":[{"border-e":I()}],"border-w-t":[{"border-t":I()}],"border-w-r":[{"border-r":I()}],"border-w-b":[{"border-b":I()}],"border-w-l":[{"border-l":I()}],"divide-x":[{"divide-x":I()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":I()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...L(),"hidden","none"]}],"divide-style":[{divide:[...L(),"hidden","none"]}],"border-color":[{border:S()}],"border-color-x":[{"border-x":S()}],"border-color-y":[{"border-y":S()}],"border-color-s":[{"border-s":S()}],"border-color-e":[{"border-e":S()}],"border-color-t":[{"border-t":S()}],"border-color-r":[{"border-r":S()}],"border-color-b":[{"border-b":S()}],"border-color-l":[{"border-l":S()}],"divide-color":[{divide:S()}],"outline-style":[{outline:[...L(),"none","hidden"]}],"outline-offset":[{"outline-offset":[X,F,N]}],"outline-w":[{outline:["",X,Sn,dt]}],"outline-color":[{outline:[e]}],shadow:[{shadow:["","none",c,Td,$d]}],"shadow-color":[{shadow:S()}],"inset-shadow":[{"inset-shadow":["none",F,N,d]}],"inset-shadow-color":[{"inset-shadow":S()}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:S()}],"ring-offset-w":[{"ring-offset":[X,dt]}],"ring-offset-color":[{"ring-offset":S()}],"inset-ring-w":[{"inset-ring":I()}],"inset-ring-color":[{"inset-ring":S()}],opacity:[{opacity:[X,F,N]}],"mix-blend":[{"mix-blend":[...Y(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Y()}],filter:[{filter:["","none",F,N]}],blur:[{blur:Z()}],brightness:[{brightness:[X,F,N]}],contrast:[{contrast:[X,F,N]}],"drop-shadow":[{"drop-shadow":["","none",f,F,N]}],grayscale:[{grayscale:["",X,F,N]}],"hue-rotate":[{"hue-rotate":[X,F,N]}],invert:[{invert:["",X,F,N]}],saturate:[{saturate:[X,F,N]}],sepia:[{sepia:["",X,F,N]}],"backdrop-filter":[{"backdrop-filter":["","none",F,N]}],"backdrop-blur":[{"backdrop-blur":Z()}],"backdrop-brightness":[{"backdrop-brightness":[X,F,N]}],"backdrop-contrast":[{"backdrop-contrast":[X,F,N]}],"backdrop-grayscale":[{"backdrop-grayscale":["",X,F,N]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[X,F,N]}],"backdrop-invert":[{"backdrop-invert":["",X,F,N]}],"backdrop-opacity":[{"backdrop-opacity":[X,F,N]}],"backdrop-saturate":[{"backdrop-saturate":[X,F,N]}],"backdrop-sepia":[{"backdrop-sepia":["",X,F,N]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":$()}],"border-spacing-x":[{"border-spacing-x":$()}],"border-spacing-y":[{"border-spacing-y":$()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",F,N]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[X,"initial",F,N]}],ease:[{ease:["linear","initial",y,F,N]}],delay:[{delay:[X,F,N]}],animate:[{animate:["none",v,F,N]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,F,N]}],"perspective-origin":[{"perspective-origin":xe()}],rotate:[{rotate:he()}],"rotate-x":[{"rotate-x":he()}],"rotate-y":[{"rotate-y":he()}],"rotate-z":[{"rotate-z":he()}],scale:[{scale:ve()}],"scale-x":[{"scale-x":ve()}],"scale-y":[{"scale-y":ve()}],"scale-z":[{"scale-z":ve()}],"scale-3d":["scale-3d"],skew:[{skew:Q()}],"skew-x":[{"skew-x":Q()}],"skew-y":[{"skew-y":Q()}],transform:[{transform:[F,N,"","none","gpu","cpu"]}],"transform-origin":[{origin:xe()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:te()}],"translate-x":[{"translate-x":te()}],"translate-y":[{"translate-y":te()}],"translate-z":[{"translate-z":te()}],"translate-none":["translate-none"],accent:[{accent:S()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:S()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F,N]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":$()}],"scroll-mx":[{"scroll-mx":$()}],"scroll-my":[{"scroll-my":$()}],"scroll-ms":[{"scroll-ms":$()}],"scroll-me":[{"scroll-me":$()}],"scroll-mt":[{"scroll-mt":$()}],"scroll-mr":[{"scroll-mr":$()}],"scroll-mb":[{"scroll-mb":$()}],"scroll-ml":[{"scroll-ml":$()}],"scroll-p":[{"scroll-p":$()}],"scroll-px":[{"scroll-px":$()}],"scroll-py":[{"scroll-py":$()}],"scroll-ps":[{"scroll-ps":$()}],"scroll-pe":[{"scroll-pe":$()}],"scroll-pt":[{"scroll-pt":$()}],"scroll-pr":[{"scroll-pr":$()}],"scroll-pb":[{"scroll-pb":$()}],"scroll-pl":[{"scroll-pl":$()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F,N]}],fill:[{fill:["none",...S()]}],"stroke-w":[{stroke:[X,Sn,dt,Sr]}],stroke:[{stroke:["none",...S()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}},Id=id(Dd),{cva:tr,cx:se}=Kc({hooks:{onComplete:e=>Id(e)}});/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jd=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Na=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Vd={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ld=p.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:s,iconNode:i,...a},l)=>p.createElement("svg",{ref:l,...Vd,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Na("lucide",o),...a},[...i.map(([u,c])=>p.createElement(u,c)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Me=(e,t)=>{const n=p.forwardRef(({className:r,...o},s)=>p.createElement(Ld,{ref:s,iconNode:t,className:Na("lucide-".concat(jd(e)),r),...o}));return n.displayName="".concat(e),n};/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Od=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],Cb=Me("User",Od),Fa=e=>p.isValidElement(e)&&!!e.props&&typeof e.props=="object"&&"key"in e,He=({children:e,ref:t,...n})=>{const r=p.Children.only(e);if(Fa(r))return p.cloneElement(r,{...n,...r.props,ref:t,style:{...n.style,...r.props.style},className:se(r.props.className,n.className)});throw new Error("Slot needs a valid react element child")};He.displayName="Slot";const Ba=({asChild:e,child:t,children:n,...r})=>C.jsx(C.Fragment,{children:e?Fa(t)?p.cloneElement(t,r,n(t.props.children)):null:n(t)}),Nd=tr({base:["relative","animate-spin","before:absolute before:top-0 before:left-0 before:block before:size-full before:rounded-full before:border-current before:opacity-40","after:top-0 after:left-0 after:block after:size-full after:rounded-full after:border-transparent after:border-t-current after:border-r-current"],variants:{size:{xs:"size-2 before:border after:border",sm:"size-3 before:border-2 after:border-2",md:"size-4 before:border-2 after:border-2",lg:"size-5 before:border-3 after:border-3"}}}),za=({className:e,size:t="md",...n})=>C.jsx("div",{"aria-label":"loading",role:"progressbar",className:Nd({size:t,className:e}),...n}),Fd=tr({base:["chunky inline-flex shrink-0 text-(--button-text-color) focus-visible:outline-none enabled:cursor-pointer disabled:pointer-events-none disabled:opacity-50","items-center justify-center gap-1.5 whitespace-nowrap","h-[calc(var(--button-height)-var(--chunky-depth,var(--chunky-default-depth)))]"],variants:{variant:{primary:"[--button-text-color:var(--color-foreground)]",secondary:["[--button-text-color:var(--color-foreground)]","[--chunky-bg-color:var(--color-background-high)]","[--chunky-shadow-bg-color:var(--color-background-low)]"],accent:["font-medium","[--button-text-color:var(--color-white)]","[--chunky-bg-color:var(--color-accent)]","[--chunky-border-color:var(--color-accent-secondary)]","[--chunky-shadow-bg-color:var(--color-accent-secondary)]"],positive:["font-medium","[--button-text-color:var(--color-white)]","[--chunky-bg-color:var(--color-positive)]","[--chunky-border-color:var(--color-positive-secondary)]","[--chunky-shadow-bg-color:var(--color-positive-secondary)]"],negative:["font-medium","[--button-text-color:var(--color-white)]","[--chunky-bg-color:var(--color-negative)]","[--chunky-border-color:var(--color-negative-secondary)]","[--chunky-shadow-bg-color:var(--color-negative-secondary)]"],warning:["font-medium","[--button-text-color:var(--color-black)]","[--chunky-bg-color:var(--color-warning)]","[--chunky-border-color:var(--color-warning-secondary)]","[--chunky-shadow-bg-color:var(--color-warning-secondary)]"]},size:{sm:"rounded-lg px-3 text-xs [--button-height:--spacing(8)]",md:"rounded-xl px-4 text-sm [--button-height:--spacing(10)]",lg:"rounded-xl px-5 text-base [--button-height:--spacing(12)]",xl:"rounded-2xl px-6 text-xl [--button-height:--spacing(14)]"},square:{true:"w-(--button-height,2.5em) px-0",false:""}},defaultVariants:{variant:"primary",size:"md"}}),To=({children:e,className:t,variant:n,asChild:r=!1,isLoading:o,size:s,square:i,type:a="button",ref:l,...u})=>C.jsx(r?He:"button",{className:se(Fd({className:t,variant:n,size:s,square:i}),o&&"text-transparent"),ref:l,type:a,...u,children:C.jsx(Ba,{asChild:r,child:e,children:c=>C.jsxs(C.Fragment,{children:[c,o&&C.jsx("span",{"data-button-spinner":!0,className:se("absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2","text-(--button-text-color)"),children:C.jsx(za,{})})]})})}),Bd="de-DE",zd="EUR",Wd=p.createContext({locale:Bd,currency:zd}),Pb=()=>p.use(Wd);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ud=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]];Me("ChevronLeft",Ud);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qd=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]];Me("ChevronRight",qd);const Wa=p.createContext(void 0),Ua=()=>p.use(Wa),Rb=({children:e,className:t,...n})=>{const r=p.useId(),[o,s]=p.useState([]),[i,a]=p.useState([]),[l,u]=p.useState([]),c=p.useCallback(x=>(s(k=>[...k,x]),()=>{s(k=>k.filter(w=>w!==x))}),[]),d=p.useCallback(x=>(a(k=>[...k,x]),()=>{a(k=>k.filter(w=>w!==x))}),[]),f=p.useCallback(x=>(u(k=>[...k,x]),()=>{u(k=>k.filter(w=>w!==x))}),[]),h=p.useCallback((x,k)=>{switch(x){case"error":return c(k);case"description":return d(k);case"label":return f(k)}},[c,d,f]),m=p.useMemo(()=>o.length>0?o.join(" "):void 0,[o]),g=p.useMemo(()=>i.length>0?i.join(" "):void 0,[i]),y=p.useMemo(()=>l.length>0?l.join(" "):void 0,[l]),v=p.useMemo(()=>({registerElement:h,id:r,"aria-errormessage":m,"aria-describedby":g,"aria-labelledby":y}),[h,r,m,g,y]),b=Object.keys(n).length===1&&"children"in n?p.Fragment:"div";return C.jsx(Wa,{value:v,children:C.jsx(b,{className:t,...n,children:e})})};function nr(){return typeof window<"u"}function at(e){return qa(e)?(e.nodeName||"").toLowerCase():"#document"}function Re(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Ke(e){var t;return(t=(qa(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function qa(e){return nr()?e instanceof Node||e instanceof Re(e).Node:!1}function ue(e){return nr()?e instanceof Element||e instanceof Re(e).Element:!1}function fe(e){return nr()?e instanceof HTMLElement||e instanceof Re(e).HTMLElement:!1}function Zr(e){return!nr()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Re(e).ShadowRoot}function mn(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Ve(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Hd(e){return["table","td","th"].includes(at(e))}function rr(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(n){return!1}})}function Co(e){const t=or(),n=ue(e)?Ve(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Kd(e){let t=_e(e);for(;fe(t)&&!Xe(t);){if(Co(t))return t;if(rr(t))return null;t=_e(t)}return null}function or(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Xe(e){return["html","body","#document"].includes(at(e))}function Ve(e){return Re(e).getComputedStyle(e)}function sr(e){return ue(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function _e(e){if(at(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Zr(e)&&e.host||Ke(e);return Zr(t)?t.host:t}function Ha(e){const t=_e(e);return Xe(t)?e.ownerDocument?e.ownerDocument.body:e.body:fe(t)&&mn(t)?t:Ha(t)}function tt(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Ha(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=Re(o);if(s){const a=Jr(i);return t.concat(i,i.visualViewport||[],mn(o)?o:[],a&&n?tt(a):[])}return t.concat(o,tt(o,[],n))}function Jr(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Ge(e){let t=e.activeElement;for(;((n=t)==null||(n=n.shadowRoot)==null?void 0:n.activeElement)!=null;){var n;t=t.shadowRoot.activeElement}return t}function Ce(e,t){if(!e||!t)return!1;const n=t.getRootNode==null?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&Zr(n)){let r=t;for(;r;){if(e===r)return!0;r=r.parentNode||r.host}}return!1}function Gd(){const e=navigator.userAgentData;return e!=null&&e.platform?e.platform:navigator.platform}function Ka(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(t=>{let{brand:n,version:r}=t;return n+"/"+r}).join(" "):navigator.userAgent}function Ga(e){return e.mozInputSource===0&&e.isTrusted?!0:Qr()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}function Xa(e){return _d()?!1:!Qr()&&e.width===0&&e.height===0||Qr()&&e.width===1&&e.height===1&&e.pressure===0&&e.detail===0&&e.pointerType==="mouse"||e.width<1&&e.height<1&&e.pressure===0&&e.detail===0&&e.pointerType==="touch"}function Xd(){return/apple/i.test(navigator.vendor)}function Qr(){const e=/android/i;return e.test(Gd())||e.test(Ka())}function _d(){return Ka().includes("jsdom/")}function Cs(e,t){return["mouse","pen"].includes(e)}function Yd(e){return"nativeEvent"in e}function Zd(e){return e.matches("html,body")}function je(e){return(e==null?void 0:e.ownerDocument)||document}function Er(e,t){if(t==null)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return n.target!=null&&t.contains(n.target)}function gt(e){return"composedPath"in e?e.composedPath()[0]:e.target}const Jd="input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])";function _a(e){return fe(e)&&e.matches(Jd)}function be(e){e.preventDefault(),e.stopPropagation()}function Ya(e){return e?e.getAttribute("role")==="combobox"&&_a(e):!1}const Qd=["top","right","bottom","left"],Vt=Math.min,Pe=Math.max,Fn=Math.round,Ct=Math.floor,Be=e=>({x:e,y:e}),ef={left:"right",right:"left",bottom:"top",top:"bottom"},tf={start:"end",end:"start"};function Ps(e,t,n){return Pe(e,Vt(t,n))}function zt(e,t){return typeof e=="function"?e(t):e}function ot(e){return e.split("-")[0]}function gn(e){return e.split("-")[1]}function Za(e){return e==="x"?"y":"x"}function Ja(e){return e==="y"?"height":"width"}function xt(e){return["top","bottom"].includes(ot(e))?"y":"x"}function Qa(e){return Za(xt(e))}function nf(e,t,n){n===void 0&&(n=!1);const r=gn(e),o=Qa(e),s=Ja(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=Bn(i)),[i,Bn(i)]}function rf(e){const t=Bn(e);return[eo(e),t,eo(t)]}function eo(e){return e.replace(/start|end/g,t=>tf[t])}function of(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function sf(e,t,n,r){const o=gn(e);let s=of(ot(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(eo)))),s}function Bn(e){return e.replace(/left|right|bottom|top/g,t=>ef[t])}function af(e){return{top:0,right:0,bottom:0,left:0,...e}}function lf(e){return typeof e!="number"?af(e):{top:e,right:e,bottom:e,left:e}}function zn(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/var uf=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],Wn=uf.join(","),el=typeof Element>"u",Lt=el?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Un=!el&&Element.prototype.getRootNode?function(e){var t;return e==null||(t=e.getRootNode)===null||t===void 0?void 0:t.call(e)}:function(e){return e==null?void 0:e.ownerDocument},qn=function e(t,n){var r;n===void 0&&(n=!0);var o=t==null||(r=t.getAttribute)===null||r===void 0?void 0:r.call(t,"inert"),s=o===""||o==="true",i=s||n&&t&&e(t.parentNode);return i},cf=function(e){var t,n=e==null||(t=e.getAttribute)===null||t===void 0?void 0:t.call(e,"contenteditable");return n===""||n==="true"},df=function(e,t,n){if(qn(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(Wn));return t&&Lt.call(e,Wn)&&r.unshift(e),r=r.filter(n),r},ff=function e(t,n,r){for(var o=[],s=Array.from(t);s.length;){var i=s.shift();if(!qn(i,!1))if(i.tagName==="SLOT"){var a=i.assignedElements(),l=a.length?a:i.children,u=e(l,!0,r);r.flatten?o.push.apply(o,u):o.push({scopeParent:i,candidates:u})}else{var c=Lt.call(i,Wn);c&&r.filter(i)&&(n||!t.includes(i))&&o.push(i);var d=i.shadowRoot||typeof r.getShadowRoot=="function"&&r.getShadowRoot(i),f=!qn(d,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(i));if(d&&f){var h=e(d===!0?i.children:d.children,!0,r);r.flatten?o.push.apply(o,h):o.push({scopeParent:i,candidates:h})}else s.unshift.apply(s,i.children)}}return o},tl=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},nl=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||cf(e))&&!tl(e)?0:e.tabIndex},hf=function(e,t){var n=nl(e);return n<0&&t&&!tl(e)?0:n},pf=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},rl=function(e){return e.tagName==="INPUT"},mf=function(e){return rl(e)&&e.type==="hidden"},gf=function(e){var t=e.tagName==="DETAILS"&&Array.prototype.slice.apply(e.children).some(function(n){return n.tagName==="SUMMARY"});return t},vf=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]},yf=function(e){if(!e.name)return!0;var t=e.form||Un(e),n=function(s){return t.querySelectorAll('input[type="radio"][name="'+s+'"]')},r;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")r=n(window.CSS.escape(e.name));else try{r=n(e.name)}catch(s){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",s.message),!1}var o=vf(r,e.form);return!o||o===e},bf=function(e){return rl(e)&&e.type==="radio"},xf=function(e){return bf(e)&&!yf(e)},$f=function(e){var t,n=e&&Un(e),r=(t=n)===null||t===void 0?void 0:t.host,o=!1;if(n&&n!==e){var s,i,a;for(o=!!((s=r)!==null&&s!==void 0&&(i=s.ownerDocument)!==null&&i!==void 0&&i.contains(r)||e!=null&&(a=e.ownerDocument)!==null&&a!==void 0&&a.contains(e));!o&&r;){var l,u,c;n=Un(r),r=(l=n)===null||l===void 0?void 0:l.host,o=!!((u=r)!==null&&u!==void 0&&(c=u.ownerDocument)!==null&&c!==void 0&&c.contains(r))}}return o},Rs=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return n===0&&r===0},wf=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if(getComputedStyle(e).visibility==="hidden")return!0;var o=Lt.call(e,"details>summary:first-of-type"),s=o?e.parentElement:e;if(Lt.call(s,"details:not([open]) *"))return!0;if(!n||n==="full"||n==="legacy-full"){if(typeof r=="function"){for(var i=e;e;){var a=e.parentElement,l=Un(e);if(a&&!a.shadowRoot&&r(a)===!0)return Rs(e);e.assignedSlot?e=e.assignedSlot:!a&&l!==e.ownerDocument?e=l.host:e=a}e=i}if($f(e))return!e.getClientRects().length;if(n!=="legacy-full")return!0}else if(n==="non-zero-area")return Rs(e);return!1},kf=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if(t.tagName==="FIELDSET"&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if(r.tagName==="LEGEND")return Lt.call(t,"fieldset[disabled] *")?!0:!r.contains(e)}return!0}t=t.parentElement}return!1},Sf=function(e,t){return!(t.disabled||qn(t)||mf(t)||wf(t,e)||gf(t)||kf(t))},to=function(e,t){return!(xf(t)||nl(t)<0||!Sf(e,t))},Ef=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},Tf=function e(t){var n=[],r=[];return t.forEach(function(o,s){var i=!!o.scopeParent,a=i?o.scopeParent:o,l=hf(a,i),u=i?e(o.candidates):a;l===0?i?n.push.apply(n,u):n.push(a):r.push({documentOrder:s,tabIndex:l,item:o,isScope:i,content:u})}),r.sort(pf).reduce(function(o,s){return s.isScope?o.push.apply(o,s.content):o.push(s.content),o},[]).concat(n)},ir=function(e,t){t=t||{};var n;return t.getShadowRoot?n=ff([e],t.includeContainer,{filter:to.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:Ef}):n=df(e,t.includeContainer,to.bind(null,t)),Tf(n)},Cf=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return Lt.call(e,Wn)===!1?!1:to(t,e)};function As(e,t,n){let{reference:r,floating:o}=e;const s=xt(t),i=Qa(t),a=Ja(i),l=ot(t),u=s==="y",c=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[a]/2-o[a]/2;let h;switch(l){case"top":h={x:c,y:r.y-o.height};break;case"bottom":h={x:c,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:d};break;case"left":h={x:r.x-o.width,y:d};break;default:h={x:r.x,y:r.y}}switch(gn(t)){case"start":h[i]-=f*(n&&u?-1:1);break;case"end":h[i]+=f*(n&&u?-1:1);break}return h}const Pf=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=As(u,r,l),f=r,h={},m=0;for(let g=0;g<a.length;g++){const{name:y,fn:v}=a[g],{x:b,y:x,data:k,reset:w}=await v({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:h,rects:u,platform:i,elements:{reference:e,floating:t}});c=b!=null?b:c,d=x!=null?x:d,h={...h,[y]:{...h[y],...k}},w&&m<=50&&(m++,typeof w=="object"&&(w.placement&&(f=w.placement),w.rects&&(u=w.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=As(u,f,l)),g=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:h}};async function on(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:h=0}=zt(t,e),m=lf(h),g=a[f?d==="floating"?"reference":"floating":d],y=zn(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(g)))==null||n?g:g.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),v=d==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,b=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),x=await(s.isElement==null?void 0:s.isElement(b))?await(s.getScale==null?void 0:s.getScale(b))||{x:1,y:1}:{x:1,y:1},k=zn(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:b,strategy:l}):v);return{top:(y.top-k.top+m.top)/x.y,bottom:(k.bottom-y.bottom+m.bottom)/x.y,left:(y.left-k.left+m.left)/x.x,right:(k.right-y.right+m.right)/x.x}}const Rf=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:c=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:g=!0,...y}=zt(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const v=ot(o),b=xt(a),x=ot(a)===a,k=await(l.isRTL==null?void 0:l.isRTL(u.floating)),w=f||(x||!g?[Bn(a)]:rf(a)),$=m!=="none";!f&&$&&w.push(...sf(a,g,m,k));const R=[a,...w],A=await on(t,y),D=[];let T=((r=s.flip)==null?void 0:r.overflows)||[];if(c&&D.push(A[v]),d){const H=nf(o,i,k);D.push(A[H[0]],A[H[1]])}if(T=[...T,{placement:o,overflows:D}],!D.every(H=>H<=0)){var V,j;const H=(((V=s.flip)==null?void 0:V.index)||0)+1,M=R[H];if(M)return{data:{index:H,overflows:T},reset:{placement:M}};let S=(j=T.filter(B=>B.overflows[0]<=0).sort((B,P)=>B.overflows[1]-P.overflows[1])[0])==null?void 0:j.placement;if(!S)switch(h){case"bestFit":{var E;const B=(E=T.filter(P=>{if($){const I=xt(P.placement);return I===b||I==="y"}return!0}).map(P=>[P.placement,P.overflows.filter(I=>I>0).reduce((I,L)=>I+L,0)]).sort((P,I)=>P[1]-I[1])[0])==null?void 0:E[0];B&&(S=B);break}case"initialPlacement":S=a;break}if(o!==S)return{reset:{placement:S}}}return{}}}};function Ms(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Ds(e){return Qd.some(t=>e[t]>=0)}const Af=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=zt(e,t);switch(r){case"referenceHidden":{const s=await on(t,{...o,elementContext:"reference"}),i=Ms(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Ds(i)}}}case"escaped":{const s=await on(t,{...o,altBoundary:!0}),i=Ms(s,n.floating);return{data:{escapedOffsets:i,escaped:Ds(i)}}}default:return{}}}}};async function Mf(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=ot(n),a=gn(n),l=xt(n)==="y",u=["left","top"].includes(i)?-1:1,c=s&&l?-1:1,d=zt(t,e);let{mainAxis:f,crossAxis:h,alignmentAxis:m}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&typeof m=="number"&&(h=a==="end"?m*-1:m),l?{x:h*c,y:f*u}:{x:f*u,y:h*c}}const Df=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:a}=t,l=await Mf(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:i}}}}},If=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:y=>{let{x:v,y:b}=y;return{x:v,y:b}}},...l}=zt(e,t),u={x:n,y:r},c=await on(t,l),d=xt(ot(o)),f=Za(d);let h=u[f],m=u[d];if(s){const y=f==="y"?"top":"left",v=f==="y"?"bottom":"right",b=h+c[y],x=h-c[v];h=Ps(b,h,x)}if(i){const y=d==="y"?"top":"left",v=d==="y"?"bottom":"right",b=m+c[y],x=m-c[v];m=Ps(b,m,x)}const g=a.fn({...t,[f]:h,[d]:m});return{...g,data:{x:g.x-n,y:g.y-r,enabled:{[f]:s,[d]:i}}}}}},jf=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...u}=zt(e,t),c=await on(t,u),d=ot(o),f=gn(o),h=xt(o)==="y",{width:m,height:g}=s.floating;let y,v;d==="top"||d==="bottom"?(y=d,v=f===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(v=d,y=f==="end"?"top":"bottom");const b=g-c.top-c.bottom,x=m-c.left-c.right,k=Vt(g-c[y],b),w=Vt(m-c[v],x),$=!t.middlewareData.shift;let R=k,A=w;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(A=x),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(R=b),$&&!f){const T=Pe(c.left,0),V=Pe(c.right,0),j=Pe(c.top,0),E=Pe(c.bottom,0);h?A=m-2*(T!==0||V!==0?T+V:Pe(c.left,c.right)):R=g-2*(j!==0||E!==0?j+E:Pe(c.top,c.bottom))}await l({...t,availableWidth:A,availableHeight:R});const D=await i.getDimensions(a.floating);return m!==D.width||g!==D.height?{reset:{rects:!0}}:{}}}};function ol(e){const t=Ve(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=fe(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=Fn(n)!==s||Fn(r)!==i;return a&&(n=s,r=i),{width:n,height:r,$:a}}function Po(e){return ue(e)?e:e.contextElement}function It(e){const t=Po(e);if(!fe(t))return Be(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=ol(t);let i=(s?Fn(n.width):n.width)/r,a=(s?Fn(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const Vf=Be(0);function sl(e){const t=Re(e);return!or()||!t.visualViewport?Vf:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Lf(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Re(e)?!1:t}function $t(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=Po(e);let i=Be(1);t&&(r?ue(r)&&(i=It(r)):i=It(e));const a=Lf(s,n,r)?sl(s):Be(0);let l=(o.left+a.x)/i.x,u=(o.top+a.y)/i.y,c=o.width/i.x,d=o.height/i.y;if(s){const f=Re(s),h=r&&ue(r)?Re(r):r;let m=f,g=Jr(m);for(;g&&r&&h!==m;){const y=It(g),v=g.getBoundingClientRect(),b=Ve(g),x=v.left+(g.clientLeft+parseFloat(b.paddingLeft))*y.x,k=v.top+(g.clientTop+parseFloat(b.paddingTop))*y.y;l*=y.x,u*=y.y,c*=y.x,d*=y.y,l+=x,u+=k,m=Re(g),g=Jr(m)}}return zn({width:c,height:d,x:l,y:u})}function Ro(e,t){const n=sr(e).scrollLeft;return t?t.left+n:$t(Ke(e)).left+n}function il(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:Ro(e,r)),s=r.top+t.scrollTop;return{x:o,y:s}}function Of(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Ke(r),a=t?rr(t.floating):!1;if(r===i||a&&s)return n;let l={scrollLeft:0,scrollTop:0},u=Be(1);const c=Be(0),d=fe(r);if((d||!d&&!s)&&((at(r)!=="body"||mn(i))&&(l=sr(r)),fe(r))){const h=$t(r);u=It(r),c.x=h.x+r.clientLeft,c.y=h.y+r.clientTop}const f=i&&!d&&!s?il(i,l,!0):Be(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x+f.x,y:n.y*u.y-l.scrollTop*u.y+c.y+f.y}}function Nf(e){return Array.from(e.getClientRects())}function Ff(e){const t=Ke(e),n=sr(e),r=e.ownerDocument.body,o=Pe(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=Pe(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Ro(e);const a=-n.scrollTop;return Ve(r).direction==="rtl"&&(i+=Pe(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:a}}function Bf(e,t){const n=Re(e),r=Ke(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,a=0,l=0;if(o){s=o.width,i=o.height;const u=or();(!u||u&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:s,height:i,x:a,y:l}}function zf(e,t){const n=$t(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=fe(e)?It(e):Be(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=o*s.x,u=r*s.y;return{width:i,height:a,x:l,y:u}}function Is(e,t,n){let r;if(t==="viewport")r=Bf(e,n);else if(t==="document")r=Ff(Ke(e));else if(ue(t))r=zf(t,n);else{const o=sl(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return zn(r)}function al(e,t){const n=_e(e);return n===t||!ue(n)||Xe(n)?!1:Ve(n).position==="fixed"||al(n,t)}function Wf(e,t){const n=t.get(e);if(n)return n;let r=tt(e,[],!1).filter(a=>ue(a)&&at(a)!=="body"),o=null;const s=Ve(e).position==="fixed";let i=s?_e(e):e;for(;ue(i)&&!Xe(i);){const a=Ve(i),l=Co(i);!l&&a.position==="fixed"&&(o=null),(s?!l&&!o:!l&&a.position==="static"&&o&&["absolute","fixed"].includes(o.position)||mn(i)&&!l&&al(e,i))?r=r.filter(u=>u!==i):o=a,i=_e(i)}return t.set(e,r),r}function Uf(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?rr(t)?[]:Wf(t,this._c):[].concat(n),r],i=s[0],a=s.reduce((l,u)=>{const c=Is(t,u,o);return l.top=Pe(c.top,l.top),l.right=Vt(c.right,l.right),l.bottom=Vt(c.bottom,l.bottom),l.left=Pe(c.left,l.left),l},Is(t,i,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function qf(e){const{width:t,height:n}=ol(e);return{width:t,height:n}}function Hf(e,t,n){const r=fe(t),o=Ke(t),s=n==="fixed",i=$t(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=Be(0);if(r||!r&&!s)if((at(t)!=="body"||mn(o))&&(a=sr(t)),r){const f=$t(t,!0,s,t);l.x=f.x+t.clientLeft,l.y=f.y+t.clientTop}else o&&(l.x=Ro(o));const u=o&&!r&&!s?il(o,a):Be(0),c=i.left+a.scrollLeft-l.x-u.x,d=i.top+a.scrollTop-l.y-u.y;return{x:c,y:d,width:i.width,height:i.height}}function Tr(e){return Ve(e).position==="static"}function js(e,t){if(!fe(e)||Ve(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Ke(e)===n&&(n=n.ownerDocument.body),n}function ll(e,t){const n=Re(e);if(rr(e))return n;if(!fe(e)){let o=_e(e);for(;o&&!Xe(o);){if(ue(o)&&!Tr(o))return o;o=_e(o)}return n}let r=js(e,t);for(;r&&Hd(r)&&Tr(r);)r=js(r,t);return r&&Xe(r)&&Tr(r)&&!Co(r)?n:r||Kd(e)||n}const Kf=async function(e){const t=this.getOffsetParent||ll,n=this.getDimensions,r=await n(e.floating);return{reference:Hf(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Gf(e){return Ve(e).direction==="rtl"}const Xf={convertOffsetParentRelativeRectToViewportRelativeRect:Of,getDocumentElement:Ke,getClippingRect:Uf,getOffsetParent:ll,getElementRects:Kf,getClientRects:Nf,getDimensions:qf,getScale:It,isElement:ue,isRTL:Gf};function ul(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function _f(e,t){let n=null,r;const o=Ke(e);function s(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const u=e.getBoundingClientRect(),{left:c,top:d,width:f,height:h}=u;if(a||t(),!f||!h)return;const m=Ct(d),g=Ct(o.clientWidth-(c+f)),y=Ct(o.clientHeight-(d+h)),v=Ct(c),b={rootMargin:-m+"px "+-g+"px "+-y+"px "+-v+"px",threshold:Pe(0,Vt(1,l))||1};let x=!0;function k(w){const $=w[0].intersectionRatio;if($!==l){if(!x)return i();$?i(!1,$):r=setTimeout(()=>{i(!1,1e-7)},1e3)}$===1&&!ul(u,e.getBoundingClientRect())&&i(),x=!1}try{n=new IntersectionObserver(k,{...b,root:o.ownerDocument})}catch(w){n=new IntersectionObserver(k,b)}n.observe(e)}return i(!0),s}function cl(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=Po(e),c=o||s?[...u?tt(u):[],...tt(t)]:[];c.forEach(v=>{o&&v.addEventListener("scroll",n,{passive:!0}),s&&v.addEventListener("resize",n)});const d=u&&a?_f(u,n):null;let f=-1,h=null;i&&(h=new ResizeObserver(v=>{let[b]=v;b&&b.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{var x;(x=h)==null||x.observe(t)})),n()}),u&&!l&&h.observe(u),h.observe(t));let m,g=l?$t(e):null;l&&y();function y(){const v=$t(e);g&&!ul(g,v)&&n(),g=v,m=requestAnimationFrame(y)}return n(),()=>{var v;c.forEach(b=>{o&&b.removeEventListener("scroll",n),s&&b.removeEventListener("resize",n)}),d==null||d(),(v=h)==null||v.disconnect(),h=null,l&&cancelAnimationFrame(m)}}const Yf=Df,Zf=If,Jf=Rf,Qf=jf,eh=Af,th=(e,t,n)=>{const r=new Map,o={platform:Xf,...n},s={...o.platform,_c:r};return Pf(e,t,{...o,platform:s})};var jn=typeof document<"u"?p.useLayoutEffect:p.useEffect;function Hn(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Hn(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!Hn(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function dl(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Vs(e,t){const n=dl(e);return Math.round(t*n)/n}function Cr(e){const t=p.useRef(e);return jn(()=>{t.current=e}),t}function nh(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[c,d]=p.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,h]=p.useState(r);Hn(f,r)||h(r);const[m,g]=p.useState(null),[y,v]=p.useState(null),b=p.useCallback(P=>{P!==$.current&&($.current=P,g(P))},[]),x=p.useCallback(P=>{P!==R.current&&(R.current=P,v(P))},[]),k=s||m,w=i||y,$=p.useRef(null),R=p.useRef(null),A=p.useRef(c),D=l!=null,T=Cr(l),V=Cr(o),j=Cr(u),E=p.useCallback(()=>{if(!$.current||!R.current)return;const P={placement:t,strategy:n,middleware:f};V.current&&(P.platform=V.current),th($.current,R.current,P).then(I=>{const L={...I,isPositioned:j.current!==!1};H.current&&!Hn(A.current,L)&&(A.current=L,Pa.flushSync(()=>{d(L)}))})},[f,t,n,V,j]);jn(()=>{u===!1&&A.current.isPositioned&&(A.current.isPositioned=!1,d(P=>({...P,isPositioned:!1})))},[u]);const H=p.useRef(!1);jn(()=>(H.current=!0,()=>{H.current=!1}),[]),jn(()=>{if(k&&($.current=k),w&&(R.current=w),k&&w){if(T.current)return T.current(k,w,E);E()}},[k,w,E,T,D]);const M=p.useMemo(()=>({reference:$,floating:R,setReference:b,setFloating:x}),[b,x]),S=p.useMemo(()=>({reference:k,floating:w}),[k,w]),B=p.useMemo(()=>{const P={position:n,left:0,top:0};if(!S.floating)return P;const I=Vs(S.floating,c.x),L=Vs(S.floating,c.y);return a?{...P,transform:"translate("+I+"px, "+L+"px)",...dl(S.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:I,top:L}},[n,a,S.floating,c.x,c.y]);return p.useMemo(()=>({...c,update:E,refs:M,elements:S,floatingStyles:B}),[c,E,M,S,B])}const fl=(e,t)=>({...Yf(e),options:[e,t]}),hl=(e,t)=>({...Zf(e),options:[e,t]}),pl=(e,t)=>({...Jf(e),options:[e,t]}),ml=(e,t)=>({...Qf(e),options:[e,t]}),rh=(e,t)=>({...eh(e),options:[e,t]});function Wt(e){const t=p.useRef(void 0),n=p.useCallback(r=>{const o=e.map(s=>{if(s!=null){if(typeof s=="function"){const i=s,a=i(r);return typeof a=="function"?a:()=>{i(null)}}return s.current=r,()=>{s.current=null}}});return()=>{o.forEach(s=>s==null?void 0:s())}},e);return p.useMemo(()=>e.every(r=>r==null)?null:r=>{t.current&&(t.current(),t.current=void 0),r!=null&&(t.current=n(r))},e)}const gl={...Hc},oh=gl.useInsertionEffect,sh=oh||(e=>e());function Se(e){const t=p.useRef(()=>{});return sh(()=>{t.current=e}),p.useCallback(function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return t.current==null?void 0:t.current(...r)},[])}const Ao="ArrowUp",vn="ArrowDown",nt="ArrowLeft",rt="ArrowRight";function En(e,t,n){return Math.floor(e/t)!==n}function Zt(e,t){return t<0||t>=e.current.length}function Pr(e,t){return we(e,{disabledIndices:t})}function Ls(e,t){return we(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function we(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:s=1}=t===void 0?{}:t;const i=e.current;let a=n;do a+=r?-s:s;while(a>=0&&a<=i.length-1&&Vn(i,a,o));return a}function ih(e,t){let{event:n,orientation:r,loop:o,rtl:s,cols:i,disabledIndices:a,minIndex:l,maxIndex:u,prevIndex:c,stopEvent:d=!1}=t,f=c;if(n.key===Ao){if(d&&be(n),c===-1)f=u;else if(f=we(e,{startingIndex:f,amount:i,decrement:!0,disabledIndices:a}),o&&(c-i<l||f<0)){const h=c%i,m=u%i,g=u-(m-h);m===h?f=u:f=m>h?g:g-i}Zt(e,f)&&(f=c)}if(n.key===vn&&(d&&be(n),c===-1?f=l:(f=we(e,{startingIndex:c,amount:i,disabledIndices:a}),o&&c+i>u&&(f=we(e,{startingIndex:c%i-i,amount:i,disabledIndices:a}))),Zt(e,f)&&(f=c)),r==="both"){const h=Ct(c/i);n.key===(s?nt:rt)&&(d&&be(n),c%i!==i-1?(f=we(e,{startingIndex:c,disabledIndices:a}),o&&En(f,i,h)&&(f=we(e,{startingIndex:c-c%i-1,disabledIndices:a}))):o&&(f=we(e,{startingIndex:c-c%i-1,disabledIndices:a})),En(f,i,h)&&(f=c)),n.key===(s?rt:nt)&&(d&&be(n),c%i!==0?(f=we(e,{startingIndex:c,decrement:!0,disabledIndices:a}),o&&En(f,i,h)&&(f=we(e,{startingIndex:c+(i-c%i),decrement:!0,disabledIndices:a}))):o&&(f=we(e,{startingIndex:c+(i-c%i),decrement:!0,disabledIndices:a})),En(f,i,h)&&(f=c));const m=Ct(u/i)===h;Zt(e,f)&&(o&&m?f=n.key===(s?rt:nt)?u:we(e,{startingIndex:c-c%i-1,disabledIndices:a}):f=c)}return f}function ah(e,t,n){const r=[];let o=0;return e.forEach((s,i)=>{let{width:a,height:l}=s,u=!1;for(n&&(o=0);!u;){const c=[];for(let d=0;d<a;d++)for(let f=0;f<l;f++)c.push(o+d+f*t);o%t+a<=t&&c.every(d=>r[d]==null)?(c.forEach(d=>{r[d]=i}),u=!0):o++}}),[...r]}function lh(e,t,n,r,o){if(e===-1)return-1;const s=n.indexOf(e),i=t[e];switch(o){case"tl":return s;case"tr":return i?s+i.width-1:s;case"bl":return i?s+(i.height-1)*r:s;case"br":return n.lastIndexOf(e)}}function uh(e,t){return t.flatMap((n,r)=>e.includes(n)?[r]:[])}function Vn(e,t,n){if(n)return n.includes(t);const r=e[t];return r==null||r.hasAttribute("disabled")||r.getAttribute("aria-disabled")==="true"}var re=typeof document<"u"?p.useLayoutEffect:p.useEffect;function ch(e,t){const n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}const vl=p.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function yl(e){const{children:t,elementsRef:n,labelsRef:r}=e,[o,s]=p.useState(()=>new Set),i=p.useCallback(u=>{s(c=>new Set(c).add(u))},[]),a=p.useCallback(u=>{s(c=>{const d=new Set(c);return d.delete(u),d})},[]),l=p.useMemo(()=>{const u=new Map;return Array.from(o.keys()).sort(ch).forEach((c,d)=>{u.set(c,d)}),u},[o]);return C.jsx(vl.Provider,{value:p.useMemo(()=>({register:i,unregister:a,map:l,elementsRef:n,labelsRef:r}),[i,a,l,n,r]),children:t})}function bl(e){e===void 0&&(e={});const{label:t}=e,{register:n,unregister:r,map:o,elementsRef:s,labelsRef:i}=p.useContext(vl),[a,l]=p.useState(null),u=p.useRef(null),c=p.useCallback(d=>{if(u.current=d,a!==null&&(s.current[a]=d,i)){var f;const h=t!==void 0;i.current[a]=h?t:(f=d==null?void 0:d.textContent)!=null?f:null}},[a,s,i,t]);return re(()=>{const d=u.current;if(d)return n(d),()=>{r(d)}},[n,r]),re(()=>{const d=u.current?o.get(u.current):null;d!=null&&l(d)},[o]),p.useMemo(()=>({ref:c,index:a!=null?a:-1}),[a,c])}let Os=!1,dh=0;const Ns=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+dh++;function fh(){const[e,t]=p.useState(()=>Os?Ns():void 0);return re(()=>{e==null&&t(Ns())},[]),p.useEffect(()=>{Os=!0},[]),e}const hh=gl.useId,Mo=hh||fh;function ph(){const e=new Map;return{emit(t,n){var r;(r=e.get(t))==null||r.forEach(o=>o(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,((r=e.get(t))==null?void 0:r.filter(o=>o!==n))||[])}}}const mh=p.createContext(null),gh=p.createContext(null),Do=()=>{var e;return((e=p.useContext(mh))==null?void 0:e.id)||null},ar=()=>p.useContext(gh);function sn(e){return"data-floating-ui-"+e}function Fs(e){e.current!==-1&&(clearTimeout(e.current),e.current=-1)}function Fe(e){const t=p.useRef(e);return re(()=>{t.current=e}),t}let Bs=0;function ht(e,t){t===void 0&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:o=!1}=t;r&&cancelAnimationFrame(Bs);const s=()=>e==null?void 0:e.focus({preventScroll:n});o?s():Bs=requestAnimationFrame(s)}function zs(e,t){var n;let r=[],o=(n=e.find(s=>s.id===t))==null?void 0:n.parentId;for(;o;){const s=e.find(i=>i.id===o);o=s==null?void 0:s.parentId,s&&(r=r.concat(s))}return r}function jt(e,t){let n=e.filter(o=>{var s;return o.parentId===t&&((s=o.context)==null?void 0:s.open)}),r=n;for(;r.length;)r=e.filter(o=>{var s;return(s=r)==null?void 0:s.some(i=>{var a;return o.parentId===i.id&&((a=o.context)==null?void 0:a.open)})}),n=n.concat(r);return n}function vh(e,t){let n,r=-1;function o(s,i){i>r&&(n=s,r=i),jt(e,s).forEach(a=>{o(a.id,i+1)})}return o(t,0),e.find(s=>s.id===n)}let Tt=new WeakMap,Tn=new WeakSet,Cn={},Rr=0;const yh=()=>typeof HTMLElement<"u"&&"inert"in HTMLElement.prototype,xl=e=>e&&(e.host||xl(e.parentNode)),bh=(e,t)=>t.map(n=>{if(e.contains(n))return n;const r=xl(n);return e.contains(r)?r:null}).filter(n=>n!=null);function xh(e,t,n,r){const o="data-floating-ui-inert",s=r?"inert":n?"aria-hidden":null,i=bh(t,e),a=new Set,l=new Set(i),u=[];Cn[o]||(Cn[o]=new WeakMap);const c=Cn[o];i.forEach(d),f(t),a.clear();function d(h){!h||a.has(h)||(a.add(h),h.parentNode&&d(h.parentNode))}function f(h){!h||l.has(h)||[].forEach.call(h.children,m=>{if(at(m)!=="script")if(a.has(m))f(m);else{const g=s?m.getAttribute(s):null,y=g!==null&&g!=="false",v=Tt.get(m)||0,b=s?v+1:v,x=(c.get(m)||0)+1;Tt.set(m,b),c.set(m,x),u.push(m),b===1&&y&&Tn.add(m),x===1&&m.setAttribute(o,""),!y&&s&&m.setAttribute(s,"true")}})}return Rr++,()=>{u.forEach(h=>{const m=Tt.get(h)||0,g=s?m-1:m,y=(c.get(h)||0)-1;Tt.set(h,g),c.set(h,y),g||(!Tn.has(h)&&s&&h.removeAttribute(s),Tn.delete(h)),y||h.removeAttribute(o)}),Rr--,Rr||(Tt=new WeakMap,Tt=new WeakMap,Tn=new WeakSet,Cn={})}}function Ws(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=je(e[0]).body;return xh(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}const lr=()=>({getShadowRoot:!0,displayCheck:typeof ResizeObserver=="function"&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function $l(e,t){const n=ir(e,lr());t==="prev"&&n.reverse();const r=n.indexOf(Ge(je(e)));return n.slice(r+1)[0]}function wl(){return $l(document.body,"next")}function kl(){return $l(document.body,"prev")}function Jt(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!Ce(n,r)}function $h(e){ir(e,lr()).forEach(t=>{t.dataset.tabindex=t.getAttribute("tabindex")||"",t.setAttribute("tabindex","-1")})}function Us(e){e.querySelectorAll("[data-tabindex]").forEach(t=>{const n=t.dataset.tabindex;delete t.dataset.tabindex,n?t.setAttribute("tabindex",n):t.removeAttribute("tabindex")})}const ur={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0},Kn=p.forwardRef(function(e,t){const[n,r]=p.useState();re(()=>{Xd()&&r("button")},[]);const o={ref:t,tabIndex:0,role:n,"aria-hidden":n?void 0:!0,[sn("focus-guard")]:"",style:ur};return C.jsx("span",{...e,...o})}),Sl=p.createContext(null),qs=sn("portal");function wh(e){e===void 0&&(e={});const{id:t,root:n}=e,r=Mo(),o=El(),[s,i]=p.useState(null),a=p.useRef(null);return re(()=>()=>{s==null||s.remove(),queueMicrotask(()=>{a.current=null})},[s]),re(()=>{if(!r||a.current)return;const l=t?document.getElementById(t):null;if(!l)return;const u=document.createElement("div");u.id=r,u.setAttribute(qs,""),l.appendChild(u),a.current=u,i(u)},[t,r]),re(()=>{if(n===null||!r||a.current)return;let l=n||(o==null?void 0:o.portalNode);l&&!ue(l)&&(l=l.current),l=l||document.body;let u=null;t&&(u=document.createElement("div"),u.id=t,l.appendChild(u));const c=document.createElement("div");c.id=r,c.setAttribute(qs,""),l=u||l,l.appendChild(c),a.current=c,i(c)},[t,n,r,o]),s}function kh(e){const{children:t,id:n,root:r,preserveTabOrder:o=!0}=e,s=wh({id:n,root:r}),[i,a]=p.useState(null),l=p.useRef(null),u=p.useRef(null),c=p.useRef(null),d=p.useRef(null),f=i==null?void 0:i.modal,h=i==null?void 0:i.open,m=!!i&&!i.modal&&i.open&&o&&!!(r||s);return p.useEffect(()=>{if(!s||!o||f)return;function g(y){s&&Jt(y)&&(y.type==="focusin"?Us:$h)(s)}return s.addEventListener("focusin",g,!0),s.addEventListener("focusout",g,!0),()=>{s.removeEventListener("focusin",g,!0),s.removeEventListener("focusout",g,!0)}},[s,o,f]),p.useEffect(()=>{s&&(h||Us(s))},[h,s]),C.jsxs(Sl.Provider,{value:p.useMemo(()=>({preserveTabOrder:o,beforeOutsideRef:l,afterOutsideRef:u,beforeInsideRef:c,afterInsideRef:d,portalNode:s,setFocusManagerState:a}),[o,s]),children:[m&&s&&C.jsx(Kn,{"data-type":"outside",ref:l,onFocus:g=>{if(Jt(g,s)){var y;(y=c.current)==null||y.focus()}else{const v=kl()||(i==null?void 0:i.domReference);v==null||v.focus()}}}),m&&s&&C.jsx("span",{"aria-owns":s.id,style:ur}),s&&Pa.createPortal(t,s),m&&s&&C.jsx(Kn,{"data-type":"outside",ref:u,onFocus:g=>{if(Jt(g,s)){var y;(y=d.current)==null||y.focus()}else{const v=wl()||(i==null?void 0:i.domReference);v==null||v.focus(),i!=null&&i.closeOnFocusOut&&(i==null||i.onOpenChange(!1,g.nativeEvent,"focus-out"))}}})]})}const El=()=>p.useContext(Sl),no="data-floating-ui-focusable";function ro(e){return e?e.hasAttribute(no)?e:e.querySelector("["+no+"]")||e:null}function Hs(e){return p.useMemo(()=>t=>{e.forEach(n=>{n&&(n.current=t)})},e)}const Sh=20;let pt=[];function Eh(e){pt=pt.filter(t=>t.isConnected),e&&at(e)!=="body"&&(pt.push(e),pt.length>Sh&&(pt=pt.slice(-20)))}function Ks(){return pt.slice().reverse().find(e=>e.isConnected)}function Th(e){const t=lr();return Cf(e,t)?e:ir(e,t)[0]||e}const Ch=p.forwardRef(function(e,t){return C.jsx("button",{...e,type:"button",ref:t,tabIndex:-1,style:ur})});function Tl(e){const{context:t,children:n,disabled:r=!1,order:o=["content"],guards:s=!0,initialFocus:i=0,returnFocus:a=!0,restoreFocus:l=!1,modal:u=!0,visuallyHiddenDismiss:c=!1,closeOnFocusOut:d=!0,outsideElementsInert:f=!1}=e,{open:h,onOpenChange:m,events:g,dataRef:y,elements:{domReference:v,floating:b}}=t,x=Se(()=>{var W;return(W=y.current.floatingContext)==null?void 0:W.nodeId}),k=typeof i=="number"&&i<0,w=Ya(v)&&k,$=yh(),R=$?s:!0,A=!R||$&&f,D=Fe(o),T=Fe(i),V=Fe(a),j=ar(),E=El(),H=p.useRef(null),M=p.useRef(null),S=p.useRef(!1),B=p.useRef(!1),P=p.useRef(-1),I=E!=null,L=ro(b),Y=Se(function(W){return W===void 0&&(W=L),W?ir(W,lr()):[]}),Z=Se(W=>{const _=Y(W);return D.current.map(K=>v&&K==="reference"?v:L&&K==="floating"?L:_).filter(Boolean).flat()});p.useEffect(()=>{if(r||!u)return;function W(K){if(K.key==="Tab"){Ce(L,Ge(je(L)))&&Y().length===0&&!w&&be(K);const G=Z(),ie=gt(K);D.current[0]==="reference"&&ie===v&&(be(K),K.shiftKey?ht(G[G.length-1]):ht(G[1])),D.current[1]==="floating"&&ie===L&&K.shiftKey&&(be(K),ht(G[0]))}}const _=je(L);return _.addEventListener("keydown",W),()=>{_.removeEventListener("keydown",W)}},[r,v,L,u,D,w,Y,Z]),p.useEffect(()=>{if(r||!b)return;function W(_){const K=gt(_),G=Y().indexOf(K);G!==-1&&(P.current=G)}return b.addEventListener("focusin",W),()=>{b.removeEventListener("focusin",W)}},[r,b,Y]),p.useEffect(()=>{if(r||!d)return;function W(){B.current=!0,setTimeout(()=>{B.current=!1})}function _(K){const G=K.relatedTarget;queueMicrotask(()=>{const ie=x(),ce=!(Ce(v,G)||Ce(b,G)||Ce(G,b)||Ce(E==null?void 0:E.portalNode,G)||G!=null&&G.hasAttribute(sn("focus-guard"))||j&&(jt(j.nodesRef.current,ie).find(J=>{var $e,pe;return Ce(($e=J.context)==null?void 0:$e.elements.floating,G)||Ce((pe=J.context)==null?void 0:pe.elements.domReference,G)})||zs(j.nodesRef.current,ie).find(J=>{var $e,pe,Ne;return[($e=J.context)==null?void 0:$e.elements.floating,ro((pe=J.context)==null?void 0:pe.elements.floating)].includes(G)||((Ne=J.context)==null?void 0:Ne.elements.domReference)===G})));if(l&&ce&&Ge(je(L))===je(L).body){fe(L)&&L.focus();const J=P.current,$e=Y(),pe=$e[J]||$e[$e.length-1]||L;fe(pe)&&pe.focus()}(w||!u)&&G&&ce&&!B.current&&G!==Ks()&&(S.current=!0,m(!1,K,"focus-out"))})}if(b&&fe(v))return v.addEventListener("focusout",_),v.addEventListener("pointerdown",W),b.addEventListener("focusout",_),()=>{v.removeEventListener("focusout",_),v.removeEventListener("pointerdown",W),b.removeEventListener("focusout",_)}},[r,v,b,L,u,j,E,m,d,l,Y,w,x]);const xe=p.useRef(null),he=p.useRef(null),ve=Hs([xe,E==null?void 0:E.beforeInsideRef]),Q=Hs([he,E==null?void 0:E.afterInsideRef]);p.useEffect(()=>{var W;if(r||!b)return;const _=Array.from((E==null||(W=E.portalNode)==null?void 0:W.querySelectorAll("["+sn("portal")+"]"))||[]),K=j&&!u?zs(j==null?void 0:j.nodesRef.current,x()).map(ce=>{var J;return(J=ce.context)==null?void 0:J.elements.floating}):[],G=[b,..._,...K,H.current,M.current,xe.current,he.current,E==null?void 0:E.beforeOutsideRef.current,E==null?void 0:E.afterOutsideRef.current,D.current.includes("reference")||w?v:null].filter(ce=>ce!=null),ie=u||w?Ws(G,!A,A):Ws(G);return()=>{ie()}},[r,v,b,u,D,E,w,R,A,j,x]),re(()=>{if(r||!fe(L))return;const W=je(L),_=Ge(W);queueMicrotask(()=>{const K=Z(L),G=T.current,ie=(typeof G=="number"?K[G]:G.current)||L,ce=Ce(L,_);!k&&!ce&&h&&ht(ie,{preventScroll:ie===L})})},[r,h,L,k,Z,T]),re(()=>{if(r||!L)return;let W=!1,_=!1;const K=je(L),G=Ge(K);let ie=y.current.openEvent;Eh(G);function ce(pe){let{open:Ne,reason:q,event:U,nested:O}=pe;if(Ne&&(ie=U),q==="escape-key"&&(_=!0),["hover","safe-polygon"].includes(q)&&U.type==="mouseleave"&&(S.current=!0),q==="outside-press")if(O)S.current=!1,W=!0;else if(Ga(U)||Xa(U))S.current=!1;else{let ne=!1;document.createElement("div").focus({get preventScroll(){return ne=!0,!1}}),ne?(S.current=!1,W=!0):S.current=!0}}g.on("openchange",ce);const J=K.createElement("span");J.setAttribute("tabindex","-1"),J.setAttribute("aria-hidden","true"),Object.assign(J.style,ur),I&&v&&v.insertAdjacentElement("afterend",J);function $e(){return typeof V.current=="boolean"?_&&v?v:Ks()||J:V.current.current||J}return()=>{g.off("openchange",ce);const pe=Ge(K),Ne=Ce(b,pe)||j&&jt(j.nodesRef.current,x()).some(U=>{var O;return Ce((O=U.context)==null?void 0:O.elements.floating,pe)});(Ne||ie&&["click","mousedown"].includes(ie.type))&&(_=!0);const q=$e();queueMicrotask(()=>{const U=Th(q);V.current&&!S.current&&fe(U)&&(!(U!==pe&&pe!==K.body)||Ne)&&U.focus({preventScroll:W}),J.remove()})}},[r,b,L,V,y,g,j,I,v,x]),p.useEffect(()=>{queueMicrotask(()=>{S.current=!1})},[r]),re(()=>{if(!r&&E)return E.setFocusManagerState({modal:u,closeOnFocusOut:d,open:h,onOpenChange:m,domReference:v}),()=>{E.setFocusManagerState(null)}},[r,E,u,h,m,d,v]),re(()=>{if(r||!L||typeof MutationObserver!="function"||k)return;const W=()=>{const K=L.getAttribute("tabindex"),G=Y(),ie=Ge(je(b)),ce=G.indexOf(ie);ce!==-1&&(P.current=ce),D.current.includes("floating")||ie!==v&&G.length===0?K!=="0"&&L.setAttribute("tabindex","0"):K!=="-1"&&L.setAttribute("tabindex","-1")};W();const _=new MutationObserver(W);return _.observe(L,{childList:!0,subtree:!0,attributes:!0}),()=>{_.disconnect()}},[r,b,L,v,D,Y,k]);function te(W){return r||!c||!u?null:C.jsx(Ch,{ref:W==="start"?H:M,onClick:_=>m(!1,_.nativeEvent),children:typeof c=="string"?c:"Dismiss"})}const de=!r&&R&&(u?!w:!0)&&(I||u);return C.jsxs(C.Fragment,{children:[de&&C.jsx(Kn,{"data-type":"inside",ref:ve,onFocus:W=>{if(u){const K=Z();ht(o[0]==="reference"?K[0]:K[K.length-1])}else if(E!=null&&E.preserveTabOrder&&E.portalNode)if(S.current=!1,Jt(W,E.portalNode)){const K=wl()||v;K==null||K.focus()}else{var _;(_=E.beforeOutsideRef.current)==null||_.focus()}}}),!w&&te("start"),n,te("end"),de&&C.jsx(Kn,{"data-type":"inside",ref:Q,onFocus:W=>{if(u)ht(Z()[0]);else if(E!=null&&E.preserveTabOrder&&E.portalNode)if(d&&(S.current=!0),Jt(W,E.portalNode)){const K=kl()||v;K==null||K.focus()}else{var _;(_=E.afterOutsideRef.current)==null||_.focus()}}})]})}function Gs(e){return fe(e.target)&&e.target.tagName==="BUTTON"}function Xs(e){return _a(e)}function Cl(e,t){t===void 0&&(t={});const{open:n,onOpenChange:r,dataRef:o,elements:{domReference:s}}=e,{enabled:i=!0,event:a="click",toggle:l=!0,ignoreMouse:u=!1,keyboardHandlers:c=!0,stickIfOpen:d=!0}=t,f=p.useRef(),h=p.useRef(!1),m=p.useMemo(()=>({onPointerDown(g){f.current=g.pointerType},onMouseDown(g){const y=f.current;g.button===0&&a!=="click"&&(Cs(y)&&u||(n&&l&&(!(o.current.openEvent&&d)||o.current.openEvent.type==="mousedown")?r(!1,g.nativeEvent,"click"):(g.preventDefault(),r(!0,g.nativeEvent,"click"))))},onClick(g){const y=f.current;if(a==="mousedown"&&f.current){f.current=void 0;return}Cs(y)&&u||(n&&l&&(!(o.current.openEvent&&d)||o.current.openEvent.type==="click")?r(!1,g.nativeEvent,"click"):r(!0,g.nativeEvent,"click"))},onKeyDown(g){f.current=void 0,!(g.defaultPrevented||!c||Gs(g))&&(g.key===" "&&!Xs(s)&&(g.preventDefault(),h.current=!0),g.key==="Enter"&&r(!(n&&l),g.nativeEvent,"click"))},onKeyUp(g){g.defaultPrevented||!c||Gs(g)||Xs(s)||g.key===" "&&h.current&&(h.current=!1,r(!(n&&l),g.nativeEvent,"click"))}}),[o,s,a,u,c,r,n,d,l]);return p.useMemo(()=>i?{reference:m}:{},[i,m])}const Ph={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},Rh={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},_s=e=>{var t,n;return{escapeKey:typeof e=="boolean"?e:(t=e==null?void 0:e.escapeKey)!=null?t:!1,outsidePress:typeof e=="boolean"?e:(n=e==null?void 0:e.outsidePress)!=null?n:!0}};function Pl(e,t){t===void 0&&(t={});const{open:n,onOpenChange:r,elements:o,dataRef:s}=e,{enabled:i=!0,escapeKey:a=!0,outsidePress:l=!0,outsidePressEvent:u="pointerdown",referencePress:c=!1,referencePressEvent:d="pointerdown",ancestorScroll:f=!1,bubbles:h,capture:m}=t,g=ar(),y=Se(typeof l=="function"?l:()=>!1),v=typeof l=="function"?y:l,b=p.useRef(!1),x=p.useRef(!1),{escapeKey:k,outsidePress:w}=_s(h),{escapeKey:$,outsidePress:R}=_s(m),A=p.useRef(!1),D=Se(M=>{var S;if(!n||!i||!a||M.key!=="Escape"||A.current)return;const B=(S=s.current.floatingContext)==null?void 0:S.nodeId,P=g?jt(g.nodesRef.current,B):[];if(!k&&(M.stopPropagation(),P.length>0)){let I=!0;if(P.forEach(L=>{var Y;if((Y=L.context)!=null&&Y.open&&!L.context.dataRef.current.__escapeKeyBubbles){I=!1;return}}),!I)return}r(!1,Yd(M)?M.nativeEvent:M,"escape-key")}),T=Se(M=>{var S;const B=()=>{var P;D(M),(P=gt(M))==null||P.removeEventListener("keydown",B)};(S=gt(M))==null||S.addEventListener("keydown",B)}),V=Se(M=>{var S;const B=b.current;b.current=!1;const P=x.current;if(x.current=!1,u==="click"&&P||B||typeof v=="function"&&!v(M))return;const I=gt(M),L="["+sn("inert")+"]",Y=je(o.floating).querySelectorAll(L);let Z=ue(I)?I:null;for(;Z&&!Xe(Z);){const Q=_e(Z);if(Xe(Q)||!ue(Q))break;Z=Q}if(Y.length&&ue(I)&&!Zd(I)&&!Ce(I,o.floating)&&Array.from(Y).every(Q=>!Ce(Z,Q)))return;if(fe(I)&&H){const Q=Xe(I),te=Ve(I),de=/auto|scroll/,W=Q||de.test(te.overflowX),_=Q||de.test(te.overflowY),K=W&&I.clientWidth>0&&I.scrollWidth>I.clientWidth,G=_&&I.clientHeight>0&&I.scrollHeight>I.clientHeight,ie=te.direction==="rtl",ce=G&&(ie?M.offsetX<=I.offsetWidth-I.clientWidth:M.offsetX>I.clientWidth),J=K&&M.offsetY>I.clientHeight;if(ce||J)return}const xe=(S=s.current.floatingContext)==null?void 0:S.nodeId,he=g&&jt(g.nodesRef.current,xe).some(Q=>{var te;return Er(M,(te=Q.context)==null?void 0:te.elements.floating)});if(Er(M,o.floating)||Er(M,o.domReference)||he)return;const ve=g?jt(g.nodesRef.current,xe):[];if(ve.length>0){let Q=!0;if(ve.forEach(te=>{var de;if((de=te.context)!=null&&de.open&&!te.context.dataRef.current.__outsidePressBubbles){Q=!1;return}}),!Q)return}r(!1,M,"outside-press")}),j=Se(M=>{var S;const B=()=>{var P;V(M),(P=gt(M))==null||P.removeEventListener(u,B)};(S=gt(M))==null||S.addEventListener(u,B)});p.useEffect(()=>{if(!n||!i)return;s.current.__escapeKeyBubbles=k,s.current.__outsidePressBubbles=w;let M=-1;function S(Y){r(!1,Y,"ancestor-scroll")}function B(){window.clearTimeout(M),A.current=!0}function P(){M=window.setTimeout(()=>{A.current=!1},or()?5:0)}const I=je(o.floating);a&&(I.addEventListener("keydown",$?T:D,$),I.addEventListener("compositionstart",B),I.addEventListener("compositionend",P)),v&&I.addEventListener(u,R?j:V,R);let L=[];return f&&(ue(o.domReference)&&(L=tt(o.domReference)),ue(o.floating)&&(L=L.concat(tt(o.floating))),!ue(o.reference)&&o.reference&&o.reference.contextElement&&(L=L.concat(tt(o.reference.contextElement)))),L=L.filter(Y=>{var Z;return Y!==((Z=I.defaultView)==null?void 0:Z.visualViewport)}),L.forEach(Y=>{Y.addEventListener("scroll",S,{passive:!0})}),()=>{a&&(I.removeEventListener("keydown",$?T:D,$),I.removeEventListener("compositionstart",B),I.removeEventListener("compositionend",P)),v&&I.removeEventListener(u,R?j:V,R),L.forEach(Y=>{Y.removeEventListener("scroll",S)}),window.clearTimeout(M)}},[s,o,a,v,u,n,r,f,i,k,w,D,$,T,V,R,j]),p.useEffect(()=>{b.current=!1},[v,u]);const E=p.useMemo(()=>({onKeyDown:D,...c&&{[Ph[d]]:M=>{r(!1,M.nativeEvent,"reference-press")},...d!=="click"&&{onClick(M){r(!1,M.nativeEvent,"reference-press")}}}}),[D,r,c,d]),H=p.useMemo(()=>({onKeyDown:D,onMouseDown(){x.current=!0},onMouseUp(){x.current=!0},[Rh[u]]:()=>{b.current=!0}}),[D,u]);return p.useMemo(()=>i?{reference:E,floating:H}:{},[i,E,H])}function Ah(e){const{open:t=!1,onOpenChange:n,elements:r}=e,o=Mo(),s=p.useRef({}),[i]=p.useState(()=>ph()),a=Do()!=null,[l,u]=p.useState(r.reference),c=Se((h,m,g)=>{s.current.openEvent=h?m:void 0,i.emit("openchange",{open:h,event:m,reason:g,nested:a}),n==null||n(h,m,g)}),d=p.useMemo(()=>({setPositionReference:u}),[]),f=p.useMemo(()=>({reference:l||r.reference||null,floating:r.floating||null,domReference:r.reference}),[l,r.reference,r.floating]);return p.useMemo(()=>({dataRef:s,open:t,onOpenChange:c,elements:f,events:i,floatingId:o,refs:d}),[t,c,f,i,o,d])}function Rl(e){e===void 0&&(e={});const{nodeId:t}=e,n=Ah({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,o=r.elements,[s,i]=p.useState(null),[a,l]=p.useState(null),u=(o==null?void 0:o.domReference)||s,c=p.useRef(null),d=ar();re(()=>{u&&(c.current=u)},[u]);const f=nh({...e,elements:{...o,...a&&{reference:a}}}),h=p.useCallback(b=>{const x=ue(b)?{getBoundingClientRect:()=>b.getBoundingClientRect(),contextElement:b}:b;l(x),f.refs.setReference(x)},[f.refs]),m=p.useCallback(b=>{(ue(b)||b===null)&&(c.current=b,i(b)),(ue(f.refs.reference.current)||f.refs.reference.current===null||b!==null&&!ue(b))&&f.refs.setReference(b)},[f.refs]),g=p.useMemo(()=>({...f.refs,setReference:m,setPositionReference:h,domReference:c}),[f.refs,m,h]),y=p.useMemo(()=>({...f.elements,domReference:u}),[f.elements,u]),v=p.useMemo(()=>({...f,...r,refs:g,elements:y,nodeId:t}),[f,g,y,t,r]);return re(()=>{r.dataRef.current.floatingContext=v;const b=d==null?void 0:d.nodesRef.current.find(x=>x.id===t);b&&(b.context=v)}),p.useMemo(()=>({...f,context:v,refs:g,elements:y}),[f,g,y,v])}const Ys="active",Zs="selected";function Ar(e,t,n){const r=new Map,o=n==="item";let s=e;if(o&&e){const{[Ys]:i,[Zs]:a,...l}=e;s=l}return{...n==="floating"&&{tabIndex:-1,[no]:""},...s,...t.map(i=>{const a=i?i[n]:null;return typeof a=="function"?e?a(e):null:a}).concat(e).reduce((i,a)=>(a&&Object.entries(a).forEach(l=>{let[u,c]=l;if(!(o&&[Ys,Zs].includes(u)))if(u.indexOf("on")===0){if(r.has(u)||r.set(u,[]),typeof c=="function"){var d;(d=r.get(u))==null||d.push(c),i[u]=function(){for(var f,h=arguments.length,m=new Array(h),g=0;g<h;g++)m[g]=arguments[g];return(f=r.get(u))==null?void 0:f.map(y=>y(...m)).find(y=>y!==void 0)}}}else i[u]=c}),i),{})}}function Al(e){e===void 0&&(e=[]);const t=e.map(a=>a==null?void 0:a.reference),n=e.map(a=>a==null?void 0:a.floating),r=e.map(a=>a==null?void 0:a.item),o=p.useCallback(a=>Ar(a,e,"reference"),t),s=p.useCallback(a=>Ar(a,e,"floating"),n),i=p.useCallback(a=>Ar(a,e,"item"),r);return p.useMemo(()=>({getReferenceProps:o,getFloatingProps:s,getItemProps:i}),[o,s,i])}const Mh="Escape";function cr(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function Mr(e,t){return cr(t,e===Ao||e===vn,e===nt||e===rt)}function Dr(e,t,n){return cr(t,e===vn,n?e===nt:e===rt)||e==="Enter"||e===" "||e===""}function Js(e,t,n){return cr(t,n?e===nt:e===rt,e===vn)}function Qs(e,t,n,r){const o=n?e===rt:e===nt,s=e===Ao;return t==="both"||t==="horizontal"&&r&&r>1?e===Mh:cr(t,o,s)}function Ml(e,t){const{open:n,onOpenChange:r,elements:o}=e,{listRef:s,activeIndex:i,onNavigate:a=()=>{},enabled:l=!0,selectedIndex:u=null,allowEscape:c=!1,loop:d=!1,nested:f=!1,rtl:h=!1,virtual:m=!1,focusItemOnOpen:g="auto",focusItemOnHover:y=!0,openOnArrowKeyDown:v=!0,disabledIndices:b=void 0,orientation:x="vertical",cols:k=1,scrollItemIntoView:w=!0,virtualItemRef:$,itemSizes:R,dense:A=!1}=t,D=ro(o.floating),T=Fe(D),V=Do(),j=ar();re(()=>{e.dataRef.current.orientation=x},[e,x]);const E=Se(()=>{a(S.current===-1?null:S.current)}),H=Ya(o.domReference),M=p.useRef(g),S=p.useRef(u!=null?u:-1),B=p.useRef(null),P=p.useRef(!0),I=p.useRef(E),L=p.useRef(!!o.floating),Y=p.useRef(n),Z=p.useRef(!1),xe=p.useRef(!1),he=Fe(b),ve=Fe(n),Q=Fe(w),te=Fe(u),[de,W]=p.useState(),[_,K]=p.useState(),G=Se(()=>{function q(O){m?(W(O.id),j==null||j.events.emit("virtualfocus",O),$&&($.current=O)):ht(O,{sync:Z.current,preventScroll:!0})}const U=s.current[S.current];U&&q(U),(Z.current?O=>O():requestAnimationFrame)(()=>{const O=s.current[S.current]||U;if(!O)return;U||q(O);const ne=Q.current;ne&&ce&&(xe.current||!P.current)&&(O.scrollIntoView==null||O.scrollIntoView(typeof ne=="boolean"?{block:"nearest",inline:"nearest"}:ne))})});re(()=>{l&&(n&&o.floating?M.current&&u!=null&&(xe.current=!0,S.current=u,E()):L.current&&(S.current=-1,I.current()))},[l,n,o.floating,u,E]),re(()=>{if(l&&n&&o.floating)if(i==null){if(Z.current=!1,te.current!=null)return;if(L.current&&(S.current=-1,G()),(!Y.current||!L.current)&&M.current&&(B.current!=null||M.current===!0&&B.current==null)){let q=0;const U=()=>{s.current[0]==null?(q<2&&(q?requestAnimationFrame:queueMicrotask)(U),q++):(S.current=B.current==null||Dr(B.current,x,h)||f?Pr(s,he.current):Ls(s,he.current),B.current=null,E())};U()}}else Zt(s,i)||(S.current=i,G(),xe.current=!1)},[l,n,o.floating,i,te,f,s,x,h,E,G,he]),re(()=>{var q;if(!l||o.floating||!j||m||!L.current)return;const U=j.nodesRef.current,O=(q=U.find(Te=>Te.id===V))==null||(q=q.context)==null?void 0:q.elements.floating,ne=Ge(je(o.floating)),ut=U.some(Te=>Te.context&&Ce(Te.context.elements.floating,ne));O&&!ut&&P.current&&O.focus({preventScroll:!0})},[l,o.floating,j,V,m]),re(()=>{if(!l||!j||!m||V)return;function q(U){K(U.id),$&&($.current=U)}return j.events.on("virtualfocus",q),()=>{j.events.off("virtualfocus",q)}},[l,j,m,V,$]),re(()=>{I.current=E,Y.current=n,L.current=!!o.floating}),re(()=>{n||(B.current=null)},[n]);const ie=i!=null,ce=p.useMemo(()=>{function q(U){if(!n)return;const O=s.current.indexOf(U);O!==-1&&S.current!==O&&(S.current=O,E())}return{onFocus(U){let{currentTarget:O}=U;Z.current=!0,q(O)},onClick:U=>{let{currentTarget:O}=U;return O.focus({preventScroll:!0})},...y&&{onMouseMove(U){let{currentTarget:O}=U;Z.current=!0,xe.current=!1,q(O)},onPointerLeave(U){let{pointerType:O}=U;if(!(!P.current||O==="touch")&&(Z.current=!0,S.current=-1,E(),!m)){var ne;(ne=T.current)==null||ne.focus({preventScroll:!0})}}}}},[n,T,y,s,E,m]),J=Se(q=>{if(P.current=!1,Z.current=!0,q.which===229||!ve.current&&q.currentTarget===T.current)return;if(f&&Qs(q.key,x,h,k)){be(q),r(!1,q.nativeEvent,"list-navigation"),fe(o.domReference)&&(m?j==null||j.events.emit("virtualfocus",o.domReference):o.domReference.focus());return}const U=S.current,O=Pr(s,b),ne=Ls(s,b);if(H||(q.key==="Home"&&(be(q),S.current=O,E()),q.key==="End"&&(be(q),S.current=ne,E())),k>1){const ut=R||Array.from({length:s.current.length},()=>({width:1,height:1})),Te=ah(ut,k,A),wr=Te.findIndex(Le=>Le!=null&&!Vn(s.current,Le,b)),wn=Te.reduce((Le,Ze,Kt)=>Ze!=null&&!Vn(s.current,Ze,b)?Kt:Le,-1),Ht=Te[ih({current:Te.map(Le=>Le!=null?s.current[Le]:null)},{event:q,orientation:x,loop:d,rtl:h,cols:k,disabledIndices:uh([...b||s.current.map((Le,Ze)=>Vn(s.current,Ze)?Ze:void 0),void 0],Te),minIndex:wr,maxIndex:wn,prevIndex:lh(S.current>ne?O:S.current,ut,Te,k,q.key===vn?"bl":q.key===(h?nt:rt)?"tr":"tl"),stopEvent:!0})];if(Ht!=null&&(S.current=Ht,E()),x==="both")return}if(Mr(q.key,x)){if(be(q),n&&!m&&Ge(q.currentTarget.ownerDocument)===q.currentTarget){S.current=Dr(q.key,x,h)?O:ne,E();return}Dr(q.key,x,h)?d?S.current=U>=ne?c&&U!==s.current.length?-1:O:we(s,{startingIndex:U,disabledIndices:b}):S.current=Math.min(ne,we(s,{startingIndex:U,disabledIndices:b})):d?S.current=U<=O?c&&U!==-1?s.current.length:ne:we(s,{startingIndex:U,decrement:!0,disabledIndices:b}):S.current=Math.max(O,we(s,{startingIndex:U,decrement:!0,disabledIndices:b})),Zt(s,S.current)&&(S.current=-1),E()}}),$e=p.useMemo(()=>m&&n&&ie&&{"aria-activedescendant":_||de},[m,n,ie,_,de]),pe=p.useMemo(()=>({"aria-orientation":x==="both"?void 0:x,...H?{}:$e,onKeyDown:J,onPointerMove(){P.current=!0}}),[$e,J,x,H]),Ne=p.useMemo(()=>{function q(O){g==="auto"&&Ga(O.nativeEvent)&&(M.current=!0)}function U(O){M.current=g,g==="auto"&&Xa(O.nativeEvent)&&(M.current=!0)}return{...$e,onKeyDown(O){var ne;P.current=!1;const ut=O.key.startsWith("Arrow"),Te=["Home","End"].includes(O.key),wr=ut||Te,wn=j==null||(ne=j.nodesRef.current.find(St=>St.id===V))==null||(ne=ne.context)==null||(ne=ne.dataRef)==null?void 0:ne.current.orientation,Ht=Js(O.key,x,h),Le=Qs(O.key,x,h,k),Ze=Js(O.key,wn,h),Kt=Mr(O.key,x),Uc=(f?Ze:Kt)||O.key==="Enter"||O.key.trim()==="";if(m&&n){const St=j==null?void 0:j.nodesRef.current.find(kn=>kn.parentId==null),Je=j&&St?vh(j.nodesRef.current,St.id):null;if(wr&&Je&&$){const kn=new KeyboardEvent("keydown",{key:O.key,bubbles:!0});if(Ht||Le){var ys,bs;const qc=((ys=Je.context)==null?void 0:ys.elements.domReference)===O.currentTarget,$s=Le&&!qc?(bs=Je.context)==null?void 0:bs.elements.domReference:Ht?s.current.find(ws=>(ws==null?void 0:ws.id)===de):null;$s&&(be(O),$s.dispatchEvent(kn),K(void 0))}if((Kt||Te)&&Je.context&&Je.context.open&&Je.parentId&&O.currentTarget!==Je.context.elements.domReference){var xs;be(O),(xs=Je.context.elements.domReference)==null||xs.dispatchEvent(kn);return}}return J(O)}if(!(!n&&!v&&ut)){if(Uc){const St=Mr(O.key,wn);B.current=f&&St?null:O.key}if(f){Ze&&(be(O),n?(S.current=Pr(s,he.current),E()):r(!0,O.nativeEvent,"list-navigation"));return}Kt&&(u!=null&&(S.current=u),be(O),!n&&v?r(!0,O.nativeEvent,"list-navigation"):J(O),n&&E())}},onFocus(){n&&!m&&(S.current=-1,E())},onPointerDown:U,onPointerEnter:U,onMouseDown:q,onClick:q}},[de,$e,k,J,he,g,s,f,E,r,n,v,x,V,h,u,j,m,$]);return p.useMemo(()=>l?{reference:Ne,floating:pe,item:ce}:{},[l,Ne,pe,ce])}const Dh=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);function Dl(e,t){var n;t===void 0&&(t={});const{open:r,floatingId:o}=e,{enabled:s=!0,role:i="dialog"}=t,a=(n=Dh.get(i))!=null?n:i,l=Mo(),u=Do()!=null,c=p.useMemo(()=>a==="tooltip"||i==="label"?{["aria-"+(i==="label"?"labelledby":"describedby")]:r?o:void 0}:{"aria-expanded":r?"true":"false","aria-haspopup":a==="alertdialog"?"dialog":a,"aria-controls":r?o:void 0,...a==="listbox"&&{role:"combobox"},...a==="menu"&&{id:l},...a==="menu"&&u&&{role:"menuitem"},...i==="select"&&{"aria-autocomplete":"none"},...i==="combobox"&&{"aria-autocomplete":"list"}},[a,o,u,r,l,i]),d=p.useMemo(()=>{const h={id:o,...a&&{role:a}};return a==="tooltip"||i==="label"?h:{...h,...a==="menu"&&{"aria-labelledby":l}}},[a,o,l,i]),f=p.useCallback(h=>{let{active:m,selected:g}=h;const y={role:"option",...m&&{id:o+"-option"}};switch(i){case"select":return{...y,"aria-selected":m&&g};case"combobox":return{...y,...m&&{"aria-selected":!0}}}return{}},[o,i]);return p.useMemo(()=>s?{reference:c,floating:d,item:f}:{},[s,c,d,f])}function Ih(e,t){const[n,r]=p.useState(e);return e&&!n&&r(!0),p.useEffect(()=>{if(!e&&n){const o=setTimeout(()=>r(!1),t);return()=>clearTimeout(o)}},[e,n,t]),n}function jh(e,t){t===void 0&&(t={});const{open:n,elements:{floating:r}}=e,{duration:o=250}=t,s=(typeof o=="number"?o:o.close)||0,[i,a]=p.useState("unmounted"),l=Ih(n,s);return!l&&i==="close"&&a("unmounted"),re(()=>{if(r){if(n){a("initial");const u=requestAnimationFrame(()=>{a("open")});return()=>{cancelAnimationFrame(u)}}a("close")}},[n,r]),{isMounted:l,status:i}}function Il(e,t){var n;const{open:r,dataRef:o}=e,{listRef:s,activeIndex:i,onMatch:a,onTypingChange:l,enabled:u=!0,findMatch:c=null,resetMs:d=750,ignoreKeys:f=[],selectedIndex:h=null}=t,m=p.useRef(-1),g=p.useRef(""),y=p.useRef((n=h!=null?h:i)!=null?n:-1),v=p.useRef(null),b=Se(a),x=Se(l),k=Fe(c),w=Fe(f);re(()=>{r&&(Fs(m),v.current=null,g.current="")},[r]),re(()=>{if(r&&g.current===""){var T;y.current=(T=h!=null?h:i)!=null?T:-1}},[r,h,i]);const $=Se(T=>{T?o.current.typing||(o.current.typing=T,x(T)):o.current.typing&&(o.current.typing=T,x(T))}),R=Se(T=>{function V(M,S,B){const P=k.current?k.current(S,B):S.find(I=>(I==null?void 0:I.toLocaleLowerCase().indexOf(B.toLocaleLowerCase()))===0);return P?M.indexOf(P):-1}const j=s.current;if(g.current.length>0&&g.current[0]!==" "&&(V(j,j,g.current)===-1?$(!1):T.key===" "&&be(T)),j==null||w.current.includes(T.key)||T.key.length!==1||T.ctrlKey||T.metaKey||T.altKey)return;r&&T.key!==" "&&(be(T),$(!0)),j.every(M=>{var S,B;return M?((S=M[0])==null?void 0:S.toLocaleLowerCase())!==((B=M[1])==null?void 0:B.toLocaleLowerCase()):!0})&&g.current===T.key&&(g.current="",y.current=v.current),g.current+=T.key,Fs(m),m.current=window.setTimeout(()=>{g.current="",y.current=v.current,$(!1)},d);const E=y.current,H=V(j,[...j.slice((E||0)+1),...j.slice(0,(E||0)+1)],g.current);H!==-1?(b(H),v.current=H):T.key!==" "&&(g.current="",$(!1))}),A=p.useMemo(()=>({onKeyDown:R}),[R]),D=p.useMemo(()=>({onKeyDown:R,onKeyUp(T){T.key===" "&&$(!1)}}),[R,$]);return p.useMemo(()=>u?{reference:A,floating:D}:{},[u,A,D])}const jl=({children:e,context:t,...n})=>{const[r,o]=p.useState(null);return p.useEffect(()=>{const s=t.elements.domReference;if(s){const i=s.closest("dialog");if(i)return o(i)}return o(void 0),()=>{o(null)}},[t.elements.domReference]),C.jsx(kh,{...n,root:r,children:e})};/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vh=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]],Lh=Me("Search",Vh),Oh=({open:e,onOpenChange:t,placement:n="bottom",offset:r=4})=>{const[o,s]=p.useState(!1),i=e!=null?e:o,a=p.useCallback((u,c,d)=>{s(u),t==null||t(u,c,d)},[t]),l=Rl({placement:n,open:i,onOpenChange:a,whileElementsMounted:(u,c,d)=>cl(u,c,d,{layoutShift:!1}),middleware:[pl({padding:8}),hl({padding:8}),fl(r),ml({apply({rects:u,elements:c,availableHeight:d}){c.floating.style.setProperty("--popover-max-height","".concat(d,"px")),c.floating.style.setProperty("--popover-width","".concat(u.reference.width,"px"))},padding:4}),rh()]});return p.useMemo(()=>({open:i,setOpen:a,...l}),[i,a,l])},Vl=p.createContext(null),Io=()=>{const e=p.use(Vl);if(e==null)throw new Error("Popover components must be wrapped in <Popover />");return e},Nh=({ref:e,children:t,asChild:n=!1,className:r,...o})=>{const s=Io(),i=n?He:"button",a=Wt([s.refs.setReference,e]);return C.jsx(i,{ref:a,type:n?void 0:"button",className:se(!n&&"disabled:opacity-40",r),"data-state":s.open?"open":"closed",...s.getReferenceProps(o),children:t})},Fh=({ref:e,style:t,className:n,children:r,...o})=>{var h,m;var s;const{context:i,refs:a,getFloatingProps:l,modal:u}=Io(),c=Wt([a.setFloating,e]),{isMounted:d,status:f}=jh(i,{duration:150});return d?C.jsx(jl,{context:i,children:C.jsx(Tl,{context:i,modal:u,children:C.jsx("div",{"data-state":["open","initial"].includes(f)?"open":"closed","data-side":i.placement.split("-")[0],...l({ref:c,className:se("border-border-soft bg-background-highlight text-foreground z-50 w-72 overflow-auto rounded-xl border p-3 outline-none","max-h-(--popover-max-height) origin-(--popover-transform-origin) transition duration-300 ease-out","data-[state=closed]:data-[side=bottom]:-translate-y-2 data-[state=closed]:data-[side=left]:translate-x-2 data-[state=closed]:data-[side=right]:-translate-x-2 data-[state=closed]:data-[side=top]:translate-y-2","data-[state=closed]:scale-95 data-[state=closed]:opacity-0 data-[state=closed]:duration-150","data-[state=open]:translate-x-0 data-[state=open]:translate-y-0 data-[state=open]:scale-100",n),style:{position:i.strategy,top:(h=i.y)!=null?h:0,left:(m=i.x)!=null?m:0,"--popover-transform-origin":Bh(i.placement),visibility:(s=i.middlewareData.hide)!=null&&s.referenceHidden?"hidden":"visible",...t},...o}),children:r})})}):null},Bh=e=>{switch(e){case"top":return"bottom";case"bottom":return"top";case"left":return"right";case"right":return"left";case"top-start":return"bottom left";case"top-end":return"bottom right";case"bottom-start":return"top left";case"bottom-end":return"top right";case"left-start":return"right top";case"left-end":return"right bottom";case"right-start":return"left top";case"right-end":return"left bottom"}},zh=({className:e,...t})=>C.jsxs("div",{className:"border-border-soft relative mb-1 flex items-center rounded-t-lg border-b bg-transparent",children:[C.jsx(Lh,{className:"text-foreground absolute left-4 size-4 shrink-0"}),C.jsx("input",{className:se("placeholder:text-foreground/30 h-10 w-full border-0 bg-transparent p-4 pl-10 text-base font-medium transition-colors outline-none focus:ring-0",e),...t})]}),Wh=({className:e,orientation:t="horizontal",decorative:n,...r})=>{const o=n?{role:"none"}:{"aria-orientation":t==="vertical"?t:void 0,role:"separator"};return C.jsx("div",{className:se("bg-border-soft shrink-0",t==="horizontal"?"h-px w-full":"h-full w-px",e),...o,...r})},Ll=e=>{const t=p.useRef(e);return p.useEffect(()=>{e&&(t.current=e)},[e]),p.useCallback((...n)=>{t.current&&t.current(...n)},[])},Ol=p.createContext(null),Nl=()=>{const e=p.use(Ol);if(e==null)throw new Error("Dropdown components must be wrapped in <Dropdown />");return e},Ab=({children:e,modal:t=!0,placement:n="bottom-start",...r})=>{const o=Oh({placement:n,...r}),[s,i]=p.useState(null),a=p.useRef([]),[l,u]=p.useState({}),c=p.useRef([]),[d,f]=p.useState(null);p.useEffect(()=>{c.current=Object.values(l).map($=>$.label)},[l]);const h=p.useCallback($=>(u(R=>({...R,[$.id]:$})),()=>{u(R=>(delete R[$.id],R))}),[]),m=Cl(o.context),g=Pl(o.context),y=Dl(o.context),v=Ml(o.context,{listRef:a,activeIndex:s,onNavigate:i,virtual:!!d||void 0}),b=Il(o.context,{enabled:!d,listRef:c,activeIndex:s,onMatch:i}),x=Al([m,g,y,v,b]),k=p.useMemo(()=>({...o,...x,modal:t}),[o,x,t]),w=p.useMemo(()=>({elementsRef:a,labelsRef:c,highlightedIndex:s,setHighlightedIndex:i,searchInputRef:d,setSearchInputRef:f,getItemProps:x.getItemProps,registerItem:h,items:l}),[a,c,s,d,x,h,l]);return C.jsx(Vl,{value:k,children:C.jsx(Ol,{value:w,children:e})})},Mb=Nh,Db=({children:e,className:t,...n})=>{const{elementsRef:r}=Nl();return C.jsx(Fh,{className:se("flex flex-col items-stretch p-0",t),...n,children:C.jsx(yl,{elementsRef:r,children:e})})},Ib=({ref:e,children:t,className:n,disabled:r,onClick:o,onSelect:s,onKeyDown:i,asChild:a,...l})=>{const u=p.useId(),c=p.useRef(null),{registerItem:d,highlightedIndex:f,getItemProps:h,items:m,searchInputRef:g,elementsRef:y}=Nl(),v=Io(),b=Ll(s),{ref:x,index:k}=bl(),w=Wt([x,e,c]),$=f===k,R=a?He:"button";p.useLayoutEffect(()=>{var T;const V=(T=c.current)==null?void 0:T.textContent;return V?d({id:u,label:V,onSelect:b}):void 0},[d,u,b]);const A=T=>{b==null||b(T),o==null||o(T),T.defaultPrevented||v.setOpen(!1)},D=T=>{var V,j,E;if(T.key==="Enter"&&f!==null){const H=(V=y.current[f])==null?void 0:V.dataset.itemId;H&&((E=(j=m[H])==null?void 0:j.onSelect)==null||E.call(j,T))}else g&&g.focus();i==null||i(T)};return C.jsx(R,{ref:w,"data-item-id":u,"data-highlighted":$||void 0,tabIndex:$?0:-1,disabled:r||void 0,"data-disabled":r||void 0,className:se("data-highlighted:bg-foreground/5 text-foreground/80 relative mx-1 flex cursor-pointer items-center gap-1.5 rounded-lg px-3 py-1.5 text-sm outline-none select-none first:mt-1 last:mb-1 data-disabled:pointer-events-none data-disabled:opacity-50",n),...h({...l,onKeyDown:D,onClick:A}),children:t})};p.createContext(null);const jb=({className:e,...t})=>C.jsx(Wh,{className:se("my-1",e),...t}),Fl=tr({base:["transition","font-primary","w-full border py-1 text-base","focus-visible:border-focus-hard focus-visible:ring-focus text-foreground placeholder:text-foreground/40 data-invalid:border-negative data-invalid:focus-visible:border-negative data-invalid:focus-visible:ring-negative/20 data-invalid:text-negative focus:outline-none focus-visible:ring-4 disabled:cursor-not-allowed disabled:opacity-50 data-invalid:hover:border-red-600","pl-[calc(var(--input-x-padding)+var(--prefix-width,0px))]","pr-[calc(var(--input-x-padding)+var(--suffix-width,0px))]"],variants:{variant:{default:"border-border-soft bg-background-highlight hover:border-border"},size:{sm:"h-8 rounded-lg text-xs [--input-x-padding:--spacing(3)]",md:"h-10 rounded-xl text-sm [--input-x-padding:--spacing(4)]",lg:"h-12 rounded-xl text-base [--input-x-padding:--spacing(5)]",xl:"h-14 rounded-2xl text-xl [--input-x-padding:--spacing(6)]"}},defaultVariants:{variant:"default",size:"md"}}),Vb=({className:e,invalid:t,variant:n,id:r,size:o="md",...s})=>{var u;const i=p.use(Uh),a=Ua(),l=t||!!(a!=null&&a["aria-errormessage"])||void 0;return C.jsx("input",{id:r!=null?r:a==null?void 0:a.id,"aria-errormessage":a==null?void 0:a["aria-errormessage"],"aria-describedby":a==null?void 0:a["aria-describedby"],"aria-labelledby":a==null?void 0:a["aria-labelledby"],"aria-invalid":l,"data-invalid":l,className:se(Fl({variant:n,size:(u=i==null?void 0:i.size)!=null?u:o}),e),...s})},Uh=p.createContext({prefixWidth:0,suffixWidth:0,setPrefixWidth:()=>{},setSuffixWidth:()=>{}});/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qh=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]];Me("Calendar",qh);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hh=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Kh=Me("ChevronDown",Hh),Gh=e=>e?e instanceof HTMLElement?e:typeof e=="string"?document.querySelector(e):e&&"current"in e&&e.current||null:document.body,Ir=new WeakMap,Pn=new WeakMap,Xh=(e,t)=>{const n=p.useRef(null);p.useEffect(()=>{n.current=Gh(t)},[t]),p.useEffect(()=>{const r=n.current;if(!r||!e)return;const o=Pn.get(r)||0;o===0&&Ir.set(r,{overflow:r.style.overflow,scrollbarGutter:r.style.scrollbarGutter});const s=o+1;return Pn.set(r,s),Object.assign(r.style,{overflow:"hidden",scrollbarGutter:"stable"}),()=>{const i=Pn.get(r)||0,a=Math.max(0,i-1);if(Pn.set(r,a),a===0){const l=Ir.get(r)||{overflow:"",scrollbarGutter:""};Object.assign(r.style,l),Ir.delete(r)}}},[e])};p.createContext(null);const ei={"emphasized-accelerate":[.3,0,.8,.15],"emphasized-decelerate":[.05,.7,.1,1]},jo=typeof window<"u",Gn={current:null},Vo={current:!1};function Bl(){if(Vo.current=!0,!!jo)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Gn.current=e.matches;e.addListener(t),t()}else Gn.current=!1}function _h(){!Vo.current&&Bl();const[e]=p.useState(Gn.current);return e}function Lo(e){let t;return()=>(t===void 0&&(t=e()),t)}const Yh=Lo(()=>window.ScrollTimeline!==void 0);let Zh=class{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(o=>{if(Yh()&&o.attachTimeline)return o.attachTimeline(t);if(typeof n=="function")return n(o)});return()=>{r.forEach((o,s)=>{o&&o(),this.animations[s].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}},zl=class extends Zh{then(t,n){return Promise.all(this.animations).then(t).catch(n)}};const ze=e=>e*1e3,We=e=>e/1e3,Wl=2e4;function Oo(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<2e4;)t+=n,r=e.next(t);return t>=2e4?1/0:t}const wt=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Ul=(e,t,n=10)=>{let r="";const o=Math.max(Math.round(t/n),2);for(let s=0;s<o;s++)r+=e(wt(0,o-1,s))+", ";return"linear(".concat(r.substring(0,r.length-2),")")},Ye=(e,t,n)=>n>t?t:n<e?e:n;function ql(e,t){return t?e*(1e3/t):0}const Jh=5;function Hl(e,t,n){const r=Math.max(t-Jh,0);return ql(n-e(r),t-r)}const le={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Ae=e=>e;let Kl=Ae;const ti=.001;function Qh({duration:e=le.duration,bounce:t=le.bounce,velocity:n=le.velocity,mass:r=le.mass}){let o,s,i=1-t;i=Ye(le.minDamping,le.maxDamping,i),e=Ye(le.minDuration,le.maxDuration,We(e)),i<1?(o=u=>{const c=u*i,d=c*e,f=c-n,h=oo(u,i),m=Math.exp(-d);return ti-f/h*m},s=u=>{const c=u*i*e,d=c*n+n,f=Math.pow(i,2)*Math.pow(u,2)*e,h=Math.exp(-c),m=oo(Math.pow(u,2),i);return(-o(u)+ti>0?-1:1)*((d-f)*h)/m}):(o=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-.001+c*d},s=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const a=5/e,l=tp(o,s,a);if(e=ze(e),isNaN(l))return{stiffness:le.stiffness,damping:le.damping,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:i*2*Math.sqrt(r*u),duration:e}}}const ep=12;function tp(e,t,n){let r=n;for(let o=1;o<ep;o++)r=r-e(r)/t(r);return r}function oo(e,t){return e*Math.sqrt(1-t*t)}const np=["duration","bounce"],rp=["stiffness","damping","mass"];function ni(e,t){return t.some(n=>e[n]!==void 0)}function op(e){let t={velocity:le.velocity,stiffness:le.stiffness,damping:le.damping,mass:le.mass,isResolvedFromDuration:!1,...e};if(!ni(e,rp)&&ni(e,np))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),o=r*r,s=2*Ye(.05,1,1-(e.bounce||0))*Math.sqrt(o);t={...t,mass:le.mass,stiffness:o,damping:s}}else{const n=Qh(e);t={...t,...n,mass:le.mass},t.isResolvedFromDuration=!0}return t}function No(e=le.visualDuration,t=le.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:o}=n;const s=n.keyframes[0],i=n.keyframes[n.keyframes.length-1],a={done:!1,value:s},{stiffness:l,damping:u,mass:c,duration:d,velocity:f,isResolvedFromDuration:h}=op({...n,velocity:-We(n.velocity||0)}),m=f||0,g=u/(2*Math.sqrt(l*c)),y=i-s,v=We(Math.sqrt(l/c)),b=Math.abs(y)<5;r||(r=b?le.restSpeed.granular:le.restSpeed.default),o||(o=b?le.restDelta.granular:le.restDelta.default);let x;if(g<1){const w=oo(v,g);x=$=>{const R=Math.exp(-g*v*$);return i-R*((m+g*v*y)/w*Math.sin(w*$)+y*Math.cos(w*$))}}else if(g===1)x=w=>i-Math.exp(-v*w)*(y+(m+v*y)*w);else{const w=v*Math.sqrt(g*g-1);x=$=>{const R=Math.exp(-g*v*$),A=Math.min(w*$,300);return i-R*((m+g*v*y)*Math.sinh(A)+w*y*Math.cosh(A))/w}}const k={calculatedDuration:h&&d||null,next:w=>{const $=x(w);if(h)a.done=w>=d;else{let R=0;g<1&&(R=w===0?ze(m):Hl(x,w,$));const A=Math.abs(R)<=r,D=Math.abs(i-$)<=o;a.done=A&&D}return a.value=a.done?i:$,a},toString:()=>{const w=Math.min(Oo(k),Wl),$=Ul(R=>k.next(w*R).value,w,30);return w+"ms "+$}};return k}function sp(e,t=100,n){const r=n({...e,keyframes:[0,t]}),o=Math.min(Oo(r),Wl);return{type:"keyframes",ease:s=>r.next(o*s).value/t,duration:We(o)}}function dr(e){return typeof e=="function"}const ip=(e,t,n)=>{const r=t-e;return((n-e)%r+r)%r+e},Gl=e=>Array.isArray(e)&&typeof e[0]!="number";function Xl(e,t){return Gl(e)?e[ip(0,e.length,t)]:e}const oe=(e,t,n)=>e+(t-e)*n;function _l(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const o=wt(0,t,r);e.push(oe(n,1,o))}}function Yl(e){const t=[0];return _l(t,e.length-1),t}const ge=e=>!!(e&&e.getVelocity);function Zl(e,t,n){var r;if(e instanceof EventTarget)return[e];if(typeof e=="string"){let o=document;const s=(r=n==null?void 0:n[e])!==null&&r!==void 0?r:o.querySelectorAll(e);return s?Array.from(s):[]}return Array.from(e)}function Fo(e){return typeof e=="object"&&!Array.isArray(e)}function Jl(e,t,n,r){return typeof e=="string"&&Fo(t)?Zl(e,n,r):e instanceof NodeList?Array.from(e):Array.isArray(e)?e:[e]}function ap(e,t,n){return e*(t+1)}function ri(e,t,n,r){var o;return typeof t=="number"?t:t.startsWith("-")||t.startsWith("+")?Math.max(0,e+parseFloat(t)):t==="<"?n:(o=r.get(t))!==null&&o!==void 0?o:e}function Bo(e,t){e.indexOf(t)===-1&&e.push(t)}function fr(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}function lp(e,t,n){for(let r=0;r<e.length;r++){const o=e[r];o.at>t&&o.at<n&&(fr(e,o),r--)}}function up(e,t,n,r,o,s){lp(e,o,s);for(let i=0;i<t.length;i++)e.push({value:t[i],at:oe(o,s,r[i]),easing:Xl(n,i)})}function cp(e,t){for(let n=0;n<e.length;n++)e[n]=e[n]/(t+1)}function dp(e,t){return e.at===t.at?e.value===null?1:t.value===null?-1:0:e.at-t.at}const fp="easeInOut";function hp(e,{defaultTransition:t={},...n}={},r,o){const s=t.duration||.3,i=new Map,a=new Map,l={},u=new Map;let c=0,d=0,f=0;for(let h=0;h<e.length;h++){const m=e[h];if(typeof m=="string"){u.set(m,d);continue}else if(!Array.isArray(m)){u.set(m.name,ri(d,m.at,c,u));continue}let[g,y,v={}]=m;v.at!==void 0&&(d=ri(d,v.at,c,u));let b=0;const x=(k,w,$,R=0,A=0)=>{const D=pp(k),{delay:T=0,times:V=Yl(D),type:j="keyframes",repeat:E,repeatType:H,repeatDelay:M=0,...S}=w;let{ease:B=t.ease||"easeOut",duration:P}=w;const I=typeof T=="function"?T(R,A):T,L=D.length,Y=dr(j)?j:o==null?void 0:o[j];if(L<=2&&Y){let ve=100;if(L===2&&vp(D)){const de=D[1]-D[0];ve=Math.abs(de)}const Q={...S};P!==void 0&&(Q.duration=ze(P));const te=sp(Q,ve,Y);B=te.ease,P=te.duration}P!=null||(P=s);const Z=d+I;V.length===1&&V[0]===0&&(V[1]=1);const xe=V.length-D.length;if(xe>0&&_l(V,xe),D.length===1&&D.unshift(null),E){P=ap(P,E);const ve=[...D],Q=[...V];B=Array.isArray(B)?[...B]:[B];const te=[...B];for(let de=0;de<E;de++){D.push(...ve);for(let W=0;W<ve.length;W++)V.push(Q[W]+(de+1)),B.push(W===0?"linear":Xl(te,W-1))}cp(V,E)}const he=Z+P;up($,D,B,V,Z,he),b=Math.max(I+P,b),f=Math.max(he,f)};if(ge(g)){const k=oi(g,a);x(y,v,si("default",k))}else{const k=Jl(g,y,r,l),w=k.length;for(let $=0;$<w;$++){y=y,v=v;const R=k[$],A=oi(R,a);for(const D in y)x(y[D],mp(v,D),si(D,A),$,w)}}c=d,d+=b}return a.forEach((h,m)=>{for(const g in h){const y=h[g];y.sort(dp);const v=[],b=[],x=[];for(let w=0;w<y.length;w++){const{at:$,value:R,easing:A}=y[w];v.push(R),b.push(wt(0,f,$)),x.push(A||"easeOut")}b[0]!==0&&(b.unshift(0),v.unshift(v[0]),x.unshift(fp)),b[b.length-1]!==1&&(b.push(1),v.push(null)),i.has(m)||i.set(m,{keyframes:{},transition:{}});const k=i.get(m);k.keyframes[g]=v,k.transition[g]={...t,duration:f,ease:x,times:b,...n}}}),i}function oi(e,t){return!t.has(e)&&t.set(e,{}),t.get(e)}function si(e,t){return t[e]||(t[e]=[]),t[e]}function pp(e){return Array.isArray(e)?e:[e]}function mp(e,t){return e&&e[t]?{...e,...e[t]}:{...e}}const gp=e=>typeof e=="number",vp=e=>e.every(gp),an=new WeakMap;function zo(e,t){return e?e[t]||e.default||e:void 0}const Rn=["read","resolveKeyframes","update","preRender","render","postRender"];function yp(e,t){let n=new Set,r=new Set,o=!1,s=!1;const i=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function l(c){i.has(c)&&(u.schedule(c),e()),c(a)}const u={schedule:(c,d=!1,f=!1)=>{const h=f&&o?n:r;return d&&i.add(c),h.has(c)||h.add(c),c},cancel:c=>{r.delete(c),i.delete(c)},process:c=>{if(a=c,o){s=!0;return}o=!0,[n,r]=[r,n],n.forEach(l),n.clear(),o=!1,s&&(s=!1,u.process(c))}};return u}const bp=40;function Ql(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},s=()=>n=!0,i=Rn.reduce((g,y)=>(g[y]=yp(s),g),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:d,postRender:f}=i,h=()=>{const g=performance.now();n=!1,o.delta=r?1e3/60:Math.max(Math.min(g-o.timestamp,bp),1),o.timestamp=g,o.isProcessing=!0,a.process(o),l.process(o),u.process(o),c.process(o),d.process(o),f.process(o),o.isProcessing=!1,n&&t&&(r=!1,e(h))},m=()=>{n=!0,r=!0,o.isProcessing||e(h)};return{schedule:Rn.reduce((g,y)=>{const v=i[y];return g[y]=(b,x=!1,k=!1)=>(n||m(),v.schedule(b,x,k)),g},{}),cancel:g=>{for(let y=0;y<Rn.length;y++)i[Rn[y]].cancel(g)},state:o,steps:i}}const{schedule:ee,cancel:st,state:ye,steps:jr}=Ql(typeof requestAnimationFrame<"u"?requestAnimationFrame:Ae,!0),Ut=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],kt=new Set(Ut),eu=new Set(["width","height","top","left","right","bottom",...Ut]);let Wo=class{constructor(){this.subscriptions=[]}add(t){return Bo(this.subscriptions,t),()=>fr(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let s=0;s<o;s++){const i=this.subscriptions[s];i&&i(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}};const xp={useManualTiming:!1};let Ln;function $p(){Ln=void 0}const Ue={now:()=>(Ln===void 0&&Ue.set(ye.isProcessing||xp.useManualTiming?ye.timestamp:performance.now()),Ln),set:e=>{Ln=e,queueMicrotask($p)}},ii=30,wp=e=>!isNaN(parseFloat(e));let kp=class{constructor(t,n={}){this.version="12.5.0",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,o=!0)=>{const s=Ue.now();this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),o&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=Ue.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=wp(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Wo);const r=this.events[t].add(n);return t==="change"?()=>{r(),ee.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Ue.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>ii)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,ii);return ql(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}};function ln(e,t){return new kp(e,t)}const so=e=>Array.isArray(e),Sp=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Ep=e=>so(e)?e[e.length-1]||0:e;function ai(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Uo(e,t,n,r){if(typeof t=="function"){const[o,s]=ai(r);t=t(n!==void 0?n:e.custom,o,s)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[o,s]=ai(r);t=t(n!==void 0?n:e.custom,o,s)}return t}function un(e,t,n){const r=e.getProps();return Uo(r,t,n!==void 0?n:r.custom,e)}function Tp(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,ln(n))}function Cp(e,t){const n=un(e,t);let{transitionEnd:r={},transition:o={},...s}=n||{};s={...s,...r};for(const i in s){const a=Ep(s[i]);Tp(e,i,a)}}function Pp(e){return!!(ge(e)&&e.add)}function io(e,t){const n=e.getValue("willChange");if(Pp(n))return n.add(t)}const qo=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Rp="framerAppearId",tu="data-"+qo(Rp);function nu(e){return e.props[tu]}function li(e,t){e.timeline=t,e.onfinish=null}const Ho=e=>Array.isArray(e)&&typeof e[0]=="number",Ap={linearEasing:void 0};function Mp(e,t){const n=Lo(e);return()=>{var r;return(r=Ap[t])!==null&&r!==void 0?r:n()}}const Xn=Mp(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing");function ru(e){return!!(typeof e=="function"&&Xn()||!e||typeof e=="string"&&(e in ao||Xn())||Ho(e)||Array.isArray(e)&&e.every(ru))}const Xt=([e,t,n,r])=>"cubic-bezier(".concat(e,", ").concat(t,", ").concat(n,", ").concat(r,")"),ao={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Xt([0,.65,.55,1]),circOut:Xt([.55,0,1,.45]),backIn:Xt([.31,.01,.66,-.59]),backOut:Xt([.33,1.53,.69,.99])};function ou(e,t){if(e)return typeof e=="function"&&Xn()?Ul(e,t):Ho(e)?Xt(e):Array.isArray(e)?e.map(n=>ou(n,t)||ao.easeOut):ao[e]}const su=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Dp=1e-7,Ip=12;function jp(e,t,n,r,o){let s,i,a=0;do i=t+(n-t)/2,s=su(i,r,o)-e,s>0?n=i:t=i;while(Math.abs(s)>Dp&&++a<Ip);return i}function yn(e,t,n,r){if(e===t&&n===r)return Ae;const o=s=>jp(s,0,1,e,n);return s=>s===0||s===1?s:su(o(s),t,r)}const iu=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,au=e=>t=>1-e(1-t),lu=yn(.33,1.53,.69,.99),Ko=au(lu),uu=iu(Ko),cu=e=>(e*=2)<1?.5*Ko(e):.5*(2-Math.pow(2,-10*(e-1))),Go=e=>1-Math.sin(Math.acos(e)),du=au(Go),fu=iu(Go),hu=e=>/^0[^.\s]+$/u.test(e);function Vp(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||hu(e):!0}const qt={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},cn={...qt,transform:e=>Ye(0,1,e)},An={...qt,default:1},Qt=e=>Math.round(e*1e5)/1e5,Xo=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Lp(e){return e==null}const Op=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,_o=(e,t)=>n=>!!(typeof n=="string"&&Op.test(n)&&n.startsWith(e)||t&&!Lp(n)&&Object.prototype.hasOwnProperty.call(n,t)),pu=(e,t,n)=>r=>{if(typeof r!="string")return r;const[o,s,i,a]=r.match(Xo);return{[e]:parseFloat(o),[t]:parseFloat(s),[n]:parseFloat(i),alpha:a!==void 0?parseFloat(a):1}},Np=e=>Ye(0,255,e),Vr={...qt,transform:e=>Math.round(Np(e))},vt={test:_o("rgb","red"),parse:pu("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Vr.transform(e)+", "+Vr.transform(t)+", "+Vr.transform(n)+", "+Qt(cn.transform(r))+")"};function Fp(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const lo={test:_o("#"),parse:Fp,transform:vt.transform},bn=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),et=bn("deg"),qe=bn("%"),z=bn("px"),Bp=bn("vh"),zp=bn("vw"),ui={...qe,parse:e=>qe.parse(e)/100,transform:e=>qe.transform(e*100)},Pt={test:_o("hsl","hue"),parse:pu("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+qe.transform(Qt(t))+", "+qe.transform(Qt(n))+", "+Qt(cn.transform(r))+")"},ke={test:e=>vt.test(e)||lo.test(e)||Pt.test(e),parse:e=>vt.test(e)?vt.parse(e):Pt.test(e)?Pt.parse(e):lo.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?vt.transform(e):Pt.transform(e)},Wp=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Up(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(Xo))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Wp))===null||n===void 0?void 0:n.length)||0)>0}const mu="number",gu="color",qp="var",Hp="var(",ci="${}",Kp=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function dn(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},o=[];let s=0;const i=t.replace(Kp,a=>(ke.test(a)?(r.color.push(s),o.push(gu),n.push(ke.parse(a))):a.startsWith(Hp)?(r.var.push(s),o.push(qp),n.push(a)):(r.number.push(s),o.push(mu),n.push(parseFloat(a))),++s,ci)).split(ci);return{values:n,split:i,indexes:r,types:o}}function vu(e){return dn(e).values}function yu(e){const{split:t,types:n}=dn(e),r=t.length;return o=>{let s="";for(let i=0;i<r;i++)if(s+=t[i],o[i]!==void 0){const a=n[i];a===mu?s+=Qt(o[i]):a===gu?s+=ke.transform(o[i]):s+=o[i]}return s}}const Gp=e=>typeof e=="number"?0:e;function Xp(e){const t=vu(e);return yu(e)(t.map(Gp))}const it={test:Up,parse:vu,createTransformer:yu,getAnimatableNone:Xp},_p=new Set(["brightness","contrast","saturate","opacity"]);function Yp(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Xo)||[];if(!r)return e;const o=n.replace(r,"");let s=_p.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+o+")"}const Zp=/\b([a-z-]*)\(.*?\)/gu,uo={...it,getAnimatableNone:e=>{const t=e.match(Zp);return t?t.map(Yp).join(" "):e}},Jp={borderWidth:z,borderTopWidth:z,borderRightWidth:z,borderBottomWidth:z,borderLeftWidth:z,borderRadius:z,radius:z,borderTopLeftRadius:z,borderTopRightRadius:z,borderBottomRightRadius:z,borderBottomLeftRadius:z,width:z,maxWidth:z,height:z,maxHeight:z,top:z,right:z,bottom:z,left:z,padding:z,paddingTop:z,paddingRight:z,paddingBottom:z,paddingLeft:z,margin:z,marginTop:z,marginRight:z,marginBottom:z,marginLeft:z,backgroundPositionX:z,backgroundPositionY:z},Qp={rotate:et,rotateX:et,rotateY:et,rotateZ:et,scale:An,scaleX:An,scaleY:An,scaleZ:An,skew:et,skewX:et,skewY:et,distance:z,translateX:z,translateY:z,translateZ:z,x:z,y:z,z,perspective:z,transformPerspective:z,opacity:cn,originX:ui,originY:ui,originZ:z},di={...qt,transform:Math.round},Yo={...Jp,...Qp,zIndex:di,size:z,fillOpacity:cn,strokeOpacity:cn,numOctaves:di},em={...Yo,color:ke,backgroundColor:ke,outlineColor:ke,fill:ke,stroke:ke,borderColor:ke,borderTopColor:ke,borderRightColor:ke,borderBottomColor:ke,borderLeftColor:ke,filter:uo,WebkitFilter:uo},bu=e=>em[e];function xu(e,t){let n=bu(e);return n!==uo&&(n=it),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const tm=new Set(["auto","none","0"]);function nm(e,t,n){let r=0,o;for(;r<e.length&&!o;){const s=e[r];typeof s=="string"&&!tm.has(s)&&dn(s).values.length&&(o=e[r]),r++}if(o&&n)for(const s of t)e[s]=xu(n,o)}const yt=e=>e*180/Math.PI,co=e=>{const t=yt(Math.atan2(e[1],e[0]));return fo(t)},rm={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:co,rotateZ:co,skewX:e=>yt(Math.atan(e[1])),skewY:e=>yt(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},fo=e=>(e=e%360,e<0&&(e+=360),e),fi=co,hi=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),pi=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),om={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:hi,scaleY:pi,scale:e=>(hi(e)+pi(e))/2,rotateX:e=>fo(yt(Math.atan2(e[6],e[5]))),rotateY:e=>fo(yt(Math.atan2(-e[2],e[0]))),rotateZ:fi,rotate:fi,skewX:e=>yt(Math.atan(e[4])),skewY:e=>yt(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function mi(e){return e.includes("scale")?1:0}function ho(e,t){if(!e||e==="none")return mi(t);const n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,o;if(n)r=om,o=n;else{const a=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=rm,o=a}if(!o)return mi(t);const s=r[t],i=o[1].split(",").map(im);return typeof s=="function"?s(i):i[s]}const sm=(e,t)=>{const{transform:n="none"}=getComputedStyle(e);return ho(n,t)};function im(e){return parseFloat(e.trim())}const gi=e=>e===qt||e===z,am=new Set(["x","y","z"]),lm=Ut.filter(e=>!am.has(e));function um(e){const t=[];return lm.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const Ot={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>ho(t,"x"),y:(e,{transform:t})=>ho(t,"y")};Ot.translateX=Ot.x;Ot.translateY=Ot.y;const bt=new Set;let po=!1,mo=!1;function $u(){if(mo){const e=Array.from(bt).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const o=um(r);o.length&&(n.set(r,o),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const o=n.get(r);o&&o.forEach(([s,i])=>{var a;(a=r.getValue(s))===null||a===void 0||a.set(i)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}mo=!1,po=!1,bt.forEach(e=>e.complete()),bt.clear()}function wu(){bt.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(mo=!0)})}function cm(){wu(),$u()}let Zo=class{constructor(t,n,r,o,s,i=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=o,this.element=s,this.isAsync=i}scheduleResolve(){this.isScheduled=!0,this.isAsync?(bt.add(this),po||(po=!0,ee.read(wu),ee.resolveKeyframes($u))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:o}=this;for(let s=0;s<t.length;s++)if(t[s]===null)if(s===0){const i=o==null?void 0:o.get(),a=t[t.length-1];if(i!==void 0)t[0]=i;else if(r&&n){const l=r.readValue(n,a);l!=null&&(t[0]=l)}t[0]===void 0&&(t[0]=a),o&&i===void 0&&o.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),bt.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,bt.delete(this))}resume(){this.isComplete||this.scheduleResolve()}};const ku=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Su=e=>t=>typeof t=="string"&&t.startsWith(e),Jo=Su("--"),dm=Su("var(--"),Qo=e=>dm(e)?fm.test(e.split("/*")[0].trim()):!1,fm=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,hm=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function pm(e){const t=hm.exec(e);if(!t)return[,];const[,n,r,o]=t;return["--".concat(n!=null?n:r),o]}function Eu(e,t,n=1){const[r,o]=pm(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const i=s.trim();return ku(i)?parseFloat(i):i}return Qo(o)?Eu(o,t,n+1):o}const Tu=e=>t=>t.test(e),mm={test:e=>e==="auto",parse:e=>e},Cu=[qt,z,qe,et,zp,Bp,mm],vi=e=>Cu.find(Tu(e));let Pu=class extends Zo{constructor(t,n,r,o,s){super(t,n,r,o,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),Qo(u))){const c=Eu(u,n.current);c!==void 0&&(t[l]=c),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!eu.has(r)||t.length!==2)return;const[o,s]=t,i=vi(o),a=vi(s);if(i!==a)if(gi(i)&&gi(a))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let o=0;o<t.length;o++)Vp(t[o])&&r.push(o);r.length&&nm(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ot[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const o=n[n.length-1];o!==void 0&&t.getValue(r,o).jump(o,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:o}=this;if(!n||!n.current)return;const s=n.getValue(r);s&&s.jump(this.measuredOrigin,!1);const i=o.length-1,a=o[i];o[i]=Ot[r](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}};const yi=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(it.test(e)||e==="0")&&!e.startsWith("url("));function gm(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function vm(e,t,n,r){const o=e[0];if(o===null)return!1;if(t==="display"||t==="visibility")return!0;const s=e[e.length-1],i=yi(o,t),a=yi(s,t);return!i||!a?!1:gm(e)||(n==="spring"||dr(n))&&r}const ym=e=>e!==null;function hr(e,{repeat:t,repeatType:n="loop"},r){const o=e.filter(ym),s=t&&n!=="loop"&&t%2===1?0:o.length-1;return!s||r===void 0?o[s]:r}const bm=40;let Ru=class{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:i="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Ue.now(),this.options={autoplay:t,delay:n,type:r,repeat:o,repeatDelay:s,repeatType:i,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>bm?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&cm(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=Ue.now(),this.hasAttemptedResolve=!0;const{name:r,type:o,velocity:s,delay:i,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!vm(t,r,o,s))if(i)this.options.duration=0;else{l&&l(hr(t,this.options,n)),a&&a(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear")}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}};function Lr(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function xm({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,s=0,i=0;if(!t)o=s=i=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;o=Lr(l,a,e+1/3),s=Lr(l,a,e),i=Lr(l,a,e-1/3)}return{red:Math.round(o*255),green:Math.round(s*255),blue:Math.round(i*255),alpha:r}}function _n(e,t){return n=>n>0?t:e}const Or=(e,t,n)=>{const r=e*e,o=n*(t*t-r)+r;return o<0?0:Math.sqrt(o)},$m=[lo,vt,Pt],wm=e=>$m.find(t=>t.test(e));function bi(e){const t=wm(e);if(!t)return!1;let n=t.parse(e);return t===Pt&&(n=xm(n)),n}const xi=(e,t)=>{const n=bi(e),r=bi(t);if(!n||!r)return _n(e,t);const o={...n};return s=>(o.red=Or(n.red,r.red,s),o.green=Or(n.green,r.green,s),o.blue=Or(n.blue,r.blue,s),o.alpha=oe(n.alpha,r.alpha,s),vt.transform(o))},km=(e,t)=>n=>t(e(n)),xn=(...e)=>e.reduce(km),go=new Set(["none","hidden"]);function Sm(e,t){return go.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function Em(e,t){return n=>oe(e,t,n)}function es(e){return typeof e=="number"?Em:typeof e=="string"?Qo(e)?_n:ke.test(e)?xi:Pm:Array.isArray(e)?Au:typeof e=="object"?ke.test(e)?xi:Tm:_n}function Au(e,t){const n=[...e],r=n.length,o=e.map((s,i)=>es(s)(s,t[i]));return s=>{for(let i=0;i<r;i++)n[i]=o[i](s);return n}}function Tm(e,t){const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=es(e[o])(e[o],t[o]));return o=>{for(const s in r)n[s]=r[s](o);return n}}function Cm(e,t){var n;const r=[],o={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const i=t.types[s],a=e.indexes[i][o[i]],l=(n=e.values[a])!==null&&n!==void 0?n:0;r[s]=l,o[i]++}return r}const Pm=(e,t)=>{const n=it.createTransformer(t),r=dn(e),o=dn(t);return r.indexes.var.length===o.indexes.var.length&&r.indexes.color.length===o.indexes.color.length&&r.indexes.number.length>=o.indexes.number.length?go.has(e)&&!o.values.length||go.has(t)&&!r.values.length?Sm(e,t):xn(Au(Cm(r,o),o.values),n):_n(e,t)};function Mu(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?oe(e,t,n):es(e)(e,t)}function $i({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:s=500,modifyTarget:i,min:a,max:l,restDelta:u=.5,restSpeed:c}){const d=e[0],f={done:!1,value:d},h=A=>a!==void 0&&A<a||l!==void 0&&A>l,m=A=>a===void 0?l:l===void 0||Math.abs(a-A)<Math.abs(l-A)?a:l;let g=n*t;const y=d+g,v=i===void 0?y:i(y);v!==y&&(g=v-d);const b=A=>-g*Math.exp(-A/r),x=A=>v+b(A),k=A=>{const D=b(A),T=x(A);f.done=Math.abs(D)<=u,f.value=f.done?v:T};let w,$;const R=A=>{h(f.value)&&(w=A,$=No({keyframes:[f.value,m(f.value)],velocity:Hl(x,A,f.value),damping:o,stiffness:s,restDelta:u,restSpeed:c}))};return R(0),{calculatedDuration:null,next:A=>{let D=!1;return!$&&w===void 0&&(D=!0,k(A),R(A)),w!==void 0&&A>=w?$.next(A-w):(!D&&k(A),f)}}}const Rm=yn(.42,0,1,1),Am=yn(0,0,.58,1),Du=yn(.42,0,.58,1),Mm={linear:Ae,easeIn:Rm,easeInOut:Du,easeOut:Am,circIn:Go,circInOut:fu,circOut:du,backIn:Ko,backInOut:uu,backOut:lu,anticipate:cu},wi=e=>{if(Ho(e)){Kl(e.length===4);const[t,n,r,o]=e;return yn(t,n,r,o)}else if(typeof e=="string")return Mm[e];return e};function Dm(e,t,n){const r=[],o=n||Mu,s=e.length-1;for(let i=0;i<s;i++){let a=o(e[i],e[i+1]);if(t){const l=Array.isArray(t)?t[i]||Ae:t;a=xn(l,a)}r.push(a)}return r}function Im(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const s=e.length;if(Kl(s===t.length),s===1)return()=>t[0];if(s===2&&t[0]===t[1])return()=>t[1];const i=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=Dm(t,r,o),l=a.length,u=c=>{if(i&&c<e[0])return t[0];let d=0;if(l>1)for(;d<e.length-2&&!(c<e[d+1]);d++);const f=wt(e[d],e[d+1],c);return a[d](f)};return n?c=>u(Ye(e[0],e[s-1],c)):u}function jm(e,t){return e.map(n=>n*t)}function Vm(e,t){return e.map(()=>t||Du).splice(0,e.length-1)}function Yn({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const o=Gl(r)?r.map(wi):wi(r),s={done:!1,value:t[0]},i=jm(n&&n.length===t.length?n:Yl(t),e),a=Im(i,t,{ease:Array.isArray(o)?o:Vm(t,o)});return{calculatedDuration:e,next:l=>(s.value=a(l),s.done=l>=e,s)}}const Lm=e=>{const t=({timestamp:n})=>e(n);return{start:()=>ee.update(t,!0),stop:()=>st(t),now:()=>ye.isProcessing?ye.timestamp:Ue.now()}},Om={decay:$i,inertia:$i,tween:Yn,keyframes:Yn,spring:No},Nm=e=>e/100;class ts extends Ru{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:r,element:o,keyframes:s}=this.options,i=(o==null?void 0:o.KeyframeResolver)||Zo,a=(l,u)=>this.onKeyframesResolved(l,u);this.resolver=new i(s,a,n,r,o),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:s,velocity:i=0}=this.options,a=dr(n)?n:Om[n]||Yn;let l,u;a!==Yn&&typeof t[0]!="number"&&(l=xn(Nm,Mu(t[0],t[1])),t=[0,100]);const c=a({...this.options,keyframes:t});s==="mirror"&&(u=a({...this.options,keyframes:[...t].reverse(),velocity:-i})),c.calculatedDuration===null&&(c.calculatedDuration=Oo(c));const{calculatedDuration:d}=c,f=d+o,h=f*(r+1)-o;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:d,resolvedDuration:f,totalDuration:h}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:A}=this.options;return{done:!0,value:A[A.length-1]}}const{finalKeyframe:o,generator:s,mirroredGenerator:i,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:u,totalDuration:c,resolvedDuration:d}=r;if(this.startTime===null)return s.next(0);const{delay:f,repeat:h,repeatType:m,repeatDelay:g,onUpdate:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const v=this.currentTime-f*(this.speed>=0?1:-1),b=this.speed>=0?v<0:v>c;this.currentTime=Math.max(v,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let x=this.currentTime,k=s;if(h){const A=Math.min(this.currentTime,c)/d;let D=Math.floor(A),T=A%1;!T&&A>=1&&(T=1),T===1&&D--,D=Math.min(D,h+1),D%2&&(m==="reverse"?(T=1-T,g&&(T-=g/d)):m==="mirror"&&(k=i)),x=Ye(0,1,T)*d}const w=b?{done:!1,value:l[0]}:k.next(x);a&&(w.value=a(w.value));let{done:$}=w;!b&&u!==null&&($=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const R=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&$);return R&&o!==void 0&&(w.value=hr(l,this.options,o)),y&&y(w.value),R&&this.finish(),w}get duration(){const{resolved:t}=this;return t?We(t.calculatedDuration):0}get time(){return We(this.currentTime)}set time(t){t=ze(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=We(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=Lm,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(s=>this.tick(s))),n&&n();const o=this.driver.now();this.holdTime!==null?this.startTime=o-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=o):this.startTime=r!=null?r:this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const Fm=new Set(["opacity","clipPath","filter","transform"]);function Bm(e,t,n,{delay:r=0,duration:o=300,repeat:s=0,repeatType:i="loop",ease:a="easeInOut",times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=ou(a,o);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:o,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:i==="reverse"?"alternate":"normal"})}const zm=Lo(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Zn=10,Wm=2e4;function Um(e){return dr(e.type)||e.type==="spring"||!ru(e.ease)}function qm(e,t){const n=new ts({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const o=[];let s=0;for(;!r.done&&s<Wm;)r=n.sample(s),o.push(r.value),s+=Zn;return{times:void 0,keyframes:o,duration:s-Zn,ease:"linear"}}const Iu={anticipate:cu,backInOut:uu,circInOut:fu};function Hm(e){return e in Iu}class ki extends Ru{constructor(t){super(t);const{name:n,motionValue:r,element:o,keyframes:s}=this.options;this.resolver=new Pu(s,(i,a)=>this.onKeyframesResolved(i,a),n,r,o),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:o,ease:s,type:i,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if(typeof s=="string"&&Xn()&&Hm(s)&&(s=Iu[s]),Um(this.options)){const{onComplete:d,onUpdate:f,motionValue:h,element:m,...g}=this.options,y=qm(t,g);t=y.keyframes,t.length===1&&(t[1]=t[0]),r=y.duration,o=y.times,s=y.ease,i="keyframes"}const c=Bm(a.owner.current,l,t,{...this.options,duration:r,times:o,ease:s});return c.startTime=u!=null?u:this.calcStartTime(),this.pendingTimeline?(li(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:d}=this.options;a.set(hr(t,this.options,n)),d&&d(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:o,type:i,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return We(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return We(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=ze(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return Ae;const{animation:r}=n;li(r,t)}return Ae}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:o,type:s,ease:i,times:a}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:d,element:f,...h}=this.options,m=new ts({...h,keyframes:r,duration:o,type:s,ease:i,times:a,isGenerator:!0}),g=ze(this.time);u.setWithVelocity(m.sample(g-Zn).value,m.sample(g).value,Zn)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:o,repeatType:s,damping:i,type:a}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=n.owner.getProps();return zm()&&r&&Fm.has(r)&&!l&&!u&&!o&&s!=="mirror"&&i!==0&&a!=="inertia"}}const Km={type:"spring",stiffness:500,damping:25,restSpeed:10},Gm=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Xm={type:"keyframes",duration:.8},_m={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Ym=(e,{keyframes:t})=>t.length>2?Xm:kt.has(e)?e.startsWith("scale")?Gm(t[1]):Km:_m;function Zm({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:s,repeatType:i,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const ns=(e,t,n,r={},o,s)=>i=>{const a=zo(r,e)||{},l=a.delay||r.delay||0;let{elapsed:u=0}=r;u=u-ze(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:f=>{t.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{i(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:s?void 0:o};Zm(a)||(c={...c,...Ym(e,c)}),c.duration&&(c.duration=ze(c.duration)),c.repeatDelay&&(c.repeatDelay=ze(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let d=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(d=!0)),c.allowFlatten=!a.type&&!a.ease,d&&!s&&t.get()!==void 0){const f=hr(c.keyframes,a);if(f!==void 0)return ee.update(()=>{c.onUpdate(f),c.onComplete()}),new zl([])}return!s&&ki.supports(c)?new ki(c):new ts(c)};function Jm({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function rs(e,t,{delay:n=0,transitionOverride:r,type:o}={}){var s;let{transition:i=e.getDefaultTransition(),transitionEnd:a,...l}=t;r&&(i=r);const u=[],c=o&&e.animationState&&e.animationState.getState()[o];for(const d in l){const f=e.getValue(d,(s=e.latestValues[d])!==null&&s!==void 0?s:null),h=l[d];if(h===void 0||c&&Jm(c,d))continue;const m={delay:n,...zo(i||{},d)};let g=!1;if(window.MotionHandoffAnimation){const v=nu(e);if(v){const b=window.MotionHandoffAnimation(v,d,ee);b!==null&&(m.startTime=b,g=!0)}}io(e,d),f.start(ns(d,f,h,e.shouldReduceMotion&&eu.has(d)?{type:!1}:m,e,g));const y=f.animation;y&&u.push(y)}return a&&Promise.all(u).then(()=>{ee.update(()=>{a&&Cp(e,a)})}),u}function ju(e){return e instanceof SVGElement&&e.tagName!=="svg"}const Si=()=>({translate:0,scale:1,origin:0,originPoint:0}),Rt=()=>({x:Si(),y:Si()}),Ei=()=>({min:0,max:0}),ae=()=>({x:Ei(),y:Ei()}),Ti={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Nt={};for(const e in Ti)Nt[e]={isEnabled:t=>Ti[e].some(n=>!!t[n])};const Qm=[...Cu,ke,it],eg=e=>Qm.find(Tu(e));function pr(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}function fn(e){return typeof e=="string"||Array.isArray(e)}const os=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ss=["initial",...os];function mr(e){return pr(e.animate)||ss.some(t=>fn(e[t]))}function Vu(e){return!!(mr(e)||e.variants)}function tg(e,t,n){for(const r in t){const o=t[r],s=n[r];if(ge(o))e.addValue(r,o);else if(ge(s))e.addValue(r,ln(o,{owner:e}));else if(s!==o)if(e.hasValue(r)){const i=e.getValue(r);i.liveStyle===!0?i.jump(o):i.hasAnimated||i.set(o)}else{const i=e.getStaticValue(r);e.addValue(r,ln(i!==void 0?i:o,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Ci=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Lu{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:o,blockInitialAnimation:s,visualState:i},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Zo,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const h=Ue.now();this.renderScheduledAt<h&&(this.renderScheduledAt=h,ee.render(this.render,!1,!0))};const{latestValues:l,renderState:u,onUpdate:c}=i;this.onUpdate=c,this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=mr(n),this.isVariantNode=Vu(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:d,...f}=this.scrapeMotionValuesFromProps(n,{},this);for(const h in f){const m=f[h];l[h]!==void 0&&ge(m)&&m.set(l[h],!1)}}mount(t){this.current=t,an.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Vo.current||Bl(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Gn.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),st(this.notifyUpdate),st(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=kt.has(t);r&&this.onBindTransform&&this.onBindTransform();const o=n.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&ee.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=n.on("renderRequest",this.scheduleRender);let i;window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{o(),s(),i&&i(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Nt){const n=Nt[t];if(!n)continue;const{isEnabled:r,Feature:o}=n;if(!this.features[t]&&o&&r(this.props)&&(this.features[t]=new o(this)),this.features[t]){const s=this.features[t];s.isMounted?s.update():(s.mount(),s.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ae()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Ci.length;r++){const o=Ci[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const s="on"+o,i=t[s];i&&(this.propEventSubscriptions[o]=this.on(o,i))}this.prevMotionValues=tg(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=ln(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let o=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return o!=null&&(typeof o=="string"&&(ku(o)||hu(o))?o=parseFloat(o):!eg(o)&&it.test(n)&&(o=xu(t,n)),this.setBaseTarget(t,ge(o)?o.get():o)),ge(o)?o.get():o}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let o;if(typeof r=="string"||typeof r=="object"){const i=Uo(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);i&&(o=i[t])}if(r&&o!==void 0)return o;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!ge(s)?s:this.initialValues[t]!==void 0&&o===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Wo),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}let Ou=class extends Lu{constructor(){super(...arguments),this.KeyframeResolver=Pu}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ge(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent="".concat(n))}))}};const Nu=(e,t)=>t&&typeof e=="number"?t.transform(e):e,ng={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rg=Ut.length;function og(e,t,n){let r="",o=!0;for(let s=0;s<rg;s++){const i=Ut[s],a=e[i];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(i.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=Nu(a,Yo[i]);if(!l){o=!1;const c=ng[i]||i;r+="".concat(c,"(").concat(u,") ")}n&&(t[i]=u)}}return r=r.trim(),n?r=n(t,o?"":r):o&&(r="none"),r}function is(e,t,n){const{style:r,vars:o,transformOrigin:s}=e;let i=!1,a=!1;for(const l in t){const u=t[l];if(kt.has(l)){i=!0;continue}else if(Jo(l)){o[l]=u;continue}else{const c=Nu(u,Yo[l]);l.startsWith("origin")?(a=!0,s[l]=c):r[l]=c}}if(t.transform||(i||n?r.transform=og(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=s;r.transformOrigin="".concat(l," ").concat(u," ").concat(c)}}const sg={offset:"stroke-dashoffset",array:"stroke-dasharray"},ig={offset:"strokeDashoffset",array:"strokeDasharray"};function ag(e,t,n=1,r=0,o=!0){e.pathLength=1;const s=o?sg:ig;e[s.offset]=z.transform(-r);const i=z.transform(t),a=z.transform(n);e[s.array]="".concat(i," ").concat(a)}function Pi(e,t,n){return typeof e=="string"?e:z.transform(t+n*e)}function lg(e,t,n){const r=Pi(t,e.x,e.width),o=Pi(n,e.y,e.height);return"".concat(r," ").concat(o)}function as(e,{attrX:t,attrY:n,attrScale:r,originX:o,originY:s,pathLength:i,pathSpacing:a=1,pathOffset:l=0,...u},c,d){if(is(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:h,dimensions:m}=e;f.transform&&(m&&(h.transform=f.transform),delete f.transform),m&&(o!==void 0||s!==void 0||h.transform)&&(h.transformOrigin=lg(m,o!==void 0?o:.5,s!==void 0?s:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),i!==void 0&&ag(f,i,a,l,!1)}const Fu=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),ls=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Bu(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch(n){t.dimensions={x:0,y:0,width:0,height:0}}}function zu(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const s in n)e.style.setProperty(s,n[s])}function Wu(e,t,n,r){zu(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(Fu.has(o)?o:qo(o),t.attrs[o])}const hn={};function ug(e){for(const t in e)hn[t]=e[t],Jo(t)&&(hn[t].isCSSVariable=!0)}function Uu(e,{layout:t,layoutId:n}){return kt.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!hn[e]||e==="opacity")}function us(e,t,n){var r;const{style:o}=e,s={};for(const i in o)(ge(o[i])||t.style&&ge(t.style[i])||Uu(i,e)||((r=n==null?void 0:n.getValue(i))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(s[i]=o[i]);return s}function qu(e,t,n){const r=us(e,t,n);for(const o in e)if(ge(e[o])||ge(t[o])){const s=Ut.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[s]=e[o]}return r}let Hu=class extends Ou{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ae,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&Bu(this.current,this.renderState)}}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(kt.has(n)){const r=bu(n);return r&&r.default||0}return n=Fu.has(n)?n:qo(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return qu(t,n,r)}onBindTransform(){this.current&&!this.renderState.dimensions&&ee.postRender(this.updateDimensions)}build(t,n,r){as(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,o){Wu(t,n,r,o)}mount(t){this.isSVGTag=ls(t.tagName),super.mount(t)}};function Ku({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function cg({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function dg(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Nr(e){return e===void 0||e===1}function vo({scale:e,scaleX:t,scaleY:n}){return!Nr(e)||!Nr(t)||!Nr(n)}function mt(e){return vo(e)||Gu(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Gu(e){return Ri(e.x)||Ri(e.y)}function Ri(e){return e&&e!=="0%"}function Jn(e,t,n){const r=e-n,o=t*r;return n+o}function Ai(e,t,n,r,o){return o!==void 0&&(e=Jn(e,o,r)),Jn(e,n,r)+t}function yo(e,t=0,n=1,r,o){e.min=Ai(e.min,t,n,r,o),e.max=Ai(e.max,t,n,r,o)}function Xu(e,{x:t,y:n}){yo(e.x,t.translate,t.scale,t.originPoint),yo(e.y,n.translate,n.scale,n.originPoint)}const Mi=.999999999999,Di=1.0000000000001;function fg(e,t,n,r=!1){const o=n.length;if(!o)return;t.x=t.y=1;let s,i;for(let a=0;a<o;a++){s=n[a],i=s.projectionDelta;const{visualElement:l}=s.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&Mt(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),i&&(t.x*=i.x.scale,t.y*=i.y.scale,Xu(e,i)),r&&mt(s.latestValues)&&Mt(e,s.latestValues))}t.x<Di&&t.x>Mi&&(t.x=1),t.y<Di&&t.y>Mi&&(t.y=1)}function At(e,t){e.min=e.min+t,e.max=e.max+t}function Ii(e,t,n,r,o=.5){const s=oe(e.min,e.max,o);yo(e,t,n,s,r)}function Mt(e,t){Ii(e.x,t.x,t.scaleX,t.scale,t.originX),Ii(e.y,t.y,t.scaleY,t.scale,t.originY)}function _u(e,t){return Ku(dg(e.getBoundingClientRect(),t))}function hg(e,t,n){const r=_u(e,n),{scroll:o}=t;return o&&(At(r.x,o.offset.x),At(r.y,o.offset.y)),r}function pg(e){return window.getComputedStyle(e)}let Yu=class extends Ou{constructor(){super(...arguments),this.type="html",this.renderInstance=zu}readValueFromInstance(t,n){if(kt.has(n))return sm(t,n);{const r=pg(t),o=(Jo(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:n}){return _u(t,n)}build(t,n,r){is(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return us(t,n,r)}};function mg(e,t){return e in t}let gg=class extends Lu{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,n){if(mg(n,t)){const r=t[n];if(typeof r=="string"||typeof r=="number")return r}}getBaseTargetFromProps(){}removeValueFromRenderState(t,n){delete n.output[t]}measureInstanceViewportBox(){return ae()}build(t,n){Object.assign(t.output,n)}renderInstance(t,{output:n}){Object.assign(t,n)}sortInstanceNodePosition(){return 0}};function vg(e){const t={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=ju(e)?new Hu(t):new Yu(t);n.mount(e),an.set(e,n)}function yg(e){const t={presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}},n=new gg(t);n.mount(e),an.set(e,n)}function Zu(e,t,n){const r=ge(e)?e:ln(e);return r.start(ns("",r,t,n)),r.animation}function bg(e,t){return ge(e)||typeof e=="number"||typeof e=="string"&&!Fo(t)}function Ju(e,t,n,r){const o=[];if(bg(e,t))o.push(Zu(e,Fo(t)&&t.default||t,n&&(n.default||n)));else{const s=Jl(e,t,r),i=s.length;for(let a=0;a<i;a++){const l=s[a],u=l instanceof Element?vg:yg;an.has(l)||u(l);const c=an.get(l),d={...n};"delay"in d&&typeof d.delay=="function"&&(d.delay=d.delay(a,i)),o.push(...rs(c,{...t,transition:d},{}))}}return o}function xg(e,t,n){const r=[];return hp(e,t,n,{spring:No}).forEach(({keyframes:o,transition:s},i)=>{r.push(...Ju(i,o,s))}),r}function $g(e){return Array.isArray(e)&&e.some(Array.isArray)}function wg(e){function t(n,r,o){let s=[];return $g(n)?s=xg(n,r,e):s=Ju(n,r,o,e),new zl(s)}return t}const kg=wg(),cs=p.createContext({});function ds(e){const t=p.useRef(null);return t.current===null&&(t.current=e()),t.current}const Qu=jo?p.useLayoutEffect:p.useEffect,gr=p.createContext(null),fs=p.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});let Sg=class extends p.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=n.offsetParent,o=r instanceof HTMLElement&&r.offsetWidth||0,s=this.props.sizeRef.current;s.height=n.offsetHeight||0,s.width=n.offsetWidth||0,s.top=n.offsetTop,s.left=n.offsetLeft,s.right=o-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}};function Eg({children:e,isPresent:t,anchorX:n}){const r=p.useId(),o=p.useRef(null),s=p.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:i}=p.useContext(fs);return p.useInsertionEffect(()=>{const{width:a,height:l,top:u,left:c,right:d}=s.current;if(t||!o.current||!a||!l)return;const f=n==="left"?"left: ".concat(c):"right: ".concat(d);o.current.dataset.motionPopId=r;const h=document.createElement("style");return i&&(h.nonce=i),document.head.appendChild(h),h.sheet&&h.sheet.insertRule('\n          [data-motion-pop-id="'.concat(r,'"] {\n            position: absolute !important;\n            width: ').concat(a,"px !important;\n            height: ").concat(l,"px !important;\n            ").concat(f,"px !important;\n            top: ").concat(u,"px !important;\n          }\n        ")),()=>{document.head.removeChild(h)}},[t]),C.jsx(Sg,{isPresent:t,childRef:o,sizeRef:s,children:p.cloneElement(e,{ref:o})})}const Tg=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:s,mode:i,anchorX:a})=>{const l=ds(Cg),u=p.useId(),c=p.useCallback(f=>{l.set(f,!0);for(const h of l.values())if(!h)return;r&&r()},[l,r]),d=p.useMemo(()=>({id:u,initial:t,isPresent:n,custom:o,onExitComplete:c,register:f=>(l.set(f,!1),()=>l.delete(f))}),s?[Math.random(),c]:[n,c]);return p.useMemo(()=>{l.forEach((f,h)=>l.set(h,!1))},[n]),p.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),i==="popLayout"&&(e=C.jsx(Eg,{isPresent:n,anchorX:a,children:e})),C.jsx(gr.Provider,{value:d,children:e})};function Cg(){return new Map}function ec(e=!0){const t=p.useContext(gr);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:o}=t,s=p.useId();p.useEffect(()=>{if(e)return o(s)},[e]);const i=p.useCallback(()=>e&&r&&r(s),[s,r,e]);return!n&&r?[!1,i]:[!0]}const Mn=e=>e.key||"";function ji(e){const t=[];return p.Children.forEach(e,n=>{p.isValidElement(n)&&t.push(n)}),t}const Pg=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:o=!0,mode:s="sync",propagate:i=!1,anchorX:a="left"})=>{const[l,u]=ec(i),c=p.useMemo(()=>ji(e),[e]),d=i&&!l?[]:c.map(Mn),f=p.useRef(!0),h=p.useRef(c),m=ds(()=>new Map),[g,y]=p.useState(c),[v,b]=p.useState(c);Qu(()=>{f.current=!1,h.current=c;for(let w=0;w<v.length;w++){const $=Mn(v[w]);d.includes($)?m.delete($):m.get($)!==!0&&m.set($,!1)}},[v,d.length,d.join("-")]);const x=[];if(c!==g){let w=[...c];for(let $=0;$<v.length;$++){const R=v[$],A=Mn(R);d.includes(A)||(w.splice($,0,R),x.push(R))}return s==="wait"&&x.length&&(w=x),b(ji(w)),y(c),null}const{forceRender:k}=p.useContext(cs);return C.jsx(C.Fragment,{children:v.map(w=>{const $=Mn(w),R=i&&!l?!1:c===v||d.includes($),A=()=>{if(m.has($))m.set($,!0);else return;let D=!0;m.forEach(T=>{T||(D=!1)}),D&&(k==null||k(),b(h.current),i&&(u==null||u()),r&&r())};return C.jsx(Tg,{isPresent:R,initial:!f.current||n?void 0:!1,custom:t,presenceAffectsLayout:o,mode:s,onExitComplete:R?void 0:A,anchorX:a,children:w},$)})})};function Rg(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,o)=>o==="create"?e:(t.has(o)||t.set(o,e(o)),t.get(o))})}function bo(e,t,n={}){var r;const o=un(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:s=e.getDefaultTransition()||{}}=o||{};n.transitionOverride&&(s=n.transitionOverride);const i=o?()=>Promise.all(rs(e,o,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:d,staggerDirection:f}=s;return Ag(e,t,c+u,d,f,n)}:()=>Promise.resolve(),{when:l}=s;if(l){const[u,c]=l==="beforeChildren"?[i,a]:[a,i];return u().then(()=>c())}else return Promise.all([i(),a(n.delay)])}function Ag(e,t,n=0,r=0,o=1,s){const i=[],a=(e.variantChildren.size-1)*r,l=o===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(Mg).forEach((u,c)=>{u.notify("AnimationStart",t),i.push(bo(u,t,{...s,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(i)}function Mg(e,t){return e.sortNodePosition(t)}function Dg(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(s=>bo(e,s,n));r=Promise.all(o)}else if(typeof t=="string")r=bo(e,t,n);else{const o=typeof t=="function"?un(e,t,n.custom):t;r=Promise.all(rs(e,o,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}function tc(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const Ig=ss.length;function nc(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?nc(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<Ig;n++){const r=ss[n],o=e.props[r];(fn(o)||o===!1)&&(t[r]=o)}return t}const jg=[...os].reverse(),Vg=os.length;function Lg(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Dg(e,n,r)))}function Og(e){let t=Lg(e),n=Vi(),r=!0;const o=l=>(u,c)=>{var d;const f=un(e,c,l==="exit"?(d=e.presenceContext)===null||d===void 0?void 0:d.custom:void 0);if(f){const{transition:h,transitionEnd:m,...g}=f;u={...u,...g,...m}}return u};function s(l){t=l(e)}function i(l){const{props:u}=e,c=nc(e.parent)||{},d=[],f=new Set;let h={},m=1/0;for(let y=0;y<Vg;y++){const v=jg[y],b=n[v],x=u[v]!==void 0?u[v]:c[v],k=fn(x),w=v===l?b.isActive:null;w===!1&&(m=y);let $=x===c[v]&&x!==u[v]&&k;if($&&r&&e.manuallyAnimateOnMount&&($=!1),b.protectedKeys={...h},!b.isActive&&w===null||!x&&!b.prevProp||pr(x)||typeof x=="boolean")continue;const R=Ng(b.prevProp,x);let A=R||v===l&&b.isActive&&!$&&k||y>m&&k,D=!1;const T=Array.isArray(x)?x:[x];let V=T.reduce(o(v),{});w===!1&&(V={});const{prevResolvedValues:j={}}=b,E={...j,...V},H=M=>{A=!0,f.has(M)&&(D=!0,f.delete(M)),b.needsAnimating[M]=!0;const S=e.getValue(M);S&&(S.liveStyle=!1)};for(const M in E){const S=V[M],B=j[M];if(h.hasOwnProperty(M))continue;let P=!1;so(S)&&so(B)?P=!tc(S,B):P=S!==B,P?S!=null?H(M):f.add(M):S!==void 0&&f.has(M)?H(M):b.protectedKeys[M]=!0}b.prevProp=x,b.prevResolvedValues=V,b.isActive&&(h={...h,...V}),r&&e.blockInitialAnimation&&(A=!1),A&&(!($&&R)||D)&&d.push(...T.map(M=>({animation:M,options:{type:v}})))}if(f.size){const y={};if(typeof u.initial!="boolean"){const v=un(e,Array.isArray(u.initial)?u.initial[0]:u.initial);v&&v.transition&&(y.transition=v.transition)}f.forEach(v=>{const b=e.getBaseTarget(v),x=e.getValue(v);x&&(x.liveStyle=!0),y[v]=b!=null?b:null}),d.push({animation:y})}let g=!!d.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(d):Promise.resolve()}function a(l,u){var c;if(n[l].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(f=>{var h;return(h=f.animationState)===null||h===void 0?void 0:h.setActive(l,u)}),n[l].isActive=u;const d=i(l);for(const f in n)n[f].protectedKeys={};return d}return{animateChanges:i,setActive:a,setAnimateFunction:s,getState:()=>n,reset:()=>{n=Vi(),r=!0}}}function Ng(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!tc(t,e):!1}function ft(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Vi(){return{animate:ft(!0),whileInView:ft(),whileHover:ft(),whileTap:ft(),whileDrag:ft(),whileFocus:ft(),exit:ft()}}let lt=class{constructor(t){this.isMounted=!1,this.node=t}update(){}},Fg=class extends lt{constructor(t){super(t),t.animationState||(t.animationState=Og(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();pr(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}},Bg=0,zg=class extends lt{constructor(){super(...arguments),this.id=Bg++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const o=this.node.animationState.setActive("exit",!t);n&&!t&&o.then(()=>{n(this.id)})}mount(){const{register:t,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),t&&(this.unmount=t(this.id))}unmount(){}};const Wg={animation:{Feature:Fg},exit:{Feature:zg}},Oe={x:!1,y:!1};function rc(){return Oe.x||Oe.y}function Ug(e){return e==="x"||e==="y"?Oe[e]?null:(Oe[e]=!0,()=>{Oe[e]=!1}):Oe.x||Oe.y?null:(Oe.x=Oe.y=!0,()=>{Oe.x=Oe.y=!1})}function pn(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const hs=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function $n(e){return{point:{x:e.pageX,y:e.pageY}}}const qg=e=>t=>hs(t)&&e(t,$n(t));function en(e,t,n,r){return pn(e,t,qg(n),r)}const oc=1e-4,Hg=1-oc,Kg=1+oc,sc=.01,Gg=0-sc,Xg=0+sc;function Ee(e){return e.max-e.min}function _g(e,t,n){return Math.abs(e-t)<=n}function Li(e,t,n,r=.5){e.origin=r,e.originPoint=oe(t.min,t.max,e.origin),e.scale=Ee(n)/Ee(t),e.translate=oe(n.min,n.max,e.origin)-e.originPoint,(e.scale>=Hg&&e.scale<=Kg||isNaN(e.scale))&&(e.scale=1),(e.translate>=Gg&&e.translate<=Xg||isNaN(e.translate))&&(e.translate=0)}function tn(e,t,n,r){Li(e.x,t.x,n.x,r?r.originX:void 0),Li(e.y,t.y,n.y,r?r.originY:void 0)}function Oi(e,t,n){e.min=n.min+t.min,e.max=e.min+Ee(t)}function Yg(e,t,n){Oi(e.x,t.x,n.x),Oi(e.y,t.y,n.y)}function Ni(e,t,n){e.min=t.min-n.min,e.max=e.min+Ee(t)}function nn(e,t,n){Ni(e.x,t.x,n.x),Ni(e.y,t.y,n.y)}function Ie(e){return[e("x"),e("y")]}const ic=({current:e})=>e?e.ownerDocument.defaultView:null;function Dt(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}const Fi=(e,t)=>Math.abs(e-t);function Zg(e,t){const n=Fi(e.x,t.x),r=Fi(e.y,t.y);return Math.sqrt(n**2+r**2)}let ac=class{constructor(t,n,{transformPagePoint:r,contextWindow:o,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=Br(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,h=Zg(d.offset,{x:0,y:0})>=3;if(!f&&!h)return;const{point:m}=d,{timestamp:g}=ye;this.history.push({...m,timestamp:g});const{onStart:y,onMove:v}=this.handlers;f||(y&&y(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),v&&v(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=Fr(f,this.transformPagePoint),ee.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:h,onSessionEnd:m,resumeAnimation:g}=this.handlers;if(this.dragSnapToOrigin&&g&&g(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const y=Br(d.type==="pointercancel"?this.lastMoveEventInfo:Fr(f,this.transformPagePoint),this.history);this.startEvent&&h&&h(d,y),m&&m(d,y)},!hs(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.contextWindow=o||window;const i=$n(t),a=Fr(i,this.transformPagePoint),{point:l}=a,{timestamp:u}=ye;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Br(a,this.history)),this.removeListeners=xn(en(this.contextWindow,"pointermove",this.handlePointerMove),en(this.contextWindow,"pointerup",this.handlePointerUp),en(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),st(this.updatePoint)}};function Fr(e,t){return t?{point:t(e.point)}:e}function Bi(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Br({point:e},t){return{point:e,delta:Bi(e,lc(t)),offset:Bi(e,Jg(t)),velocity:Qg(t,.1)}}function Jg(e){return e[0]}function lc(e){return e[e.length-1]}function Qg(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=lc(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>ze(t)));)n--;if(!r)return{x:0,y:0};const s=We(o.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const i={x:(o.x-r.x)/s,y:(o.y-r.y)/s};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function ev(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?oe(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?oe(n,e,r.max):Math.min(e,n)),e}function zi(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function tv(e,{top:t,left:n,bottom:r,right:o}){return{x:zi(e.x,n,o),y:zi(e.y,t,r)}}function Wi(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nv(e,t){return{x:Wi(e.x,t.x),y:Wi(e.y,t.y)}}function rv(e,t){let n=.5;const r=Ee(e),o=Ee(t);return o>r?n=wt(t.min,t.max-r,e.min):r>o&&(n=wt(e.min,e.max-o,t.min)),Ye(0,1,n)}function ov(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const xo=.35;function sv(e=xo){return e===!1?e=0:e===!0&&(e=xo),{x:Ui(e,"left","right"),y:Ui(e,"top","bottom")}}function Ui(e,t,n){return{min:qi(e,t),max:qi(e,n)}}function qi(e,t){return typeof e=="number"?e:e[t]||0}const iv=new WeakMap;class av{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ae(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const o=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor($n(c).point)},s=(c,d)=>{const{drag:f,dragPropagation:h,onDragStart:m}=this.getProps();if(f&&!h&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Ug(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ie(y=>{let v=this.getAxisMotionValue(y).get()||0;if(qe.test(v)){const{projection:b}=this.visualElement;if(b&&b.layout){const x=b.layout.layoutBox[y];x&&(v=Ee(x)*(parseFloat(v)/100))}}this.originPoint[y]=v}),m&&ee.postRender(()=>m(c,d)),io(this.visualElement,"transform");const{animationState:g}=this.visualElement;g&&g.setActive("whileDrag",!0)},i=(c,d)=>{const{dragPropagation:f,dragDirectionLock:h,onDirectionLock:m,onDrag:g}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:y}=d;if(h&&this.currentDirection===null){this.currentDirection=lv(y),this.currentDirection!==null&&m&&m(this.currentDirection);return}this.updateAxis("x",d.point,y),this.updateAxis("y",d.point,y),this.visualElement.render(),g&&g(c,d)},a=(c,d)=>this.stop(c,d),l=()=>Ie(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new ac(t,{onSessionStart:o,onStart:s,onMove:i,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:ic(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:o}=n;this.startAnimation(o);const{onDragEnd:s}=this.getProps();s&&ee.postRender(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!Dn(t,o,this.currentDirection))return;const s=this.getAxisMotionValue(t);let i=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(i=ev(i,this.constraints[t],this.elastic[t])),s.set(i)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,s=this.constraints;n&&Dt(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&o?this.constraints=tv(o.layoutBox,n):this.constraints=!1,this.elastic=sv(r),s!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&Ie(i=>{this.constraints!==!1&&this.getAxisMotionValue(i)&&(this.constraints[i]=ov(o.layoutBox[i],this.constraints[i]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Dt(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const s=hg(r,o.root,this.visualElement.getTransformPagePoint());let i=nv(o.layout.layoutBox,s);if(n){const a=n(cg(i));this.hasMutatedConstraints=!!a,a&&(i=Ku(a))}return i}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:s,dragSnapToOrigin:i,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Ie(c=>{if(!Dn(c,n,this.currentDirection))return;let d=l&&l[c]||{};i&&(d={min:0,max:0});const f=o?200:1e6,h=o?40:1e7,m={type:"inertia",velocity:r?t[c]:0,bounceStiffness:f,bounceDamping:h,timeConstant:750,restDelta:1,restSpeed:10,...s,...d};return this.startAxisValueAnimation(c,m)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return io(this.visualElement,t),r.start(ns(t,r,0,n,this.visualElement,!1))}stopAnimation(){Ie(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ie(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag".concat(t.toUpperCase()),r=this.visualElement.getProps();return r[n]||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Ie(n=>{const{drag:r}=this.getProps();if(!Dn(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,s=this.getAxisMotionValue(n);if(o&&o.layout){const{min:i,max:a}=o.layout.layoutBox[n];s.set(t[n]-oe(i,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Dt(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};Ie(i=>{const a=this.getAxisMotionValue(i);if(a&&this.constraints!==!1){const l=a.get();o[i]=rv({min:l,max:l},this.constraints[i])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ie(i=>{if(!Dn(i,t,null))return;const a=this.getAxisMotionValue(i),{min:l,max:u}=this.constraints[i];a.set(oe(l,u,o[i]))})}addListeners(){if(!this.visualElement.current)return;iv.set(this.visualElement,this);const t=this.visualElement.current,n=en(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();Dt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,s=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),ee.read(r);const i=pn(window,"resize",()=>this.scalePositionWithinConstraints()),a=o.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Ie(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=l[c].translate,d.set(d.get()+l[c].translate))}),this.visualElement.render())});return()=>{i(),n(),s(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:s=!1,dragElastic:i=xo,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:s,dragElastic:i,dragMomentum:a}}}function Dn(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function lv(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}let uv=class extends lt{constructor(t){super(t),this.removeGroupControls=Ae,this.removeListeners=Ae,this.controls=new av(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ae}unmount(){this.removeGroupControls(),this.removeListeners()}};const Hi=e=>(t,n)=>{e&&ee.postRender(()=>e(t,n))};let cv=class extends lt{constructor(){super(...arguments),this.removePointerDownListener=Ae}onPointerDown(t){this.session=new ac(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ic(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:Hi(t),onStart:Hi(n),onMove:r,onEnd:(s,i)=>{delete this.session,o&&ee.postRender(()=>o(s,i))}}}mount(){this.removePointerDownListener=en(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}};const{schedule:ps}=Ql(queueMicrotask,!1),uc=p.createContext({}),On={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Ki(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Gt={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(z.test(e))e=parseFloat(e);else return e;const n=Ki(e,t.target.x),r=Ki(e,t.target.y);return"".concat(n,"% ").concat(r,"%")}},dv={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=it.parse(e);if(o.length>5)return r;const s=it.createTransformer(e),i=typeof o[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;o[0+i]/=a,o[1+i]/=l;const u=oe(a,l,.5);return typeof o[2+i]=="number"&&(o[2+i]/=u),typeof o[3+i]=="number"&&(o[3+i]/=u),s(o)}};let fv=class extends p.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:s}=t;ug(hv),s&&(n.group&&n.group.add(s),r&&r.register&&o&&r.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),On.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:s}=this.props,i=r.projection;return i&&(i.isPresent=s,o||t.layoutDependency!==n||n===void 0||t.isPresent!==s?i.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?i.promote():i.relegate()||ee.postRender(()=>{const a=i.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ps.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}};function cc(e){const[t,n]=ec(),r=p.useContext(cs);return C.jsx(fv,{...e,layoutGroup:r,switchLayoutGroup:p.useContext(uc),isPresent:t,safeToRemove:n})}const hv={borderRadius:{...Gt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Gt,borderTopRightRadius:Gt,borderBottomLeftRadius:Gt,borderBottomRightRadius:Gt,boxShadow:dv},pv=(e,t)=>e.depth-t.depth;let mv=class{constructor(){this.children=[],this.isDirty=!1}add(t){Bo(this.children,t),this.isDirty=!0}remove(t){fr(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(pv),this.isDirty=!1,this.children.forEach(t)}};function gv(e,t){const n=Ue.now(),r=({timestamp:o})=>{const s=o-n;s>=t&&(st(r),e(s-t))};return ee.read(r,!0),()=>st(r)}function Nn(e){const t=ge(e)?e.get():e;return Sp(t)?t.toValue():t}const dc=["TopLeft","TopRight","BottomLeft","BottomRight"],vv=dc.length,Gi=e=>typeof e=="string"?parseFloat(e):e,Xi=e=>typeof e=="number"||z.test(e);function yv(e,t,n,r,o,s){o?(e.opacity=oe(0,n.opacity!==void 0?n.opacity:1,bv(r)),e.opacityExit=oe(t.opacity!==void 0?t.opacity:1,0,xv(r))):s&&(e.opacity=oe(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let i=0;i<vv;i++){const a="border".concat(dc[i],"Radius");let l=_i(t,a),u=_i(n,a);l===void 0&&u===void 0||(l||(l=0),u||(u=0),l===0||u===0||Xi(l)===Xi(u)?(e[a]=Math.max(oe(Gi(l),Gi(u),r),0),(qe.test(u)||qe.test(l))&&(e[a]+="%")):e[a]=u)}(t.rotate||n.rotate)&&(e.rotate=oe(t.rotate||0,n.rotate||0,r))}function _i(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const bv=fc(0,.5,du),xv=fc(.5,.95,Ae);function fc(e,t,n){return r=>r<e?0:r>t?1:n(wt(e,t,r))}function Yi(e,t){e.min=t.min,e.max=t.max}function De(e,t){Yi(e.x,t.x),Yi(e.y,t.y)}function Zi(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Ji(e,t,n,r,o){return e-=t,e=Jn(e,1/n,r),o!==void 0&&(e=Jn(e,1/o,r)),e}function $v(e,t=0,n=1,r=.5,o,s=e,i=e){if(qe.test(t)&&(t=parseFloat(t),t=oe(i.min,i.max,t/100)-i.min),typeof t!="number")return;let a=oe(s.min,s.max,r);e===s&&(a-=t),e.min=Ji(e.min,t,n,a,o),e.max=Ji(e.max,t,n,a,o)}function Qi(e,t,[n,r,o],s,i){$v(e,t[n],t[r],t[o],t.scale,s,i)}const wv=["x","scaleX","originX"],kv=["y","scaleY","originY"];function ea(e,t,n,r){Qi(e.x,t,wv,n?n.x:void 0,r?r.x:void 0),Qi(e.y,t,kv,n?n.y:void 0,r?r.y:void 0)}function ta(e){return e.translate===0&&e.scale===1}function hc(e){return ta(e.x)&&ta(e.y)}function na(e,t){return e.min===t.min&&e.max===t.max}function Sv(e,t){return na(e.x,t.x)&&na(e.y,t.y)}function ra(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function pc(e,t){return ra(e.x,t.x)&&ra(e.y,t.y)}function oa(e){return Ee(e.x)/Ee(e.y)}function sa(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}let Ev=class{constructor(){this.members=[]}add(t){Bo(this.members,t),t.scheduleRender()}remove(t){if(fr(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const s=this.members[o];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}};function Tv(e,t,n){let r="";const o=e.x.translate/t.x,s=e.y.translate/t.y,i=(n==null?void 0:n.z)||0;if((o||s||i)&&(r="translate3d(".concat(o,"px, ").concat(s,"px, ").concat(i,"px) ")),(t.x!==1||t.y!==1)&&(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),n){const{transformPerspective:u,rotate:c,rotateX:d,rotateY:f,skewX:h,skewY:m}=n;u&&(r="perspective(".concat(u,"px) ").concat(r)),c&&(r+="rotate(".concat(c,"deg) ")),d&&(r+="rotateX(".concat(d,"deg) ")),f&&(r+="rotateY(".concat(f,"deg) ")),h&&(r+="skewX(".concat(h,"deg) ")),m&&(r+="skewY(".concat(m,"deg) "))}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(r+="scale(".concat(a,", ").concat(l,")")),r||"none"}const zr=["","X","Y","Z"],Cv={visibility:"hidden"},ia=1e3;let Pv=0;function Wr(e,t,n,r){const{latestValues:o}=t;o[e]&&(n[e]=o[e],t.setStaticValue(e,0),r&&(r[e]=0))}function mc(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=nu(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:o,layoutId:s}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",ee,!(o||s))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&mc(r)}function gc({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(s={},i=t==null?void 0:t()){this.id=Pv++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Mv),this.nodes.forEach(Lv),this.nodes.forEach(Ov),this.nodes.forEach(Dv)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new mv)}addEventListener(s,i){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new Wo),this.eventHandlers.get(s).add(i)}notifyListeners(s,...i){const a=this.eventHandlers.get(s);a&&a.notify(...i)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=ju(s),this.instance=s;const{layoutId:a,layout:l,visualElement:u}=this.options;if(u&&!u.current&&u.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(l||a)&&(this.isLayoutDirty=!0),e){let c;const d=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=gv(d,250),On.hasAnimatedSinceResize&&(On.hasAnimatedSinceResize=!1,this.nodes.forEach(la))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&u&&(a||l)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:d,hasRelativeLayoutChanged:f,layout:h})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const m=this.options.transition||u.getDefaultTransition()||Wv,{onLayoutAnimationStart:g,onLayoutAnimationComplete:y}=u.getProps(),v=!this.targetLayout||!pc(this.targetLayout,h),b=!d&&f;if(this.options.layoutRoot||this.resumeFrom||b||d&&(v||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(c,b);const x={...zo(m,"layout"),onPlay:g,onComplete:y};(u.shouldReduceMotion||this.options.layoutRoot)&&(x.delay=0,x.type=!1),this.startAnimation(x)}else d||la(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=h})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,st(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Nv),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&mc(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const c=this.path[u];c.shouldResetTransform=!0,c.updateScroll("snapshot"),c.options.layoutRoot&&c.willUpdate(!1)}const{layoutId:i,layout:a}=this.options;if(i===void 0&&!a)return;const l=this.getTransformTemplate();this.prevTransformTemplateValue=l?l(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(aa);return}this.isUpdating||this.nodes.forEach(jv),this.isUpdating=!1,this.nodes.forEach(Vv),this.nodes.forEach(Rv),this.nodes.forEach(Av),this.clearAllSnapshots();const s=Ue.now();ye.delta=Ye(0,1e3/60,s-ye.timestamp),ye.timestamp=s,ye.isProcessing=!0,jr.update.process(ye),jr.preRender.process(ye),jr.render.process(ye),ye.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ps.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Iv),this.sharedNodes.forEach(Fv)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ee.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ee.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!Ee(this.snapshot.measuredBox.x)&&!Ee(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ae(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:i}=this.options;i&&i.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let i=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(i=!1),i){const a=r(this.instance);this.scroll={animationId:this.root.animationId,phase:s,isRoot:a,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:a}}}resetTransform(){if(!o)return;const s=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,i=this.projectionDelta&&!hc(this.projectionDelta),a=this.getTransformTemplate(),l=a?a(this.latestValues,""):void 0,u=l!==this.prevTransformTemplateValue;s&&(i||mt(this.latestValues)||u)&&(o(this.instance,l),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const i=this.measurePageBox();let a=this.removeElementScroll(i);return s&&(a=this.removeTransform(a)),Uv(a),{animationId:this.root.animationId,measuredBox:i,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){var s;const{visualElement:i}=this.options;if(!i)return ae();const a=i.measureViewportBox();if(!(!((s=this.scroll)===null||s===void 0)&&s.wasRoot||this.path.some(qv))){const{scroll:l}=this.root;l&&(At(a.x,l.offset.x),At(a.y,l.offset.y))}return a}removeElementScroll(s){var i;const a=ae();if(De(a,s),!((i=this.scroll)===null||i===void 0)&&i.wasRoot)return a;for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:d}=u;u!==this.root&&c&&d.layoutScroll&&(c.wasRoot&&De(a,s),At(a.x,c.offset.x),At(a.y,c.offset.y))}return a}applyTransform(s,i=!1){const a=ae();De(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l];!i&&u.options.layoutScroll&&u.scroll&&u!==u.root&&Mt(a,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),mt(u.latestValues)&&Mt(a,u.latestValues)}return mt(this.latestValues)&&Mt(a,this.latestValues),a}removeTransform(s){const i=ae();De(i,s);for(let a=0;a<this.path.length;a++){const l=this.path[a];if(!l.instance||!mt(l.latestValues))continue;vo(l.latestValues)&&l.updateSnapshot();const u=ae(),c=l.measurePageBox();De(u,c),ea(i,l.latestValues,l.snapshot?l.snapshot.layoutBox:void 0,u)}return mt(this.latestValues)&&ea(i,this.latestValues),i}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ye.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var i;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(s||l&&this.isSharedProjectionDirty||this.isProjectionDirty||!((i=this.parent)===null||i===void 0)&&i.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:u,layoutId:c}=this.options;if(!(!this.layout||!(u||c))){if(this.resolvedRelativeTargetAt=ye.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ae(),this.relativeTargetOrigin=ae(),nn(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),De(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=ae(),this.targetWithTransforms=ae()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Yg(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):De(this.target,this.layout.layoutBox),Xu(this.target,this.targetDelta)):De(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ae(),this.relativeTargetOrigin=ae(),nn(this.relativeTargetOrigin,this.target,d.target),De(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||vo(this.parent.latestValues)||Gu(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const i=this.getLead(),a=!!this.resumingFrom||this!==i;let l=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===ye.timestamp&&(l=!1),l)return;const{layout:u,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||c))return;De(this.layoutCorrected,this.layout.layoutBox);const d=this.treeScale.x,f=this.treeScale.y;fg(this.layoutCorrected,this.treeScale,this.path,a),i.layout&&!i.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(i.target=i.layout.layoutBox,i.targetWithTransforms=ae());const{target:h}=i;if(!h){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Zi(this.prevProjectionDelta.x,this.projectionDelta.x),Zi(this.prevProjectionDelta.y,this.projectionDelta.y)),tn(this.projectionDelta,this.layoutCorrected,h,this.latestValues),(this.treeScale.x!==d||this.treeScale.y!==f||!sa(this.projectionDelta.x,this.prevProjectionDelta.x)||!sa(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",h))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var i;if((i=this.options.visualElement)===null||i===void 0||i.scheduleRender(),s){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Rt(),this.projectionDelta=Rt(),this.projectionDeltaWithTransform=Rt()}setAnimationOrigin(s,i=!1){const a=this.snapshot,l=a?a.latestValues:{},u={...this.latestValues},c=Rt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!i;const d=ae(),f=a?a.source:void 0,h=this.layout?this.layout.source:void 0,m=f!==h,g=this.getStack(),y=!g||g.members.length<=1,v=!!(m&&!y&&this.options.crossfade===!0&&!this.path.some(zv));this.animationProgress=0;let b;this.mixTargetDelta=x=>{const k=x/1e3;ua(c.x,s.x,k),ua(c.y,s.y,k),this.setTargetDelta(c),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(nn(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Bv(this.relativeTarget,this.relativeTargetOrigin,d,k),b&&Sv(this.relativeTarget,b)&&(this.isProjectionDirty=!1),b||(b=ae()),De(b,this.relativeTarget)),m&&(this.animationValues=u,yv(u,l,this.latestValues,k,v,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=k},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(st(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ee.update(()=>{On.hasAnimatedSinceResize=!0,this.currentAnimation=Zu(0,ia,{...s,onUpdate:i=>{this.mixTargetDelta(i),s.onUpdate&&s.onUpdate(i)},onStop:()=>{},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(ia),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:i,target:a,layout:l,latestValues:u}=s;if(!(!i||!a||!l)){if(this!==s&&this.layout&&l&&vc(this.options.animationType,this.layout.layoutBox,l.layoutBox)){a=this.target||ae();const c=Ee(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+c;const d=Ee(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+d}De(i,a),Mt(i,u),tn(this.projectionDeltaWithTransform,this.layoutCorrected,i,u)}}registerSharedNode(s,i){this.sharedNodes.has(s)||this.sharedNodes.set(s,new Ev),this.sharedNodes.get(s).add(i);const a=i.options.initialPromotionConfig;i.promote({transition:a?a.transition:void 0,preserveFollowOpacity:a&&a.shouldPreserveFollowOpacity?a.shouldPreserveFollowOpacity(i):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:i}=this.options;return i?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:i}=this.options;return i?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:i,preserveFollowOpacity:a}={}){const l=this.getStack();l&&l.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),i&&this.setOptions({transition:i})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetSkewAndRotation(){const{visualElement:s}=this.options;if(!s)return;let i=!1;const{latestValues:a}=s;if((a.z||a.rotate||a.rotateX||a.rotateY||a.rotateZ||a.skewX||a.skewY)&&(i=!0),!i)return;const l={};a.z&&Wr("z",s,l,this.animationValues);for(let u=0;u<zr.length;u++)Wr("rotate".concat(zr[u]),s,l,this.animationValues),Wr("skew".concat(zr[u]),s,l,this.animationValues);s.render();for(const u in l)s.setStaticValue(u,l[u]),this.animationValues&&(this.animationValues[u]=l[u]);s.scheduleRender()}getProjectionStyles(s){var i,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Cv;const l={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,l.opacity="",l.pointerEvents=Nn(s==null?void 0:s.pointerEvents)||"",l.transform=u?u(this.latestValues,""):"none",l;const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){const m={};return this.options.layoutId&&(m.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,m.pointerEvents=Nn(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!mt(this.latestValues)&&(m.transform=u?u({},""):"none",this.hasProjected=!1),m}const d=c.animationValues||c.latestValues;this.applyTransformsToTarget(),l.transform=Tv(this.projectionDeltaWithTransform,this.treeScale,d),u&&(l.transform=u(d,l.transform));const{x:f,y:h}=this.projectionDelta;l.transformOrigin="".concat(f.origin*100,"% ").concat(h.origin*100,"% 0"),c.animationValues?l.opacity=c===this?(a=(i=d.opacity)!==null&&i!==void 0?i:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:l.opacity=c===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(const m in hn){if(d[m]===void 0)continue;const{correct:g,applyTo:y,isCSSVariable:v}=hn[m],b=l.transform==="none"?d[m]:g(d[m],c);if(y){const x=y.length;for(let k=0;k<x;k++)l[y[k]]=b}else v?this.options.visualElement.renderState.vars[m]=b:l[m]=b}return this.options.layoutId&&(l.pointerEvents=c===this?Nn(s==null?void 0:s.pointerEvents)||"":"none"),l}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var i;return(i=s.currentAnimation)===null||i===void 0?void 0:i.stop()}),this.root.nodes.forEach(aa),this.root.sharedNodes.clear()}}}function Rv(e){e.updateLayout()}function Av(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=e.layout,{animationType:s}=e.options,i=n.source!==e.layout.source;s==="size"?Ie(d=>{const f=i?n.measuredBox[d]:n.layoutBox[d],h=Ee(f);f.min=r[d].min,f.max=f.min+h}):vc(s,n.layoutBox,r)&&Ie(d=>{const f=i?n.measuredBox[d]:n.layoutBox[d],h=Ee(r[d]);f.max=f.min+h,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+h)});const a=Rt();tn(a,r,n.layoutBox);const l=Rt();i?tn(l,e.applyTransform(o,!0),n.measuredBox):tn(l,r,n.layoutBox);const u=!hc(a);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:f,layout:h}=d;if(f&&h){const m=ae();nn(m,n.layoutBox,f.layoutBox);const g=ae();nn(g,r,h.layoutBox),pc(m,g)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=g,e.relativeTargetOrigin=m,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeLayoutChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function Mv(e){e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Dv(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Iv(e){e.clearSnapshot()}function aa(e){e.clearMeasurements()}function jv(e){e.isLayoutDirty=!1}function Vv(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function la(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Lv(e){e.resolveTargetDelta()}function Ov(e){e.calcProjection()}function Nv(e){e.resetSkewAndRotation()}function Fv(e){e.removeLeadSnapshot()}function ua(e,t,n){e.translate=oe(t.translate,0,n),e.scale=oe(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function ca(e,t,n,r){e.min=oe(t.min,n.min,r),e.max=oe(t.max,n.max,r)}function Bv(e,t,n,r){ca(e.x,t.x,n.x,r),ca(e.y,t.y,n.y,r)}function zv(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Wv={duration:.45,ease:[.4,0,.1,1]},da=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),fa=da("applewebkit/")&&!da("chrome/")?Math.round:Ae;function ha(e){e.min=fa(e.min),e.max=fa(e.max)}function Uv(e){ha(e.x),ha(e.y)}function vc(e,t,n){return e==="position"||e==="preserve-aspect"&&!_g(oa(t),oa(n),.2)}function qv(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const Hv=gc({attachResizeListener:(e,t)=>pn(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ur={current:void 0},yc=gc({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Ur.current){const e=new Hv({});e.mount(window),e.setOptions({layoutScroll:!0}),Ur.current=e}return Ur.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Kv={pan:{Feature:cv},drag:{Feature:uv,ProjectionNode:yc,MeasureLayout:cc}};function bc(e,t){const n=Zl(e),r=new AbortController,o={passive:!0,...t,signal:r.signal};return[n,o,()=>r.abort()]}function pa(e){return!(e.pointerType==="touch"||rc())}function Gv(e,t,n={}){const[r,o,s]=bc(e,n),i=a=>{if(!pa(a))return;const{target:l}=a,u=t(l,a);if(typeof u!="function"||!l)return;const c=d=>{pa(d)&&(u(d),l.removeEventListener("pointerleave",c))};l.addEventListener("pointerleave",c,o)};return r.forEach(a=>{a.addEventListener("pointerenter",i,o)}),s}function ma(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const o="onHover"+n,s=r[o];s&&ee.postRender(()=>s(t,$n(t)))}let Xv=class extends lt{mount(){const{current:t}=this.node;t&&(this.unmount=Gv(t,(n,r)=>(ma(this.node,r,"Start"),o=>ma(this.node,o,"End"))))}unmount(){}},_v=class extends lt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(n){t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=xn(pn(this.node.current,"focus",()=>this.onFocus()),pn(this.node.current,"blur",()=>this.onBlur()))}unmount(){}};function ga(e,t){const n="".concat(t,"PointerCapture");if(e.target instanceof Element&&n in e.target&&e.pointerId!==void 0)try{e.target[n](e.pointerId)}catch(r){}}const xc=(e,t)=>t?e===t?!0:xc(e,t.parentElement):!1,Yv=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Zv(e){return Yv.has(e.tagName)||e.tabIndex!==-1}const _t=new WeakSet;function va(e){return t=>{t.key==="Enter"&&e(t)}}function qr(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const Jv=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=va(()=>{if(_t.has(n))return;qr(n,"down");const o=va(()=>{qr(n,"up")}),s=()=>qr(n,"cancel");n.addEventListener("keyup",o,t),n.addEventListener("blur",s,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function ya(e){return hs(e)&&!rc()}function Qv(e,t,n={}){const[r,o,s]=bc(e,n),i=a=>{const l=a.currentTarget;if(!l||!ya(a)||_t.has(l))return;_t.add(l),ga(a,"set");const u=t(l,a),c=(h,m)=>{l.removeEventListener("pointerup",d),l.removeEventListener("pointercancel",f),ga(h,"release"),!(!ya(h)||!_t.has(l))&&(_t.delete(l),typeof u=="function"&&u(h,{success:m}))},d=h=>{h.isTrusted&&ey(h,l instanceof Element?l.getBoundingClientRect():{left:0,top:0,right:window.innerWidth,bottom:window.innerHeight})?c(h,!1):c(h,!(l instanceof Element)||xc(l,h.target))},f=h=>{c(h,!1)};l.addEventListener("pointerup",d,o),l.addEventListener("pointercancel",f,o),l.addEventListener("lostpointercapture",f,o)};return r.forEach(a=>{a=n.useGlobalTarget?window:a;let l=!1;a instanceof HTMLElement&&(l=!0,!Zv(a)&&a.getAttribute("tabindex")===null&&(a.tabIndex=0)),a.addEventListener("pointerdown",i,o),l&&a.addEventListener("focus",u=>Jv(u,o),o)}),s}function ey(e,t){return e.clientX<t.left||e.clientX>t.right||e.clientY<t.top||e.clientY>t.bottom}function ba(e,t,n){const{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const o="onTap"+(n==="End"?"":n),s=r[o];s&&ee.postRender(()=>s(t,$n(t)))}let ty=class extends lt{mount(){const{current:t}=this.node;t&&(this.unmount=Qv(t,(n,r)=>(ba(this.node,r,"Start"),(o,{success:s})=>ba(this.node,o,s?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}};const $o=new WeakMap,Hr=new WeakMap,ny=e=>{const t=$o.get(e.target);t&&t(e)},ry=e=>{e.forEach(ny)};function oy({root:e,...t}){const n=e||document;Hr.has(n)||Hr.set(n,{});const r=Hr.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(ry,{root:e,...t})),r[o]}function sy(e,t,n){const r=oy(t);return $o.set(e,n),r.observe(e),()=>{$o.delete(e),r.unobserve(e)}}const iy={some:0,all:1};class ay extends lt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:o="some",once:s}=t,i={root:n?n.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:iy[o]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=u?c:d;f&&f(l)};return sy(this.node.current,i,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(ly(t,n))&&this.startObserver()}unmount(){}}function ly({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const uy={inView:{Feature:ay},tap:{Feature:ty},focus:{Feature:_v},hover:{Feature:Xv}},cy={layout:{ProjectionNode:yc,MeasureLayout:cc}},$c=p.createContext({strict:!1}),vr=p.createContext({});function dy(e,t){if(mr(e)){const{initial:n,animate:r}=e;return{initial:n===!1||fn(n)?n:void 0,animate:fn(r)?r:void 0}}return e.inherit!==!1?t:{}}function fy(e){const{initial:t,animate:n}=dy(e,p.useContext(vr));return p.useMemo(()=>({initial:t,animate:n}),[xa(t),xa(n)])}function xa(e){return Array.isArray(e)?e.join(" "):e}function hy(e){for(const t in e)Nt[t]={...Nt[t],...e[t]}}const py=Symbol.for("motionComponentSymbol");function my(e,t,n){return p.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Dt(n)&&(n.current=r))},[t])}function gy(e,t,n,r,o){var s,i;const{visualElement:a}=p.useContext(vr),l=p.useContext($c),u=p.useContext(gr),c=p.useContext(fs).reducedMotion,d=p.useRef(null);r=r||l.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:a,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const f=d.current,h=p.useContext(uc);f&&!f.projection&&o&&(f.type==="html"||f.type==="svg")&&vy(d.current,n,o,h);const m=p.useRef(!1);p.useInsertionEffect(()=>{f&&m.current&&f.update(n,u)});const g=n[tu],y=p.useRef(!!g&&!(!((s=window.MotionHandoffIsComplete)===null||s===void 0)&&s.call(window,g))&&((i=window.MotionHasOptimisedAnimation)===null||i===void 0?void 0:i.call(window,g)));return Qu(()=>{f&&(m.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),ps.render(f.render),y.current&&f.animationState&&f.animationState.animateChanges())}),p.useEffect(()=>{f&&(!y.current&&f.animationState&&f.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{var v;(v=window.MotionHandoffMarkAsComplete)===null||v===void 0||v.call(window,g)}),y.current=!1))}),f}function vy(e,t,n,r){const{layoutId:o,layout:s,drag:i,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:wc(e.parent)),e.projection.setOptions({layoutId:o,layout:s,alwaysMeasureLayout:!!i||a&&Dt(a),visualElement:e,animationType:typeof s=="string"?s:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}function wc(e){if(e)return e.options.allowProjection!==!1?e.projection:wc(e.parent)}function yy({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:o}){var s,i;e&&hy(e);function a(u,c){let d;const f={...p.useContext(fs),...u,layoutId:by(u)},{isStatic:h}=f,m=fy(u),g=r(u,h);if(!h&&jo){xy();const y=$y(f);d=y.MeasureLayout,m.visualElement=gy(o,g,f,t,y.ProjectionNode)}return C.jsxs(vr.Provider,{value:m,children:[d&&m.visualElement?C.jsx(d,{visualElement:m.visualElement,...f}):null,n(o,u,my(g,m.visualElement,c),g,h,m.visualElement)]})}a.displayName="motion.".concat(typeof o=="string"?o:"create(".concat((i=(s=o.displayName)!==null&&s!==void 0?s:o.name)!==null&&i!==void 0?i:"",")"));const l=p.forwardRef(a);return l[py]=o,l}function by({layoutId:e}){const t=p.useContext(cs).id;return t&&e!==void 0?t+"-"+e:e}function xy(e,t){p.useContext($c).strict}function $y(e){const{drag:t,layout:n}=Nt;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const ms=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function kc(e,t,n){for(const r in t)!ge(t[r])&&!Uu(r,n)&&(e[r]=t[r])}function wy({transformTemplate:e},t){return p.useMemo(()=>{const n=ms();return is(n,t,e),Object.assign({},n.vars,n.style)},[t])}function ky(e,t){const n=e.style||{},r={};return kc(r,n,e),Object.assign(r,wy(e,t)),r}function Sy(e,t){const n={},r=ky(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":"pan-".concat(e.drag==="x"?"y":"x")),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const Ey=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Qn(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Ey.has(e)}let Sc=e=>!Qn(e);function Ty(e){e&&(Sc=t=>t.startsWith("on")?!Qn(t):e(t))}try{Ty(require("@emotion/is-prop-valid").default)}catch(e){}function Cy(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(Sc(o)||n===!0&&Qn(o)||!t&&!Qn(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}const Py=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function gs(e){return typeof e!="string"||e.includes("-")?!1:!!(Py.indexOf(e)>-1||/[A-Z]/u.test(e))}const Ec=()=>({...ms(),attrs:{}});function Ry(e,t,n,r){const o=p.useMemo(()=>{const s=Ec();return as(s,t,ls(r),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};kc(s,e.style,e),o.style={...s,...o.style}}return o}function Ay(e=!1){return(t,n,r,{latestValues:o},s)=>{const i=(gs(t)?Ry:Sy)(n,o,s,t),a=Cy(n,typeof t=="string",e),l=t!==p.Fragment?{...a,...i,ref:r}:{},{children:u}=n,c=p.useMemo(()=>ge(u)?u.get():u,[u]);return p.createElement(t,{...l,children:c})}}function My({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,o,s){const i={latestValues:Dy(r,o,s,e),renderState:t()};return n&&(i.onMount=a=>n({props:r,current:a,...i}),i.onUpdate=a=>n(a)),i}const Tc=e=>(t,n)=>{const r=p.useContext(vr),o=p.useContext(gr),s=()=>My(e,t,r,o);return n?s():ds(s)};function Dy(e,t,n,r){const o={},s=r(e,{});for(const f in s)o[f]=Nn(s[f]);let{initial:i,animate:a}=e;const l=mr(e),u=Vu(e);t&&u&&!l&&e.inherit!==!1&&(i===void 0&&(i=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||i===!1;const d=c?a:i;if(d&&typeof d!="boolean"&&!pr(d)){const f=Array.isArray(d)?d:[d];for(let h=0;h<f.length;h++){const m=Uo(e,f[h]);if(m){const{transitionEnd:g,transition:y,...v}=m;for(const b in v){let x=v[b];if(Array.isArray(x)){const k=c?x.length-1:0;x=x[k]}x!==null&&(o[b]=x)}for(const b in g)o[b]=g[b]}}}return o}const Iy={useVisualState:Tc({scrapeMotionValuesFromProps:us,createRenderState:ms})},$a=["x","y","width","height","cx","cy","r"],jy={useVisualState:Tc({scrapeMotionValuesFromProps:qu,createRenderState:Ec,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:o})=>{if(!n)return;let s=!!e.drag;if(!s){for(const a in o)if(kt.has(a)){s=!0;break}}if(!s)return;let i=!t;if(t)for(let a=0;a<$a.length;a++){const l=$a[a];e[l]!==t[l]&&(i=!0)}i&&ee.read(()=>{Bu(n,r),ee.render(()=>{as(r,o,ls(n.tagName),e.transformTemplate),Wu(n,r)})})}})};function Vy(e,t){return function(n,{forwardMotionProps:r}={forwardMotionProps:!1}){const o={...gs(n)?jy:Iy,preloadedFeatures:e,useRender:Ay(r),createVisualElement:t,Component:n};return yy(o)}}const Ly=(e,t)=>gs(e)?new Hu(t):new Yu(t,{allowProjection:e!==p.Fragment}),Oy=Vy({...Wg,...uy,...Kv,...cy},Ly),Ny=Rg(Oy),Cc=p.createContext(null),yr=()=>{const e=p.use(Cc);if(!e)throw new Error("Drawer components must be wrapped in <Drawer />");return e},i0=({open:e,defaultOpen:t,onOpenChange:n,children:r,scrollLockTarget:o})=>{const s=Ll(n),[i,a]=p.useState(void 0),[l,u]=p.useState(t!=null?t:!1),c=e!=null?e:l,d=p.useCallback(f=>{u(f),s==null||s(f)},[s]);return Xh(c,o),C.jsx(Cc,{value:{open:c,setOpen:d,labelId:i,setLabelId:a},children:r})},a0=({children:e,asChild:t,...n})=>{const{setOpen:r}=yr();return C.jsx(t?He:"button",{...n,onClick:o=>{var s;(s=n.onClick)==null||s.call(n,o),o.defaultPrevented||r(!0)},children:e})},l0=({children:e,asChild:t,...n})=>{const{setOpen:r}=yr();return C.jsx(t?He:"button",{...n,onClick:o=>{var s;(s=n.onClick)==null||s.call(n,o),o.defaultPrevented||r(!1)},children:e})},Kr={open:{"--transform":"0%","--backdrop-opacity":1,transition:{"--transform":{duration:.4,ease:ei["emphasized-decelerate"]},"--backdrop-opacity":{duration:.4}}},closed:{"--transform":"100%","--backdrop-opacity":0,transition:{"--transform":{duration:.25,ease:ei["emphasized-accelerate"]},"--backdrop-opacity":{duration:.4,delay:.1}}}},u0=({children:e,persistExitAnimation:t,...n})=>{const r=_h(),{labelId:o,open:s,setOpen:i}=yr(),a=p.useRef(null),l=p.useRef(!1);p.useLayoutEffect(()=>{const c=a.current;if(!c)return;c.open||c.showModal();const d=f=>{f.preventDefault(),i(!1)};return c.addEventListener("cancel",d),()=>{c.removeEventListener("cancel",d)}},[s,i]),p.useEffect(()=>{s&&(l.current=!1);const c=a.current;if(!(r||!t||!c))return()=>{const d=!!c&&!a.current,f=!l.current;if(d&&f){const h=c.cloneNode(!0);h.setAttribute("inert",""),h.setAttribute("tabindex","-1"),document.body.appendChild(h),h.close(),h.showModal(),h.focus(),kg(h,Kr.closed,Kr.closed.transition).then(()=>{h.remove()})}}},[s,t,r]);const u=c=>{if(c.target.nodeName==="DIALOG"){const d=c.target,{top:f,left:h,width:m,height:g}=d.getBoundingClientRect();(f>c.clientY||c.clientY>f+g||h>c.clientX||c.clientX>h+m)&&(i(!1),c.stopPropagation())}};return C.jsx(Pg,{initial:!1,onExitComplete:()=>l.current=!0,children:s&&C.jsxs(Ny.dialog,{...n,ref:a,role:"dialog","aria-modal":"true","aria-labelledby":o,initial:"closed",animate:"open",exit:"closed",variants:r?{}:Kr,className:se("bg-background-high border-border mx-auto flex w-full max-w-screen flex-col overflow-y-auto border *:shrink-0","max-md:mt-auto max-md:max-h-[calc(100dvh-(--spacing(12)))] max-md:translate-y-(--transform) max-md:rounded-xl max-md:rounded-b-none max-md:border-b-0","md:mr-0 md:h-full md:max-h-screen md:max-w-160 md:translate-x-(--transform) md:border-0 md:border-l","backdrop:bg-[rgba(0,0,0,0.2)] backdrop:opacity-(--backdrop-opacity) backdrop:backdrop-blur-sm",!r&&"will-change-transform",n.className),onClick:u,children:[C.jsx("div",{className:"safari:block sr-only hidden","aria-hidden":"true",autoFocus:!0,tabIndex:-1}),e]})})},c0=({children:e,...t})=>{const n=t.asChild?He:"header";return C.jsx(n,{...t,className:se("bg-background-highlight border-border sticky top-0 z-10 border-b p-3 md:px-5 md:py-4",t.className),children:e})},d0=({children:e,...t})=>{var i;const{setLabelId:n}=yr(),r=p.useId(),o=(i=t.id)!=null?i:r;p.useLayoutEffect(()=>(n(o),()=>n(void 0)),[o,n]);const s=t.asChild?He:"p";return C.jsx(s,{...t,id:o,className:se("text-sm md:text-base",t.className),children:e})},f0=({children:e,...t})=>{const n=t.asChild?He:"div";return C.jsx(n,{...t,className:se("bg-background-highlight border-border sticky bottom-0 z-10 mt-auto flex gap-2 border-t p-3 pb-8 md:p-5 md:pb-5",t.className),children:e})};p.createContext({getIndex:()=>0,invalidate:()=>{}});p.createContext({activeIndex:0,length:0,next:()=>{},previous:()=>{}});se("ring-focus flex size-6 cursor-pointer items-center justify-center rounded-md opacity-60 outline-0 hover:opacity-100 focus-visible:ring-4");/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fy=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],By=Me("Check",Fy),Pc=e=>typeof e=="object"&&e!==null&&"children"in e;function er(e){return typeof e=="string"?e:typeof e=="number"?e.toString():Array.isArray(e)?e.map(er).join(""):p.isValidElement(e)&&Pc(e.props)?er(e.props.children):""}const Rc=p.createContext(null),br=()=>{const e=p.use(Rc);if(e==null)throw new Error("useListboxContext must be used within a Listbox");return e},wa=e=>typeof e=="object"&&e!==null&&"id"in e,ka=e=>typeof e!="object"&&e!==null,zy=(e,t)=>wa(e)&&wa(t)?e.id===t.id:ka(e)&&ka(t)?e===t:JSON.stringify(e)===JSON.stringify(t),Wy=({value:e,onChange:t,disabled:n,invalid:r,placement:o="bottom",getIsSelected:s=zy,matchReferenceWidth:i=!0})=>{const[a,l]=p.useState(!1),[u,c]=p.useState(!1),[d,f]=p.useState(null),[h,m]=p.useState([]),g=p.useRef([]),y=p.useMemo(()=>e?h.findIndex(V=>s(V.value,e)):-1,[h,e,s]),v=Rl({placement:o,open:a,onOpenChange:l,whileElementsMounted:cl,middleware:[pl({padding:4}),hl({padding:4}),fl(4),ml({apply({rects:V,elements:j,availableHeight:E}){j.floating.style.setProperty("--listbox-max-height","".concat(E,"px")),i&&j.floating.style.setProperty("--listbox-width","".concat(V.reference.width,"px"))},padding:4})]}),b=p.useCallback(V=>{if(!(V===null||!h[V])){if(Array.isArray(e)){const j=e.some(E=>s(h[V].value,E));return t(j?e.filter(E=>!s(h[V].value,E)):[...e,h[V].value])}t(h[V].value),l(!1)}},[t,h,e,s]),x=Ml(v.context,{listRef:g,activeIndex:d,selectedIndex:y,onNavigate:f,virtual:u||void 0,loop:u||void 0}),k=p.useCallback(V=>{a?f(V):b(V)},[a,b]),w=p.useRef([]);p.useEffect(()=>{w.current=h.map(V=>V.label)},[h]);const $=Il(v.context,{enabled:!(u&&a),listRef:w,activeIndex:d,selectedIndex:y,onMatch:k}),R=Cl(v.context,{enabled:!n,event:"mousedown"}),A=Pl(v.context),D=Dl(v.context,{role:"listbox"}),T=Al([x,$,R,A,D]);return p.useMemo(()=>({elementsRef:g,labelsRef:w,highlightedIndex:d,setHighlightedIndex:f,value:e,invalid:r,disabled:n,setOptions:m,handleSelect:b,setIsSearchable:c,getIsSelected:s,...T,...v}),[d,e,r,n,m,b,s,T,v])},h0=({children:e,ref:t,...n})=>{const r=Wy(n);return p.useImperativeHandle(t,()=>r),C.jsx(Rc,{value:r,children:e})},p0=({ref:e,id:t,children:n,className:r,variant:o,placeholder:s,...i})=>{const a=br(),l=Ua(),u=a.invalid||(l==null?void 0:l["aria-errormessage"])||void 0,c=Wt([a.refs.setReference,e]);return C.jsx(Uy,{ref:c,placeholder:s,variant:o,className:r,disabled:a.disabled,"data-state":a.context.open?"open":"closed","data-invalid":u,id:t!=null?t:l==null?void 0:l.id,"aria-describedby":l==null?void 0:l["aria-describedby"],"aria-labelledby":l==null?void 0:l["aria-labelledby"],...a.getReferenceProps(i),children:n})},Uy=({ref:e,children:t,placeholder:n,variant:r,className:o,...s})=>C.jsxs("button",{ref:e,type:"button",className:se(Fl({variant:r}),"flex items-center gap-1.5 enabled:cursor-pointer","relative w-full pr-10 pl-4",o),...s,children:[C.jsx("span",{className:"flex flex-1 items-center gap-1.5 truncate text-left",children:t!=null?t:C.jsx("span",{className:"text-foreground/40",children:n})}),C.jsx(Kh,{className:"text-foreground/80 absolute top-1/2 right-3 -translate-y-1/2 text-base"})]}),qy=e=>typeof e=="object"&&e!==null&&"value"in e,m0=({ref:e,children:t,className:n,style:r,...o})=>{const{setOptions:s,setIsSearchable:i,refs:a,elementsRef:l,labelsRef:u,context:c,floatingStyles:d,getFloatingProps:f}=br();p.useEffect(()=>{const m=g=>p.Children.toArray(g).reduce((y,v)=>(p.isValidElement(v)&&(v.type===Hy&&qy(v.props)?y.push({value:v.props.value,label:er(v.props.children),disabled:v.props.disabled}):Pc(v.props)&&y.push(...m(v.props.children))),y),[]);s(m(t))},[t,s]),p.useEffect(()=>{p.Children.toArray(t).some(m=>p.isValidElement(m)&&m.type===Ky)&&i(!0)},[t,i]);const h=Wt([a.setFloating,e]);return c.open?C.jsx(jl,{context:c,children:C.jsx(Tl,{context:c,children:C.jsx("div",{ref:h,"data-state":c.open?"open":"closed",className:se("border-border-soft bg-background-highlight text-foreground z-50 flex flex-col items-stretch rounded-xl border p-0 focus:outline-none","overflow-y-auto overscroll-contain","max-h-(--listbox-max-height) w-(--listbox-width)",n),style:{...d,...r},...f({...o}),children:C.jsx(yl,{elementsRef:l,labelsRef:u,children:t})})})}):null},Hy=({ref:e,value:t,children:n,disabled:r,className:o,withCheckmark:s=!0,...i})=>{const{highlightedIndex:a,value:l,getIsSelected:u,getItemProps:c,handleSelect:d}=br(),f=er(n),{ref:h,index:m}=bl({label:f}),g=Wt([h,e]),y=a===m,v=Array.isArray(l)?l.some(b=>u(b,t)):l&&u(l,t);return C.jsxs("button",{ref:g,role:"option","aria-selected":y&&v,"data-selected":v||void 0,"data-highlighted":y||void 0,tabIndex:y?0:-1,disabled:r||void 0,"data-disabled":r||void 0,className:se("data-highlighted:bg-foreground/5 text-foreground/80 relative mx-1 flex cursor-pointer items-center gap-1.5 rounded-lg px-4 py-2 text-left text-sm outline-none select-none first-of-type:mt-1 last-of-type:mb-1 data-disabled:pointer-events-none data-disabled:opacity-50",s&&"pr-8",o),...c({...i,onClick:b=>{var x;d(m),(x=i.onClick)==null||x.call(i,b)}}),children:[n,v&&s&&C.jsx(By,{strokeWidth:2.5,className:"text-accent absolute top-1/2 right-3 -translate-y-1/2 text-sm"})]})},Ky=({ref:e,onKeyDown:t,onChange:n,...r})=>{const{highlightedIndex:o,setHighlightedIndex:s,handleSelect:i}=br();return C.jsx(zh,{ref:e,onKeyDown:a=>{a.key==="Enter"&&(a.preventDefault(),i(o)),t==null||t(a)},onChange:a=>{n==null||n(a),s(0)},...r})};/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gy=[["path",{d:"M5 12h14",key:"1ays0h"}]],Xy=Me("Minus",Gy);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _y=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Yy=Me("Trash2",_y);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zy=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Jy=Me("Plus",Zy),Ac=p.createContext(null),xr=()=>{const e=p.use(Ac);if(!e)throw new Error("useQuantitySelectorContext needs to be used within a QuantitySelector");return e},g0=({quantity:e,onChange:t,onDelete:n,min:r=1,max:o=1/0,children:s,className:i,size:a,...l})=>{const[u,c]=p.useReducer((g,y)=>Math.min(Math.max(y,r),o),e!=null?e:r),d=e!==void 0,f=d?e:u,h=p.useCallback(g=>{d?t==null||t(g):c(g)},[d,t]),m=p.useMemo(()=>({quantity:f,onChange:h,onDelete:n,min:r,max:o,size:a}),[f,h,n,r,o,a]);return C.jsx(Ac.Provider,{value:m,children:C.jsx("div",{className:se("flex items-center justify-between gap-2",i),...l,children:s})})},v0=({onClick:e,"aria-label":t="Decrease quantity",...n})=>{const{quantity:r,onChange:o,min:s,size:i}=xr();return C.jsx(To,{"data-testid":"quantity-selector-decrease","aria-label":t,square:!0,disabled:r<=s,onClick:a=>{e==null||e(a),!a.defaultPrevented&&o(r-1)},size:i,...n,children:C.jsx(Xy,{})})},y0=({"aria-label":e="Delete",...t})=>{const{size:n}=xr();return C.jsx(To,{"data-testid":"quantity-selector-delete","aria-label":e,square:!0,size:n,...t,children:C.jsx(Yy,{})})},b0=({onClick:e,ariaLabel:t="Increase quantity",...n})=>{const{quantity:r,onChange:o,max:s,size:i}=xr();return C.jsx(To,{"data-testid":"quantity-selector-increase","aria-label":t,square:!0,disabled:r>=s,onClick:a=>{e==null||e(a),a.defaultPrevented||o(r+1)},size:i,...n,children:C.jsx(Jy,{})})},x0=({className:e,render:t=r=>r,...n})=>{const{quantity:r}=xr();return C.jsx("span",{className:se("min-w-[2.2ch] text-center tabular-nums select-none",e),...n,children:t(r)})};p.createContext({});p.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});const Qy=e=>e,Mc=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"];new Set(Mc);const eb=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tb="framerAppearId";""+eb(tb);const Dc=(e,t,n)=>n>t?t:n<e?e:n,$r={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Ic={...$r,transform:e=>Dc(0,1,e)};({...$r});const rn=e=>Math.round(e*1e5)/1e5,jc=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function nb(e){return e==null}const rb=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,vs=(e,t)=>n=>!!(typeof n=="string"&&rb.test(n)&&n.startsWith(e)||t&&!nb(n)&&Object.prototype.hasOwnProperty.call(n,t)),Vc=(e,t,n)=>r=>{if(typeof r!="string")return r;const[o,s,i,a]=r.match(jc);return{[e]:parseFloat(o),[t]:parseFloat(s),[n]:parseFloat(i),alpha:a!==void 0?parseFloat(a):1}},ob=e=>Dc(0,255,e),Gr={...$r,transform:e=>Math.round(ob(e))},Yt={test:vs("rgb","red"),parse:Vc("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Gr.transform(e)+", "+Gr.transform(t)+", "+Gr.transform(n)+", "+rn(Ic.transform(r))+")"};function sb(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const Sa={test:vs("#"),parse:sb,transform:Yt.transform},ib=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),wo=ib("%");({...wo});const In={test:vs("hsl","hue"),parse:Vc("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+wo.transform(rn(t))+", "+wo.transform(rn(n))+", "+rn(Ic.transform(r))+")"},ko={test:e=>Yt.test(e)||Sa.test(e)||In.test(e),parse:e=>Yt.test(e)?Yt.parse(e):In.test(e)?In.parse(e):Sa.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Yt.transform(e):In.transform(e)},ab=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function lb(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(jc))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(ab))===null||n===void 0?void 0:n.length)||0)>0}const Lc="number",Oc="color",ub="var",cb="var(",Ea="${}",db=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Nc(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},o=[];let s=0;const i=t.replace(db,a=>(ko.test(a)?(r.color.push(s),o.push(Oc),n.push(ko.parse(a))):a.startsWith(cb)?(r.var.push(s),o.push(ub),n.push(a)):(r.number.push(s),o.push(Lc),n.push(parseFloat(a))),++s,Ea)).split(Ea);return{values:n,split:i,indexes:r,types:o}}function Fc(e){return Nc(e).values}function Bc(e){const{split:t,types:n}=Nc(e),r=t.length;return o=>{let s="";for(let i=0;i<r;i++)if(s+=t[i],o[i]!==void 0){const a=n[i];a===Lc?s+=rn(o[i]):a===Oc?s+=ko.transform(o[i]):s+=o[i]}return s}}const fb=e=>typeof e=="number"?0:e;function hb(e){const t=Fc(e);return Bc(e)(t.map(fb))}const pb={test:lb,parse:Fc,createTransformer:Bc,getAnimatableNone:hb};({...pb});({...$r});const mb=new Set(["x","y","z"]);Mc.filter(e=>!mb.has(e));p.createContext({});const Ta=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e);Ta("applewebkit/")&&Ta("chrome/");p.createContext({strict:!1});try{require("@emotion/is-prop-valid").default}catch(e){}/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gb=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]];Me("X",gb);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vb=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]];Me("TriangleAlert",vb);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yb=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]];Me("Info",yb);const bb=tr({base:["relative h-10 w-fit rounded-xl px-3.5","flex items-center justify-center gap-2.5","text-xs leading-none text-(--button-text-color) [--button-text-color:var(--color-foreground)]","[&_.lucide]:stroke-1 [&_.lucide]:text-xl","focus-visible:ring-focus focus:outline-none focus-visible:ring-4"],variants:{square:{true:"w-12 px-0",false:""}}}),Ca=({children:e,className:t,asChild:n=!1,isLoading:r=!1,square:o=!1,ref:s,...i})=>{const a=n?He:"button";return C.jsx(a,{className:se(bb({square:o}),r&&"text-transparent",t),ref:s,...i,children:C.jsx(Ba,{child:e,asChild:n,children:l=>C.jsxs(C.Fragment,{children:[l,r&&C.jsx("span",{"data-button-spinner":!0,className:se("absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2","text-(--button-text-color)"),children:C.jsx(za,{})})]})})})};try{Ca.displayName="FlatButton",Ca.__docgenInfo={description:"",displayName:"FlatButton",props:{asChild:{defaultValue:{value:"false"},description:"",name:"asChild",required:!1,type:{name:"boolean | undefined"}},square:{defaultValue:{value:"false"},description:"",name:"square",required:!1,type:{name:"boolean | undefined"}},isLoading:{defaultValue:{value:"false"},description:"",name:"isLoading",required:!1,type:{name:"boolean | undefined"}}}}}catch(e){}/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xb=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),zc=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var $b={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wb=p.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:s,iconNode:i,...a},l)=>p.createElement("svg",{ref:l,...$b,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:zc("lucide",o),...a},[...i.map(([u,c])=>p.createElement(u,c)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wc=(e,t)=>{const n=p.forwardRef(({className:r,...o},s)=>p.createElement(wb,{ref:s,iconNode:t,className:zc("lucide-".concat(xb(e)),r),...o}));return n.displayName="".concat(e),n};/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kb=[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]],$0=Wc("Menu",kb);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sb=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],w0=Wc("X",Sb);export{u0 as $,Ua as A,Rb as B,jb as C,Ca as F,y0 as G,b0 as H,Ab as I,x0 as J,$0 as M,Hy as N,Ib as R,i0 as W,w0 as X,a0 as Z,l0 as _,g0 as a,Db as b,Wc as c,v0 as d,ei as e,c0 as f,f0 as g,Fl as h,tr as i,He as j,Vb as k,Pb as l,za as m,se as n,h0 as o,p0 as p,m0 as q,Cb as r,d0 as t,Mb as v,To as z};

!function(){var e=["class","className"],t=["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"],n=["className"],r=["children","ref"],i=["asChild","child","children"],o=["className","size"],a=["children","className","variant","asChild","isLoading","size","square","type","ref"],u=["children","className"],s=["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"],l=["strategy"],c=["mainAxis","crossAxis","limiter"],f=["apply"],d=["children","context"],v=["ref","children","asChild","className"],h=["ref","style","className","children"],p=["className"],m=["className","orientation","decorative"],y=["children","modal","placement"],g=["children","className"],b=["ref","children","className","disabled","onClick","onSelect","onKeyDown","asChild"],x=["className"],w=["className","invalid","variant","id","size"],k=["defaultTransition"],F=["delay","times","type","repeat","repeatType","repeatDelay"],E=["transitionEnd","transition"],A=["autoplay","delay","type","repeat","repeatDelay","repeatType"],S=["onComplete","onUpdate","motionValue","element"],P=["motionValue","onUpdate","onComplete","element"],C=["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"],T=["transition","transitionEnd"],R=["willChange"],M=["attrX","attrY","attrScale","originX","originY","pathLength","pathSpacing","pathOffset"],D=["transition","transitionEnd"],j=["root"],I=["transitionEnd","transition"],O=["children","asChild"],L=["children","asChild"],V=["children","persistExitAnimation"],N=["children"],B=["children"],z=["children"],W=["children","ref"],U=["ref","id","children","className","variant","placeholder"],q=["ref","children","placeholder","variant","className"],H=["ref","children","className","style"],K=["ref","value","children","disabled","className","withCheckmark"],X=["ref","onKeyDown","onChange"],_=["quantity","onChange","onDelete","min","max","children","className","size"],Y=["onClick","aria-label"],G=["aria-label"],$=["onClick","ariaLabel"],Z=["className","render"],J=["children","className","asChild","isLoading","square","ref"],Q=["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"],ee=["className"];function te(e,t,n,r){var i=ne(ae(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof i?function(e){return i.apply(n,e)}:i}function ne(){return ne="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=function(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=ae(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},ne.apply(null,arguments)}function re(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=Fe(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){u=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw o}}}}function ie(e,t,n){return t=ae(t),function(e,t){if(t&&("object"==Ae(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,oe()?Reflect.construct(t,n||[],ae(e).constructor):t.apply(e,n))}function oe(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(oe=function(){return!!e})()}function ae(e){return ae=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ae(e)}function ue(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&se(e,t)}function se(e,t){return se=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},se(e,t)}function le(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ce(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,we(r.key),r)}}function fe(e,t,n){return t&&ce(e.prototype,t),n&&ce(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function de(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function o(n,r,i,o){var s=r&&r.prototype instanceof u?r:u,l=Object.create(s.prototype);return ve(l,"_invoke",function(n,r,i){var o,u,s,l=0,c=i||[],f=!1,d={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return o=t,u=0,s=e,d.n=n,a}};function v(n,r){for(u=n,s=r,t=0;!f&&l&&!i&&t<c.length;t++){var i,o=c[t],v=d.p,h=o[2];n>3?(i=h===r)&&(s=o[(u=o[4])?5:(u=3,3)],o[4]=o[5]=e):o[0]<=v&&((i=n<2&&v<o[1])?(u=0,d.v=r,d.n=o[1]):v<h&&(i=n<3||o[0]>r||r>h)&&(o[4]=n,o[5]=r,d.n=h,u=0))}if(i||n>1)return a;throw f=!0,r}return function(i,c,h){if(l>1)throw TypeError("Generator is already running");for(f&&1===c&&v(c,h),u=c,s=h;(t=u<2?e:s)||!f;){o||(u?u<3?(u>1&&(d.n=-1),v(u,s)):d.n=s:d.v=s);try{if(l=2,o){if(u||(i="next"),t=o[i]){if(!(t=t.call(o,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,u<2&&(u=0)}else 1===u&&(t=o.return)&&t.call(o),u<2&&(s=TypeError("The iterator does not provide a '"+i+"' method"),u=1);o=e}else if((t=(f=d.n<0)?s:n.call(r,d))!==a)break}catch(t){o=e,u=1,s=t}finally{l=1}}return{value:t,done:f}}}(n,i,o),!0),l}var a={};function u(){}function s(){}function l(){}t=Object.getPrototypeOf;var c=[][r]?t(t([][r]())):(ve(t={},r,(function(){return this})),t),f=l.prototype=u.prototype=Object.create(c);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,ve(e,i,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=l,ve(f,"constructor",l),ve(l,"constructor",s),s.displayName="GeneratorFunction",ve(l,i,"GeneratorFunction"),ve(f),ve(f,i,"Generator"),ve(f,r,(function(){return this})),ve(f,"toString",(function(){return"[object Generator]"})),(de=function(){return{w:o,m:d}})()}function ve(e,t,n,r){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}ve=function(e,t,n,r){if(t)i?i(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{function o(t,n){ve(e,t,(function(e){return this._invoke(t,n,e)}))}o("next",0),o("throw",1),o("return",2)}},ve(e,t,n,r)}function he(e,t,n,r,i,o,a){try{var u=e[o](a),s=u.value}catch(e){return void n(e)}u.done?t(s):Promise.resolve(s).then(r,i)}function pe(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){he(o,r,i,a,u,"next",e)}function u(e){he(o,r,i,a,u,"throw",e)}a(void 0)}))}}function me(e){return function(e){if(Array.isArray(e))return Ee(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Fe(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ye(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function ge(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function be(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ge(Object(n),!0).forEach((function(t){xe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ge(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function xe(e,t,n){return(t=we(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function we(e){var t=function(e,t){if("object"!=Ae(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Ae(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ae(t)?t:t+""}function ke(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,u=[],s=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(u.push(r.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(e,t)||Fe(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fe(e,t){if(e){if("string"==typeof e)return Ee(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ee(e,t):void 0}}function Ee(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Ae(e){return Ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ae(e)}System.register(["./index-legacy-C2Kr5I5h.js","./index-legacy-8Bh_oy_I.js"],(function(ne,oe){"use strict";var ae,se,ce,ve;return{setters:[function(e){ae=e.j,se=e.r},function(e){ce=e.r,ve=e.a}],execute:function(){function oe(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==Ae(e))if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=oe(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function he(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=oe(e))&&(r&&(r+=" "),r+=t);return r}var ge=function(e){return"boolean"==typeof e?"".concat(e):0===e?"0":e},Fe=function(e,t){var n;if(0===e.length)return t.classGroupId;var r=e[0],i=t.nextPart.get(r),o=i?Fe(e.slice(1),i):void 0;if(o)return o;if(0!==t.validators.length){var a=e.join("-");return null==(n=t.validators.find((function(e){return(0,e.validator)(a)})))?void 0:n.classGroupId}},Ee=/^\[(.+)\]$/,Se=function(e){if(Ee.test(e)){var t=Ee.exec(e)[1],n=null==t?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Pe=function(e){var t=e.theme,n=e.classGroups,r={nextPart:new Map,validators:[]};for(var i in n)Ce(n[i],r,i,t);return r},Ce=function(e,t,n,r){e.forEach((function(e){if("string"!=typeof e){if("function"==typeof e)return Re(e)?void Ce(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach((function(e){var i=ke(e,2),o=i[0],a=i[1];Ce(a,Te(t,o),n,r)}))}else{(""===e?t:Te(t,e)).classGroupId=n}}))},Te=function(e,t){var n=e;return t.split("-").forEach((function(e){n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)})),n},Re=function(e){return e.isThemeGetter},Me=function(e){if(e<1)return{get:function(){},set:function(){}};var t=0,n=new Map,r=new Map,i=function(i,o){n.set(i,o),++t>e&&(t=0,r=n,n=new Map)};return{get:function(e){var t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(i(e,t),t):void 0},set:function(e,t){n.has(e)?n.set(e,t):i(e,t)}}},De=function(e){var t=e.prefix,n=e.experimentalParseClassName,r=function(e){for(var t,n=[],r=0,i=0,o=0,a=0;a<e.length;a++){var u=e[a];if(0===r&&0===i){if(":"===u){n.push(e.slice(o,a)),o=a+1;continue}if("/"===u){t=a;continue}}"["===u?r++:"]"===u?r--:"("===u?i++:")"===u&&i--}var s=0===n.length?e:e.substring(o),l=je(s);return{modifiers:n,hasImportantModifier:l!==s,baseClassName:l,maybePostfixModifierPosition:t&&t>o?t-o:void 0}};if(t){var i=t+":",o=r;r=function(e){return e.startsWith(i)?o(e.substring(i.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}}if(n){var a=r;r=function(e){return n({className:e,parseClassName:a})}}return r},je=function(e){return e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e},Ie=function(e){var t=Object.fromEntries(e.orderSensitiveModifiers.map((function(e){return[e,!0]})));return function(e){if(e.length<=1)return e;var n=[],r=[];return e.forEach((function(e){"["===e[0]||t[e]?(n.push.apply(n,me(r.sort()).concat([e])),r=[]):r.push(e)})),n.push.apply(n,me(r.sort())),n}},Oe=function(e){return be({cache:Me(e.cacheSize),parseClassName:De(e),sortModifiers:Ie(e)},function(e){var t=Pe(e),n=e.conflictingClassGroups,r=e.conflictingClassGroupModifiers;return{getClassGroupId:function(e){var n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),Fe(n,t)||Se(e)},getConflictingClassGroupIds:function(e,t){var i=n[e]||[];return t&&r[e]?[].concat(me(i),me(r[e])):i}}}(e))},Le=/\s+/;function Ve(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=Ne(e))&&(r&&(r+=" "),r+=t);return r}var Ne=function(e){if("string"==typeof e)return e;for(var t,n="",r=0;r<e.length;r++)e[r]&&(t=Ne(e[r]))&&(n&&(n+=" "),n+=t);return n};function Be(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var i,o,a,u=function(t){var r=n.reduce((function(e,t){return t(e)}),e());return i=Oe(r),o=i.cache.get,a=i.cache.set,u=s,s(t)};function s(e){var t=o(e);if(t)return t;var n=function(e,t){for(var n=t.parseClassName,r=t.getClassGroupId,i=t.getConflictingClassGroupIds,o=t.sortModifiers,a=[],u=e.trim().split(Le),s="",l=u.length-1;l>=0;l-=1){var c=u[l],f=n(c),d=f.isExternal,v=f.modifiers,h=f.hasImportantModifier,p=f.baseClassName,m=f.maybePostfixModifierPosition;if(d)s=c+(s.length>0?" "+s:s);else{var y=!!m,g=r(y?p.substring(0,m):p);if(!g){if(!y){s=c+(s.length>0?" "+s:s);continue}if(!(g=r(p))){s=c+(s.length>0?" "+s:s);continue}y=!1}var b=o(v).join(":"),x=h?b+"!":b,w=x+g;if(!a.includes(w)){a.push(w);for(var k=i(g,y),F=0;F<k.length;++F){var E=k[F];a.push(x+E)}s=c+(s.length>0?" "+s:s)}}}return s}(e,i);return a(e,n),n}return function(){return u(Ve.apply(null,arguments))}}var ze=function(e){var t=function(t){return t[e]||[]};return t.isThemeGetter=!0,t},We=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ue=/^\((?:(\w[\w-]*):)?(.+)\)$/i,qe=/^\d+\/\d+$/,He=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ke=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Xe=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,_e=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Ye=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ge=function(e){return qe.test(e)},$e=function(e){return!!e&&!Number.isNaN(Number(e))},Ze=function(e){return!!e&&Number.isInteger(Number(e))},Je=function(e){return e.endsWith("%")&&$e(e.slice(0,-1))},Qe=function(e){return He.test(e)},et=function(){return!0},tt=function(e){return Ke.test(e)&&!Xe.test(e)},nt=function(){return!1},rt=function(e){return _e.test(e)},it=function(e){return Ye.test(e)},ot=function(e){return!ut(e)&&!vt(e)},at=function(e){return xt(e,St,nt)},ut=function(e){return We.test(e)},st=function(e){return xt(e,Pt,tt)},lt=function(e){return xt(e,Ct,$e)},ct=function(e){return xt(e,kt,nt)},ft=function(e){return xt(e,Et,it)},dt=function(e){return xt(e,nt,rt)},vt=function(e){return Ue.test(e)},ht=function(e){return wt(e,Pt)},pt=function(e){return wt(e,Tt)},mt=function(e){return wt(e,kt)},yt=function(e){return wt(e,St)},gt=function(e){return wt(e,Et)},bt=function(e){return wt(e,Rt,!0)},xt=function(e,t,n){var r=We.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},wt=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=Ue.exec(e);return!!r&&(r[1]?t(r[1]):n)},kt=function(e){return"position"===e},Ft=new Set(["image","url"]),Et=function(e){return Ft.has(e)},At=new Set(["length","size","percentage"]),St=function(e){return At.has(e)},Pt=function(e){return"length"===e},Ct=function(e){return"number"===e},Tt=function(e){return"family-name"===e},Rt=function(e){return"shadow"===e},Mt=function(){var e=ze("color"),t=ze("font"),n=ze("text"),r=ze("font-weight"),i=ze("tracking"),o=ze("leading"),a=ze("breakpoint"),u=ze("container"),s=ze("spacing"),l=ze("radius"),c=ze("shadow"),f=ze("inset-shadow"),d=ze("drop-shadow"),v=ze("blur"),h=ze("perspective"),p=ze("aspect"),m=ze("ease"),y=ze("animate"),g=function(){return[vt,ut,s]},b=function(){return[Ge,"full","auto"].concat(me(g()))},x=function(){return[Ze,"none","subgrid",vt,ut]},w=function(){return["auto",{span:["full",Ze,vt,ut]},vt,ut]},k=function(){return[Ze,"auto",vt,ut]},F=function(){return["auto","min","max","fr",vt,ut]},E=function(){return["auto"].concat(me(g()))},A=function(){return[Ge,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit"].concat(me(g()))},S=function(){return[e,vt,ut]},P=function(){return[Je,st]},C=function(){return["","none","full",l,vt,ut]},T=function(){return["",$e,ht,st]},R=function(){return["","none",v,vt,ut]},M=function(){return["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",vt,ut]},D=function(){return["none",$e,vt,ut]},j=function(){return["none",$e,vt,ut]},I=function(){return[$e,vt,ut]},O=function(){return[Ge,"full"].concat(me(g()))};return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Qe],breakpoint:[Qe],color:[et],container:[Qe],"drop-shadow":[Qe],ease:["in","out","in-out"],font:[ot],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Qe],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Qe],shadow:[Qe],spacing:["px",$e],text:[Qe],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ge,ut,vt,p]}],container:["container"],columns:[{columns:[$e,ut,vt,u]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(me(["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"]),[ut,vt])}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:b()}],"inset-x":[{"inset-x":b()}],"inset-y":[{"inset-y":b()}],start:[{start:b()}],end:[{end:b()}],top:[{top:b()}],right:[{right:b()}],bottom:[{bottom:b()}],left:[{left:b()}],visibility:["visible","invisible","collapse"],z:[{z:[Ze,"auto",vt,ut]}],basis:[{basis:[Ge,"full","auto",u].concat(me(g()))}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[$e,Ge,"auto","initial","none",ut]}],grow:[{grow:["",$e,vt,ut]}],shrink:[{shrink:["",$e,vt,ut]}],order:[{order:[Ze,"first","last","none",vt,ut]}],"grid-cols":[{"grid-cols":x()}],"col-start-end":[{col:w()}],"col-start":[{"col-start":k()}],"col-end":[{"col-end":k()}],"grid-rows":[{"grid-rows":x()}],"row-start-end":[{row:w()}],"row-start":[{"row-start":k()}],"row-end":[{"row-end":k()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":F()}],"auto-rows":[{"auto-rows":F()}],gap:[{gap:g()}],"gap-x":[{"gap-x":g()}],"gap-y":[{"gap-y":g()}],"justify-content":[{justify:[].concat(me(["start","end","center","between","around","evenly","stretch","baseline"]),["normal"])}],"justify-items":[{"justify-items":[].concat(me(["start","end","center","stretch"]),["normal"])}],"justify-self":[{"justify-self":["auto"].concat(me(["start","end","center","stretch"]))}],"align-content":[{content:["normal"].concat(me(["start","end","center","between","around","evenly","stretch","baseline"]))}],"align-items":[{items:[].concat(me(["start","end","center","stretch"]),["baseline"])}],"align-self":[{self:["auto"].concat(me(["start","end","center","stretch"]),["baseline"])}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":[].concat(me(["start","end","center","stretch"]),["baseline"])}],"place-self":[{"place-self":["auto"].concat(me(["start","end","center","stretch"]))}],p:[{p:g()}],px:[{px:g()}],py:[{py:g()}],ps:[{ps:g()}],pe:[{pe:g()}],pt:[{pt:g()}],pr:[{pr:g()}],pb:[{pb:g()}],pl:[{pl:g()}],m:[{m:E()}],mx:[{mx:E()}],my:[{my:E()}],ms:[{ms:E()}],me:[{me:E()}],mt:[{mt:E()}],mr:[{mr:E()}],mb:[{mb:E()}],ml:[{ml:E()}],"space-x":[{"space-x":g()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":g()}],"space-y-reverse":["space-y-reverse"],size:[{size:A()}],w:[{w:[u,"screen"].concat(me(A()))}],"min-w":[{"min-w":[u,"screen","none"].concat(me(A()))}],"max-w":[{"max-w":[u,"screen","none","prose",{screen:[a]}].concat(me(A()))}],h:[{h:["screen"].concat(me(A()))}],"min-h":[{"min-h":["screen","none"].concat(me(A()))}],"max-h":[{"max-h":["screen"].concat(me(A()))}],"font-size":[{text:["base",n,ht,st]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,vt,lt]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Je,ut]}],"font-family":[{font:[pt,ut,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,vt,ut]}],"line-clamp":[{"line-clamp":[$e,"none",vt,lt]}],leading:[{leading:[o].concat(me(g()))}],"list-image":[{"list-image":["none",vt,ut]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",vt,ut]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:S()}],"text-color":[{text:S()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(me(["solid","dashed","dotted","double"]),["wavy"])}],"text-decoration-thickness":[{decoration:[$e,"from-font","auto",vt,st]}],"text-decoration-color":[{decoration:S()}],"underline-offset":[{"underline-offset":[$e,"auto",vt,ut]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:g()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",vt,ut]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",vt,ut]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(me(["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"]),[mt,ct])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",yt,at]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Ze,vt,ut],radial:["",vt,ut],conic:[Ze,vt,ut]},gt,ft]}],"bg-color":[{bg:S()}],"gradient-from-pos":[{from:P()}],"gradient-via-pos":[{via:P()}],"gradient-to-pos":[{to:P()}],"gradient-from":[{from:S()}],"gradient-via":[{via:S()}],"gradient-to":[{to:S()}],rounded:[{rounded:C()}],"rounded-s":[{"rounded-s":C()}],"rounded-e":[{"rounded-e":C()}],"rounded-t":[{"rounded-t":C()}],"rounded-r":[{"rounded-r":C()}],"rounded-b":[{"rounded-b":C()}],"rounded-l":[{"rounded-l":C()}],"rounded-ss":[{"rounded-ss":C()}],"rounded-se":[{"rounded-se":C()}],"rounded-ee":[{"rounded-ee":C()}],"rounded-es":[{"rounded-es":C()}],"rounded-tl":[{"rounded-tl":C()}],"rounded-tr":[{"rounded-tr":C()}],"rounded-br":[{"rounded-br":C()}],"rounded-bl":[{"rounded-bl":C()}],"border-w":[{border:T()}],"border-w-x":[{"border-x":T()}],"border-w-y":[{"border-y":T()}],"border-w-s":[{"border-s":T()}],"border-w-e":[{"border-e":T()}],"border-w-t":[{"border-t":T()}],"border-w-r":[{"border-r":T()}],"border-w-b":[{"border-b":T()}],"border-w-l":[{"border-l":T()}],"divide-x":[{"divide-x":T()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":T()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[].concat(me(["solid","dashed","dotted","double"]),["hidden","none"])}],"divide-style":[{divide:[].concat(me(["solid","dashed","dotted","double"]),["hidden","none"])}],"border-color":[{border:S()}],"border-color-x":[{"border-x":S()}],"border-color-y":[{"border-y":S()}],"border-color-s":[{"border-s":S()}],"border-color-e":[{"border-e":S()}],"border-color-t":[{"border-t":S()}],"border-color-r":[{"border-r":S()}],"border-color-b":[{"border-b":S()}],"border-color-l":[{"border-l":S()}],"divide-color":[{divide:S()}],"outline-style":[{outline:[].concat(me(["solid","dashed","dotted","double"]),["none","hidden"])}],"outline-offset":[{"outline-offset":[$e,vt,ut]}],"outline-w":[{outline:["",$e,ht,st]}],"outline-color":[{outline:[e]}],shadow:[{shadow:["","none",c,bt,dt]}],"shadow-color":[{shadow:S()}],"inset-shadow":[{"inset-shadow":["none",vt,ut,f]}],"inset-shadow-color":[{"inset-shadow":S()}],"ring-w":[{ring:T()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:S()}],"ring-offset-w":[{"ring-offset":[$e,st]}],"ring-offset-color":[{"ring-offset":S()}],"inset-ring-w":[{"inset-ring":T()}],"inset-ring-color":[{"inset-ring":S()}],opacity:[{opacity:[$e,vt,ut]}],"mix-blend":[{"mix-blend":[].concat(me(["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]),["plus-darker","plus-lighter"])}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none",vt,ut]}],blur:[{blur:R()}],brightness:[{brightness:[$e,vt,ut]}],contrast:[{contrast:[$e,vt,ut]}],"drop-shadow":[{"drop-shadow":["","none",d,vt,ut]}],grayscale:[{grayscale:["",$e,vt,ut]}],"hue-rotate":[{"hue-rotate":[$e,vt,ut]}],invert:[{invert:["",$e,vt,ut]}],saturate:[{saturate:[$e,vt,ut]}],sepia:[{sepia:["",$e,vt,ut]}],"backdrop-filter":[{"backdrop-filter":["","none",vt,ut]}],"backdrop-blur":[{"backdrop-blur":R()}],"backdrop-brightness":[{"backdrop-brightness":[$e,vt,ut]}],"backdrop-contrast":[{"backdrop-contrast":[$e,vt,ut]}],"backdrop-grayscale":[{"backdrop-grayscale":["",$e,vt,ut]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[$e,vt,ut]}],"backdrop-invert":[{"backdrop-invert":["",$e,vt,ut]}],"backdrop-opacity":[{"backdrop-opacity":[$e,vt,ut]}],"backdrop-saturate":[{"backdrop-saturate":[$e,vt,ut]}],"backdrop-sepia":[{"backdrop-sepia":["",$e,vt,ut]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":g()}],"border-spacing-x":[{"border-spacing-x":g()}],"border-spacing-y":[{"border-spacing-y":g()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",vt,ut]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[$e,"initial",vt,ut]}],ease:[{ease:["linear","initial",m,vt,ut]}],delay:[{delay:[$e,vt,ut]}],animate:[{animate:["none",y,vt,ut]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,vt,ut]}],"perspective-origin":[{"perspective-origin":M()}],rotate:[{rotate:D()}],"rotate-x":[{"rotate-x":D()}],"rotate-y":[{"rotate-y":D()}],"rotate-z":[{"rotate-z":D()}],scale:[{scale:j()}],"scale-x":[{"scale-x":j()}],"scale-y":[{"scale-y":j()}],"scale-z":[{"scale-z":j()}],"scale-3d":["scale-3d"],skew:[{skew:I()}],"skew-x":[{"skew-x":I()}],"skew-y":[{"skew-y":I()}],transform:[{transform:[vt,ut,"","none","gpu","cpu"]}],"transform-origin":[{origin:M()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:O()}],"translate-x":[{"translate-x":O()}],"translate-y":[{"translate-y":O()}],"translate-z":[{"translate-z":O()}],"translate-none":["translate-none"],accent:[{accent:S()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:S()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",vt,ut]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":g()}],"scroll-mx":[{"scroll-mx":g()}],"scroll-my":[{"scroll-my":g()}],"scroll-ms":[{"scroll-ms":g()}],"scroll-me":[{"scroll-me":g()}],"scroll-mt":[{"scroll-mt":g()}],"scroll-mr":[{"scroll-mr":g()}],"scroll-mb":[{"scroll-mb":g()}],"scroll-ml":[{"scroll-ml":g()}],"scroll-p":[{"scroll-p":g()}],"scroll-px":[{"scroll-px":g()}],"scroll-py":[{"scroll-py":g()}],"scroll-ps":[{"scroll-ps":g()}],"scroll-pe":[{"scroll-pe":g()}],"scroll-pt":[{"scroll-pt":g()}],"scroll-pr":[{"scroll-pr":g()}],"scroll-pb":[{"scroll-pb":g()}],"scroll-pl":[{"scroll-pl":g()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",vt,ut]}],fill:[{fill:["none"].concat(me(S()))}],"stroke-w":[{stroke:[$e,ht,st,lt]}],stroke:[{stroke:["none"].concat(me(S()))}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}},Dt=Be(Mt),jt=function(t){var n=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i,o;return Ae(null==t||null===(i=t.hooks)||void 0===i?void 0:i["cx:done"])<"u"?null==t?void 0:t.hooks["cx:done"](he(n)):Ae(null==t||null===(o=t.hooks)||void 0===o?void 0:o.onComplete)<"u"?null==t?void 0:t.hooks.onComplete(he(n)):he(n)};return{compose:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){var r=Object.fromEntries(Object.entries(e||{}).filter((function(e){var t=ke(e,1)[0];return!["class","className"].includes(t)})));return n(t.map((function(e){return e(r)})),null==e?void 0:e.class,null==e?void 0:e.className)}},cva:function(t){return function(r){var i;if(null==(null==t?void 0:t.variants))return n(null==t?void 0:t.base,null==r?void 0:r.class,null==r?void 0:r.className);var o=t.variants,a=t.defaultVariants,u=Object.keys(o).map((function(e){var t=null==r?void 0:r[e],n=null==a?void 0:a[e],i=ge(t)||ge(n);return o[e][i]})),s=be(be({},a),r&&Object.entries(r).reduce((function(e,t){var n=ke(t,2),r=n[0],i=n[1];return Ae(i)>"u"?e:be(be({},e),{},xe({},r,i))}),{})),l=null==t||null===(i=t.compoundVariants)||void 0===i?void 0:i.reduce((function(t,n){var r=n.class,i=n.className,o=ye(n,e);return Object.entries(o).every((function(e){var t=ke(e,2),n=t[0],r=t[1],i=s[n];return Array.isArray(r)?r.includes(i):i===r}))?[].concat(me(t),[r,i]):t}),[]);return n(null==t?void 0:t.base,u,l,null==r?void 0:r.class,null==r?void 0:r.className)}},cx:n}}({hooks:{onComplete:function(e){return Dt(e)}}}),It=jt.cva,Ot=jt.cx;ne({i:It,n:Ot});
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */
var Lt=function(e){return e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()},Vt=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((function(e,t,n){return!!e&&""!==e.trim()&&n.indexOf(e)===t})).join(" ").trim()},Nt={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},Bt=ce.forwardRef((function(e,n){var r=e.color,i=void 0===r?"currentColor":r,o=e.size,a=void 0===o?24:o,u=e.strokeWidth,s=void 0===u?2:u,l=e.absoluteStrokeWidth,c=e.className,f=void 0===c?"":c,d=e.children,v=e.iconNode,h=ye(e,t);return ce.createElement("svg",be(be({ref:n},Nt),{},{width:a,height:a,stroke:i,strokeWidth:l?24*Number(s)/Number(a):s,className:Vt("lucide",f)},h),[].concat(me(v.map((function(e){var t=ke(e,2),n=t[0],r=t[1];return ce.createElement(n,r)}))),me(Array.isArray(d)?d:[d])))})),zt=function(e,t){var r=ce.forwardRef((function(r,i){var o=r.className,a=ye(r,n);return ce.createElement(Bt,be({ref:i,iconNode:t,className:Vt("lucide-".concat(Lt(e)),o)},a))}));return r.displayName="".concat(e),r},Wt=(ne("r",zt("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])),function(e){return ce.isValidElement(e)&&!!e.props&&"object"==Ae(e.props)&&"key"in e}),Ut=ne("j",(function(e){var t=e.children,n=e.ref,i=ye(e,r),o=ce.Children.only(t);if(Wt(o))return ce.cloneElement(o,be(be(be({},i),o.props),{},{ref:n,style:be(be({},i.style),o.props.style),className:Ot(o.props.className,i.className)}));throw new Error("Slot needs a valid react element child")}));
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */Ut.displayName="Slot";var qt=function(e){var t=e.asChild,n=e.child,r=e.children,o=ye(e,i);return ae.jsx(ae.Fragment,{children:t?Wt(n)?ce.cloneElement(n,o,r(n.props.children)):null:r(n)})},Ht=It({base:["relative","animate-spin","before:absolute before:top-0 before:left-0 before:block before:size-full before:rounded-full before:border-current before:opacity-40","after:top-0 after:left-0 after:block after:size-full after:rounded-full after:border-transparent after:border-t-current after:border-r-current"],variants:{size:{xs:"size-2 before:border after:border",sm:"size-3 before:border-2 after:border-2",md:"size-4 before:border-2 after:border-2",lg:"size-5 before:border-3 after:border-3"}}}),Kt=ne("m",(function(e){var t=e.className,n=e.size,r=void 0===n?"md":n,i=ye(e,o);return ae.jsx("div",be({"aria-label":"loading",role:"progressbar",className:Ht({size:r,className:t})},i))})),Xt=It({base:["chunky inline-flex shrink-0 text-(--button-text-color) focus-visible:outline-none enabled:cursor-pointer disabled:pointer-events-none disabled:opacity-50","items-center justify-center gap-1.5 whitespace-nowrap","h-[calc(var(--button-height)-var(--chunky-depth,var(--chunky-default-depth)))]"],variants:{variant:{primary:"[--button-text-color:var(--color-foreground)]",secondary:["[--button-text-color:var(--color-foreground)]","[--chunky-bg-color:var(--color-background-high)]","[--chunky-shadow-bg-color:var(--color-background-low)]"],accent:["font-medium","[--button-text-color:var(--color-white)]","[--chunky-bg-color:var(--color-accent)]","[--chunky-border-color:var(--color-accent-secondary)]","[--chunky-shadow-bg-color:var(--color-accent-secondary)]"],positive:["font-medium","[--button-text-color:var(--color-white)]","[--chunky-bg-color:var(--color-positive)]","[--chunky-border-color:var(--color-positive-secondary)]","[--chunky-shadow-bg-color:var(--color-positive-secondary)]"],negative:["font-medium","[--button-text-color:var(--color-white)]","[--chunky-bg-color:var(--color-negative)]","[--chunky-border-color:var(--color-negative-secondary)]","[--chunky-shadow-bg-color:var(--color-negative-secondary)]"],warning:["font-medium","[--button-text-color:var(--color-black)]","[--chunky-bg-color:var(--color-warning)]","[--chunky-border-color:var(--color-warning-secondary)]","[--chunky-shadow-bg-color:var(--color-warning-secondary)]"]},size:{sm:"rounded-lg px-3 text-xs [--button-height:--spacing(8)]",md:"rounded-xl px-4 text-sm [--button-height:--spacing(10)]",lg:"rounded-xl px-5 text-base [--button-height:--spacing(12)]",xl:"rounded-2xl px-6 text-xl [--button-height:--spacing(14)]"},square:{true:"w-(--button-height,2.5em) px-0",false:""}},defaultVariants:{variant:"primary",size:"md"}}),_t=ne("z",(function(e){var t=e.children,n=e.className,r=e.variant,i=e.asChild,o=void 0!==i&&i,u=e.isLoading,s=e.size,l=e.square,c=e.type,f=void 0===c?"button":c,d=e.ref,v=ye(e,a);return ae.jsx(o?Ut:"button",be(be({className:Ot(Xt({className:n,variant:r,size:s,square:l}),u&&"text-transparent"),ref:d,type:f},v),{},{children:ae.jsx(qt,{asChild:o,child:t,children:function(e){return ae.jsxs(ae.Fragment,{children:[e,u&&ae.jsx("span",{"data-button-spinner":!0,className:Ot("absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2","text-(--button-text-color)"),children:ae.jsx(Kt,{})})]})}})}))})),Yt=ce.createContext({locale:"de-DE",currency:"EUR"});ne("l",(function(){return ce.use(Yt)}));zt("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);zt("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var Gt=ce.createContext(void 0),$t=function(){return ce.use(Gt)};function Zt(){return("undefined"==typeof window?"undefined":Ae(window))<"u"}function Jt(e){return tn(e)?(e.nodeName||"").toLowerCase():"#document"}function Qt(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function en(e){var t;return null==(t=(tn(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function tn(e){return!!Zt()&&(e instanceof Node||e instanceof Qt(e).Node)}function nn(e){return!!Zt()&&(e instanceof Element||e instanceof Qt(e).Element)}function rn(e){return!!Zt()&&(e instanceof HTMLElement||e instanceof Qt(e).HTMLElement)}function on(e){return!(!Zt()||("undefined"==typeof ShadowRoot?"undefined":Ae(ShadowRoot))>"u")&&(e instanceof ShadowRoot||e instanceof Qt(e).ShadowRoot)}function an(e){var t=dn(e),n=t.overflow,r=t.overflowX,i=t.overflowY,o=t.display;return/auto|scroll|overlay|hidden|clip/.test(n+i+r)&&!["inline","contents"].includes(o)}function un(e){return["table","td","th"].includes(Jt(e))}function sn(e){return[":popover-open",":modal"].some((function(t){try{return e.matches(t)}catch(n){return!1}}))}function ln(e){var t=cn(),n=nn(e)?dn(e):e;return["transform","translate","scale","rotate","perspective"].some((function(e){return!!n[e]&&"none"!==n[e]}))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((function(e){return(n.willChange||"").includes(e)}))||["paint","layout","strict","content"].some((function(e){return(n.contain||"").includes(e)}))}function cn(){return!(("undefined"==typeof CSS?"undefined":Ae(CSS))>"u"||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function fn(e){return["html","body","#document"].includes(Jt(e))}function dn(e){return Qt(e).getComputedStyle(e)}function vn(e){return nn(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function hn(e){if("html"===Jt(e))return e;var t=e.assignedSlot||e.parentNode||on(e)&&e.host||en(e);return on(t)?t.host:t}function pn(e){var t=hn(e);return fn(t)?e.ownerDocument?e.ownerDocument.body:e.body:rn(t)&&an(t)?t:pn(t)}function mn(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);var i=pn(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),a=Qt(i);if(o){var u=yn(a);return t.concat(a,a.visualViewport||[],an(i)?i:[],u&&n?mn(u):[])}return t.concat(i,mn(i,[],n))}function yn(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function gn(e){for(var t=e.activeElement;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}function bn(e,t){if(!e||!t)return!1;var n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&on(n))for(var r=t;r;){if(e===r)return!0;r=r.parentNode||r.host}return!1}function xn(){var e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function wn(e){return!(0!==e.mozInputSource||!e.isTrusted)||(Fn()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function kn(e){return!xn().includes("jsdom/")&&(!Fn()&&0===e.width&&0===e.height||Fn()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}function Fn(){var e=/android/i;return e.test(function(){var e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}())||e.test(xn())}function En(e,t){return["mouse","pen"].includes(e)}function An(e){return(null==e?void 0:e.ownerDocument)||document}function Sn(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);var n=e;return null!=n.target&&t.contains(n.target)}function Pn(e){return"composedPath"in e?e.composedPath()[0]:e.target}ne({A:$t,B:function(e){var t=e.children,n=e.className,r=ye(e,u),i=ce.useId(),o=ke(ce.useState([]),2),a=o[0],s=o[1],l=ke(ce.useState([]),2),c=l[0],f=l[1],d=ke(ce.useState([]),2),v=d[0],h=d[1],p=ce.useCallback((function(e){return s((function(t){return[].concat(me(t),[e])})),function(){s((function(t){return t.filter((function(t){return t!==e}))}))}}),[]),m=ce.useCallback((function(e){return f((function(t){return[].concat(me(t),[e])})),function(){f((function(t){return t.filter((function(t){return t!==e}))}))}}),[]),y=ce.useCallback((function(e){return h((function(t){return[].concat(me(t),[e])})),function(){h((function(t){return t.filter((function(t){return t!==e}))}))}}),[]),g=ce.useCallback((function(e,t){switch(e){case"error":return p(t);case"description":return m(t);case"label":return y(t)}}),[p,m,y]),b=ce.useMemo((function(){return a.length>0?a.join(" "):void 0}),[a]),x=ce.useMemo((function(){return c.length>0?c.join(" "):void 0}),[c]),w=ce.useMemo((function(){return v.length>0?v.join(" "):void 0}),[v]),k=ce.useMemo((function(){return{registerElement:g,id:i,"aria-errormessage":b,"aria-describedby":x,"aria-labelledby":w}}),[g,i,b,x,w]),F=1===Object.keys(r).length&&"children"in r?ce.Fragment:"div";return ae.jsx(Gt,{value:k,children:ae.jsx(F,be(be({className:n},r),{},{children:t}))})}});function Cn(e){return rn(e)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])")}function Tn(e){e.preventDefault(),e.stopPropagation()}function Rn(e){return!!e&&("combobox"===e.getAttribute("role")&&Cn(e))}var Mn=["top","right","bottom","left"],Dn=Math.min,jn=Math.max,In=Math.round,On=Math.floor,Ln=function(e){return{x:e,y:e}},Vn={left:"right",right:"left",bottom:"top",top:"bottom"},Nn={start:"end",end:"start"};function Bn(e,t,n){return jn(e,Dn(t,n))}function zn(e,t){return"function"==typeof e?e(t):e}function Wn(e){return e.split("-")[0]}function Un(e){return e.split("-")[1]}function qn(e){return"x"===e?"y":"x"}function Hn(e){return"y"===e?"height":"width"}function Kn(e){return["top","bottom"].includes(Wn(e))?"y":"x"}function Xn(e){return qn(Kn(e))}function _n(e,t,n){void 0===n&&(n=!1);var r=Un(e),i=Xn(e),o=Hn(i),a="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(a=Zn(a)),[a,Zn(a)]}function Yn(e){var t=Zn(e);return[Gn(e),t,Gn(t)]}function Gn(e){return e.replace(/start|end/g,(function(e){return Nn[e]}))}function $n(e,t,n,r){var i=Un(e),o=function(e,t,n){var r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":return n?t?i:r:t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(Wn(e),"start"===n,r);return i&&(o=o.map((function(e){return e+"-"+i})),t&&(o=o.concat(o.map(Gn)))),o}function Zn(e){return e.replace(/left|right|bottom|top/g,(function(e){return Vn[e]}))}function Jn(e){return"number"!=typeof e?function(e){return be({top:0,right:0,bottom:0,left:0},e)}(e):{top:e,right:e,bottom:e,left:e}}function Qn(e){var t=e.x,n=e.y,r=e.width,i=e.height;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}
/*!
      * tabbable 6.2.0
      * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
      */var er=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"].join(","),tr=("undefined"==typeof Element?"undefined":Ae(Element))>"u",nr=tr?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,rr=!tr&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},ir=function e(t,n){var r;void 0===n&&(n=!0);var i=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert");return""===i||"true"===i||n&&t&&e(t.parentNode)},or=function e(t,n,r){for(var i=[],o=Array.from(t);o.length;){var a=o.shift();if(!ir(a,!1))if("SLOT"===a.tagName){var u=a.assignedElements(),s=e(u.length?u:a.children,!0,r);r.flatten?i.push.apply(i,s):i.push({scopeParent:a,candidates:s})}else{nr.call(a,er)&&r.filter(a)&&(n||!t.includes(a))&&i.push(a);var l=a.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(a),c=!ir(l,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(a));if(l&&c){var f=e(!0===l?a.children:l.children,!0,r);r.flatten?i.push.apply(i,f):i.push({scopeParent:a,candidates:f})}else o.unshift.apply(o,a.children)}}return i},ar=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},ur=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!ar(e)?0:e.tabIndex},sr=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},lr=function(e){return"INPUT"===e.tagName},cr=function(e){return function(e){return lr(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||rr(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if(("undefined"==typeof window?"undefined":Ae(window))<"u"&&Ae(window.CSS)<"u"&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(o){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",o.message),!1}var i=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!i||i===e}(e)},fr=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},dr=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var i=nr.call(e,"details>summary:first-of-type")?e.parentElement:e;if(nr.call(i,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return fr(e)}else{if("function"==typeof r){for(var o=e;e;){var a=e.parentElement,u=rr(e);if(a&&!a.shadowRoot&&!0===r(a))return fr(e);e=e.assignedSlot?e.assignedSlot:a||u===e.ownerDocument?a:u.host}e=o}if(function(e){var t,n,r,i,o=e&&rr(e),a=null===(t=o)||void 0===t?void 0:t.host,u=!1;if(o&&o!==e)for(u=!!(null!==(n=a)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(a)||null!=e&&null!==(i=e.ownerDocument)&&void 0!==i&&i.contains(e));!u&&a;){var s,l,c;u=!(null===(l=a=null===(s=o=rr(a))||void 0===s?void 0:s.host)||void 0===l||null===(c=l.ownerDocument)||void 0===c||!c.contains(a))}return u}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},vr=function(e,t){return!(t.disabled||ir(t)||function(e){return lr(e)&&"hidden"===e.type}(t)||dr(t,e)||function(e){var t="DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}));return t}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!nr.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},hr=function(e,t){return!(cr(t)||ur(t)<0||!vr(e,t))},pr=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},mr=function e(t){var n=[],r=[];return t.forEach((function(t,i){var o=!!t.scopeParent,a=o?t.scopeParent:t,u=function(e,t){var n=ur(e);return n<0&&t&&!ar(e)?0:n}(a,o),s=o?e(t.candidates):a;0===u?o?n.push.apply(n,s):n.push(a):r.push({documentOrder:i,tabIndex:u,item:t,isScope:o,content:s})})),r.sort(sr).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},yr=function(e,t){var n;return n=(t=t||{}).getShadowRoot?or([e],t.includeContainer,{filter:hr.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:pr}):function(e,t,n){if(ir(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(er));return t&&nr.call(e,er)&&r.unshift(e),r.filter(n)}(e,t.includeContainer,hr.bind(null,t)),mr(n)};function gr(e,t,n){var r,i=e.reference,o=e.floating,a=Kn(t),u=Xn(t),s=Hn(u),l=Wn(t),c="y"===a,f=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,v=i[s]/2-o[s]/2;switch(l){case"top":r={x:f,y:i.y-o.height};break;case"bottom":r={x:f,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(Un(t)){case"start":r[u]-=v*(n&&c?-1:1);break;case"end":r[u]+=v*(n&&c?-1:1)}return r}var br=function(){var e=pe(de().m((function e(t,n,r){var i,o,a,u,s,l,c,f,d,v,h,p,m,y,g,b,x,w,k,F,E,A,S,P,C,T,R;return de().w((function(e){for(;;)switch(e.n){case 0:return i=r.placement,o=void 0===i?"bottom":i,a=r.strategy,u=void 0===a?"absolute":a,s=r.middleware,l=void 0===s?[]:s,c=r.platform,f=l.filter(Boolean),e.n=1,null==c.isRTL?void 0:c.isRTL(n);case 1:return d=e.v,e.n=2,c.getElementRects({reference:t,floating:n,strategy:u});case 2:v=e.v,h=gr(v,o,d),p=h.x,m=h.y,y=o,g={},b=0,x=0;case 3:if(!(x<f.length)){e.n=11;break}return k=f[x],F=k.name,E=k.fn,e.n=4,E({x:p,y:m,initialPlacement:o,placement:y,strategy:u,middlewareData:g,rects:v,platform:c,elements:{reference:t,floating:n}});case 4:if(A=e.v,S=A.x,P=A.y,C=A.data,T=A.reset,p=null!=S?S:p,m=null!=P?P:m,g=be(be({},g),{},xe({},F,be(be({},g[F]),C))),!(T&&b<=50)){e.n=10;break}if(b++,!("object"==Ae(T))){e.n=9;break}if(T.placement&&(y=T.placement),!T.rects){e.n=8;break}if(!0!==T.rects){e.n=6;break}return e.n=5,c.getElementRects({reference:t,floating:n,strategy:u});case 5:R=e.v,e.n=7;break;case 6:R=T.rects;case 7:v=R;case 8:w=gr(v,y,d),p=w.x,m=w.y;case 9:x=-1;case 10:x++,e.n=3;break;case 11:return e.a(2,{x:p,y:m,placement:y,strategy:u,middlewareData:g})}}),e)})));return function(t,n,r){return e.apply(this,arguments)}}();function xr(e,t){return wr.apply(this,arguments)}function wr(){return wr=pe(de().m((function e(t,n){var r,i,o,a,u,s,l,c,f,d,v,h,p,m,y,g,b,x,w,k,F,E,A,S,P,C,T,R,M,D,j,I,O,L,V,N;return de().w((function(e){for(;;)switch(e.n){case 0:return void 0===n&&(n={}),i=t.x,o=t.y,a=t.platform,u=t.rects,s=t.elements,l=t.strategy,c=zn(n,t),f=c.boundary,d=void 0===f?"clippingAncestors":f,v=c.rootBoundary,h=void 0===v?"viewport":v,p=c.elementContext,m=void 0===p?"floating":p,y=c.altBoundary,g=void 0!==y&&y,b=c.padding,x=Jn(void 0===b?0:b),w=s[g?"floating"===m?"reference":"floating":m],P=Qn,C=a,e.n=1,null==a.isElement?void 0:a.isElement(w);case 1:if(R=r=e.v,T=null==R){e.n=2;break}T=r;case 2:if(!T){e.n=3;break}M=w,e.n=6;break;case 3:if(D=w.contextElement){e.n=5;break}return e.n=4,null==a.getDocumentElement?void 0:a.getDocumentElement(s.floating);case 4:D=e.v;case 5:M=D;case 6:return j={element:M,boundary:d,rootBoundary:h,strategy:l},e.n=7,C.getClippingRect.call(C,j);case 7:return I=e.v,k=P(I),F="floating"===m?{x:i,y:o,width:u.floating.width,height:u.floating.height}:u.reference,e.n=8,null==a.getOffsetParent?void 0:a.getOffsetParent(s.floating);case 8:return E=e.v,e.n=9,null==a.isElement?void 0:a.isElement(E);case 9:if(!e.v){e.n=12;break}return e.n=10,null==a.getScale?void 0:a.getScale(E);case 10:if(L=e.v){e.n=11;break}L={x:1,y:1};case 11:O=L,e.n=13;break;case 12:O={x:1,y:1};case 13:if(A=O,V=Qn,!a.convertOffsetParentRelativeRectToViewportRelativeRect){e.n=15;break}return e.n=14,a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:F,offsetParent:E,strategy:l});case 14:N=e.v,e.n=16;break;case 15:N=F;case 16:return S=V(N),e.a(2,{top:(k.top-S.top+x.top)/A.y,bottom:(S.bottom-k.bottom+x.bottom)/A.y,left:(k.left-S.left+x.left)/A.x,right:(S.right-k.right+x.right)/A.x})}}),e)}))),wr.apply(this,arguments)}function kr(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Fr(e){return Mn.some((function(t){return e[t]>=0}))}function Er(e,t){return Ar.apply(this,arguments)}function Ar(){return Ar=pe(de().m((function e(t,n){var r,i,o,a,u,s,l,c,f,d,v,h,p,m;return de().w((function(e){for(;;)switch(e.n){case 0:return r=t.placement,i=t.platform,o=t.elements,e.n=1,null==i.isRTL?void 0:i.isRTL(o.floating);case 1:return a=e.v,u=Wn(r),s=Un(r),l="y"===Kn(r),c=["left","top"].includes(u)?-1:1,f=a&&l?-1:1,d=zn(n,t),v="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis},h=v.mainAxis,p=v.crossAxis,m=v.alignmentAxis,e.a(2,(s&&"number"==typeof m&&(p="end"===s?-1*m:m),l?{x:p*f,y:h*c}:{x:h*c,y:p*f}))}}),e)}))),Ar.apply(this,arguments)}function Sr(e){var t=dn(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=rn(e),o=i?e.offsetWidth:n,a=i?e.offsetHeight:r,u=In(n)!==o||In(r)!==a;return u&&(n=o,r=a),{width:n,height:r,$:u}}function Pr(e){return nn(e)?e:e.contextElement}function Cr(e){var t=Pr(e);if(!rn(t))return Ln(1);var n=t.getBoundingClientRect(),r=Sr(t),i=r.width,o=r.height,a=r.$,u=(a?In(n.width):n.width)/i,s=(a?In(n.height):n.height)/o;return(!u||!Number.isFinite(u))&&(u=1),(!s||!Number.isFinite(s))&&(s=1),{x:u,y:s}}var Tr=Ln(0);function Rr(e){var t=Qt(e);return cn()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Tr}function Mr(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);var i=e.getBoundingClientRect(),o=Pr(e),a=Ln(1);t&&(r?nn(r)&&(a=Cr(r)):a=Cr(e));var u=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==Qt(e))&&t}(o,n,r)?Rr(o):Ln(0),s=(i.left+u.x)/a.x,l=(i.top+u.y)/a.y,c=i.width/a.x,f=i.height/a.y;if(o)for(var d=Qt(o),v=r&&nn(r)?Qt(r):r,h=d,p=yn(h);p&&r&&v!==h;){var m=Cr(p),y=p.getBoundingClientRect(),g=dn(p),b=y.left+(p.clientLeft+parseFloat(g.paddingLeft))*m.x,x=y.top+(p.clientTop+parseFloat(g.paddingTop))*m.y;s*=m.x,l*=m.y,c*=m.x,f*=m.y,s+=b,l+=x,p=yn(h=Qt(p))}return Qn({width:c,height:f,x:s,y:l})}function Dr(e,t){var n=vn(e).scrollLeft;return t?t.left+n:Mr(en(e)).left+n}function jr(e,t,n){void 0===n&&(n=!1);var r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Dr(e,r)),y:r.top+t.scrollTop}}function Ir(e,t,n){var r;if("viewport"===t)r=function(e,t){var n=Qt(e),r=en(e),i=n.visualViewport,o=r.clientWidth,a=r.clientHeight,u=0,s=0;if(i){o=i.width,a=i.height;var l=cn();(!l||l&&"fixed"===t)&&(u=i.offsetLeft,s=i.offsetTop)}return{width:o,height:a,x:u,y:s}}(e,n);else if("document"===t)r=function(e){var t=en(e),n=vn(e),r=e.ownerDocument.body,i=jn(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=jn(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Dr(e),u=-n.scrollTop;return"rtl"===dn(r).direction&&(a+=jn(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:u}}(en(e));else if(nn(t))r=function(e,t){var n=Mr(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=rn(e)?Cr(e):Ln(1);return{width:e.clientWidth*o.x,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{var i=Rr(e);r={x:t.x-i.x,y:t.y-i.y,width:t.width,height:t.height}}return Qn(r)}function Or(e,t){var n=hn(e);return!(n===t||!nn(n)||fn(n))&&("fixed"===dn(n).position||Or(n,t))}function Lr(e,t,n){var r=rn(t),i=en(t),o="fixed"===n,a=Mr(e,!0,o,t),u={scrollLeft:0,scrollTop:0},s=Ln(0);if(r||!r&&!o)if(("body"!==Jt(t)||an(i))&&(u=vn(t)),r){var l=Mr(t,!0,o,t);s.x=l.x+t.clientLeft,s.y=l.y+t.clientTop}else i&&(s.x=Dr(i));var c=!i||r||o?Ln(0):jr(i,u);return{x:a.left+u.scrollLeft-s.x-c.x,y:a.top+u.scrollTop-s.y-c.y,width:a.width,height:a.height}}function Vr(e){return"static"===dn(e).position}function Nr(e,t){if(!rn(e)||"fixed"===dn(e).position)return null;if(t)return t(e);var n=e.offsetParent;return en(e)===n&&(n=n.ownerDocument.body),n}function Br(e,t){var n=Qt(e);if(sn(e))return n;if(!rn(e)){for(var r=hn(e);r&&!fn(r);){if(nn(r)&&!Vr(r))return r;r=hn(r)}return n}for(var i=Nr(e,t);i&&un(i)&&Vr(i);)i=Nr(i,t);return i&&fn(i)&&Vr(i)&&!ln(i)?n:i||function(e){for(var t=hn(e);rn(t)&&!fn(t);){if(ln(t))return t;if(sn(t))return null;t=hn(t)}return null}(e)||n}var zr=function(){var e=pe(de().m((function e(t){var n,r,i,o,a,u,s,l,c;return de().w((function(e){for(;;)switch(e.n){case 0:return n=this.getOffsetParent||Br,r=this.getDimensions,e.n=1,r(t.floating);case 1:return i=e.v,o=Lr,a=t.reference,e.n=2,n(t.floating);case 2:return u=e.v,s=t.strategy,l=o(a,u,s),c={x:0,y:0,width:i.width,height:i.height},e.a(2,{reference:l,floating:c})}}),e,this)})));return function(t){return e.apply(this,arguments)}}();var Wr={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){var t=e.elements,n=e.rect,r=e.offsetParent,i="fixed"===e.strategy,o=en(r),a=!!t&&sn(t.floating);if(r===o||a&&i)return n;var u={scrollLeft:0,scrollTop:0},s=Ln(1),l=Ln(0),c=rn(r);if((c||!c&&!i)&&(("body"!==Jt(r)||an(o))&&(u=vn(r)),rn(r))){var f=Mr(r);s=Cr(r),l.x=f.x+r.clientLeft,l.y=f.y+r.clientTop}var d=!o||c||i?Ln(0):jr(o,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+l.x+d.x,y:n.y*s.y-u.scrollTop*s.y+l.y+d.y}},getDocumentElement:en,getClippingRect:function(e){var t=e.element,n=e.boundary,r=e.rootBoundary,i=e.strategy,o=[].concat(me("clippingAncestors"===n?sn(t)?[]:function(e,t){var n=t.get(e);if(n)return n;for(var r=mn(e,[],!1).filter((function(e){return nn(e)&&"body"!==Jt(e)})),i=null,o="fixed"===dn(e).position,a=o?hn(e):e;nn(a)&&!fn(a);){var u=dn(a),s=ln(a);!s&&"fixed"===u.position&&(i=null),(o?!s&&!i:!s&&"static"===u.position&&i&&["absolute","fixed"].includes(i.position)||an(a)&&!s&&Or(e,a))?r=r.filter((function(e){return e!==a})):i=u,a=hn(a)}return t.set(e,r),r}(t,this._c):[].concat(n)),[r]),a=o[0],u=o.reduce((function(e,n){var r=Ir(t,n,i);return e.top=jn(r.top,e.top),e.right=Dn(r.right,e.right),e.bottom=Dn(r.bottom,e.bottom),e.left=jn(r.left,e.left),e}),Ir(t,a,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:Br,getElementRects:zr,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){var t=Sr(e);return{width:t.width,height:t.height}},getScale:Cr,isElement:nn,isRTL:function(e){return"rtl"===dn(e).direction}};function Ur(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function qr(e,t,n,r){void 0===r&&(r={});var i=r,o=i.ancestorScroll,a=void 0===o||o,u=i.ancestorResize,s=void 0===u||u,l=i.elementResize,c=void 0===l?"function"==typeof ResizeObserver:l,f=i.layoutShift,d=void 0===f?"function"==typeof IntersectionObserver:f,v=i.animationFrame,h=void 0!==v&&v,p=Pr(e),m=a||s?[].concat(me(p?mn(p):[]),me(mn(t))):[];m.forEach((function(e){a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)}));var y=p&&d?function(e,t){var n,r=null,i=en(e);function o(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function a(u,s){void 0===u&&(u=!1),void 0===s&&(s=1),o();var l=e.getBoundingClientRect(),c=l.left,f=l.top,d=l.width,v=l.height;if(u||t(),d&&v){var h={rootMargin:-On(f)+"px "+-On(i.clientWidth-(c+d))+"px "+-On(i.clientHeight-(f+v))+"px "+-On(c)+"px",threshold:jn(0,Dn(1,s))||1},p=!0;try{r=new IntersectionObserver(m,be(be({},h),{},{root:i.ownerDocument}))}catch(y){r=new IntersectionObserver(m,h)}r.observe(e)}function m(t){var r=t[0].intersectionRatio;if(r!==s){if(!p)return a();r?a(!1,r):n=setTimeout((function(){a(!1,1e-7)}),1e3)}1===r&&!Ur(l,e.getBoundingClientRect())&&a(),p=!1}}(!0),o}(p,n):null,g=-1,b=null;c&&(b=new ResizeObserver((function(e){var r=ke(e,1)[0];r&&r.target===p&&b&&(b.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame((function(){var e;null==(e=b)||e.observe(t)}))),n()})),p&&!h&&b.observe(p),b.observe(t));var x,w=h?Mr(e):null;return h&&function t(){var r=Mr(e);w&&!Ur(w,r)&&n(),w=r,x=requestAnimationFrame(t)}(),n(),function(){var e;m.forEach((function(e){a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)})),null==y||y(),null==(e=b)||e.disconnect(),b=null,h&&cancelAnimationFrame(x)}}var Hr=function(e){return void 0===e&&(e=0),{name:"offset",options:e,fn:function(t){return pe(de().m((function n(){var r,i,o,a,u,s,l;return de().w((function(n){for(;;)switch(n.n){case 0:return o=t.x,a=t.y,u=t.placement,s=t.middlewareData,n.n=1,Er(t,e);case 1:return l=n.v,n.a(2,u===(null==(r=s.offset)?void 0:r.placement)&&null!=(i=s.arrow)&&i.alignmentOffset?{}:{x:o+l.x,y:a+l.y,data:be(be({},l),{},{placement:u})})}}),n)})))()}}},Kr=function(e){return void 0===e&&(e={}),{name:"shift",options:e,fn:function(t){return pe(de().m((function n(){var r,i,o,a,u,s,l,f,d,v,h,p,m,y,g,b,x,w,k,F,E,A,S,P;return de().w((function(n){for(;;)switch(n.n){case 0:return r=t.x,i=t.y,o=t.placement,a=zn(e,t),u=a.mainAxis,s=void 0===u||u,l=a.crossAxis,f=void 0!==l&&l,d=a.limiter,v=void 0===d?{fn:function(e){return{x:e.x,y:e.y}}}:d,h=ye(a,c),p={x:r,y:i},n.n=1,xr(t,h);case 1:return m=n.v,y=Kn(Wn(o)),g=qn(y),b=p[g],x=p[y],s&&(w="y"===g?"bottom":"right",k=b+m["y"===g?"top":"left"],F=b-m[w],b=Bn(k,b,F)),f&&(E="y"===y?"bottom":"right",A=x+m["y"===y?"top":"left"],S=x-m[E],x=Bn(A,x,S)),P=v.fn(be(be({},t),{},xe(xe({},g,b),y,x))),n.a(2,be(be({},P),{},{data:{x:P.x-r,y:P.y-i,enabled:xe(xe({},g,s),y,f)}}))}}),n)})))()}}},Xr=function(e){return void 0===e&&(e={}),{name:"flip",options:e,fn:function(t){return pe(de().m((function n(){var r,i,o,a,u,l,c,f,d,v,h,p,m,y,g,b,x,w,k,F,E,A,S,P,C,T,R,M,D,j,I,O,L,V,N,B,z,W,U,q;return de().w((function(n){for(;;)switch(n.n){case 0:if(o=t.placement,a=t.middlewareData,u=t.rects,l=t.initialPlacement,c=t.platform,f=t.elements,d=zn(e,t),v=d.mainAxis,h=void 0===v||v,p=d.crossAxis,m=void 0===p||p,y=d.fallbackPlacements,g=d.fallbackStrategy,b=void 0===g?"bestFit":g,x=d.fallbackAxisSideDirection,w=void 0===x?"none":x,k=d.flipAlignment,F=void 0===k||k,E=ye(d,s),null==(r=a.arrow)||!r.alignmentOffset){n.n=1;break}return n.a(2,{});case 1:return A=Wn(o),S=Kn(l),P=Wn(l)===l,n.n=2,null==c.isRTL?void 0:c.isRTL(f.floating);case 2:return C=n.v,T=y||(P||!F?[Zn(l)]:Yn(l)),R="none"!==w,!y&&R&&T.push.apply(T,me($n(l,F,w,C))),M=[l].concat(me(T)),n.n=3,xr(t,E);case 3:if(D=n.v,j=[],I=(null==(i=a.flip)?void 0:i.overflows)||[],h&&j.push(D[A]),m&&(O=_n(o,u,C),j.push(D[O[0]],D[O[1]])),I=[].concat(me(I),[{placement:o,overflows:j}]),j.every((function(e){return e<=0}))){n.n=8;break}if(N=((null==(L=a.flip)?void 0:L.index)||0)+1,!(B=M[N])){n.n=4;break}return n.a(2,{data:{index:N,overflows:I},reset:{placement:B}});case 4:if(z=null==(V=I.filter((function(e){return e.overflows[0]<=0})).sort((function(e,t){return e.overflows[1]-t.overflows[1]}))[0])?void 0:V.placement,z){n.n=7;break}q=b,n.n="bestFit"===q?5:"initialPlacement"===q?6:7;break;case 5:return U=null==(W=I.filter((function(e){if(R){var t=Kn(e.placement);return t===S||"y"===t}return!0})).map((function(e){return[e.placement,e.overflows.filter((function(e){return e>0})).reduce((function(e,t){return e+t}),0)]})).sort((function(e,t){return e[1]-t[1]}))[0])?void 0:W[0],U&&(z=U),n.a(3,7);case 6:return z=l,n.a(3,7);case 7:if(o===z){n.n=8;break}return n.a(2,{reset:{placement:z}});case 8:return n.a(2,{})}}),n)})))()}}},_r=function(e){return void 0===e&&(e={}),{name:"size",options:e,fn:function(t){return pe(de().m((function n(){var r,i,o,a,u,s,l,c,d,v,h,p,m,y,g,b,x,w,k,F,E,A,S,P,C,T,R,M,D,j,I,O,L,V;return de().w((function(n){for(;;)switch(n.n){case 0:return o=t.placement,a=t.rects,u=t.platform,s=t.elements,l=zn(e,t),c=l.apply,d=void 0===c?function(){}:c,v=ye(l,f),n.n=1,xr(t,v);case 1:if(h=n.v,p=Wn(o),m=Un(o),y="y"===Kn(o),g=a.floating,b=g.width,x=g.height,"top"!==p&&"bottom"!==p){n.n=7;break}return w=p,O=m,n.n=2,null==u.isRTL?void 0:u.isRTL(s.floating);case 2:if(!n.v){n.n=3;break}L="start",n.n=4;break;case 3:L="end";case 4:if(O!==L){n.n=5;break}V="left",n.n=6;break;case 5:V="right";case 6:k=V,n.n=8;break;case 7:k=p,w="end"===m?"top":"bottom";case 8:return F=x-h.top-h.bottom,E=b-h.left-h.right,A=Dn(x-h[w],F),S=Dn(b-h[k],E),P=!t.middlewareData.shift,C=A,T=S,null!=(r=t.middlewareData.shift)&&r.enabled.x&&(T=E),null!=(i=t.middlewareData.shift)&&i.enabled.y&&(C=F),P&&!m&&(R=jn(h.left,0),M=jn(h.right,0),D=jn(h.top,0),j=jn(h.bottom,0),y?T=b-2*(0!==R||0!==M?R+M:jn(h.left,h.right)):C=x-2*(0!==D||0!==j?D+j:jn(h.top,h.bottom))),n.n=9,d(be(be({},t),{},{availableWidth:T,availableHeight:C}));case 9:return n.n=10,u.getDimensions(s.floating);case 10:return I=n.v,n.a(2,b!==I.width||x!==I.height?{reset:{rects:!0}}:{})}}),n)})))()}}},Yr=function(e){return void 0===e&&(e={}),{name:"hide",options:e,fn:function(t){return pe(de().m((function n(){var r,i,o,a,u,s,c,f,d,v;return de().w((function(n){for(;;)switch(n.n){case 0:r=t.rects,i=zn(e,t),o=i.strategy,a=void 0===o?"referenceHidden":o,u=ye(i,l),v=a,n.n="referenceHidden"===v?1:"escaped"===v?3:5;break;case 1:return n.n=2,xr(t,be(be({},u),{},{elementContext:"reference"}));case 2:return s=n.v,c=kr(s,r.reference),n.a(2,{data:{referenceHiddenOffsets:c,referenceHidden:Fr(c)}});case 3:return n.n=4,xr(t,be(be({},u),{},{altBoundary:!0}));case 4:return f=n.v,d=kr(f,r.floating),n.a(2,{data:{escapedOffsets:d,escaped:Fr(d)}});case 5:return n.a(2,{});case 6:return n.a(2)}}),n)})))()}}},Gr=("undefined"==typeof document?"undefined":Ae(document))<"u"?ce.useLayoutEffect:ce.useEffect;function $r(e,t){if(e===t)return!0;if(Ae(e)!=Ae(t))return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;var n,r,i;if(e&&t&&"object"==Ae(e)){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!==r--;)if(!$r(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!==r--;){var o=i[r];if(!("_owner"===o&&e.$$typeof||$r(e[o],t[o])))return!1}return!0}return e!=e&&t!=t}function Zr(e){return("undefined"==typeof window?"undefined":Ae(window))>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Jr(e,t){var n=Zr(e);return Math.round(t*n)/n}function Qr(e){var t=ce.useRef(e);return Gr((function(){t.current=e})),t}function ei(e){void 0===e&&(e={});var t=e,n=t.placement,r=void 0===n?"bottom":n,i=t.strategy,o=void 0===i?"absolute":i,a=t.middleware,u=void 0===a?[]:a,s=t.platform,l=t.elements,c=void 0===l?{}:l,f=c.reference,d=c.floating,v=t.transform,h=void 0===v||v,p=t.whileElementsMounted,m=t.open,y=ke(ce.useState({x:0,y:0,strategy:o,placement:r,middlewareData:{},isPositioned:!1}),2),g=y[0],b=y[1],x=ke(ce.useState(u),2),w=x[0],k=x[1];$r(w,u)||k(u);var F=ke(ce.useState(null),2),E=F[0],A=F[1],S=ke(ce.useState(null),2),P=S[0],C=S[1],T=ce.useCallback((function(e){e!==j.current&&(j.current=e,A(e))}),[]),R=ce.useCallback((function(e){e!==I.current&&(I.current=e,C(e))}),[]),M=f||E,D=d||P,j=ce.useRef(null),I=ce.useRef(null),O=ce.useRef(g),L=null!=p,V=Qr(p),N=Qr(s),B=Qr(m),z=ce.useCallback((function(){if(j.current&&I.current){var e={placement:r,strategy:o,middleware:w};N.current&&(e.platform=N.current),function(e,t,n){var r=new Map,i=be({platform:Wr},n),o=be(be({},i.platform),{},{_c:r});return br(e,t,be(be({},i),{},{platform:o}))}(j.current,I.current,e).then((function(e){var t=be(be({},e),{},{isPositioned:!1!==B.current});W.current&&!$r(O.current,t)&&(O.current=t,se.flushSync((function(){b(t)})))}))}}),[w,r,o,N,B]);Gr((function(){!1===m&&O.current.isPositioned&&(O.current.isPositioned=!1,b((function(e){return be(be({},e),{},{isPositioned:!1})})))}),[m]);var W=ce.useRef(!1);Gr((function(){return W.current=!0,function(){W.current=!1}}),[]),Gr((function(){if(M&&(j.current=M),D&&(I.current=D),M&&D){if(V.current)return V.current(M,D,z);z()}}),[M,D,z,V,L]);var U=ce.useMemo((function(){return{reference:j,floating:I,setReference:T,setFloating:R}}),[T,R]),q=ce.useMemo((function(){return{reference:M,floating:D}}),[M,D]),H=ce.useMemo((function(){var e={position:o,left:0,top:0};if(!q.floating)return e;var t=Jr(q.floating,g.x),n=Jr(q.floating,g.y);return h?be(be({},e),{},{transform:"translate("+t+"px, "+n+"px)"},Zr(q.floating)>=1.5&&{willChange:"transform"}):{position:o,left:t,top:n}}),[o,h,q.floating,g.x,g.y]);return ce.useMemo((function(){return be(be({},g),{},{update:z,refs:U,elements:q,floatingStyles:H})}),[g,z,U,q,H])}var ti=function(e,t){return be(be({},Hr(e)),{},{options:[e,t]})},ni=function(e,t){return be(be({},Kr(e)),{},{options:[e,t]})},ri=function(e,t){return be(be({},Xr(e)),{},{options:[e,t]})},ii=function(e,t){return be(be({},_r(e)),{},{options:[e,t]})},oi=function(e,t){return be(be({},Yr(e)),{},{options:[e,t]})};function ai(e){var t=ce.useRef(void 0),n=ce.useCallback((function(t){var n=e.map((function(e){if(null!=e){if("function"==typeof e){var n=e,r=n(t);return"function"==typeof r?r:function(){n(null)}}return e.current=t,function(){e.current=null}}}));return function(){n.forEach((function(e){return null==e?void 0:e()}))}}),e);return ce.useMemo((function(){return e.every((function(e){return null==e}))?null:function(e){t.current&&(t.current(),t.current=void 0),null!=e&&(t.current=n(e))}}),e)}var ui=be({},ve),si=ui.useInsertionEffect||function(e){return e()};function li(e){var t=ce.useRef((function(){}));return si((function(){t.current=e})),ce.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current.apply(t,n)}),[])}var ci="ArrowUp",fi="ArrowDown",di="ArrowLeft",vi="ArrowRight";function hi(e,t,n){return Math.floor(e/t)!==n}function pi(e,t){return t<0||t>=e.current.length}function mi(e,t){return gi(e,{disabledIndices:t})}function yi(e,t){return gi(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function gi(e,t){var n=void 0===t?{}:t,r=n.startingIndex,i=void 0===r?-1:r,o=n.decrement,a=void 0!==o&&o,u=n.disabledIndices,s=n.amount,l=void 0===s?1:s,c=e.current,f=i;do{f+=a?-l:l}while(f>=0&&f<=c.length-1&&wi(c,f,u));return f}function bi(e,t,n,r,i){if(-1===e)return-1;var o=n.indexOf(e),a=t[e];switch(i){case"tl":return o;case"tr":return a?o+a.width-1:o;case"bl":return a?o+(a.height-1)*r:o;case"br":return n.lastIndexOf(e)}}function xi(e,t){return t.flatMap((function(t,n){return e.includes(t)?[n]:[]}))}function wi(e,t,n){if(n)return n.includes(t);var r=e[t];return null==r||r.hasAttribute("disabled")||"true"===r.getAttribute("aria-disabled")}var ki=("undefined"==typeof document?"undefined":Ae(document))<"u"?ce.useLayoutEffect:ce.useEffect;function Fi(e,t){var n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}var Ei=ce.createContext({register:function(){},unregister:function(){},map:new Map,elementsRef:{current:[]}});function Ai(e){var t=e.children,n=e.elementsRef,r=e.labelsRef,i=ke(ce.useState((function(){return new Set})),2),o=i[0],a=i[1],u=ce.useCallback((function(e){a((function(t){return new Set(t).add(e)}))}),[]),s=ce.useCallback((function(e){a((function(t){var n=new Set(t);return n.delete(e),n}))}),[]),l=ce.useMemo((function(){var e=new Map;return Array.from(o.keys()).sort(Fi).forEach((function(t,n){e.set(t,n)})),e}),[o]);return ae.jsx(Ei.Provider,{value:ce.useMemo((function(){return{register:u,unregister:s,map:l,elementsRef:n,labelsRef:r}}),[u,s,l,n,r]),children:t})}function Si(e){void 0===e&&(e={});var t=e.label,n=ce.useContext(Ei),r=n.register,i=n.unregister,o=n.map,a=n.elementsRef,u=n.labelsRef,s=ke(ce.useState(null),2),l=s[0],c=s[1],f=ce.useRef(null),d=ce.useCallback((function(e){if(f.current=e,null!==l&&(a.current[l]=e,u)){var n,r=void 0!==t;u.current[l]=r?t:null!=(n=null==e?void 0:e.textContent)?n:null}}),[l,a,u,t]);return ki((function(){var e=f.current;if(e)return r(e),function(){i(e)}}),[r,i]),ki((function(){var e=f.current?o.get(f.current):null;null!=e&&c(e)}),[o]),ce.useMemo((function(){return{ref:d,index:null!=l?l:-1}}),[l,d])}var Pi=!1,Ci=0,Ti=function(){return"floating-ui-"+Math.random().toString(36).slice(2,6)+Ci++};var Ri=ui.useId||function(){var e=ke(ce.useState((function(){return Pi?Ti():void 0})),2),t=e[0],n=e[1];return ki((function(){null==t&&n(Ti())}),[]),ce.useEffect((function(){Pi=!0}),[]),t};var Mi=ce.createContext(null),Di=ce.createContext(null),ji=function(){var e;return(null==(e=ce.useContext(Mi))?void 0:e.id)||null},Ii=function(){return ce.useContext(Di)};function Oi(e){return"data-floating-ui-"+e}function Li(e){-1!==e.current&&(clearTimeout(e.current),e.current=-1)}function Vi(e){var t=ce.useRef(e);return ki((function(){t.current=e})),t}var Ni=0;function Bi(e,t){void 0===t&&(t={});var n=t,r=n.preventScroll,i=void 0!==r&&r,o=n.cancelPrevious,a=void 0===o||o,u=n.sync,s=void 0!==u&&u;a&&cancelAnimationFrame(Ni);var l=function(){return null==e?void 0:e.focus({preventScroll:i})};s?l():Ni=requestAnimationFrame(l)}function zi(e,t){for(var n,r=[],i=null==(n=e.find((function(e){return e.id===t})))?void 0:n.parentId;i;){var o=e.find((function(e){return e.id===i}));i=null==o?void 0:o.parentId,o&&(r=r.concat(o))}return r}function Wi(e,t){for(var n=e.filter((function(e){var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)})),r=n;r.length;)r=e.filter((function(e){var t;return null==(t=r)?void 0:t.some((function(t){var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)}))})),n=n.concat(r);return n}var Ui=new WeakMap,qi=new WeakSet,Hi={},Ki=0,Xi=function(e){return e&&(e.host||Xi(e.parentNode))};function _i(e,t,n,r){var i="data-floating-ui-inert",o=r?"inert":n?"aria-hidden":null,a=function(e,t){return t.map((function(t){if(e.contains(t))return t;var n=Xi(t);return e.contains(n)?n:null})).filter((function(e){return null!=e}))}(t,e),u=new Set,s=new Set(a),l=[];Hi[i]||(Hi[i]=new WeakMap);var c=Hi[i];return a.forEach((function e(t){!t||u.has(t)||(u.add(t),t.parentNode&&e(t.parentNode))})),function e(t){!t||s.has(t)||[].forEach.call(t.children,(function(t){if("script"!==Jt(t))if(u.has(t))e(t);else{var n=o?t.getAttribute(o):null,r=null!==n&&"false"!==n,a=Ui.get(t)||0,s=o?a+1:a,f=(c.get(t)||0)+1;Ui.set(t,s),c.set(t,f),l.push(t),1===s&&r&&qi.add(t),1===f&&t.setAttribute(i,""),!r&&o&&t.setAttribute(o,"true")}}))}(t),u.clear(),Ki++,function(){l.forEach((function(e){var t=Ui.get(e)||0,n=o?t-1:t,r=(c.get(e)||0)-1;Ui.set(e,n),c.set(e,r),n||(!qi.has(e)&&o&&e.removeAttribute(o),qi.delete(e)),r||e.removeAttribute(i)})),--Ki||(Ui=new WeakMap,Ui=new WeakMap,qi=new WeakSet,Hi={})}}function Yi(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=An(e[0]).body;return _i(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}var Gi=function(){return{getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"}};function $i(e,t){var n=yr(e,Gi());"prev"===t&&n.reverse();var r=n.indexOf(gn(An(e)));return n.slice(r+1)[0]}function Zi(){return $i(document.body,"next")}function Ji(){return $i(document.body,"prev")}function Qi(e,t){var n=t||e.currentTarget,r=e.relatedTarget;return!r||!bn(n,r)}function eo(e){yr(e,Gi()).forEach((function(e){e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}))}function to(e){e.querySelectorAll("[data-tabindex]").forEach((function(e){var t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")}))}var no={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0},ro=ce.forwardRef((function(e,t){var n=ke(ce.useState(),2),r=n[0],i=n[1];ki((function(){/apple/i.test(navigator.vendor)&&i("button")}),[]);var o=xe(xe({ref:t,tabIndex:0,role:r,"aria-hidden":!r||void 0},Oi("focus-guard"),""),"style",no);return ae.jsx("span",be(be({},e),o))})),io=ce.createContext(null),oo=Oi("portal");function ao(e){var t=e.children,n=e.id,r=e.root,i=e.preserveTabOrder,o=void 0===i||i,a=function(e){void 0===e&&(e={});var t=e,n=t.id,r=t.root,i=Ri(),o=uo(),a=ke(ce.useState(null),2),u=a[0],s=a[1],l=ce.useRef(null);return ki((function(){return function(){null==u||u.remove(),queueMicrotask((function(){l.current=null}))}}),[u]),ki((function(){if(i&&!l.current){var e=n?document.getElementById(n):null;if(e){var t=document.createElement("div");t.id=i,t.setAttribute(oo,""),e.appendChild(t),l.current=t,s(t)}}}),[n,i]),ki((function(){if(null!==r&&i&&!l.current){var e=r||(null==o?void 0:o.portalNode);e&&!nn(e)&&(e=e.current),e=e||document.body;var t=null;n&&((t=document.createElement("div")).id=n,e.appendChild(t));var a=document.createElement("div");a.id=i,a.setAttribute(oo,""),(e=t||e).appendChild(a),l.current=a,s(a)}}),[n,r,i,o]),u}({id:n,root:r}),u=ke(ce.useState(null),2),s=u[0],l=u[1],c=ce.useRef(null),f=ce.useRef(null),d=ce.useRef(null),v=ce.useRef(null),h=null==s?void 0:s.modal,p=null==s?void 0:s.open,m=!!s&&!s.modal&&s.open&&o&&!(!r&&!a);return ce.useEffect((function(){if(a&&o&&!h)return a.addEventListener("focusin",e,!0),a.addEventListener("focusout",e,!0),function(){a.removeEventListener("focusin",e,!0),a.removeEventListener("focusout",e,!0)};function e(e){a&&Qi(e)&&("focusin"===e.type?to:eo)(a)}}),[a,o,h]),ce.useEffect((function(){a&&(p||to(a))}),[p,a]),ae.jsxs(io.Provider,{value:ce.useMemo((function(){return{preserveTabOrder:o,beforeOutsideRef:c,afterOutsideRef:f,beforeInsideRef:d,afterInsideRef:v,portalNode:a,setFocusManagerState:l}}),[o,a]),children:[m&&a&&ae.jsx(ro,{"data-type":"outside",ref:c,onFocus:function(e){if(Qi(e,a)){var t;null==(t=d.current)||t.focus()}else{var n=Ji()||(null==s?void 0:s.domReference);null==n||n.focus()}}}),m&&a&&ae.jsx("span",{"aria-owns":a.id,style:no}),a&&se.createPortal(t,a),m&&a&&ae.jsx(ro,{"data-type":"outside",ref:f,onFocus:function(e){if(Qi(e,a)){var t;null==(t=v.current)||t.focus()}else{var n=Zi()||(null==s?void 0:s.domReference);null==n||n.focus(),null!=s&&s.closeOnFocusOut&&(null==s||s.onOpenChange(!1,e.nativeEvent,"focus-out"))}}})]})}var uo=function(){return ce.useContext(io)},so="data-floating-ui-focusable";function lo(e){return e?e.hasAttribute(so)?e:e.querySelector("["+so+"]")||e:null}function co(e){return ce.useMemo((function(){return function(t){e.forEach((function(e){e&&(e.current=t)}))}}),e)}var fo=[];function vo(){return fo.slice().reverse().find((function(e){return e.isConnected}))}function ho(e){var t=Gi();return function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==nr.call(e,er)&&hr(t,e)}(e,t)?e:yr(e,t)[0]||e}var po=ce.forwardRef((function(e,t){return ae.jsx("button",be(be({},e),{},{type:"button",ref:t,tabIndex:-1,style:no}))}));function mo(e){var t=e.context,n=e.children,r=e.disabled,i=void 0!==r&&r,o=e.order,a=void 0===o?["content"]:o,u=e.guards,s=void 0===u||u,l=e.initialFocus,c=void 0===l?0:l,f=e.returnFocus,d=void 0===f||f,v=e.restoreFocus,h=void 0!==v&&v,p=e.modal,m=void 0===p||p,y=e.visuallyHiddenDismiss,g=void 0!==y&&y,b=e.closeOnFocusOut,x=void 0===b||b,w=e.outsideElementsInert,k=void 0!==w&&w,F=t.open,E=t.onOpenChange,A=t.events,S=t.dataRef,P=t.elements,C=P.domReference,T=P.floating,R=li((function(){var e;return null==(e=S.current.floatingContext)?void 0:e.nodeId})),M="number"==typeof c&&c<0,D=Rn(C)&&M,j=("undefined"==typeof HTMLElement?"undefined":Ae(HTMLElement))<"u"&&"inert"in HTMLElement.prototype,I=!j||s,O=!I||j&&k,L=Vi(a),V=Vi(c),N=Vi(d),B=Ii(),z=uo(),W=ce.useRef(null),U=ce.useRef(null),q=ce.useRef(!1),H=ce.useRef(!1),K=ce.useRef(-1),X=null!=z,_=lo(T),Y=li((function(e){return void 0===e&&(e=_),e?yr(e,Gi()):[]})),G=li((function(e){var t=Y(e);return L.current.map((function(e){return C&&"reference"===e?C:_&&"floating"===e?_:t})).filter(Boolean).flat()}));ce.useEffect((function(){if(!i&&m){var e=An(_);return e.addEventListener("keydown",t),function(){e.removeEventListener("keydown",t)}}function t(e){if("Tab"===e.key){bn(_,gn(An(_)))&&0===Y().length&&!D&&Tn(e);var t=G(),n=Pn(e);"reference"===L.current[0]&&n===C&&(Tn(e),e.shiftKey?Bi(t[t.length-1]):Bi(t[1])),"floating"===L.current[1]&&n===_&&e.shiftKey&&(Tn(e),Bi(t[0]))}}}),[i,C,_,m,L,D,Y,G]),ce.useEffect((function(){if(!i&&T)return T.addEventListener("focusin",e),function(){T.removeEventListener("focusin",e)};function e(e){var t=Pn(e),n=Y().indexOf(t);-1!==n&&(K.current=n)}}),[i,T,Y]),ce.useEffect((function(){if(!i&&x)return T&&rn(C)?(C.addEventListener("focusout",t),C.addEventListener("pointerdown",e),T.addEventListener("focusout",t),function(){C.removeEventListener("focusout",t),C.removeEventListener("pointerdown",e),T.removeEventListener("focusout",t)}):void 0;function e(){H.current=!0,setTimeout((function(){H.current=!1}))}function t(e){var t=e.relatedTarget;queueMicrotask((function(){var n=R(),r=!(bn(C,t)||bn(T,t)||bn(t,T)||bn(null==z?void 0:z.portalNode,t)||null!=t&&t.hasAttribute(Oi("focus-guard"))||B&&(Wi(B.nodesRef.current,n).find((function(e){var n,r;return bn(null==(n=e.context)?void 0:n.elements.floating,t)||bn(null==(r=e.context)?void 0:r.elements.domReference,t)}))||zi(B.nodesRef.current,n).find((function(e){var n,r,i;return[null==(n=e.context)?void 0:n.elements.floating,lo(null==(r=e.context)?void 0:r.elements.floating)].includes(t)||(null==(i=e.context)?void 0:i.elements.domReference)===t}))));if(h&&r&&gn(An(_))===An(_).body){rn(_)&&_.focus();var i=K.current,o=Y(),a=o[i]||o[o.length-1]||_;rn(a)&&a.focus()}(D||!m)&&t&&r&&!H.current&&t!==vo()&&(q.current=!0,E(!1,e,"focus-out"))}))}}),[i,C,T,_,m,B,z,E,x,h,Y,D,R]);var $=ce.useRef(null),Z=ce.useRef(null),J=co([$,null==z?void 0:z.beforeInsideRef]),Q=co([Z,null==z?void 0:z.afterInsideRef]);function ee(e){return!i&&g&&m?ae.jsx(po,{ref:"start"===e?W:U,onClick:function(e){return E(!1,e.nativeEvent)},children:"string"==typeof g?g:"Dismiss"}):null}ce.useEffect((function(){var e;if(!i&&T){var t=Array.from((null==z||null==(e=z.portalNode)?void 0:e.querySelectorAll("["+Oi("portal")+"]"))||[]),n=B&&!m?zi(null==B?void 0:B.nodesRef.current,R()).map((function(e){var t;return null==(t=e.context)?void 0:t.elements.floating})):[],r=[T].concat(t,me(n),[W.current,U.current,$.current,Z.current,null==z?void 0:z.beforeOutsideRef.current,null==z?void 0:z.afterOutsideRef.current,L.current.includes("reference")||D?C:null]).filter((function(e){return null!=e})),o=m||D?Yi(r,!O,O):Yi(r);return function(){o()}}}),[i,C,T,m,L,z,D,I,O,B,R]),ki((function(){if(!i&&rn(_)){var e=gn(An(_));queueMicrotask((function(){var t=G(_),n=V.current,r=("number"==typeof n?t[n]:n.current)||_,i=bn(_,e);!M&&!i&&F&&Bi(r,{preventScroll:r===_})}))}}),[i,F,_,M,G,V]),ki((function(){if(!i&&_){var e=!1,t=!1,n=An(_),r=gn(n),o=S.current.openEvent;!function(e){fo=fo.filter((function(e){return e.isConnected})),e&&"body"!==Jt(e)&&(fo.push(e),fo.length>20&&(fo=fo.slice(-20)))}(r),A.on("openchange",u);var a=n.createElement("span");return a.setAttribute("tabindex","-1"),a.setAttribute("aria-hidden","true"),Object.assign(a.style,no),X&&C&&C.insertAdjacentElement("afterend",a),function(){A.off("openchange",u);var r=gn(n),i=bn(T,r)||B&&Wi(B.nodesRef.current,R()).some((function(e){var t;return bn(null==(t=e.context)?void 0:t.elements.floating,r)}));(i||o&&["click","mousedown"].includes(o.type))&&(t=!0);var s="boolean"==typeof N.current?t&&C?C:vo()||a:N.current.current||a;queueMicrotask((function(){var t=ho(s);N.current&&!q.current&&rn(t)&&(!(t!==r&&r!==n.body)||i)&&t.focus({preventScroll:e}),a.remove()}))}}function u(n){var r=n.open,i=n.reason,a=n.event,u=n.nested;if(r&&(o=a),"escape-key"===i&&(t=!0),["hover","safe-polygon"].includes(i)&&"mouseleave"===a.type&&(q.current=!0),"outside-press"===i)if(u)q.current=!1,e=!0;else if(wn(a)||kn(a))q.current=!1;else{var s=!1;document.createElement("div").focus({get preventScroll(){return s=!0,!1}}),s?(q.current=!1,e=!0):q.current=!0}}}),[i,T,_,N,S,A,B,X,C,R]),ce.useEffect((function(){queueMicrotask((function(){q.current=!1}))}),[i]),ki((function(){if(!i&&z)return z.setFocusManagerState({modal:m,closeOnFocusOut:x,open:F,onOpenChange:E,domReference:C}),function(){z.setFocusManagerState(null)}}),[i,z,m,F,E,x,C]),ki((function(){if(!i&&_&&"function"==typeof MutationObserver&&!M){var e=function(){var e=_.getAttribute("tabindex"),t=Y(),n=gn(An(T)),r=t.indexOf(n);-1!==r&&(K.current=r),L.current.includes("floating")||n!==C&&0===t.length?"0"!==e&&_.setAttribute("tabindex","0"):"-1"!==e&&_.setAttribute("tabindex","-1")};e();var t=new MutationObserver(e);return t.observe(_,{childList:!0,subtree:!0,attributes:!0}),function(){t.disconnect()}}}),[i,T,_,C,L,Y,M]);var te=!i&&I&&(!m||!D)&&(X||m);return ae.jsxs(ae.Fragment,{children:[te&&ae.jsx(ro,{"data-type":"inside",ref:J,onFocus:function(e){if(m){var t=G();Bi("reference"===a[0]?t[0]:t[t.length-1])}else if(null!=z&&z.preserveTabOrder&&z.portalNode)if(q.current=!1,Qi(e,z.portalNode)){var n=Zi()||C;null==n||n.focus()}else{var r;null==(r=z.beforeOutsideRef.current)||r.focus()}}}),!D&&ee("start"),n,ee("end"),te&&ae.jsx(ro,{"data-type":"inside",ref:Q,onFocus:function(e){if(m)Bi(G()[0]);else if(null!=z&&z.preserveTabOrder&&z.portalNode)if(x&&(q.current=!0),Qi(e,z.portalNode)){var t=Ji()||C;null==t||t.focus()}else{var n;null==(n=z.afterOutsideRef.current)||n.focus()}}})]})}function yo(e){return rn(e.target)&&"BUTTON"===e.target.tagName}function go(e){return Cn(e)}function bo(e,t){void 0===t&&(t={});var n=e.open,r=e.onOpenChange,i=e.dataRef,o=e.elements.domReference,a=t,u=a.enabled,s=void 0===u||u,l=a.event,c=void 0===l?"click":l,f=a.toggle,d=void 0===f||f,v=a.ignoreMouse,h=void 0!==v&&v,p=a.keyboardHandlers,m=void 0===p||p,y=a.stickIfOpen,g=void 0===y||y,b=ce.useRef(),x=ce.useRef(!1),w=ce.useMemo((function(){return{onPointerDown:function(e){b.current=e.pointerType},onMouseDown:function(e){var t=b.current;0===e.button&&"click"!==c&&(En(t)&&h||(!n||!d||i.current.openEvent&&g&&"mousedown"!==i.current.openEvent.type?(e.preventDefault(),r(!0,e.nativeEvent,"click")):r(!1,e.nativeEvent,"click")))},onClick:function(e){var t=b.current;"mousedown"===c&&b.current?b.current=void 0:En(t)&&h||(!n||!d||i.current.openEvent&&g&&"click"!==i.current.openEvent.type?r(!0,e.nativeEvent,"click"):r(!1,e.nativeEvent,"click"))},onKeyDown:function(e){b.current=void 0,!e.defaultPrevented&&m&&!yo(e)&&(" "===e.key&&!go(o)&&(e.preventDefault(),x.current=!0),"Enter"===e.key&&r(!(n&&d),e.nativeEvent,"click"))},onKeyUp:function(e){e.defaultPrevented||!m||yo(e)||go(o)||" "===e.key&&x.current&&(x.current=!1,r(!(n&&d),e.nativeEvent,"click"))}}}),[i,o,c,h,m,r,n,g,d]);return ce.useMemo((function(){return s?{reference:w}:{}}),[s,w])}var xo={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},wo={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},ko=function(e){var t,n;return{escapeKey:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePress:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}};function Fo(e,t){void 0===t&&(t={});var n=e.open,r=e.onOpenChange,i=e.elements,o=e.dataRef,a=t,u=a.enabled,s=void 0===u||u,l=a.escapeKey,c=void 0===l||l,f=a.outsidePress,d=void 0===f||f,v=a.outsidePressEvent,h=void 0===v?"pointerdown":v,p=a.referencePress,m=void 0!==p&&p,y=a.referencePressEvent,g=void 0===y?"pointerdown":y,b=a.ancestorScroll,x=void 0!==b&&b,w=a.bubbles,k=a.capture,F=Ii(),E=li("function"==typeof d?d:function(){return!1}),A="function"==typeof d?E:d,S=ce.useRef(!1),P=ce.useRef(!1),C=ko(w),T=C.escapeKey,R=C.outsidePress,M=ko(k),D=M.escapeKey,j=M.outsidePress,I=ce.useRef(!1),O=li((function(e){var t;if(n&&s&&c&&"Escape"===e.key&&!I.current){var i=null==(t=o.current.floatingContext)?void 0:t.nodeId,a=F?Wi(F.nodesRef.current,i):[];if(!T&&(e.stopPropagation(),a.length>0)){var u=!0;if(a.forEach((function(e){var t;null==(t=e.context)||!t.open||e.context.dataRef.current.__escapeKeyBubbles||(u=!1)})),!u)return}r(!1,function(e){return"nativeEvent"in e}(e)?e.nativeEvent:e,"escape-key")}})),L=li((function(e){var t,n=function(){var t;O(e),null==(t=Pn(e))||t.removeEventListener("keydown",n)};null==(t=Pn(e))||t.addEventListener("keydown",n)})),V=li((function(e){var t,n=S.current;S.current=!1;var a=P.current;if(P.current=!1,!("click"===h&&a||n||"function"==typeof A&&!A(e))){for(var u=Pn(e),s="["+Oi("inert")+"]",l=An(i.floating).querySelectorAll(s),c=nn(u)?u:null;c&&!fn(c);){var f=hn(c);if(fn(f)||!nn(f))break;c=f}if(!l.length||!nn(u)||function(e){return e.matches("html,body")}(u)||bn(u,i.floating)||!Array.from(l).every((function(e){return!bn(c,e)}))){if(rn(u)&&z){var d=fn(u),v=dn(u),p=/auto|scroll/,m=d||p.test(v.overflowX),y=d||p.test(v.overflowY),g=m&&u.clientWidth>0&&u.scrollWidth>u.clientWidth,b=y&&u.clientHeight>0&&u.scrollHeight>u.clientHeight,x="rtl"===v.direction,w=b&&(x?e.offsetX<=u.offsetWidth-u.clientWidth:e.offsetX>u.clientWidth),k=g&&e.offsetY>u.clientHeight;if(w||k)return}var E=null==(t=o.current.floatingContext)?void 0:t.nodeId,C=F&&Wi(F.nodesRef.current,E).some((function(t){var n;return Sn(e,null==(n=t.context)?void 0:n.elements.floating)}));if(!(Sn(e,i.floating)||Sn(e,i.domReference)||C)){var T=F?Wi(F.nodesRef.current,E):[];if(T.length>0){var R=!0;if(T.forEach((function(e){var t;null==(t=e.context)||!t.open||e.context.dataRef.current.__outsidePressBubbles||(R=!1)})),!R)return}r(!1,e,"outside-press")}}}})),N=li((function(e){var t,n=function(){var t;V(e),null==(t=Pn(e))||t.removeEventListener(h,n)};null==(t=Pn(e))||t.addEventListener(h,n)}));ce.useEffect((function(){if(n&&s){o.current.__escapeKeyBubbles=T,o.current.__outsidePressBubbles=R;var e=-1,t=An(i.floating);c&&(t.addEventListener("keydown",D?L:O,D),t.addEventListener("compositionstart",l),t.addEventListener("compositionend",f)),A&&t.addEventListener(h,j?N:V,j);var a=[];return x&&(nn(i.domReference)&&(a=mn(i.domReference)),nn(i.floating)&&(a=a.concat(mn(i.floating))),!nn(i.reference)&&i.reference&&i.reference.contextElement&&(a=a.concat(mn(i.reference.contextElement)))),a=a.filter((function(e){var n;return e!==(null==(n=t.defaultView)?void 0:n.visualViewport)})),a.forEach((function(e){e.addEventListener("scroll",u,{passive:!0})})),function(){c&&(t.removeEventListener("keydown",D?L:O,D),t.removeEventListener("compositionstart",l),t.removeEventListener("compositionend",f)),A&&t.removeEventListener(h,j?N:V,j),a.forEach((function(e){e.removeEventListener("scroll",u)})),window.clearTimeout(e)}}function u(e){r(!1,e,"ancestor-scroll")}function l(){window.clearTimeout(e),I.current=!0}function f(){e=window.setTimeout((function(){I.current=!1}),cn()?5:0)}}),[o,i,c,A,h,n,r,x,s,T,R,O,D,L,V,j,N]),ce.useEffect((function(){S.current=!1}),[A,h]);var B=ce.useMemo((function(){return be({onKeyDown:O},m&&be(xe({},xo[g],(function(e){r(!1,e.nativeEvent,"reference-press")})),"click"!==g&&{onClick:function(e){r(!1,e.nativeEvent,"reference-press")}}))}),[O,r,m,g]),z=ce.useMemo((function(){return xe({onKeyDown:O,onMouseDown:function(){P.current=!0},onMouseUp:function(){P.current=!0}},wo[h],(function(){S.current=!0}))}),[O,h]);return ce.useMemo((function(){return s?{reference:B,floating:z}:{}}),[s,B,z])}function Eo(e){var t=e.open,n=void 0!==t&&t,r=e.onOpenChange,i=e.elements,o=Ri(),a=ce.useRef({}),u=ce.useState((function(){return function(){var e=new Map;return{emit:function(t,n){var r;null==(r=e.get(t))||r.forEach((function(e){return e(n)}))},on:function(t,n){e.set(t,[].concat(me(e.get(t)||[]),[n]))},off:function(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter((function(e){return e!==n})))||[])}}}()})),s=ke(u,1)[0],l=null!=ji(),c=ke(ce.useState(i.reference),2),f=c[0],d=c[1],v=li((function(e,t,n){a.current.openEvent=e?t:void 0,s.emit("openchange",{open:e,event:t,reason:n,nested:l}),null==r||r(e,t,n)})),h=ce.useMemo((function(){return{setPositionReference:d}}),[]),p=ce.useMemo((function(){return{reference:f||i.reference||null,floating:i.floating||null,domReference:i.reference}}),[f,i.reference,i.floating]);return ce.useMemo((function(){return{dataRef:a,open:n,onOpenChange:v,elements:p,events:s,floatingId:o,refs:h}}),[n,v,p,s,o,h])}function Ao(e){void 0===e&&(e={});var t=e.nodeId,n=Eo(be(be({},e),{},{elements:be({reference:null,floating:null},e.elements)})),r=e.rootContext||n,i=r.elements,o=ke(ce.useState(null),2),a=o[0],u=o[1],s=ke(ce.useState(null),2),l=s[0],c=s[1],f=(null==i?void 0:i.domReference)||a,d=ce.useRef(null),v=Ii();ki((function(){f&&(d.current=f)}),[f]);var h=ei(be(be({},e),{},{elements:be(be({},i),l&&{reference:l})})),p=ce.useCallback((function(e){var t=nn(e)?{getBoundingClientRect:function(){return e.getBoundingClientRect()},contextElement:e}:e;c(t),h.refs.setReference(t)}),[h.refs]),m=ce.useCallback((function(e){(nn(e)||null===e)&&(d.current=e,u(e)),(nn(h.refs.reference.current)||null===h.refs.reference.current||null!==e&&!nn(e))&&h.refs.setReference(e)}),[h.refs]),y=ce.useMemo((function(){return be(be({},h.refs),{},{setReference:m,setPositionReference:p,domReference:d})}),[h.refs,m,p]),g=ce.useMemo((function(){return be(be({},h.elements),{},{domReference:f})}),[h.elements,f]),b=ce.useMemo((function(){return be(be(be({},h),r),{},{refs:y,elements:g,nodeId:t})}),[h,y,g,t,r]);return ki((function(){r.dataRef.current.floatingContext=b;var e=null==v?void 0:v.nodesRef.current.find((function(e){return e.id===t}));e&&(e.context=b)})),ce.useMemo((function(){return be(be({},h),{},{context:b,refs:y,elements:g})}),[h,y,g,b])}var So="active",Po="selected";function Co(e,t,n){var r=new Map,i="item"===n,o=e;if(i&&e){e[So],e[Po];o=ye(e,[So,Po].map(we))}return be(be(be({},"floating"===n&&xe({tabIndex:-1},so,"")),o),t.map((function(t){var r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r})).concat(e).reduce((function(e,t){return t&&Object.entries(t).forEach((function(t){var n=ke(t,2),o=n[0],a=n[1];if(!i||![So,Po].includes(o))if(0===o.indexOf("on")){var u;if(r.has(o)||r.set(o,[]),"function"==typeof a)null==(u=r.get(o))||u.push(a),e[o]=function(){for(var e,t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return null==(e=r.get(o))?void 0:e.map((function(e){return e.apply(void 0,n)})).find((function(e){return void 0!==e}))}}else e[o]=a})),e}),{}))}function To(e){void 0===e&&(e=[]);var t=e.map((function(e){return null==e?void 0:e.reference})),n=e.map((function(e){return null==e?void 0:e.floating})),r=e.map((function(e){return null==e?void 0:e.item})),i=ce.useCallback((function(t){return Co(t,e,"reference")}),t),o=ce.useCallback((function(t){return Co(t,e,"floating")}),n),a=ce.useCallback((function(t){return Co(t,e,"item")}),r);return ce.useMemo((function(){return{getReferenceProps:i,getFloatingProps:o,getItemProps:a}}),[i,o,a])}function Ro(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function Mo(e,t){return Ro(t,e===ci||e===fi,e===di||e===vi)}function Do(e,t,n){return Ro(t,e===fi,n?e===di:e===vi)||"Enter"===e||" "===e||""===e}function jo(e,t,n){return Ro(t,n?e===di:e===vi,e===fi)}function Io(e,t,n,r){return"both"===t||"horizontal"===t&&r&&r>1?"Escape"===e:Ro(t,n?e===vi:e===di,e===ci)}function Oo(e,t){var n=e.open,r=e.onOpenChange,i=e.elements,o=t.listRef,a=t.activeIndex,u=t.onNavigate,s=void 0===u?function(){}:u,l=t.enabled,c=void 0===l||l,f=t.selectedIndex,d=void 0===f?null:f,v=t.allowEscape,h=void 0!==v&&v,p=t.loop,m=void 0!==p&&p,y=t.nested,g=void 0!==y&&y,b=t.rtl,x=void 0!==b&&b,w=t.virtual,k=void 0!==w&&w,F=t.focusItemOnOpen,E=void 0===F?"auto":F,A=t.focusItemOnHover,S=void 0===A||A,P=t.openOnArrowKeyDown,C=void 0===P||P,T=t.disabledIndices,R=void 0===T?void 0:T,M=t.orientation,D=void 0===M?"vertical":M,j=t.cols,I=void 0===j?1:j,O=t.scrollItemIntoView,L=void 0===O||O,V=t.virtualItemRef,N=t.itemSizes,B=t.dense,z=void 0!==B&&B,W=Vi(lo(i.floating)),U=ji(),q=Ii();ki((function(){e.dataRef.current.orientation=D}),[e,D]);var H=li((function(){s(-1===_.current?null:_.current)})),K=Rn(i.domReference),X=ce.useRef(E),_=ce.useRef(null!=d?d:-1),Y=ce.useRef(null),G=ce.useRef(!0),$=ce.useRef(H),Z=ce.useRef(!!i.floating),J=ce.useRef(n),Q=ce.useRef(!1),ee=ce.useRef(!1),te=Vi(R),ne=Vi(n),re=Vi(L),ie=Vi(d),oe=ke(ce.useState(),2),ae=oe[0],ue=oe[1],se=ke(ce.useState(),2),le=se[0],fe=se[1],de=li((function(){function e(e){k?(ue(e.id),null==q||q.events.emit("virtualfocus",e),V&&(V.current=e)):Bi(e,{sync:Q.current,preventScroll:!0})}var t=o.current[_.current];t&&e(t),(Q.current?function(e){return e()}:requestAnimationFrame)((function(){var n=o.current[_.current]||t;if(n){t||e(n);var r=re.current;r&&he&&(ee.current||!G.current)&&(null==n.scrollIntoView||n.scrollIntoView("boolean"==typeof r?{block:"nearest",inline:"nearest"}:r))}}))}));ki((function(){c&&(n&&i.floating?X.current&&null!=d&&(ee.current=!0,_.current=d,H()):Z.current&&(_.current=-1,$.current()))}),[c,n,i.floating,d,H]),ki((function(){if(c&&n&&i.floating)if(null==a){if(Q.current=!1,null!=ie.current)return;if(Z.current&&(_.current=-1,de()),(!J.current||!Z.current)&&X.current&&(null!=Y.current||!0===X.current&&null==Y.current)){var e=0,t=function(){null==o.current[0]?(e<2&&(e?requestAnimationFrame:queueMicrotask)(t),e++):(_.current=null==Y.current||Do(Y.current,D,x)||g?mi(o,te.current):yi(o,te.current),Y.current=null,H())};t()}}else pi(o,a)||(_.current=a,de(),ee.current=!1)}),[c,n,i.floating,a,ie,g,o,D,x,H,de,te]),ki((function(){var e;if(c&&!i.floating&&q&&!k&&Z.current){var t=q.nodesRef.current,n=null==(e=t.find((function(e){return e.id===U})))||null==(e=e.context)?void 0:e.elements.floating,r=gn(An(i.floating)),o=t.some((function(e){return e.context&&bn(e.context.elements.floating,r)}));n&&!o&&G.current&&n.focus({preventScroll:!0})}}),[c,i.floating,q,U,k]),ki((function(){if(c&&q&&k&&!U)return q.events.on("virtualfocus",e),function(){q.events.off("virtualfocus",e)};function e(e){fe(e.id),V&&(V.current=e)}}),[c,q,k,U,V]),ki((function(){$.current=H,J.current=n,Z.current=!!i.floating})),ki((function(){n||(Y.current=null)}),[n]);var ve=null!=a,he=ce.useMemo((function(){function e(e){if(n){var t=o.current.indexOf(e);-1!==t&&_.current!==t&&(_.current=t,H())}}return be({onFocus:function(t){var n=t.currentTarget;Q.current=!0,e(n)},onClick:function(e){return e.currentTarget.focus({preventScroll:!0})}},S&&{onMouseMove:function(t){var n=t.currentTarget;Q.current=!0,ee.current=!1,e(n)},onPointerLeave:function(e){var t,n=e.pointerType;G.current&&"touch"!==n&&(Q.current=!0,_.current=-1,H(),!k)&&(null==(t=W.current)||t.focus({preventScroll:!0}))}})}),[n,W,S,o,H,k]),pe=li((function(e){if(G.current=!1,Q.current=!0,229!==e.which&&(ne.current||e.currentTarget!==W.current)){if(g&&Io(e.key,D,x,I))return Tn(e),r(!1,e.nativeEvent,"list-navigation"),void(rn(i.domReference)&&(k?null==q||q.events.emit("virtualfocus",i.domReference):i.domReference.focus()));var t=_.current,a=mi(o,R),u=yi(o,R);if(K||("Home"===e.key&&(Tn(e),_.current=a,H()),"End"===e.key&&(Tn(e),_.current=u,H())),I>1){var s=N||Array.from({length:o.current.length},(function(){return{width:1,height:1}})),l=function(e,t,n){var r=[],i=0;return e.forEach((function(e,o){var a=e.width,u=e.height,s=!1;for(n&&(i=0);!s;){for(var l=[],c=0;c<a;c++)for(var f=0;f<u;f++)l.push(i+c+f*t);i%t+a<=t&&l.every((function(e){return null==r[e]}))?(l.forEach((function(e){r[e]=o})),s=!0):i++}})),[].concat(r)}(s,I,z),c=l.findIndex((function(e){return null!=e&&!wi(o.current,e,R)})),f=l.reduce((function(e,t,n){return null==t||wi(o.current,t,R)?e:n}),-1),d=l[function(e,t){var n=t.event,r=t.orientation,i=t.loop,o=t.rtl,a=t.cols,u=t.disabledIndices,s=t.minIndex,l=t.maxIndex,c=t.prevIndex,f=t.stopEvent,d=void 0!==f&&f,v=c;if(n.key===ci){if(d&&Tn(n),-1===c)v=l;else if(v=gi(e,{startingIndex:v,amount:a,decrement:!0,disabledIndices:u}),i&&(c-a<s||v<0)){var h=c%a,p=l%a,m=l-(p-h);v=p===h?l:p>h?m:m-a}pi(e,v)&&(v=c)}if(n.key===fi&&(d&&Tn(n),-1===c?v=s:(v=gi(e,{startingIndex:c,amount:a,disabledIndices:u}),i&&c+a>l&&(v=gi(e,{startingIndex:c%a-a,amount:a,disabledIndices:u}))),pi(e,v)&&(v=c)),"both"===r){var y=On(c/a);n.key===(o?di:vi)&&(d&&Tn(n),c%a!==a-1?(v=gi(e,{startingIndex:c,disabledIndices:u}),i&&hi(v,a,y)&&(v=gi(e,{startingIndex:c-c%a-1,disabledIndices:u}))):i&&(v=gi(e,{startingIndex:c-c%a-1,disabledIndices:u})),hi(v,a,y)&&(v=c)),n.key===(o?vi:di)&&(d&&Tn(n),c%a!==0?(v=gi(e,{startingIndex:c,decrement:!0,disabledIndices:u}),i&&hi(v,a,y)&&(v=gi(e,{startingIndex:c+(a-c%a),decrement:!0,disabledIndices:u}))):i&&(v=gi(e,{startingIndex:c+(a-c%a),decrement:!0,disabledIndices:u})),hi(v,a,y)&&(v=c));var g=On(l/a)===y;pi(e,v)&&(v=i&&g?n.key===(o?vi:di)?l:gi(e,{startingIndex:c-c%a-1,disabledIndices:u}):c)}return v}({current:l.map((function(e){return null!=e?o.current[e]:null}))},{event:e,orientation:D,loop:m,rtl:x,cols:I,disabledIndices:xi([].concat(me(R||o.current.map((function(e,t){return wi(o.current,t)?t:void 0}))),[void 0]),l),minIndex:c,maxIndex:f,prevIndex:bi(_.current>u?a:_.current,s,l,I,e.key===fi?"bl":e.key===(x?di:vi)?"tr":"tl"),stopEvent:!0})];if(null!=d&&(_.current=d,H()),"both"===D)return}if(Mo(e.key,D)){if(Tn(e),n&&!k&&gn(e.currentTarget.ownerDocument)===e.currentTarget)return _.current=Do(e.key,D,x)?a:u,void H();Do(e.key,D,x)?_.current=m?t>=u?h&&t!==o.current.length?-1:a:gi(o,{startingIndex:t,disabledIndices:R}):Math.min(u,gi(o,{startingIndex:t,disabledIndices:R})):_.current=m?t<=a?h&&-1!==t?o.current.length:u:gi(o,{startingIndex:t,decrement:!0,disabledIndices:R}):Math.max(a,gi(o,{startingIndex:t,decrement:!0,disabledIndices:R})),pi(o,_.current)&&(_.current=-1),H()}}})),ye=ce.useMemo((function(){return k&&n&&ve&&{"aria-activedescendant":le||ae}}),[k,n,ve,le,ae]),ge=ce.useMemo((function(){return be(be({"aria-orientation":"both"===D?void 0:D},K?{}:ye),{},{onKeyDown:pe,onPointerMove:function(){G.current=!0}})}),[ye,pe,D,K]),xe=ce.useMemo((function(){function e(e){"auto"===E&&wn(e.nativeEvent)&&(X.current=!0)}function t(e){X.current=E,"auto"===E&&kn(e.nativeEvent)&&(X.current=!0)}return be(be({},ye),{},{onKeyDown:function(e){var t;G.current=!1;var i=e.key.startsWith("Arrow"),a=["Home","End"].includes(e.key),u=i||a,s=null==q||null==(t=q.nodesRef.current.find((function(e){return e.id===U})))||null==(t=t.context)||null==(t=t.dataRef)?void 0:t.current.orientation,l=jo(e.key,D,x),c=Io(e.key,D,x,I),f=jo(e.key,s,x),v=Mo(e.key,D),h=(g?f:v)||"Enter"===e.key||""===e.key.trim();if(k&&n){var p=null==q?void 0:q.nodesRef.current.find((function(e){return null==e.parentId})),m=q&&p?function(e,t){var n,r=-1;return function t(i,o){o>r&&(n=i,r=o),Wi(e,i).forEach((function(e){t(e.id,o+1)}))}(t,0),e.find((function(e){return e.id===n}))}(q.nodesRef.current,p.id):null;if(u&&m&&V){var y,b=new KeyboardEvent("keydown",{key:e.key,bubbles:!0});if(l||c){var w,F,E=(null==(w=m.context)?void 0:w.elements.domReference)===e.currentTarget,A=c&&!E?null==(F=m.context)?void 0:F.elements.domReference:l?o.current.find((function(e){return(null==e?void 0:e.id)===ae})):null;A&&(Tn(e),A.dispatchEvent(b),fe(void 0))}if((v||a)&&m.context&&m.context.open&&m.parentId&&e.currentTarget!==m.context.elements.domReference)return Tn(e),void(null==(y=m.context.elements.domReference)||y.dispatchEvent(b))}return pe(e)}if(n||C||!i){if(h){var S=Mo(e.key,s);Y.current=g&&S?null:e.key}if(g)return void(f&&(Tn(e),n?(_.current=mi(o,te.current),H()):r(!0,e.nativeEvent,"list-navigation")));v&&(null!=d&&(_.current=d),Tn(e),!n&&C?r(!0,e.nativeEvent,"list-navigation"):pe(e),n&&H())}},onFocus:function(){n&&!k&&(_.current=-1,H())},onPointerDown:t,onPointerEnter:t,onMouseDown:e,onClick:e})}),[ae,ye,I,pe,te,E,o,g,H,r,n,C,D,U,x,d,q,k,V]);return ce.useMemo((function(){return c?{reference:xe,floating:ge,item:he}:{}}),[c,xe,ge,he])}var Lo=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);function Vo(e,t){var n;void 0===t&&(t={});var r=e.open,i=e.floatingId,o=t,a=o.enabled,u=void 0===a||a,s=o.role,l=void 0===s?"dialog":s,c=null!=(n=Lo.get(l))?n:l,f=Ri(),d=null!=ji(),v=ce.useMemo((function(){return"tooltip"===c||"label"===l?xe({},"aria-"+("label"===l?"labelledby":"describedby"),r?i:void 0):be(be(be(be(be({"aria-expanded":r?"true":"false","aria-haspopup":"alertdialog"===c?"dialog":c,"aria-controls":r?i:void 0},"listbox"===c&&{role:"combobox"}),"menu"===c&&{id:f}),"menu"===c&&d&&{role:"menuitem"}),"select"===l&&{"aria-autocomplete":"none"}),"combobox"===l&&{"aria-autocomplete":"list"})}),[c,i,d,r,f,l]),h=ce.useMemo((function(){var e=be({id:i},c&&{role:c});return"tooltip"===c||"label"===l?e:be(be({},e),"menu"===c&&{"aria-labelledby":f})}),[c,i,f,l]),p=ce.useCallback((function(e){var t=e.active,n=e.selected,r=be({role:"option"},t&&{id:i+"-option"});switch(l){case"select":return be(be({},r),{},{"aria-selected":t&&n});case"combobox":return be(be({},r),t&&{"aria-selected":!0})}return{}}),[i,l]);return ce.useMemo((function(){return u?{reference:v,floating:h,item:p}:{}}),[u,v,h,p])}function No(e,t){void 0===t&&(t={});var n=e.open,r=e.elements.floating,i=t.duration,o=void 0===i?250:i,a=("number"==typeof o?o:o.close)||0,u=ke(ce.useState("unmounted"),2),s=u[0],l=u[1],c=function(e,t){var n=ke(ce.useState(e),2),r=n[0],i=n[1];return e&&!r&&i(!0),ce.useEffect((function(){if(!e&&r){var n=setTimeout((function(){return i(!1)}),t);return function(){return clearTimeout(n)}}}),[e,r,t]),r}(n,a);return!c&&"close"===s&&l("unmounted"),ki((function(){if(r){if(n){l("initial");var e=requestAnimationFrame((function(){l("open")}));return function(){cancelAnimationFrame(e)}}l("close")}}),[n,r]),{isMounted:c,status:s}}function Bo(e,t){var n,r=e.open,i=e.dataRef,o=t.listRef,a=t.activeIndex,u=t.onMatch,s=t.onTypingChange,l=t.enabled,c=void 0===l||l,f=t.findMatch,d=void 0===f?null:f,v=t.resetMs,h=void 0===v?750:v,p=t.ignoreKeys,m=void 0===p?[]:p,y=t.selectedIndex,g=void 0===y?null:y,b=ce.useRef(-1),x=ce.useRef(""),w=ce.useRef(null!=(n=null!=g?g:a)?n:-1),k=ce.useRef(null),F=li(u),E=li(s),A=Vi(d),S=Vi(m);ki((function(){r&&(Li(b),k.current=null,x.current="")}),[r]),ki((function(){var e;r&&""===x.current&&(w.current=null!=(e=null!=g?g:a)?e:-1)}),[r,g,a]);var P=li((function(e){e?i.current.typing||(i.current.typing=e,E(e)):i.current.typing&&(i.current.typing=e,E(e))})),C=li((function(e){function t(e,t,n){var r=A.current?A.current(t,n):t.find((function(e){return 0===(null==e?void 0:e.toLocaleLowerCase().indexOf(n.toLocaleLowerCase()))}));return r?e.indexOf(r):-1}var n=o.current;if(x.current.length>0&&" "!==x.current[0]&&(-1===t(n,n,x.current)?P(!1):" "===e.key&&Tn(e)),!(null==n||S.current.includes(e.key)||1!==e.key.length||e.ctrlKey||e.metaKey||e.altKey)){r&&" "!==e.key&&(Tn(e),P(!0)),n.every((function(e){var t,n;return!e||(null==(t=e[0])?void 0:t.toLocaleLowerCase())!==(null==(n=e[1])?void 0:n.toLocaleLowerCase())}))&&x.current===e.key&&(x.current="",w.current=k.current),x.current+=e.key,Li(b),b.current=window.setTimeout((function(){x.current="",w.current=k.current,P(!1)}),h);var i=w.current,a=t(n,[].concat(me(n.slice((i||0)+1)),me(n.slice(0,(i||0)+1))),x.current);-1!==a?(F(a),k.current=a):" "!==e.key&&(x.current="",P(!1))}})),T=ce.useMemo((function(){return{onKeyDown:C}}),[C]),R=ce.useMemo((function(){return{onKeyDown:C,onKeyUp:function(e){" "===e.key&&P(!1)}}}),[C,P]);return ce.useMemo((function(){return c?{reference:T,floating:R}:{}}),[c,T,R])}var zo=function(e){var t=e.children,n=e.context,r=ye(e,d),i=ke(ce.useState(null),2),o=i[0],a=i[1];return ce.useEffect((function(){var e=n.elements.domReference;if(e){var t=e.closest("dialog");if(t)return a(t)}return a(void 0),function(){a(null)}}),[n.elements.domReference]),ae.jsx(ao,be(be({},r),{},{root:o,children:t}))},Wo=zt("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),Uo=ce.createContext(null),qo=function(){var e=ce.use(Uo);if(null==e)throw new Error("Popover components must be wrapped in <Popover />");return e},Ho=function(e){var t,n,r,i=e.ref,o=e.style,a=e.className,u=e.children,s=ye(e,h),l=qo(),c=l.context,f=l.refs,d=l.getFloatingProps,v=l.modal,p=ai([f.setFloating,i]),m=No(c,{duration:150}),y=m.isMounted,g=m.status;return y?ae.jsx(zo,{context:c,children:ae.jsx(mo,{context:c,modal:v,children:ae.jsx("div",be(be({"data-state":["open","initial"].includes(g)?"open":"closed","data-side":c.placement.split("-")[0]},d(be({ref:p,className:Ot("border-border-soft bg-background-highlight text-foreground z-50 w-72 overflow-auto rounded-xl border p-3 outline-none","max-h-(--popover-max-height) origin-(--popover-transform-origin) transition duration-300 ease-out","data-[state=closed]:data-[side=bottom]:-translate-y-2 data-[state=closed]:data-[side=left]:translate-x-2 data-[state=closed]:data-[side=right]:-translate-x-2 data-[state=closed]:data-[side=top]:translate-y-2","data-[state=closed]:scale-95 data-[state=closed]:opacity-0 data-[state=closed]:duration-150","data-[state=open]:translate-x-0 data-[state=open]:translate-y-0 data-[state=open]:scale-100",a),style:be({position:c.strategy,top:null!==(t=c.y)&&void 0!==t?t:0,left:null!==(n=c.x)&&void 0!==n?n:0,"--popover-transform-origin":Ko(c.placement),visibility:null!=(r=c.middlewareData.hide)&&r.referenceHidden?"hidden":"visible"},o)},s))),{},{children:u}))})}):null},Ko=function(e){switch(e){case"top":return"bottom";case"bottom":return"top";case"left":return"right";case"right":return"left";case"top-start":return"bottom left";case"top-end":return"bottom right";case"bottom-start":return"top left";case"bottom-end":return"top right";case"left-start":return"right top";case"left-end":return"right bottom";case"right-start":return"left top";case"right-end":return"left bottom"}},Xo=function(e){var t=e.className,n=ye(e,p);return ae.jsxs("div",{className:"border-border-soft relative mb-1 flex items-center rounded-t-lg border-b bg-transparent",children:[ae.jsx(Wo,{className:"text-foreground absolute left-4 size-4 shrink-0"}),ae.jsx("input",be({className:Ot("placeholder:text-foreground/30 h-10 w-full border-0 bg-transparent p-4 pl-10 text-base font-medium transition-colors outline-none focus:ring-0",t)},n))]})},_o=function(e){var t=e.className,n=e.orientation,r=void 0===n?"horizontal":n,i=e.decorative,o=ye(e,m),a=i?{role:"none"}:{"aria-orientation":"vertical"===r?r:void 0,role:"separator"};return ae.jsx("div",be(be({className:Ot("bg-border-soft shrink-0","horizontal"===r?"h-px w-full":"h-full w-px",t)},a),o))},Yo=function(e){var t=ce.useRef(e);return ce.useEffect((function(){e&&(t.current=e)}),[e]),ce.useCallback((function(){t.current&&t.current.apply(t,arguments)}),[])},Go=ce.createContext(null),$o=function(){var e=ce.use(Go);if(null==e)throw new Error("Dropdown components must be wrapped in <Dropdown />");return e},Zo=function(e){var t=e.ref,n=e.children,r=e.asChild,i=void 0!==r&&r,o=e.className,a=ye(e,v),u=qo(),s=i?Ut:"button",l=ai([u.refs.setReference,t]);return ae.jsx(s,be(be({ref:l,type:i?void 0:"button",className:Ot(!i&&"disabled:opacity-40",o),"data-state":u.open?"open":"closed"},u.getReferenceProps(a)),{},{children:n}))};
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */ce.createContext(null);ne({I:function(e){var t=e.children,n=e.modal,r=void 0===n||n,i=e.placement,o=function(e){var t=e.open,n=e.onOpenChange,r=e.placement,i=void 0===r?"bottom":r,o=e.offset,a=void 0===o?4:o,u=ke(ce.useState(!1),2),s=u[0],l=u[1],c=null!=t?t:s,f=ce.useCallback((function(e,t,r){l(e),null==n||n(e,t,r)}),[n]),d=Ao({placement:i,open:c,onOpenChange:f,whileElementsMounted:function(e,t,n){return qr(e,t,n,{layoutShift:!1})},middleware:[ri({padding:8}),ni({padding:8}),ti(a),ii({apply:function(e){var t=e.rects,n=e.elements,r=e.availableHeight;n.floating.style.setProperty("--popover-max-height","".concat(r,"px")),n.floating.style.setProperty("--popover-width","".concat(t.reference.width,"px"))},padding:4}),oi()]});return ce.useMemo((function(){return be({open:c,setOpen:f},d)}),[c,f,d])}(be({placement:void 0===i?"bottom-start":i},ye(e,y))),a=ke(ce.useState(null),2),u=a[0],s=a[1],l=ce.useRef([]),c=ke(ce.useState({}),2),f=c[0],d=c[1],v=ce.useRef([]),h=ke(ce.useState(null),2),p=h[0],m=h[1];ce.useEffect((function(){v.current=Object.values(f).map((function(e){return e.label}))}),[f]);var g=ce.useCallback((function(e){return d((function(t){return be(be({},t),{},xe({},e.id,e))})),function(){d((function(t){return delete t[e.id],t}))}}),[]),b=To([bo(o.context),Fo(o.context),Vo(o.context),Oo(o.context,{listRef:l,activeIndex:u,onNavigate:s,virtual:!!p||void 0}),Bo(o.context,{enabled:!p,listRef:v,activeIndex:u,onMatch:s})]),x=ce.useMemo((function(){return be(be(be({},o),b),{},{modal:r})}),[o,b,r]),w=ce.useMemo((function(){return{elementsRef:l,labelsRef:v,highlightedIndex:u,setHighlightedIndex:s,searchInputRef:p,setSearchInputRef:m,getItemProps:b.getItemProps,registerItem:g,items:f}}),[l,v,u,p,b,g,f]);return ae.jsx(Uo,{value:x,children:ae.jsx(Go,{value:w,children:t})})},v:Zo,b:function(e){var t=e.children,n=e.className,r=ye(e,g),i=$o().elementsRef;return ae.jsx(Ho,be(be({className:Ot("flex flex-col items-stretch p-0",n)},r),{},{children:ae.jsx(Ai,{elementsRef:i,children:t})}))},R:function(e){var t=e.ref,n=e.children,r=e.className,i=e.disabled,o=e.onClick,a=e.onSelect,u=e.onKeyDown,s=e.asChild,l=ye(e,b),c=ce.useId(),f=ce.useRef(null),d=$o(),v=d.registerItem,h=d.highlightedIndex,p=d.getItemProps,m=d.items,y=d.searchInputRef,g=d.elementsRef,x=qo(),w=Yo(a),k=Si(),F=k.ref,E=k.index,A=ai([F,t,f]),S=h===E,P=s?Ut:"button";ce.useLayoutEffect((function(){var e,t=null==(e=f.current)?void 0:e.textContent;return t?v({id:c,label:t,onSelect:w}):void 0}),[v,c,w]);return ae.jsx(P,be(be({ref:A,"data-item-id":c,"data-highlighted":S||void 0,tabIndex:S?0:-1,disabled:i||void 0,"data-disabled":i||void 0,className:Ot("data-highlighted:bg-foreground/5 text-foreground/80 relative mx-1 flex cursor-pointer items-center gap-1.5 rounded-lg px-3 py-1.5 text-sm outline-none select-none first:mt-1 last:mb-1 data-disabled:pointer-events-none data-disabled:opacity-50",r)},p(be(be({},l),{},{onKeyDown:function(e){var t,n,r;if("Enter"===e.key&&null!==h){var i=null==(t=g.current[h])?void 0:t.dataset.itemId;i&&(null==(r=null==(n=m[i])?void 0:n.onSelect)||r.call(n,e))}else y&&y.focus();null==u||u(e)},onClick:function(e){null==w||w(e),null==o||o(e),e.defaultPrevented||x.setOpen(!1)}}))),{},{children:n}))},C:function(e){var t=e.className,n=ye(e,x);return ae.jsx(_o,be({className:Ot("my-1",t)},n))}});var Jo=It({base:["transition","font-primary","w-full border py-1 text-base","focus-visible:border-focus-hard focus-visible:ring-focus text-foreground placeholder:text-foreground/40 data-invalid:border-negative data-invalid:focus-visible:border-negative data-invalid:focus-visible:ring-negative/20 data-invalid:text-negative focus:outline-none focus-visible:ring-4 disabled:cursor-not-allowed disabled:opacity-50 data-invalid:hover:border-red-600","pl-[calc(var(--input-x-padding)+var(--prefix-width,0px))]","pr-[calc(var(--input-x-padding)+var(--suffix-width,0px))]"],variants:{variant:{default:"border-border-soft bg-background-highlight hover:border-border"},size:{sm:"h-8 rounded-lg text-xs [--input-x-padding:--spacing(3)]",md:"h-10 rounded-xl text-sm [--input-x-padding:--spacing(4)]",lg:"h-12 rounded-xl text-base [--input-x-padding:--spacing(5)]",xl:"h-14 rounded-2xl text-xl [--input-x-padding:--spacing(6)]"}},defaultVariants:{variant:"default",size:"md"}}),Qo=ce.createContext({prefixWidth:0,suffixWidth:0,setPrefixWidth:function(){},setSuffixWidth:function(){}});ne({h:Jo,k:function(e){var t,n=e.className,r=e.invalid,i=e.variant,o=e.id,a=e.size,u=void 0===a?"md":a,s=ye(e,w),l=ce.use(Qo),c=$t(),f=r||!(null==c||!c["aria-errormessage"])||void 0;return ae.jsx("input",be({id:null!=o?o:null==c?void 0:c.id,"aria-errormessage":null==c?void 0:c["aria-errormessage"],"aria-describedby":null==c?void 0:c["aria-describedby"],"aria-labelledby":null==c?void 0:c["aria-labelledby"],"aria-invalid":f,"data-invalid":f,className:Ot(Jo({variant:i,size:null!==(t=null==l?void 0:l.size)&&void 0!==t?t:u}),n)},s))}});zt("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */
var ea=zt("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),ta=new WeakMap,na=new WeakMap,ra=function(e,t){var n=ce.useRef(null);ce.useEffect((function(){n.current=function(e){return e?e instanceof HTMLElement?e:"string"==typeof e?document.querySelector(e):e&&"current"in e&&e.current||null:document.body}(t)}),[t]),ce.useEffect((function(){var t=n.current;if(t&&e){var r=na.get(t)||0;0===r&&ta.set(t,{overflow:t.style.overflow,scrollbarGutter:t.style.scrollbarGutter});var i=r+1;return na.set(t,i),Object.assign(t.style,{overflow:"hidden",scrollbarGutter:"stable"}),function(){var e=na.get(t)||0,n=Math.max(0,e-1);if(na.set(t,n),0===n){var r=ta.get(t)||{overflow:"",scrollbarGutter:""};Object.assign(t.style,r),ta.delete(t)}}}}),[e])};ce.createContext(null);var ia=ne("e",{"emphasized-accelerate":[.3,0,.8,.15],"emphasized-decelerate":[.05,.7,.1,1]}),oa=("undefined"==typeof window?"undefined":Ae(window))<"u",aa={current:null},ua={current:!1};function sa(){if(ua.current=!0,oa)if(window.matchMedia){var e=window.matchMedia("(prefers-reduced-motion)"),t=function(){return aa.current=e.matches};e.addListener(t),t()}else aa.current=!1}function la(e){var t;return function(){return void 0===t&&(t=e()),t}}var ca=la((function(){return void 0!==window.ScrollTimeline})),fa=function(){return fe((function e(t){var n=this;le(this,e),this.stop=function(){return n.runAll("stop")},this.animations=t.filter(Boolean)}),[{key:"finished",get:function(){return Promise.all(this.animations.map((function(e){return"finished"in e?e.finished:e})))}},{key:"getAll",value:function(e){return this.animations[0][e]}},{key:"setAll",value:function(e,t){for(var n=0;n<this.animations.length;n++)this.animations[n][e]=t}},{key:"attachTimeline",value:function(e,t){var n=this,r=this.animations.map((function(n){return ca()&&n.attachTimeline?n.attachTimeline(e):"function"==typeof t?t(n):void 0}));return function(){r.forEach((function(e,t){e&&e(),n.animations[t].stop()}))}}},{key:"time",get:function(){return this.getAll("time")},set:function(e){this.setAll("time",e)}},{key:"speed",get:function(){return this.getAll("speed")},set:function(e){this.setAll("speed",e)}},{key:"startTime",get:function(){return this.getAll("startTime")}},{key:"duration",get:function(){for(var e=0,t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}},{key:"runAll",value:function(e){this.animations.forEach((function(t){return t[e]()}))}},{key:"flatten",value:function(){this.runAll("flatten")}},{key:"play",value:function(){this.runAll("play")}},{key:"pause",value:function(){this.runAll("pause")}},{key:"cancel",value:function(){this.runAll("cancel")}},{key:"complete",value:function(){this.runAll("complete")}}])}(),da=function(e){function t(){return le(this,t),ie(this,t,arguments)}return ue(t,e),fe(t,[{key:"then",value:function(e,t){return Promise.all(this.animations).then(e).catch(t)}}])}(fa),va=function(e){return 1e3*e},ha=function(e){return e/1e3};function pa(e){for(var t=0,n=e.next(t);!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}var ma=function(e,t,n){var r=t-e;return 0===r?1:(n-e)/r},ya=function(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r="",i=Math.max(Math.round(t/n),2),o=0;o<i;o++)r+=e(ma(0,i-1,o))+", ";return"linear(".concat(r.substring(0,r.length-2),")")},ga=function(e,t,n){return n>t?t:n<e?e:n};function ba(e,t){return t?e*(1e3/t):0}function xa(e,t,n){var r=Math.max(t-5,0);return ba(n-e(r),t-r)}var wa=100,ka=10,Fa=1,Ea=0,Aa=800,Sa=.3,Pa=.3,Ca={granular:.01,default:2},Ta={granular:.005,default:.5},Ra=.01,Ma=10,Da=.05,ja=1,Ia=function(e){return e},Oa=Ia;function La(e){var t,n,r=e.duration,i=void 0===r?Aa:r,o=e.bounce,a=void 0===o?Sa:o,u=e.velocity,s=void 0===u?Ea:u,l=e.mass,c=void 0===l?Fa:l,f=1-a;f=ga(Da,ja,f),i=ga(Ra,Ma,ha(i)),f<1?(t=function(e){var t=e*f,n=t*i;return.001-(t-s)/Na(e,f)*Math.exp(-n)},n=function(e){var n=e*f*i,r=n*s+s,o=Math.pow(f,2)*Math.pow(e,2)*i,a=Math.exp(-n),u=Na(Math.pow(e,2),f);return(.001-t(e)>0?-1:1)*((r-o)*a)/u}):(t=function(e){return Math.exp(-e*i)*((e-s)*i+1)-.001},n=function(e){return Math.exp(-e*i)*(i*i*(s-e))});var d=function(e,t,n){for(var r=n,i=1;i<Va;i++)r-=e(r)/t(r);return r}(t,n,5/i);if(i=va(i),isNaN(d))return{stiffness:wa,damping:ka,duration:i};var v=Math.pow(d,2)*c;return{stiffness:v,damping:2*f*Math.sqrt(c*v),duration:i}}var Va=12;function Na(e,t){return e*Math.sqrt(1-t*t)}var Ba=["duration","bounce"],za=["stiffness","damping","mass"];function Wa(e,t){return t.some((function(t){return void 0!==e[t]}))}function Ua(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Pa,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Sa,r="object"!=Ae(t)?{visualDuration:t,keyframes:[0,1],bounce:n}:t,i=r.restSpeed,o=r.restDelta,a=r.keyframes[0],u=r.keyframes[r.keyframes.length-1],s={done:!1,value:a},l=function(e){var t=be({velocity:Ea,stiffness:wa,damping:ka,mass:Fa,isResolvedFromDuration:!1},e);if(!Wa(e,za)&&Wa(e,Ba))if(e.visualDuration){var n=e.visualDuration,r=2*Math.PI/(1.2*n),i=r*r,o=2*ga(.05,1,1-(e.bounce||0))*Math.sqrt(i);t=be(be({},t),{},{mass:Fa,stiffness:i,damping:o})}else{var a=La(e);(t=be(be(be({},t),a),{},{mass:Fa})).isResolvedFromDuration=!0}return t}(be(be({},r),{},{velocity:-ha(r.velocity||0)})),c=l.stiffness,f=l.damping,d=l.mass,v=l.duration,h=l.velocity,p=l.isResolvedFromDuration,m=h||0,y=f/(2*Math.sqrt(c*d)),g=u-a,b=ha(Math.sqrt(c/d)),x=Math.abs(g)<5;if(i||(i=x?Ca.granular:Ca.default),o||(o=x?Ta.granular:Ta.default),y<1){var w=Na(b,y);e=function(e){var t=Math.exp(-y*b*e);return u-t*((m+y*b*g)/w*Math.sin(w*e)+g*Math.cos(w*e))}}else if(1===y)e=function(e){return u-Math.exp(-b*e)*(g+(m+b*g)*e)};else{var k=b*Math.sqrt(y*y-1);e=function(e){var t=Math.exp(-y*b*e),n=Math.min(k*e,300);return u-t*((m+y*b*g)*Math.sinh(n)+k*g*Math.cosh(n))/k}}var F={calculatedDuration:p&&v||null,next:function(t){var n=e(t);if(p)s.done=t>=v;else{var r=0;y<1&&(r=0===t?va(m):xa(e,t,n));var a=Math.abs(r)<=i,l=Math.abs(u-n)<=o;s.done=a&&l}return s.value=s.done?u:n,s},toString:function(){var e=Math.min(pa(F),2e4),t=ya((function(t){return F.next(e*t).value}),e,30);return e+"ms "+t}};return F}function qa(e){return"function"==typeof e}var Ha=function(e){return Array.isArray(e)&&"number"!=typeof e[0]};function Ka(e,t){return Ha(e)?e[function(e,t,n){var r=t-e;return((n-e)%r+r)%r+e}(0,e.length,t)]:e}var Xa=function(e,t,n){return e+(t-e)*n};function _a(e,t){for(var n=e[e.length-1],r=1;r<=t;r++){var i=ma(0,t,r);e.push(Xa(n,1,i))}}function Ya(e){var t=[0];return _a(t,e.length-1),t}var Ga=function(e){return!(!e||!e.getVelocity)};function $a(e,t,n){var r;if(e instanceof EventTarget)return[e];if("string"==typeof e){var i=document,o=null!==(r=null==n?void 0:n[e])&&void 0!==r?r:i.querySelectorAll(e);return o?Array.from(o):[]}return Array.from(e)}function Za(e){return"object"==Ae(e)&&!Array.isArray(e)}function Ja(e,t,n,r){return"string"==typeof e&&Za(t)?$a(e,0,r):e instanceof NodeList?Array.from(e):Array.isArray(e)?e:[e]}function Qa(e,t,n,r){var i;return"number"==typeof t?t:t.startsWith("-")||t.startsWith("+")?Math.max(0,e+parseFloat(t)):"<"===t?n:null!==(i=r.get(t))&&void 0!==i?i:e}function eu(e,t){-1===e.indexOf(t)&&e.push(t)}function tu(e,t){var n=e.indexOf(t);n>-1&&e.splice(n,1)}function nu(e,t,n,r,i,o){!function(e,t,n){for(var r=0;r<e.length;r++){var i=e[r];i.at>t&&i.at<n&&(tu(e,i),r--)}}(e,i,o);for(var a=0;a<t.length;a++)e.push({value:t[a],at:Xa(i,o,r[a]),easing:Ka(n,a)})}function ru(e,t){return e.at===t.at?null===e.value?1:null===t.value?-1:0:e.at-t.at}function iu(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.defaultTransition,r=void 0===n?{}:n,i=ye(t,k),o=arguments.length>3?arguments[3]:void 0,a=r.duration||.3,u=new Map,s=new Map,l={},c=new Map,f=0,d=0,v=0,h=function(){var t=e[p];if("string"==typeof t)return c.set(t,d),0;if(!Array.isArray(t))return c.set(t.name,Qa(d,t.at,f,c)),0;var n=ke(t,3),i=n[0],u=n[1],h=n[2],m=void 0===h?{}:h;void 0!==m.at&&(d=Qa(d,m.at,f,c));var y=0,g=function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,u=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,s=function(e){return Array.isArray(e)?e:[e]}(e),l=t.delay,c=void 0===l?0:l,f=t.times,h=void 0===f?Ya(s):f,p=t.type,m=void 0===p?"keyframes":p,g=t.repeat,b=(t.repeatType,t.repeatDelay,ye(t,F)),x=t.ease,w=void 0===x?r.ease||"easeOut":x,k=t.duration,E="function"==typeof c?c(i,u):c,A=s.length,S=qa(m)?m:null==o?void 0:o[m];if(A<=2&&S){var P=100;if(2===A&&lu(s)){var C=s[1]-s[0];P=Math.abs(C)}var T=be({},b);void 0!==k&&(T.duration=va(k));var R=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,n=(arguments.length>2?arguments[2]:void 0)(be(be({},e),{},{keyframes:[0,t]})),r=Math.min(pa(n),2e4);return{type:"keyframes",ease:function(e){return n.next(r*e).value/t},duration:ha(r)}}(T,P,S);w=R.ease,k=R.duration}null!=k||(k=a);var M=d+E;1===h.length&&0===h[0]&&(h[1]=1);var D=h.length-s.length;if(D>0&&_a(h,D),1===s.length&&s.unshift(null),g){k=function(e,t){return e*(t+1)}(k,g);var j=me(s),I=me(h);w=Array.isArray(w)?me(w):[w];for(var O=me(w),L=0;L<g;L++){s.push.apply(s,me(j));for(var V=0;V<j.length;V++)h.push(I[V]+(L+1)),w.push(0===V?"linear":Ka(O,V-1))}!function(e,t){for(var n=0;n<e.length;n++)e[n]=e[n]/(t+1)}(h,g)}var N=M+k;nu(n,s,w,h,M,N),y=Math.max(E+k,y),v=Math.max(N,v)};if(Ga(i))g(u,m,au("default",ou(i,s)));else for(var b=Ja(i,u,0,l),x=b.length,w=0;w<x;w++){var k=ou(b[w],s);for(var E in u)g(u[E],uu(m,E),au(E,k),w,x)}f=d,d+=y},p=0;p<e.length;p++)h();return s.forEach((function(e,t){for(var n in e){var o=e[n];o.sort(ru);for(var a=[],s=[],l=[],c=0;c<o.length;c++){var f=o[c],d=f.at,h=f.value,p=f.easing;a.push(h),s.push(ma(0,v,d)),l.push(p||"easeOut")}0!==s[0]&&(s.unshift(0),a.unshift(a[0]),l.unshift("easeInOut")),1!==s[s.length-1]&&(s.push(1),a.push(null)),u.has(t)||u.set(t,{keyframes:{},transition:{}});var m=u.get(t);m.keyframes[n]=a,m.transition[n]=be(be({},r),{},{duration:v,ease:l,times:s},i)}})),u}function ou(e,t){return!t.has(e)&&t.set(e,{}),t.get(e)}function au(e,t){return t[e]||(t[e]=[]),t[e]}function uu(e,t){return e&&e[t]?be(be({},e),e[t]):be({},e)}var su=function(e){return"number"==typeof e},lu=function(e){return e.every(su)},cu=new WeakMap;function fu(e,t){return e?e[t]||e.default||e:void 0}var du=["read","resolveKeyframes","update","preRender","render","postRender"];function vu(e,t){var n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=function(){return n=!0},a=du.reduce((function(e,t){return e[t]=function(e){var t=new Set,n=new Set,r=!1,i=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1};function u(t){o.has(t)&&(s.schedule(t),e()),t(a)}var s={schedule:function(e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&r?t:n;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&o.add(e),i.has(e)||i.add(e),e},cancel:function(e){n.delete(e),o.delete(e)},process:function(e){var o;a=e,r?i=!0:(r=!0,n=(o=[n,t])[1],(t=o[0]).forEach(u),t.clear(),r=!1,i&&(i=!1,s.process(e)))}};return s}(o),e}),{}),u=a.read,s=a.resolveKeyframes,l=a.update,c=a.preRender,f=a.render,d=a.postRender,v=function(){var o=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1),i.timestamp=o,i.isProcessing=!0,u.process(i),s.process(i),l.process(i),c.process(i),f.process(i),d.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(v))};return{schedule:du.reduce((function(t,o){var u=a[o];return t[o]=function(t){var o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return n||(n=!0,r=!0,i.isProcessing||e(v)),u.schedule(t,o,a)},t}),{}),cancel:function(e){for(var t=0;t<du.length;t++)a[du[t]].cancel(e)},state:i,steps:a}}var hu,pu=vu(("undefined"==typeof requestAnimationFrame?"undefined":Ae(requestAnimationFrame))<"u"?requestAnimationFrame:Ia,!0),mu=pu.schedule,yu=pu.cancel,gu=pu.state,bu=pu.steps,xu=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],wu=new Set(xu),ku=new Set(["width","height","top","left","right","bottom"].concat(xu)),Fu=function(){return fe((function e(){le(this,e),this.subscriptions=[]}),[{key:"add",value:function(e){var t=this;return eu(this.subscriptions,e),function(){return tu(t.subscriptions,e)}}},{key:"notify",value:function(e,t,n){var r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(var i=0;i<r;i++){var o=this.subscriptions[i];o&&o(e,t,n)}}},{key:"getSize",value:function(){return this.subscriptions.length}},{key:"clear",value:function(){this.subscriptions.length=0}}])}(),Eu=!1;function Au(){hu=void 0}var Su={now:function(){return void 0===hu&&Su.set(gu.isProcessing||Eu?gu.timestamp:performance.now()),hu},set:function(e){hu=e,queueMicrotask(Au)}},Pu=function(){return fe((function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};le(this,e),this.version="12.5.0",this.canTrackVelocity=null,this.events={},this.updateAndNotify=function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=Su.now();n.updatedAt!==r&&n.setPrevFrameValue(),n.prev=n.current,n.setCurrent(e),n.current!==n.prev&&n.events.change&&n.events.change.notify(n.current),t&&n.events.renderRequest&&n.events.renderRequest.notify(n.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=r.owner}),[{key:"setCurrent",value:function(e){this.current=e,this.updatedAt=Su.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=function(e){return!isNaN(parseFloat(e))}(this.current))}},{key:"setPrevFrameValue",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.current;this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}},{key:"onChange",value:function(e){return this.on("change",e)}},{key:"on",value:function(e,t){var n=this;this.events[e]||(this.events[e]=new Fu);var r=this.events[e].add(t);return"change"===e?function(){r(),mu.read((function(){n.events.change.getSize()||n.stop()}))}:r}},{key:"clearListeners",value:function(){for(var e in this.events)this.events[e].clear()}},{key:"attach",value:function(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}},{key:"set",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}},{key:"setWithVelocity",value:function(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}},{key:"jump",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}},{key:"get",value:function(){return this.current}},{key:"getPrevious",value:function(){return this.prev}},{key:"getVelocity",value:function(){var e=Su.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;var t=Math.min(this.updatedAt-this.prevUpdatedAt,30);return ba(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}},{key:"start",value:function(e){var t=this;return this.stop(),new Promise((function(n){t.hasAnimated=!0,t.animation=e(n),t.events.animationStart&&t.events.animationStart.notify()})).then((function(){t.events.animationComplete&&t.events.animationComplete.notify(),t.clearAnimation()}))}},{key:"stop",value:function(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}},{key:"isAnimating",value:function(){return!!this.animation}},{key:"clearAnimation",value:function(){delete this.animation}},{key:"destroy",value:function(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}])}();function Cu(e,t){return new Pu(e,t)}var Tu=function(e){return Array.isArray(e)},Ru=function(e){return Tu(e)?e[e.length-1]||0:e};function Mu(e){var t=[{},{}];return null==e||e.values.forEach((function(e,n){t[0][n]=e.get(),t[1][n]=e.getVelocity()})),t}function Du(e,t,n,r){if("function"==typeof t){var i=ke(Mu(r),2),o=i[0],a=i[1];t=t(void 0!==n?n:e.custom,o,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){var u=ke(Mu(r),2),s=u[0],l=u[1];t=t(void 0!==n?n:e.custom,s,l)}return t}function ju(e,t,n){var r=e.getProps();return Du(r,t,void 0!==n?n:r.custom,e)}function Iu(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Cu(n))}function Ou(e,t){var n=e.getValue("willChange");if(function(e){return!(!Ga(e)||!e.add)}(n))return n.add(t)}var Lu=function(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()},Vu="data-"+Lu("framerAppearId");function Nu(e){return e.props[Vu]}function Bu(e,t){e.timeline=t,e.onfinish=null}var zu=function(e){return Array.isArray(e)&&"number"==typeof e[0]},Wu={linearEasing:void 0};function Uu(e,t){var n=la(e);return function(){var e;return null!==(e=Wu[t])&&void 0!==e?e:n()}}var qu=Uu((function(){try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0}),"linearEasing");function Hu(e){return!!("function"==typeof e&&qu()||!e||"string"==typeof e&&(e in Xu||qu())||zu(e)||Array.isArray(e)&&e.every(Hu))}var Ku=function(e){var t=ke(e,4),n=t[0],r=t[1],i=t[2],o=t[3];return"cubic-bezier(".concat(n,", ").concat(r,", ").concat(i,", ").concat(o,")")},Xu={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ku([0,.65,.55,1]),circOut:Ku([.55,0,1,.45]),backIn:Ku([.31,.01,.66,-.59]),backOut:Ku([.33,1.53,.69,.99])};function _u(e,t){if(e)return"function"==typeof e&&qu()?ya(e,t):zu(e)?Ku(e):Array.isArray(e)?e.map((function(e){return _u(e,t)||Xu.easeOut})):Xu[e]}var Yu=function(e,t,n){return(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e};function Gu(e,t,n,r){if(e===t&&n===r)return Ia;var i=function(t){return function(e,t,n,r,i){var o,a,u=0;do{(o=Yu(a=t+(n-t)/2,r,i)-e)>0?n=a:t=a}while(Math.abs(o)>1e-7&&++u<12);return a}(t,0,1,e,n)};return function(e){return 0===e||1===e?e:Yu(i(e),t,r)}}var $u=function(e){return function(t){return t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2}},Zu=function(e){return function(t){return 1-e(1-t)}},Ju=Gu(.33,1.53,.69,.99),Qu=Zu(Ju),es=$u(Qu),ts=function(e){return(e*=2)<1?.5*Qu(e):.5*(2-Math.pow(2,-10*(e-1)))},ns=function(e){return 1-Math.sin(Math.acos(e))},rs=Zu(ns),is=$u(ns),os=function(e){return/^0(?:[\0-\x08\x0E-\x1F!-\x2D\/-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+$/.test(e)};function as(e){return"number"==typeof e?0===e:null===e||("none"===e||"0"===e||os(e))}var us={test:function(e){return"number"==typeof e},parse:parseFloat,transform:function(e){return e}},ss=be(be({},us),{},{transform:function(e){return ga(0,1,e)}}),ls=be(be({},us),{},{default:1}),cs=function(e){return Math.round(1e5*e)/1e5},fs=/-?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)/g;var ds=/^(?:#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\))$/i,vs=function(e,t){return function(n){return!!("string"==typeof n&&ds.test(n)&&n.startsWith(e)||t&&!function(e){return null==e}(n)&&Object.prototype.hasOwnProperty.call(n,t))}},hs=function(e,t,n){return function(r){if("string"!=typeof r)return r;var i=ke(r.match(fs),4),o=i[0],a=i[1],u=i[2],s=i[3];return xe(xe(xe(xe({},e,parseFloat(o)),t,parseFloat(a)),n,parseFloat(u)),"alpha",void 0!==s?parseFloat(s):1)}},ps=be(be({},us),{},{transform:function(e){return Math.round(function(e){return ga(0,255,e)}(e))}}),ms={test:vs("rgb","red"),parse:hs("red","green","blue"),transform:function(e){var t=e.red,n=e.green,r=e.blue,i=e.alpha,o=void 0===i?1:i;return"rgba("+ps.transform(t)+", "+ps.transform(n)+", "+ps.transform(r)+", "+cs(ss.transform(o))+")"}};var ys={test:vs("#"),parse:function(e){var t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ms.transform},gs=function(e){return{test:function(t){return"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length},parse:parseFloat,transform:function(t){return"".concat(t).concat(e)}}},bs=gs("deg"),xs=gs("%"),ws=gs("px"),ks=gs("vh"),Fs=gs("vw"),Es=be(be({},xs),{},{parse:function(e){return xs.parse(e)/100},transform:function(e){return xs.transform(100*e)}}),As={test:vs("hsl","hue"),parse:hs("hue","saturation","lightness"),transform:function(e){var t=e.hue,n=e.saturation,r=e.lightness,i=e.alpha,o=void 0===i?1:i;return"hsla("+Math.round(t)+", "+xs.transform(cs(n))+", "+xs.transform(cs(r))+", "+cs(ss.transform(o))+")"}},Ss={test:function(e){return ms.test(e)||ys.test(e)||As.test(e)},parse:function(e){return ms.test(e)?ms.parse(e):As.test(e)?As.parse(e):ys.parse(e)},transform:function(e){return"string"==typeof e?e:e.hasOwnProperty("red")?ms.transform(e):As.transform(e)}},Ps=/(?:#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\))/gi;var Cs="number",Ts="color",Rs=/var[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\([\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*--(?:[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*|[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*,(?:[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:(?![\t-\r \(\)\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uD800-\uDFFF\uFEFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\((?:(?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|\((?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])*\))*\))+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)\)|#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\)|-?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)/gi;function Ms(e){var t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,a=t.replace(Rs,(function(e){return Ss.test(e)?(r.color.push(o),i.push(Ts),n.push(Ss.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(Cs),n.push(parseFloat(e))),++o,"${}"})).split("${}");return{values:n,split:a,indexes:r,types:i}}function Ds(e){return Ms(e).values}function js(e){var t=Ms(e),n=t.split,r=t.types,i=n.length;return function(e){for(var t="",o=0;o<i;o++)if(t+=n[o],void 0!==e[o]){var a=r[o];t+=a===Cs?cs(e[o]):a===Ts?Ss.transform(e[o]):e[o]}return t}}var Is=function(e){return"number"==typeof e?0:e};var Os={test:function(e){var t,n;return isNaN(e)&&"string"==typeof e&&((null===(t=e.match(fs))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(Ps))||void 0===n?void 0:n.length)||0)>0},parse:Ds,createTransformer:js,getAnimatableNone:function(e){var t=Ds(e);return js(e)(t.map(Is))}},Ls=new Set(["brightness","contrast","saturate","opacity"]);function Vs(e){var t=ke(e.slice(0,-1).split("("),2),n=t[0],r=t[1];if("drop-shadow"===n)return e;var i=ke(r.match(fs)||[],1)[0];if(!i)return e;var o=r.replace(i,""),a=Ls.has(n)?1:0;return i!==r&&(a*=100),n+"("+a+o+")"}var Ns=/\b([\x2Da-z]*)\((?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\)/g,Bs=be(be({},Os),{},{getAnimatableNone:function(e){var t=e.match(Ns);return t?t.map(Vs).join(" "):e}}),zs={borderWidth:ws,borderTopWidth:ws,borderRightWidth:ws,borderBottomWidth:ws,borderLeftWidth:ws,borderRadius:ws,radius:ws,borderTopLeftRadius:ws,borderTopRightRadius:ws,borderBottomRightRadius:ws,borderBottomLeftRadius:ws,width:ws,maxWidth:ws,height:ws,maxHeight:ws,top:ws,right:ws,bottom:ws,left:ws,padding:ws,paddingTop:ws,paddingRight:ws,paddingBottom:ws,paddingLeft:ws,margin:ws,marginTop:ws,marginRight:ws,marginBottom:ws,marginLeft:ws,backgroundPositionX:ws,backgroundPositionY:ws},Ws={rotate:bs,rotateX:bs,rotateY:bs,rotateZ:bs,scale:ls,scaleX:ls,scaleY:ls,scaleZ:ls,skew:bs,skewX:bs,skewY:bs,distance:ws,translateX:ws,translateY:ws,translateZ:ws,x:ws,y:ws,z:ws,perspective:ws,transformPerspective:ws,opacity:ss,originX:Es,originY:Es,originZ:ws},Us=be(be({},us),{},{transform:Math.round}),qs=be(be(be({},zs),Ws),{},{zIndex:Us,size:ws,fillOpacity:ss,strokeOpacity:ss,numOctaves:Us}),Hs=be(be({},qs),{},{color:Ss,backgroundColor:Ss,outlineColor:Ss,fill:Ss,stroke:Ss,borderColor:Ss,borderTopColor:Ss,borderRightColor:Ss,borderBottomColor:Ss,borderLeftColor:Ss,filter:Bs,WebkitFilter:Bs}),Ks=function(e){return Hs[e]};function Xs(e,t){var n=Ks(e);return n!==Bs&&(n=Os),n.getAnimatableNone?n.getAnimatableNone(t):void 0}var _s=new Set(["auto","none","0"]);var Ys=function(e){return 180*e/Math.PI},Gs=function(e){var t=Ys(Math.atan2(e[1],e[0]));return Zs(t)},$s={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:function(e){return(Math.abs(e[0])+Math.abs(e[3]))/2},rotate:Gs,rotateZ:Gs,skewX:function(e){return Ys(Math.atan(e[1]))},skewY:function(e){return Ys(Math.atan(e[2]))},skew:function(e){return(Math.abs(e[1])+Math.abs(e[2]))/2}},Zs=function(e){return(e%=360)<0&&(e+=360),e},Js=function(e){return Math.sqrt(e[0]*e[0]+e[1]*e[1])},Qs=function(e){return Math.sqrt(e[4]*e[4]+e[5]*e[5])},el={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Js,scaleY:Qs,scale:function(e){return(Js(e)+Qs(e))/2},rotateX:function(e){return Zs(Ys(Math.atan2(e[6],e[5])))},rotateY:function(e){return Zs(Ys(Math.atan2(-e[2],e[0])))},rotateZ:Gs,rotate:Gs,skewX:function(e){return Ys(Math.atan(e[4]))},skewY:function(e){return Ys(Math.atan(e[1]))},skew:function(e){return(Math.abs(e[1])+Math.abs(e[4]))/2}};function tl(e){return e.includes("scale")?1:0}function nl(e,t){if(!e||"none"===e)return tl(t);var n,r,i=e.match(/^matrix3d\(([\t-\r ,-\.0-9e\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+)\)$/);if(i)n=el,r=i;else{var o=e.match(/^matrix\(([\t-\r ,-\.0-9e\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+)\)$/);n=$s,r=o}if(!r)return tl(t);var a=n[t],u=r[1].split(",").map(rl);return"function"==typeof a?a(u):u[a]}function rl(e){return parseFloat(e.trim())}var il=function(e){return e===us||e===ws},ol=new Set(["x","y","z"]),al=xu.filter((function(e){return!ol.has(e)}));var ul={width:function(e,t){var n=e.x,r=t.paddingLeft,i=void 0===r?"0":r,o=t.paddingRight,a=void 0===o?"0":o;return n.max-n.min-parseFloat(i)-parseFloat(a)},height:function(e,t){var n=e.y,r=t.paddingTop,i=void 0===r?"0":r,o=t.paddingBottom,a=void 0===o?"0":o;return n.max-n.min-parseFloat(i)-parseFloat(a)},top:function(e,t){var n=t.top;return parseFloat(n)},left:function(e,t){var n=t.left;return parseFloat(n)},bottom:function(e,t){var n=e.y,r=t.top;return parseFloat(r)+(n.max-n.min)},right:function(e,t){var n=e.x,r=t.left;return parseFloat(r)+(n.max-n.min)},x:function(e,t){return nl(t.transform,"x")},y:function(e,t){return nl(t.transform,"y")}};ul.translateX=ul.x,ul.translateY=ul.y;var sl=new Set,ll=!1,cl=!1;function fl(){if(cl){var e=Array.from(sl).filter((function(e){return e.needsMeasurement})),t=new Set(e.map((function(e){return e.element}))),n=new Map;t.forEach((function(e){var t=function(e){var t=[];return al.forEach((function(n){var r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))})),t}(e);t.length&&(n.set(e,t),e.render())})),e.forEach((function(e){return e.measureInitialState()})),t.forEach((function(e){e.render();var t=n.get(e);t&&t.forEach((function(t){var n,r=ke(t,2),i=r[0],o=r[1];null===(n=e.getValue(i))||void 0===n||n.set(o)}))})),e.forEach((function(e){return e.measureEndState()})),e.forEach((function(e){void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)}))}cl=!1,ll=!1,sl.forEach((function(e){return e.complete()})),sl.clear()}function dl(){sl.forEach((function(e){e.readKeyframes(),e.needsMeasurement&&(cl=!0)}))}var vl=function(){return fe((function e(t,n,r,i,o){var a=arguments.length>5&&void 0!==arguments[5]&&arguments[5];le(this,e),this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=me(t),this.onComplete=n,this.name=r,this.motionValue=i,this.element=o,this.isAsync=a}),[{key:"scheduleResolve",value:function(){this.isScheduled=!0,this.isAsync?(sl.add(this),ll||(ll=!0,mu.read(dl),mu.resolveKeyframes(fl))):(this.readKeyframes(),this.complete())}},{key:"readKeyframes",value:function(){for(var e=this.unresolvedKeyframes,t=this.name,n=this.element,r=this.motionValue,i=0;i<e.length;i++)if(null===e[i])if(0===i){var o=null==r?void 0:r.get(),a=e[e.length-1];if(void 0!==o)e[0]=o;else if(n&&t){var u=n.readValue(t,a);null!=u&&(e[0]=u)}void 0===e[0]&&(e[0]=a),r&&void 0===o&&r.set(e[0])}else e[i]=e[i-1]}},{key:"setFinalKeyframe",value:function(){}},{key:"measureInitialState",value:function(){}},{key:"renderEndStyles",value:function(){}},{key:"measureEndState",value:function(){}},{key:"complete",value:function(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),sl.delete(this)}},{key:"cancel",value:function(){this.isComplete||(this.isScheduled=!1,sl.delete(this))}},{key:"resume",value:function(){this.isComplete||this.scheduleResolve()}}])}(),hl=function(e){return/^-?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)$/.test(e)},pl=function(e){return function(t){return"string"==typeof t&&t.startsWith(e)}},ml=pl("--"),yl=pl("var(--"),gl=function(e){return!!yl(e)&&bl.test(e.split("/*")[0].trim())},bl=/var\(--(?:[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*|[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*,(?:[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:(?![\t-\r \(\)\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uD800-\uDFFF\uFEFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\((?:(?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|\((?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])*\))*\))+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)\)$/i,xl=/^var\(--(?:([\x2D0-9A-Z_a-z]+)|([\x2D0-9A-Z_a-z]+), ?([ #%\(\),-\.0-9A-Za-z]+))\)/;function wl(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=function(e){var t=xl.exec(e);if(!t)return[,];var n=ke(t,4),r=n[1],i=n[2],o=n[3];return["--".concat(null!=r?r:i),o]}(e),i=ke(r,2),o=i[0],a=i[1];if(o){var u=window.getComputedStyle(t).getPropertyValue(o);if(u){var s=u.trim();return hl(s)?parseFloat(s):s}return gl(a)?wl(a,t,n+1):a}}var kl=function(e){return function(t){return t.test(e)}},Fl=[us,ws,xs,bs,Fs,ks,{test:function(e){return"auto"===e},parse:function(e){return e}}],El=function(e){return Fl.find(kl(e))},Al=function(e){function t(e,n,r,i,o){return le(this,t),ie(this,t,[e,n,r,i,o,!0])}return ue(t,e),fe(t,[{key:"readKeyframes",value:function(){var e=this.unresolvedKeyframes,n=this.element,r=this.name;if(n&&n.current){te(t,"readKeyframes",this,3)([]);for(var i=0;i<e.length;i++){var o=e[i];if("string"==typeof o&&(o=o.trim(),gl(o))){var a=wl(o,n.current);void 0!==a&&(e[i]=a),i===e.length-1&&(this.finalKeyframe=o)}}if(this.resolveNoneKeyframes(),ku.has(r)&&2===e.length){var u=ke(e,2),s=u[0],l=u[1],c=El(s),f=El(l);if(c!==f)if(il(c)&&il(f))for(var d=0;d<e.length;d++){var v=e[d];"string"==typeof v&&(e[d]=parseFloat(v))}else this.needsMeasurement=!0}}}},{key:"resolveNoneKeyframes",value:function(){for(var e=this.unresolvedKeyframes,t=this.name,n=[],r=0;r<e.length;r++)as(e[r])&&n.push(r);n.length&&function(e,t,n){for(var r,i=0;i<e.length&&!r;){var o=e[i];"string"==typeof o&&!_s.has(o)&&Ms(o).values.length&&(r=e[i]),i++}if(r&&n){var a,u=re(t);try{for(u.s();!(a=u.n()).done;)e[a.value]=Xs(n,r)}catch(s){u.e(s)}finally{u.f()}}}(e,n,t)}},{key:"measureInitialState",value:function(){var e=this.element,t=this.unresolvedKeyframes,n=this.name;if(e&&e.current){"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ul[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;var r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}}},{key:"measureEndState",value:function(){var e,t=this.element,n=this.name,r=this.unresolvedKeyframes;if(t&&t.current){var i=t.getValue(n);i&&i.jump(this.measuredOrigin,!1);var o=r.length-1,a=r[o];r[o]=ul[n](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),!(null===(e=this.removedTransforms)||void 0===e)&&e.length&&this.removedTransforms.forEach((function(e){var n=ke(e,2),r=n[0],i=n[1];t.getValue(r).set(i)})),this.resolveNoneKeyframes()}}}])}(vl),Sl=function(e,t){return"zIndex"!==t&&!("number"!=typeof e&&!Array.isArray(e)&&("string"!=typeof e||!Os.test(e)&&"0"!==e||e.startsWith("url(")))};var Pl=function(e){return null!==e};function Cl(e,t,n){var r=t.repeat,i=t.repeatType,o=void 0===i?"loop":i,a=e.filter(Pl),u=r&&"loop"!==o&&r%2==1?0:a.length-1;return u&&void 0!==n?n:a[u]}var Tl=function(){return fe((function e(t){var n=t.autoplay,r=void 0===n||n,i=t.delay,o=void 0===i?0:i,a=t.type,u=void 0===a?"keyframes":a,s=t.repeat,l=void 0===s?0:s,c=t.repeatDelay,f=void 0===c?0:c,d=t.repeatType,v=void 0===d?"loop":d,h=ye(t,A);le(this,e),this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Su.now(),this.options=be({autoplay:r,delay:o,type:u,repeat:l,repeatDelay:f,repeatType:v},h),this.updateFinishedPromise()}),[{key:"calcStartTime",value:function(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}},{key:"resolved",get:function(){return!this._resolved&&!this.hasAttemptedResolve&&(dl(),fl()),this._resolved}},{key:"onKeyframesResolved",value:function(e,t){this.resolvedAt=Su.now(),this.hasAttemptedResolve=!0;var n=this.options,r=n.name,i=n.type,o=n.velocity,a=n.delay,u=n.onComplete,s=n.onUpdate;if(!n.isGenerator&&!function(e,t,n,r){var i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;var o=e[e.length-1],a=Sl(i,t),u=Sl(o,t);return!(!a||!u)&&(function(e){var t=e[0];if(1===e.length)return!0;for(var n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||qa(n))&&r)}(e,r,i,o)){if(!a)return s&&s(Cl(e,this.options,t)),u&&u(),void this.resolveFinishedPromise();this.options.duration=0}var l=this.initPlayback(e,t);!1!==l&&(this._resolved=be({keyframes:e,finalKeyframe:t},l),this.onPostResolved())}},{key:"onPostResolved",value:function(){}},{key:"then",value:function(e,t){return this.currentFinishedPromise.then(e,t)}},{key:"flatten",value:function(){this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear")}},{key:"updateFinishedPromise",value:function(){var e=this;this.currentFinishedPromise=new Promise((function(t){e.resolveFinishedPromise=t}))}}])}();function Rl(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Ml(e,t){return function(n){return n>0?t:e}}var Dl=function(e,t,n){var r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},jl=[ys,ms,As];function Il(e){var t=function(e){return jl.find((function(t){return t.test(e)}))}(e);if(!t)return!1;var n=t.parse(e);return t===As&&(n=function(e){var t=e.hue,n=e.saturation,r=e.lightness,i=e.alpha;t/=360,r/=100;var o=0,a=0,u=0;if(n/=100){var s=r<.5?r*(1+n):r+n-r*n,l=2*r-s;o=Rl(l,s,t+1/3),a=Rl(l,s,t),u=Rl(l,s,t-1/3)}else o=a=u=r;return{red:Math.round(255*o),green:Math.round(255*a),blue:Math.round(255*u),alpha:i}}(n)),n}var Ol=function(e,t){var n=Il(e),r=Il(t);if(!n||!r)return Ml(e,t);var i=be({},n);return function(e){return i.red=Dl(n.red,r.red,e),i.green=Dl(n.green,r.green,e),i.blue=Dl(n.blue,r.blue,e),i.alpha=Xa(n.alpha,r.alpha,e),ms.transform(i)}},Ll=function(e,t){return function(n){return t(e(n))}},Vl=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(Ll)},Nl=new Set(["none","hidden"]);function Bl(e,t){return function(n){return Xa(e,t,n)}}function zl(e){return"number"==typeof e?Bl:"string"==typeof e?gl(e)?Ml:Ss.test(e)?Ol:ql:Array.isArray(e)?Wl:"object"==Ae(e)?Ss.test(e)?Ol:Ul:Ml}function Wl(e,t){var n=me(e),r=n.length,i=e.map((function(e,n){return zl(e)(e,t[n])}));return function(e){for(var t=0;t<r;t++)n[t]=i[t](e);return n}}function Ul(e,t){var n=be(be({},e),t),r={};for(var i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=zl(e[i])(e[i],t[i]));return function(e){for(var t in r)n[t]=r[t](e);return n}}var ql=function(e,t){var n=Os.createTransformer(t),r=Ms(e),i=Ms(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?Nl.has(e)&&!i.values.length||Nl.has(t)&&!r.values.length?function(e,t){return Nl.has(e)?function(n){return n<=0?e:t}:function(n){return n>=1?t:e}}(e,t):Vl(Wl(function(e,t){for(var n,r=[],i={color:0,var:0,number:0},o=0;o<t.values.length;o++){var a=t.types[o],u=e.indexes[a][i[a]],s=null!==(n=e.values[u])&&void 0!==n?n:0;r[o]=s,i[a]++}return r}(r,i),i.values),n):Ml(e,t)};function Hl(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?Xa(e,t,n):zl(e)(e,t)}function Kl(e){var t=e.keyframes,n=e.velocity,r=void 0===n?0:n,i=e.power,o=void 0===i?.8:i,a=e.timeConstant,u=void 0===a?325:a,s=e.bounceDamping,l=void 0===s?10:s,c=e.bounceStiffness,f=void 0===c?500:c,d=e.modifyTarget,v=e.min,h=e.max,p=e.restDelta,m=void 0===p?.5:p,y=e.restSpeed,g=t[0],b={done:!1,value:g},x=function(e){return void 0===v?h:void 0===h||Math.abs(v-e)<Math.abs(h-e)?v:h},w=o*r,k=g+w,F=void 0===d?k:d(k);F!==k&&(w=F-g);var E,A,S=function(e){return-w*Math.exp(-e/u)},P=function(e){return F+S(e)},C=function(e){var t=S(e),n=P(e);b.done=Math.abs(t)<=m,b.value=b.done?F:n},T=function(e){(function(e){return void 0!==v&&e<v||void 0!==h&&e>h})(b.value)&&(E=e,A=Ua({keyframes:[b.value,x(b.value)],velocity:xa(P,e,b.value),damping:l,stiffness:f,restDelta:m,restSpeed:y}))};return T(0),{calculatedDuration:null,next:function(e){var t=!1;return!A&&void 0===E&&(t=!0,C(e),T(e)),void 0!==E&&e>=E?A.next(e-E):(!t&&C(e),b)}}}var Xl=Gu(.42,0,1,1),_l=Gu(0,0,.58,1),Yl=Gu(.42,0,.58,1),Gl={linear:Ia,easeIn:Xl,easeInOut:Yl,easeOut:_l,circIn:ns,circInOut:is,circOut:rs,backIn:Qu,backInOut:es,backOut:Ju,anticipate:ts},$l=function(e){if(zu(e)){Oa(4===e.length);var t=ke(e,4);return Gu(t[0],t[1],t[2],t[3])}return"string"==typeof e?Gl[e]:e};function Zl(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.clamp,i=void 0===r||r,o=n.ease,a=n.mixer,u=e.length;if(Oa(u===t.length),1===u)return function(){return t[0]};if(2===u&&t[0]===t[1])return function(){return t[1]};var s=e[0]===e[1];e[0]>e[u-1]&&(e=me(e).reverse(),t=me(t).reverse());var l=function(e,t,n){for(var r=[],i=n||Hl,o=e.length-1,a=0;a<o;a++){var u=i(e[a],e[a+1]);if(t){var s=Array.isArray(t)?t[a]||Ia:t;u=Vl(s,u)}r.push(u)}return r}(t,o,a),c=l.length,f=function(n){if(s&&n<e[0])return t[0];var r=0;if(c>1)for(;r<e.length-2&&!(n<e[r+1]);r++);var i=ma(e[r],e[r+1],n);return l[r](i)};return i?function(t){return f(ga(e[0],e[u-1],t))}:f}function Jl(e,t){return e.map((function(){return t||Yl})).splice(0,e.length-1)}function Ql(e){var t=e.duration,n=void 0===t?300:t,r=e.keyframes,i=e.times,o=e.ease,a=void 0===o?"easeInOut":o,u=Ha(a)?a.map($l):$l(a),s={done:!1,value:r[0]},l=function(e,t){return e.map((function(e){return e*t}))}(i&&i.length===r.length?i:Ya(r),n),c=Zl(l,r,{ease:Array.isArray(u)?u:Jl(r,u)});return{calculatedDuration:n,next:function(e){return s.value=c(e),s.done=e>=n,s}}}var ec=function(e){var t=function(t){var n=t.timestamp;return e(n)};return{start:function(){return mu.update(t,!0)},stop:function(){return yu(t)},now:function(){return gu.isProcessing?gu.timestamp:Su.now()}}},tc={decay:Kl,inertia:Kl,tween:Ql,keyframes:Ql,spring:Ua},nc=function(e){return e/100},rc=function(e){function t(e){var n;le(this,t),(n=ie(this,t,[e])).holdTime=null,n.cancelTime=null,n.currentTime=0,n.playbackSpeed=1,n.pendingPlayState="running",n.startTime=null,n.state="idle",n.stop=function(){if(n.resolver.cancel(),n.isStopped=!0,"idle"!==n.state){n.teardown();var e=n.options.onStop;e&&e()}};var r=n.options,i=r.name,o=r.motionValue,a=r.element,u=r.keyframes,s=(null==a?void 0:a.KeyframeResolver)||vl;return n.resolver=new s(u,(function(e,t){return n.onKeyframesResolved(e,t)}),i,o,a),n.resolver.scheduleResolve(),n}return ue(t,e),fe(t,[{key:"flatten",value:function(){te(t,"flatten",this,3)([]),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}},{key:"initPlayback",value:function(e){var t,n,r=this.options,i=r.type,o=void 0===i?"keyframes":i,a=r.repeat,u=void 0===a?0:a,s=r.repeatDelay,l=void 0===s?0:s,c=r.repeatType,f=r.velocity,d=void 0===f?0:f,v=qa(o)?o:tc[o]||Ql;v!==Ql&&"number"!=typeof e[0]&&(t=Vl(nc,Hl(e[0],e[1])),e=[0,100]);var h=v(be(be({},this.options),{},{keyframes:e}));"mirror"===c&&(n=v(be(be({},this.options),{},{keyframes:me(e).reverse(),velocity:-d}))),null===h.calculatedDuration&&(h.calculatedDuration=pa(h));var p=h.calculatedDuration,m=p+l;return{generator:h,mirroredGenerator:n,mapPercentToKeyframes:t,calculatedDuration:p,resolvedDuration:m,totalDuration:m*(u+1)-l}}},{key:"onPostResolved",value:function(){var e=this.options.autoplay,t=void 0===e||e;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}},{key:"tick",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.resolved;if(!n){var r=this.options.keyframes;return{done:!0,value:r[r.length-1]}}var i=n.finalKeyframe,o=n.generator,a=n.mirroredGenerator,u=n.mapPercentToKeyframes,s=n.keyframes,l=n.calculatedDuration,c=n.totalDuration,f=n.resolvedDuration;if(null===this.startTime)return o.next(0);var d=this.options,v=d.delay,h=d.repeat,p=d.repeatType,m=d.repeatDelay,y=d.onUpdate;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-c/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;var g=this.currentTime-v*(this.speed>=0?1:-1),b=this.speed>=0?g<0:g>c;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=c);var x=this.currentTime,w=o;if(h){var k=Math.min(this.currentTime,c)/f,F=Math.floor(k),E=k%1;!E&&k>=1&&(E=1),1===E&&F--,!!((F=Math.min(F,h+1))%2)&&("reverse"===p?(E=1-E,m&&(E-=m/f)):"mirror"===p&&(w=a)),x=ga(0,1,E)*f}var A=b?{done:!1,value:s[0]}:w.next(x);u&&(A.value=u(A.value));var S=A.done;!b&&null!==l&&(S=this.speed>=0?this.currentTime>=c:this.currentTime<=0);var P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&S);return P&&void 0!==i&&(A.value=Cl(s,this.options,i)),y&&y(A.value),P&&this.finish(),A}},{key:"duration",get:function(){var e=this.resolved;return e?ha(e.calculatedDuration):0}},{key:"time",get:function(){return ha(this.currentTime)},set:function(e){e=va(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}},{key:"speed",get:function(){return this.playbackSpeed},set:function(e){var t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=ha(this.currentTime))}},{key:"play",value:function(){var e=this;if(this.resolver.isScheduled||this.resolver.resume(),this._resolved){if(!this.isStopped){var t=this.options,n=t.driver,r=void 0===n?ec:n,i=t.onPlay,o=t.startTime;this.driver||(this.driver=r((function(t){return e.tick(t)}))),i&&i();var a=this.driver.now();null!==this.holdTime?this.startTime=a-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=a):this.startTime=null!=o?o:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}}else this.pendingPlayState="running"}},{key:"pause",value:function(){var e;this._resolved?(this.state="paused",this.holdTime=null!==(e=this.currentTime)&&void 0!==e?e:0):this.pendingPlayState="paused"}},{key:"complete",value:function(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}},{key:"finish",value:function(){this.teardown(),this.state="finished";var e=this.options.onComplete;e&&e()}},{key:"cancel",value:function(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}},{key:"teardown",value:function(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}},{key:"stopDriver",value:function(){this.driver&&(this.driver.stop(),this.driver=void 0)}},{key:"sample",value:function(e){return this.startTime=0,this.tick(e,!0)}}])}(Tl),ic=new Set(["opacity","clipPath","filter","transform"]);function oc(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=r.delay,o=void 0===i?0:i,a=r.duration,u=void 0===a?300:a,s=r.repeat,l=void 0===s?0:s,c=r.repeatType,f=void 0===c?"loop":c,d=r.ease,v=void 0===d?"easeInOut":d,h=r.times,p=xe({},t,n);h&&(p.offset=h);var m=_u(v,u);return Array.isArray(m)&&(p.easing=m),e.animate(p,{delay:o,duration:u,easing:Array.isArray(m)?"linear":m,fill:"both",iterations:l+1,direction:"reverse"===f?"alternate":"normal"})}var ac=la((function(){return Object.hasOwnProperty.call(Element.prototype,"animate")}));var uc={anticipate:ts,backInOut:es,circInOut:is};var sc=function(e){function t(e){var n;le(this,t);var r=(n=ie(this,t,[e])).options,i=r.name,o=r.motionValue,a=r.element,u=r.keyframes;return n.resolver=new Al(u,(function(e,t){return n.onKeyframesResolved(e,t)}),i,o,a),n.resolver.scheduleResolve(),n}return ue(t,e),fe(t,[{key:"initPlayback",value:function(e,t){var n=this,r=this.options,i=r.duration,o=void 0===i?300:i,a=r.times,u=r.ease,s=r.type,l=r.motionValue,c=r.name,f=r.startTime;if(!l.owner||!l.owner.current)return!1;if("string"==typeof u&&qu()&&function(e){return e in uc}(u)&&(u=uc[u]),function(e){return qa(e.type)||"spring"===e.type||!Hu(e.ease)}(this.options)){var d=this.options,v=(d.onComplete,d.onUpdate,d.motionValue,d.element,ye(d,S)),h=function(e,t){for(var n=new rc(be(be({},t),{},{keyframes:e,repeat:0,delay:0,isGenerator:!0})),r={done:!1,value:e[0]},i=[],o=0;!r.done&&o<2e4;)r=n.sample(o),i.push(r.value),o+=10;return{times:void 0,keyframes:i,duration:o-10,ease:"linear"}}(e,v);1===(e=h.keyframes).length&&(e[1]=e[0]),o=h.duration,a=h.times,u=h.ease,s="keyframes"}var p=oc(l.owner.current,c,e,be(be({},this.options),{},{duration:o,times:a,ease:u}));return p.startTime=null!=f?f:this.calcStartTime(),this.pendingTimeline?(Bu(p,this.pendingTimeline),this.pendingTimeline=void 0):p.onfinish=function(){var r=n.options.onComplete;l.set(Cl(e,n.options,t)),r&&r(),n.cancel(),n.resolveFinishedPromise()},{animation:p,duration:o,times:a,type:s,ease:u,keyframes:e}}},{key:"duration",get:function(){var e=this.resolved;if(!e)return 0;var t=e.duration;return ha(t)}},{key:"time",get:function(){var e=this.resolved;if(!e)return 0;var t=e.animation;return ha(t.currentTime||0)},set:function(e){var t=this.resolved;t&&(t.animation.currentTime=va(e))}},{key:"speed",get:function(){var e=this.resolved;return e?e.animation.playbackRate:1},set:function(e){var t=this.resolved;t&&(t.animation.playbackRate=e)}},{key:"state",get:function(){var e=this.resolved;return e?e.animation.playState:"idle"}},{key:"startTime",get:function(){var e=this.resolved;return e?e.animation.startTime:null}},{key:"attachTimeline",value:function(e){if(this._resolved){var t=this.resolved;if(!t)return Ia;Bu(t.animation,e)}else this.pendingTimeline=e;return Ia}},{key:"play",value:function(){if(!this.isStopped){var e=this.resolved;if(e){var t=e.animation;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}}}},{key:"pause",value:function(){var e=this.resolved;e&&e.animation.pause()}},{key:"stop",value:function(){if(this.resolver.cancel(),this.isStopped=!0,"idle"!==this.state){this.resolveFinishedPromise(),this.updateFinishedPromise();var e=this.resolved;if(e){var t=e.animation,n=e.keyframes,r=e.duration,i=e.type,o=e.ease,a=e.times;if("idle"!==t.playState&&"finished"!==t.playState){if(this.time){var u=this.options,s=u.motionValue,l=(u.onUpdate,u.onComplete,u.element,ye(u,P)),c=new rc(be(be({},l),{},{keyframes:n,duration:r,type:i,ease:o,times:a,isGenerator:!0})),f=va(this.time);s.setWithVelocity(c.sample(f-10).value,c.sample(f).value,10)}var d=this.options.onStop;d&&d(),this.cancel()}}}}},{key:"complete",value:function(){var e=this.resolved;e&&e.animation.finish()}},{key:"cancel",value:function(){var e=this.resolved;e&&e.animation.cancel()}}],[{key:"supports",value:function(e){var t=e.motionValue,n=e.name,r=e.repeatDelay,i=e.repeatType,o=e.damping,a=e.type;if(!(t&&t.owner&&t.owner.current instanceof HTMLElement))return!1;var u=t.owner.getProps(),s=u.onUpdate,l=u.transformTemplate;return ac()&&n&&ic.has(n)&&!s&&!l&&!r&&"mirror"!==i&&0!==o&&"inertia"!==a}}])}(Tl),lc={type:"spring",stiffness:500,damping:25,restSpeed:10},cc={type:"keyframes",duration:.8},fc={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},dc=function(e,t){var n=t.keyframes;return n.length>2?cc:wu.has(e)?e.startsWith("scale")?function(e){return{type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}}(n[1]):lc:fc};var vc=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0;return function(a){var u=fu(r,e)||{},s=u.delay||r.delay||0,l=r.elapsed,c=void 0===l?0:l;c-=va(s);var f=be(be({keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity()},u),{},{delay:-c,onUpdate:function(e){t.set(e),u.onUpdate&&u.onUpdate(e)},onComplete:function(){a(),u.onComplete&&u.onComplete()},name:e,motionValue:t,element:o?void 0:i});(function(e){e.when,e.delay,e.delayChildren,e.staggerChildren,e.staggerDirection,e.repeat,e.repeatType,e.repeatDelay,e.from,e.elapsed;var t=ye(e,C);return!!Object.keys(t).length})(u)||(f=be(be({},f),dc(e,f))),f.duration&&(f.duration=va(f.duration)),f.repeatDelay&&(f.repeatDelay=va(f.repeatDelay)),void 0!==f.from&&(f.keyframes[0]=f.from);var d=!1;if((!1===f.type||0===f.duration&&!f.repeatDelay)&&(f.duration=0,0===f.delay&&(d=!0)),f.allowFlatten=!u.type&&!u.ease,d&&!o&&void 0!==t.get()){var v=Cl(f.keyframes,u);if(void 0!==v)return mu.update((function(){f.onUpdate(v),f.onComplete()})),new da([])}return!o&&sc.supports(f)?new sc(f):new rc(f)}};function hc(e,t){var n=e.protectedKeys,r=e.needsAnimating,i=n.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,i}function pc(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=r.delay,o=void 0===i?0:i,a=r.transitionOverride,u=r.type,s=t.transition,l=void 0===s?e.getDefaultTransition():s,c=t.transitionEnd,f=ye(t,T);a&&(l=a);var d=[],v=u&&e.animationState&&e.animationState.getState()[u];for(var h in f){var p=e.getValue(h,null!==(n=e.latestValues[h])&&void 0!==n?n:null),m=f[h];if(!(void 0===m||v&&hc(v,h))){var y=be({delay:o},fu(l||{},h)),g=!1;if(window.MotionHandoffAnimation){var b=Nu(e);if(b){var x=window.MotionHandoffAnimation(b,h,mu);null!==x&&(y.startTime=x,g=!0)}}Ou(e,h),p.start(vc(h,p,m,e.shouldReduceMotion&&ku.has(h)?{type:!1}:y,e,g));var w=p.animation;w&&d.push(w)}}return c&&Promise.all(d).then((function(){mu.update((function(){c&&function(e,t){var n=ju(e,t)||{},r=n.transitionEnd,i=void 0===r?{}:r,o=(n.transition,ye(n,E));for(var a in o=be(be({},o),i))Iu(e,a,Ru(o[a]))}(e,c)}))})),d}function mc(e){return e instanceof SVGElement&&"svg"!==e.tagName}var yc=function(){return{x:{min:0,max:0},y:{min:0,max:0}}},gc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},bc={},xc=function(e){bc[e]={isEnabled:function(t){return gc[e].some((function(e){return!!t[e]}))}}};for(var wc in gc)xc(wc);var kc=[].concat(Fl,[Ss,Os]);function Fc(e){return null!==e&&"object"==Ae(e)&&"function"==typeof e.start}function Ec(e){return"string"==typeof e||Array.isArray(e)}var Ac=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Sc=["initial"].concat(Ac);function Pc(e){return Fc(e.animate)||Sc.some((function(t){return Ec(e[t])}))}function Cc(e){return!(!Pc(e)&&!e.variants)}var Tc=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Rc=function(){return fe((function e(t){var n=this,r=t.parent,i=t.props,o=t.presenceContext,a=t.reducedMotionConfig,u=t.blockInitialAnimation,s=t.visualState,l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};le(this,e),this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=vl,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=function(){return n.notify("Update",n.latestValues)},this.render=function(){n.current&&(n.triggerBuild(),n.renderInstance(n.current,n.renderState,n.props.style,n.projection))},this.renderScheduledAt=0,this.scheduleRender=function(){var e=Su.now();n.renderScheduledAt<e&&(n.renderScheduledAt=e,mu.render(n.render,!1,!0))};var c=s.latestValues,f=s.renderState,d=s.onUpdate;this.onUpdate=d,this.latestValues=c,this.baseTarget=be({},c),this.initialValues=i.initial?be({},c):{},this.renderState=f,this.parent=r,this.props=i,this.presenceContext=o,this.depth=r?r.depth+1:0,this.reducedMotionConfig=a,this.options=l,this.blockInitialAnimation=!!u,this.isControllingVariants=Pc(i),this.isVariantNode=Cc(i),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!(!r||!r.current);var v=this.scrapeMotionValuesFromProps(i,{},this),h=(v.willChange,ye(v,R));for(var p in h){var m=h[p];void 0!==c[p]&&Ga(m)&&m.set(c[p],!1)}}),[{key:"scrapeMotionValuesFromProps",value:function(e,t,n){return{}}},{key:"mount",value:function(e){var t=this;this.current=e,cu.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((function(e,n){return t.bindToMotionValue(n,e)})),ua.current||sa(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||aa.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}},{key:"unmount",value:function(){for(var e in this.projection&&this.projection.unmount(),yu(this.notifyUpdate),yu(this.render),this.valueSubscriptions.forEach((function(e){return e()})),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(var t in this.features){var n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}},{key:"bindToMotionValue",value:function(e,t){var n=this;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();var r=wu.has(e);r&&this.onBindTransform&&this.onBindTransform();var i,o=t.on("change",(function(t){n.latestValues[e]=t,n.props.onUpdate&&mu.preRender(n.notifyUpdate),r&&n.projection&&(n.projection.isTransformDirty=!0)})),a=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,(function(){o(),a(),i&&i(),t.owner&&t.stop()}))}},{key:"sortNodePosition",value:function(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}},{key:"updateFeatures",value:function(){var e="animation";for(e in bc){var t=bc[e];if(t){var n=t.isEnabled,r=t.Feature;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){var i=this.features[e];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}}},{key:"triggerBuild",value:function(){this.build(this.renderState,this.latestValues,this.props)}},{key:"measureViewportBox",value:function(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}},{key:"getStaticValue",value:function(e){return this.latestValues[e]}},{key:"setStaticValue",value:function(e,t){this.latestValues[e]=t}},{key:"update",value:function(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(var n=0;n<Tc.length;n++){var r=Tc[n];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);var i=e["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(e,t,n){for(var r in t){var i=t[r],o=n[r];if(Ga(i))e.addValue(r,i);else if(Ga(o))e.addValue(r,Cu(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){var a=e.getValue(r);!0===a.liveStyle?a.jump(i):a.hasAnimated||a.set(i)}else{var u=e.getStaticValue(r);e.addValue(r,Cu(void 0!==u?u:i,{owner:e}))}}for(var s in n)void 0===t[s]&&e.removeValue(s);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}},{key:"getProps",value:function(){return this.props}},{key:"getVariant",value:function(e){return this.props.variants?this.props.variants[e]:void 0}},{key:"getDefaultTransition",value:function(){return this.props.transition}},{key:"getTransformPagePoint",value:function(){return this.props.transformPagePoint}},{key:"getClosestVariantNode",value:function(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}},{key:"addVariantChild",value:function(e){var t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),function(){return t.variantChildren.delete(e)}}},{key:"addValue",value:function(e,t){var n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}},{key:"removeValue",value:function(e){this.values.delete(e);var t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}},{key:"hasValue",value:function(e){return this.values.has(e)}},{key:"getValue",value:function(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];var n=this.values.get(e);return void 0===n&&void 0!==t&&(n=Cu(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}},{key:"readValue",value:function(e,t){var n,r=void 0===this.latestValues[e]&&this.current?null!==(n=this.getBaseTargetFromProps(this.props,e))&&void 0!==n?n:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(hl(r)||os(r))?r=parseFloat(r):!function(e){return kc.find(kl(e))}(r)&&Os.test(t)&&(r=Xs(e,t)),this.setBaseTarget(e,Ga(r)?r.get():r)),Ga(r)?r.get():r}},{key:"setBaseTarget",value:function(e,t){this.baseTarget[e]=t}},{key:"getBaseTarget",value:function(e){var t,n,r=this.props.initial;if("string"==typeof r||"object"==Ae(r)){var i=Du(this.props,r,null===(t=this.presenceContext)||void 0===t?void 0:t.custom);i&&(n=i[e])}if(r&&void 0!==n)return n;var o=this.getBaseTargetFromProps(this.props,e);return void 0===o||Ga(o)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:o}},{key:"on",value:function(e,t){return this.events[e]||(this.events[e]=new Fu),this.events[e].add(t)}},{key:"notify",value:function(e){for(var t,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];this.events[e]&&(t=this.events[e]).notify.apply(t,r)}}])}(),Mc=function(e){function t(){var e;return le(this,t),(e=ie(this,t,arguments)).KeyframeResolver=Al,e}return ue(t,e),fe(t,[{key:"sortInstanceNodePosition",value:function(e,t){return 2&e.compareDocumentPosition(t)?1:-1}},{key:"getBaseTargetFromProps",value:function(e,t){return e.style?e.style[t]:void 0}},{key:"removeValueFromRenderState",value:function(e,t){var n=t.vars,r=t.style;delete n[e],delete r[e]}},{key:"handleChildMotionValue",value:function(){var e=this;this.childSubscription&&(this.childSubscription(),delete this.childSubscription);var t=this.props.children;Ga(t)&&(this.childSubscription=t.on("change",(function(t){e.current&&(e.current.textContent="".concat(t))})))}}])}(Rc),Dc=function(e,t){return t&&"number"==typeof e?t.transform(e):e},jc={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ic=xu.length;function Oc(e,t,n){var r=e.style,i=e.vars,o=e.transformOrigin,a=!1,u=!1;for(var s in t){var l=t[s];if(wu.has(s))a=!0;else if(ml(s))i[s]=l;else{var c=Dc(l,qs[s]);s.startsWith("origin")?(u=!0,o[s]=c):r[s]=c}}if(t.transform||(a||n?r.transform=function(e,t,n){for(var r="",i=!0,o=0;o<Ic;o++){var a=xu[o],u=e[a];if(void 0!==u){var s=!0;if(!(s="number"==typeof u?u===(a.startsWith("scale")?1:0):0===parseFloat(u))||n){var l=Dc(u,qs[a]);s||(i=!1,r+="".concat(jc[a]||a,"(").concat(l,") ")),n&&(t[a]=l)}}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),u){var f=o.originX,d=void 0===f?"50%":f,v=o.originY,h=void 0===v?"50%":v,p=o.originZ,m=void 0===p?0:p;r.transformOrigin="".concat(d," ").concat(h," ").concat(m)}}var Lc={offset:"stroke-dashoffset",array:"stroke-dasharray"},Vc={offset:"strokeDashoffset",array:"strokeDasharray"};function Nc(e,t,n){return"string"==typeof e?e:ws.transform(t+n*e)}function Bc(e,t,n,r){var i=t.attrX,o=t.attrY,a=t.attrScale,u=t.originX,s=t.originY,l=t.pathLength,c=t.pathSpacing,f=void 0===c?1:c,d=t.pathOffset,v=void 0===d?0:d;if(Oc(e,ye(t,M),r),n)e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);else{e.attrs=e.style,e.style={};var h=e.attrs,p=e.style,m=e.dimensions;h.transform&&(m&&(p.transform=h.transform),delete h.transform),m&&(void 0!==u||void 0!==s||p.transform)&&(p.transformOrigin=function(e,t,n){var r=Nc(t,e.x,e.width),i=Nc(n,e.y,e.height);return"".concat(r," ").concat(i)}(m,void 0!==u?u:.5,void 0!==s?s:.5)),void 0!==i&&(h.x=i),void 0!==o&&(h.y=o),void 0!==a&&(h.scale=a),void 0!==l&&function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];e.pathLength=1;var o=i?Lc:Vc;e[o.offset]=ws.transform(-r);var a=ws.transform(t),u=ws.transform(n);e[o.array]="".concat(a," ").concat(u)}(h,l,f,v,!1)}}var zc=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),Wc=function(e){return"string"==typeof e&&"svg"===e.toLowerCase()};function Uc(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(n){t.dimensions={x:0,y:0,width:0,height:0}}}function qc(e,t,n,r){var i=t.style,o=t.vars;for(var a in Object.assign(e.style,i,r&&r.getProjectionStyles(n)),o)e.style.setProperty(a,o[a])}function Hc(e,t,n,r){for(var i in qc(e,t,void 0,r),t.attrs)e.setAttribute(zc.has(i)?i:Lu(i),t.attrs[i])}var Kc={};function Xc(e,t){var n=t.layout,r=t.layoutId;return wu.has(e)||e.startsWith("origin")||(n||void 0!==r)&&(!!Kc[e]||"opacity"===e)}function _c(e,t,n){var r,i=e.style,o={};for(var a in i)(Ga(i[a])||t.style&&Ga(t.style[a])||Xc(a,e)||void 0!==(null===(r=null==n?void 0:n.getValue(a))||void 0===r?void 0:r.liveStyle))&&(o[a]=i[a]);return o}function Yc(e,t,n){var r=_c(e,t,n);for(var i in e)if(Ga(e[i])||Ga(t[i])){r[-1!==xu.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]}return r}var Gc=function(e){function t(){var e;return le(this,t),(e=ie(this,t,arguments)).type="svg",e.isSVGTag=!1,e.measureInstanceViewportBox=yc,e.updateDimensions=function(){e.current&&!e.renderState.dimensions&&Uc(e.current,e.renderState)},e}return ue(t,e),fe(t,[{key:"getBaseTargetFromProps",value:function(e,t){return e[t]}},{key:"readValueFromInstance",value:function(e,t){if(wu.has(t)){var n=Ks(t);return n&&n.default||0}return t=zc.has(t)?t:Lu(t),e.getAttribute(t)}},{key:"scrapeMotionValuesFromProps",value:function(e,t,n){return Yc(e,t,n)}},{key:"onBindTransform",value:function(){this.current&&!this.renderState.dimensions&&mu.postRender(this.updateDimensions)}},{key:"build",value:function(e,t,n){Bc(e,t,this.isSVGTag,n.transformTemplate)}},{key:"renderInstance",value:function(e,t,n,r){Hc(e,t,0,r)}},{key:"mount",value:function(e){this.isSVGTag=Wc(e.tagName),te(t,"mount",this,3)([e])}}])}(Mc);function $c(e){var t=e.top;return{x:{min:e.left,max:e.right},y:{min:t,max:e.bottom}}}function Zc(e){return void 0===e||1===e}function Jc(e){var t=e.scale,n=e.scaleX,r=e.scaleY;return!Zc(t)||!Zc(n)||!Zc(r)}function Qc(e){return Jc(e)||ef(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function ef(e){return tf(e.x)||tf(e.y)}function tf(e){return e&&"0%"!==e}function nf(e,t,n){return n+t*(e-n)}function rf(e,t,n,r,i){return void 0!==i&&(e=nf(e,i,r)),nf(e,n,r)+t}function of(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;e.min=rf(e.min,t,n,r,i),e.max=rf(e.max,t,n,r,i)}function af(e,t){var n=t.x,r=t.y;of(e.x,n.translate,n.scale,n.originPoint),of(e.y,r.translate,r.scale,r.originPoint)}var uf=.999999999999,sf=1.0000000000001;function lf(e,t){e.min=e.min+t,e.max=e.max+t}function cf(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5;of(e,t,n,Xa(e.min,e.max,i),r)}function ff(e,t){cf(e.x,t.x,t.scaleX,t.scale,t.originX),cf(e.y,t.y,t.scaleY,t.scale,t.originY)}function df(e,t){return $c(function(e,t){if(!t)return e;var n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}var vf=function(e){function t(){var e;return le(this,t),(e=ie(this,t,arguments)).type="html",e.renderInstance=qc,e}return ue(t,e),fe(t,[{key:"readValueFromInstance",value:function(e,t){if(wu.has(t))return function(e,t){var n=getComputedStyle(e).transform;return nl(void 0===n?"none":n,t)}(e,t);var n=function(e){return window.getComputedStyle(e)}(e),r=(ml(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}},{key:"measureInstanceViewportBox",value:function(e,t){return df(e,t.transformPagePoint)}},{key:"build",value:function(e,t,n){Oc(e,t,n.transformTemplate)}},{key:"scrapeMotionValuesFromProps",value:function(e,t,n){return _c(e,t,n)}}])}(Mc);var hf=function(e){function t(){var e;return le(this,t),(e=ie(this,t,arguments)).type="object",e}return ue(t,e),fe(t,[{key:"readValueFromInstance",value:function(e,t){if(function(e,t){return e in t}(t,e)){var n=e[t];if("string"==typeof n||"number"==typeof n)return n}}},{key:"getBaseTargetFromProps",value:function(){}},{key:"removeValueFromRenderState",value:function(e,t){delete t.output[e]}},{key:"measureInstanceViewportBox",value:function(){return{x:{min:0,max:0},y:{min:0,max:0}}}},{key:"build",value:function(e,t){Object.assign(e.output,t)}},{key:"renderInstance",value:function(e,t){var n=t.output;Object.assign(e,n)}},{key:"sortInstanceNodePosition",value:function(){return 0}}])}(Rc);function pf(e){var t={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=mc(e)?new Gc(t):new vf(t);n.mount(e),cu.set(e,n)}function mf(e){var t=new hf({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});t.mount(e),cu.set(e,t)}function yf(e,t,n){var r=Ga(e)?e:Cu(e);return r.start(vc("",r,t,n)),r.animation}function gf(e,t,n,r){var i=[];if(function(e,t){return Ga(e)||"number"==typeof e||"string"==typeof e&&!Za(t)}(e,t))i.push(yf(e,Za(t)&&t.default||t,n&&(n.default||n)));else for(var o=Ja(e,t),a=o.length,u=0;u<a;u++){var s=o[u],l=s instanceof Element?pf:mf;cu.has(s)||l(s);var c=cu.get(s),f=be({},n);"delay"in f&&"function"==typeof f.delay&&(f.delay=f.delay(u,a)),i.push.apply(i,me(pc(c,be(be({},t),{},{transition:f}),{})))}return i}var bf=function(e){return function(t,n,r){var i=[];return i=function(e){return Array.isArray(e)&&e.some(Array.isArray)}(t)?function(e,t,n){var r=[];return iu(e,t,n,{spring:Ua}).forEach((function(e,t){var n=e.keyframes,i=e.transition;r.push.apply(r,me(gf(t,n,i)))})),r}(t,n,e):gf(t,n,r),new da(i)}}(),xf=ce.createContext({});function wf(e){var t=ce.useRef(null);return null===t.current&&(t.current=e()),t.current}var kf=oa?ce.useLayoutEffect:ce.useEffect,Ff=ce.createContext(null),Ef=ce.createContext({transformPagePoint:function(e){return e},isStatic:!1,reducedMotion:"never"}),Af=function(e){function t(){return le(this,t),ie(this,t,arguments)}return ue(t,e),fe(t,[{key:"getSnapshotBeforeUpdate",value:function(e){var t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){var n=t.offsetParent,r=n instanceof HTMLElement&&n.offsetWidth||0,i=this.props.sizeRef.current;i.height=t.offsetHeight||0,i.width=t.offsetWidth||0,i.top=t.offsetTop,i.left=t.offsetLeft,i.right=r-i.width-i.left}return null}},{key:"componentDidUpdate",value:function(){}},{key:"render",value:function(){return this.props.children}}])}(ce.Component);function Sf(e){var t=e.children,n=e.isPresent,r=e.anchorX,i=ce.useId(),o=ce.useRef(null),a=ce.useRef({width:0,height:0,top:0,left:0,right:0}),u=ce.useContext(Ef).nonce;return ce.useInsertionEffect((function(){var e=a.current,t=e.width,s=e.height,l=e.top,c=e.left,f=e.right;if(!n&&o.current&&t&&s){var d="left"===r?"left: ".concat(c):"right: ".concat(f);o.current.dataset.motionPopId=i;var v=document.createElement("style");return u&&(v.nonce=u),document.head.appendChild(v),v.sheet&&v.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(s,"px !important;\n            ").concat(d,"px !important;\n            top: ").concat(l,"px !important;\n          }\n        ")),function(){document.head.removeChild(v)}}}),[n]),ae.jsx(Af,{isPresent:n,childRef:o,sizeRef:a,children:ce.cloneElement(t,{ref:o})})}var Pf=function(e){var t=e.children,n=e.initial,r=e.isPresent,i=e.onExitComplete,o=e.custom,a=e.presenceAffectsLayout,u=e.mode,s=e.anchorX,l=wf(Cf),c=ce.useId(),f=ce.useCallback((function(e){l.set(e,!0);var t,n=re(l.values());try{for(n.s();!(t=n.n()).done;){if(!t.value)return}}catch(r){n.e(r)}finally{n.f()}i&&i()}),[l,i]),d=ce.useMemo((function(){return{id:c,initial:n,isPresent:r,custom:o,onExitComplete:f,register:function(e){return l.set(e,!1),function(){return l.delete(e)}}}}),a?[Math.random(),f]:[r,f]);return ce.useMemo((function(){l.forEach((function(e,t){return l.set(t,!1)}))}),[r]),ce.useEffect((function(){!r&&!l.size&&i&&i()}),[r]),"popLayout"===u&&(t=ae.jsx(Sf,{isPresent:r,anchorX:s,children:t})),ae.jsx(Ff.Provider,{value:d,children:t})};function Cf(){return new Map}function Tf(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=ce.useContext(Ff);if(null===t)return[!0,null];var n=t.isPresent,r=t.onExitComplete,i=t.register,o=ce.useId();ce.useEffect((function(){if(e)return i(o)}),[e]);var a=ce.useCallback((function(){return e&&r&&r(o)}),[o,r,e]);return!n&&r?[!1,a]:[!0]}var Rf=function(e){return e.key||""};function Mf(e){var t=[];return ce.Children.forEach(e,(function(e){ce.isValidElement(e)&&t.push(e)})),t}var Df=function(e){var t=e.children,n=e.custom,r=e.initial,i=void 0===r||r,o=e.onExitComplete,a=e.presenceAffectsLayout,u=void 0===a||a,s=e.mode,l=void 0===s?"sync":s,c=e.propagate,f=void 0!==c&&c,d=e.anchorX,v=void 0===d?"left":d,h=ke(Tf(f),2),p=h[0],m=h[1],y=ce.useMemo((function(){return Mf(t)}),[t]),g=f&&!p?[]:y.map(Rf),b=ce.useRef(!0),x=ce.useRef(y),w=wf((function(){return new Map})),k=ke(ce.useState(y),2),F=k[0],E=k[1],A=ke(ce.useState(y),2),S=A[0],P=A[1];kf((function(){b.current=!1,x.current=y;for(var e=0;e<S.length;e++){var t=Rf(S[e]);g.includes(t)?w.delete(t):!0!==w.get(t)&&w.set(t,!1)}}),[S,g.length,g.join("-")]);var C=[];if(y!==F){for(var T=me(y),R=0;R<S.length;R++){var M=S[R],D=Rf(M);g.includes(D)||(T.splice(R,0,M),C.push(M))}return"wait"===l&&C.length&&(T=C),P(Mf(T)),E(y),null}var j=ce.useContext(xf).forceRender;return ae.jsx(ae.Fragment,{children:S.map((function(e){var t=Rf(e),r=!(f&&!p)&&(y===S||g.includes(t));return ae.jsx(Pf,{isPresent:r,initial:!(b.current&&!i)&&void 0,custom:n,presenceAffectsLayout:u,mode:l,onExitComplete:r?void 0:function(){if(w.has(t)){w.set(t,!0);var e=!0;w.forEach((function(t){t||(e=!1)})),e&&(null==j||j(),P(x.current),f&&(null==m||m()),o&&o())}},anchorX:v,children:e},t)}))})};function jf(e){if(("undefined"==typeof Proxy?"undefined":Ae(Proxy))>"u")return e;var t=new Map;return new Proxy((function(){return e.apply(void 0,arguments)}),{get:function(n,r){return"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))}})}function If(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=ju(e,t,"exit"===r.type?null===(n=e.presenceContext)||void 0===n?void 0:n.custom:void 0),o=(i||{}).transition,a=void 0===o?e.getDefaultTransition()||{}:o;r.transitionOverride&&(a=r.transitionOverride);var u=i?function(){return Promise.all(pc(e,i,r))}:function(){return Promise.resolve()},s=e.variantChildren&&e.variantChildren.size?function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,i=a,o=i.delayChildren,u=void 0===o?0:o,s=i.staggerChildren,l=i.staggerDirection;return function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=arguments.length>5?arguments[5]:void 0,a=[],u=(e.variantChildren.size-1)*r,s=1===i?function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r}:function(){return u-(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r};return Array.from(e.variantChildren).sort(Of).forEach((function(e,r){e.notify("AnimationStart",t),a.push(If(e,t,be(be({},o),{},{delay:n+s(r)})).then((function(){return e.notify("AnimationComplete",t)})))})),Promise.all(a)}(e,t,u+n,s,l,r)}:function(){return Promise.resolve()},l=a.when;if(l){var c=ke("beforeChildren"===l?[u,s]:[s,u],2),f=c[0],d=c[1];return f().then((function(){return d()}))}return Promise.all([u(),s(r.delay)])}function Of(e,t){return e.sortNodePosition(t)}function Lf(e,t){if(!Array.isArray(t))return!1;var n=t.length;if(n!==e.length)return!1;for(var r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}var Vf=Sc.length;function Nf(e){if(e){if(!e.isControllingVariants){var t=e.parent&&Nf(e.parent)||{};return void 0!==e.props.initial&&(t.initial=e.props.initial),t}for(var n={},r=0;r<Vf;r++){var i=Sc[r],o=e.props[i];(Ec(o)||!1===o)&&(n[i]=o)}return n}}var Bf=[].concat(Ac).reverse(),zf=Ac.length;function Wf(e){return function(t){return Promise.all(t.map((function(t){var n=t.animation,r=t.options;return function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t)){var i=t.map((function(t){return If(e,t,r)}));n=Promise.all(i)}else if("string"==typeof t)n=If(e,t,r);else{var o="function"==typeof t?ju(e,t,r.custom):t;n=Promise.all(pc(e,o,r))}return n.then((function(){e.notify("AnimationComplete",t)}))}(e,n,r)})))}}function Uf(e){var t=Wf(e),n=Hf(),r=!0;function i(i){for(var o=e.props,a=Nf(e.parent)||{},u=[],s=new Set,l={},c=1/0,f=function(){var t=Bf[d],f=n[t],v=void 0!==o[t]?o[t]:a[t],h=Ec(v),p=t===i?f.isActive:null;!1===p&&(c=d);var m=v===a[t]&&v!==o[t]&&h;if(m&&r&&e.manuallyAnimateOnMount&&(m=!1),f.protectedKeys=be({},l),!f.isActive&&null===p||!v&&!f.prevProp||Fc(v)||"boolean"==typeof v)return 1;var y=function(e,t){return"string"==typeof t?t!==e:!!Array.isArray(t)&&!Lf(t,e)}(f.prevProp,v),g=y||t===i&&f.isActive&&!m&&h||d>c&&h,b=!1,x=Array.isArray(v)?v:[v],w=x.reduce(function(t){return function(n,r){var i,o=ju(e,r,"exit"===t?null===(i=e.presenceContext)||void 0===i?void 0:i.custom:void 0);if(o){o.transition;var a=o.transitionEnd,u=ye(o,D);n=be(be(be({},n),u),a)}return n}}(t),{});!1===p&&(w={});var k=f.prevResolvedValues,F=void 0===k?{}:k,E=be(be({},F),w),A=function(t){g=!0,s.has(t)&&(b=!0,s.delete(t)),f.needsAnimating[t]=!0;var n=e.getValue(t);n&&(n.liveStyle=!1)};for(var S in E){var P=w[S],C=F[S];if(!l.hasOwnProperty(S)){(Tu(P)&&Tu(C)?!Lf(P,C):P!==C)?null!=P?A(S):s.add(S):void 0!==P&&s.has(S)?A(S):f.protectedKeys[S]=!0}}f.prevProp=v,f.prevResolvedValues=w,f.isActive&&(l=be(be({},l),w)),r&&e.blockInitialAnimation&&(g=!1),g&&(!m||!y||b)&&u.push.apply(u,me(x.map((function(e){return{animation:e,options:{type:t}}}))))},d=0;d<zf;d++)f();if(s.size){var v={};if("boolean"!=typeof o.initial){var h=ju(e,Array.isArray(o.initial)?o.initial[0]:o.initial);h&&h.transition&&(v.transition=h.transition)}s.forEach((function(t){var n=e.getBaseTarget(t),r=e.getValue(t);r&&(r.liveStyle=!0),v[t]=null!=n?n:null})),u.push({animation:v})}var p=!!u.length;return r&&(!1===o.initial||o.initial===o.animate)&&!e.manuallyAnimateOnMount&&(p=!1),r=!1,p?t(u):Promise.resolve()}return{animateChanges:i,setActive:function(t,r){var o;if(n[t].isActive===r)return Promise.resolve();null===(o=e.variantChildren)||void 0===o||o.forEach((function(e){var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,r)})),n[t].isActive=r;var a=i(t);for(var u in n)n[u].protectedKeys={};return a},setAnimateFunction:function(n){t=n(e)},getState:function(){return n},reset:function(){n=Hf(),r=!0}}}function qf(){return{isActive:arguments.length>0&&void 0!==arguments[0]&&arguments[0],protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Hf(){return{animate:qf(!0),whileInView:qf(),whileHover:qf(),whileTap:qf(),whileDrag:qf(),whileFocus:qf(),exit:qf()}}var Kf=function(){return fe((function e(t){le(this,e),this.isMounted=!1,this.node=t}),[{key:"update",value:function(){}}])}(),Xf=function(e){function t(e){var n;return le(this,t),n=ie(this,t,[e]),e.animationState||(e.animationState=Uf(e)),n}return ue(t,e),fe(t,[{key:"updateAnimationControlsSubscription",value:function(){var e=this.node.getProps().animate;Fc(e)&&(this.unmountControls=e.subscribe(this.node))}},{key:"mount",value:function(){this.updateAnimationControlsSubscription()}},{key:"update",value:function(){this.node.getProps().animate!==(this.node.prevProps||{}).animate&&this.updateAnimationControlsSubscription()}},{key:"unmount",value:function(){var e;this.node.animationState.reset(),null===(e=this.unmountControls)||void 0===e||e.call(this)}}])}(Kf),_f=0,Yf=function(e){function t(){var e;return le(this,t),(e=ie(this,t,arguments)).id=_f++,e}return ue(t,e),fe(t,[{key:"update",value:function(){var e=this;if(this.node.presenceContext){var t=this.node.presenceContext,n=t.isPresent,r=t.onExitComplete,i=(this.node.prevPresenceContext||{}).isPresent;if(this.node.animationState&&n!==i){var o=this.node.animationState.setActive("exit",!n);r&&!n&&o.then((function(){r(e.id)}))}}}},{key:"mount",value:function(){var e=this.node.presenceContext||{},t=e.register,n=e.onExitComplete;n&&n(this.id),t&&(this.unmount=t(this.id))}},{key:"unmount",value:function(){}}])}(Kf),Gf={animation:{Feature:Xf},exit:{Feature:Yf}},$f={x:!1,y:!1};function Zf(){return $f.x||$f.y}function Jf(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,n,r),function(){return e.removeEventListener(t,n)}}var Qf=function(e){return"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary};function ed(e){return{point:{x:e.pageX,y:e.pageY}}}function td(e,t,n,r){return Jf(e,t,function(e){return function(t){return Qf(t)&&e(t,ed(t))}}(n),r)}function nd(e){return e.max-e.min}function rd(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=Xa(t.min,t.max,e.origin),e.scale=nd(n)/nd(t),e.translate=Xa(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function id(e,t,n,r){rd(e.x,t.x,n.x,r?r.originX:void 0),rd(e.y,t.y,n.y,r?r.originY:void 0)}function od(e,t,n){e.min=n.min+t.min,e.max=e.min+nd(t)}function ad(e,t,n){e.min=t.min-n.min,e.max=e.min+nd(t)}function ud(e,t,n){ad(e.x,t.x,n.x),ad(e.y,t.y,n.y)}function sd(e){return[e("x"),e("y")]}var ld=function(e){var t=e.current;return t?t.ownerDocument.defaultView:null};function cd(e){return e&&"object"==Ae(e)&&Object.prototype.hasOwnProperty.call(e,"current")}var fd=function(e,t){return Math.abs(e-t)};var dd=function(){return fe((function e(t,n){var r=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=i.transformPagePoint,a=i.contextWindow,u=i.dragSnapToOrigin,s=void 0!==u&&u;if(le(this,e),this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=function(){if(r.lastMoveEvent&&r.lastMoveEventInfo){var e=pd(r.lastMoveEventInfo,r.history),t=null!==r.startEvent,n=function(e,t){var n=fd(e.x,t.x),r=fd(e.y,t.y);return Math.sqrt(Math.pow(n,2)+Math.pow(r,2))}(e.offset,{x:0,y:0})>=3;if(t||n){var i=e.point,o=gu.timestamp;r.history.push(be(be({},i),{},{timestamp:o}));var a=r.handlers,u=a.onStart,s=a.onMove;t||(u&&u(r.lastMoveEvent,e),r.startEvent=r.lastMoveEvent),s&&s(r.lastMoveEvent,e)}}},this.handlePointerMove=function(e,t){r.lastMoveEvent=e,r.lastMoveEventInfo=vd(t,r.transformPagePoint),mu.update(r.updatePoint,!0)},this.handlePointerUp=function(e,t){r.end();var n=r.handlers,i=n.onEnd,o=n.onSessionEnd,a=n.resumeAnimation;if(r.dragSnapToOrigin&&a&&a(),r.lastMoveEvent&&r.lastMoveEventInfo){var u=pd("pointercancel"===e.type?r.lastMoveEventInfo:vd(t,r.transformPagePoint),r.history);r.startEvent&&i&&i(e,u),o&&o(e,u)}},Qf(t)){this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=o,this.contextWindow=a||window;var l=vd(ed(t),this.transformPagePoint),c=l.point,f=gu.timestamp;this.history=[be(be({},c),{},{timestamp:f})];var d=n.onSessionStart;d&&d(t,pd(l,this.history)),this.removeListeners=Vl(td(this.contextWindow,"pointermove",this.handlePointerMove),td(this.contextWindow,"pointerup",this.handlePointerUp),td(this.contextWindow,"pointercancel",this.handlePointerUp))}}),[{key:"updateHandlers",value:function(e){this.handlers=e}},{key:"end",value:function(){this.removeListeners&&this.removeListeners(),yu(this.updatePoint)}}])}();function vd(e,t){return t?{point:t(e.point)}:e}function hd(e,t){return{x:e.x-t.x,y:e.y-t.y}}function pd(e,t){var n=e.point;return{point:n,delta:hd(n,yd(t)),offset:hd(n,md(t)),velocity:gd(t,.1)}}function md(e){return e[0]}function yd(e){return e[e.length-1]}function gd(e,t){if(e.length<2)return{x:0,y:0};for(var n=e.length-1,r=null,i=yd(e);n>=0&&(r=e[n],!(i.timestamp-r.timestamp>va(t)));)n--;if(!r)return{x:0,y:0};var o=ha(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};var a={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}function bd(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function xd(e,t){var n,r=t.min-e.min,i=t.max-e.max;return t.max-t.min<e.max-e.min&&(r=(n=[i,r])[0],i=n[1]),{min:r,max:i}}var wd=.35;function kd(e,t,n){return{min:Fd(e,t),max:Fd(e,n)}}function Fd(e,t){return"number"==typeof e?e:e[t]||0}var Ed=new WeakMap,Ad=function(){return fe((function e(t){le(this,e),this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}),[{key:"start",value:function(e){var t=this,n=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).snapToCursor,r=void 0!==n&&n,i=this.visualElement.presenceContext;if(!i||!1!==i.isPresent){var o=this.getProps().dragSnapToOrigin;this.panSession=new dd(e,{onSessionStart:function(e){t.getProps().dragSnapToOrigin?t.pauseAnimation():t.stopAnimation(),r&&t.snapToCursor(ed(e).point)},onStart:function(e,n){var r=t.getProps(),i=r.drag,o=r.dragPropagation,a=r.onDragStart;if(!i||o||(t.openDragLock&&t.openDragLock(),t.openDragLock=function(e){return"x"===e||"y"===e?$f[e]?null:($f[e]=!0,function(){$f[e]=!1}):$f.x||$f.y?null:($f.x=$f.y=!0,function(){$f.x=$f.y=!1})}(i),t.openDragLock)){t.isDragging=!0,t.currentDirection=null,t.resolveConstraints(),t.visualElement.projection&&(t.visualElement.projection.isAnimationBlocked=!0,t.visualElement.projection.target=void 0),sd((function(e){var n=t.getAxisMotionValue(e).get()||0;if(xs.test(n)){var r=t.visualElement.projection;if(r&&r.layout){var i=r.layout.layoutBox[e];i&&(n=nd(i)*(parseFloat(n)/100))}}t.originPoint[e]=n})),a&&mu.postRender((function(){return a(e,n)})),Ou(t.visualElement,"transform");var u=t.visualElement.animationState;u&&u.setActive("whileDrag",!0)}},onMove:function(e,n){var r=t.getProps(),i=r.dragPropagation,o=r.dragDirectionLock,a=r.onDirectionLock,u=r.onDrag;if(i||t.openDragLock){var s=n.offset;if(o&&null===t.currentDirection)return t.currentDirection=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),void(null!==t.currentDirection&&a&&a(t.currentDirection));t.updateAxis("x",n.point,s),t.updateAxis("y",n.point,s),t.visualElement.render(),u&&u(e,n)}},onSessionEnd:function(e,n){return t.stop(e,n)},resumeAnimation:function(){return sd((function(e){var n;return"paused"===t.getAnimationState(e)&&(null===(n=t.getAxisMotionValue(e).animation)||void 0===n?void 0:n.play())}))}},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:o,contextWindow:ld(this.visualElement)})}}},{key:"stop",value:function(e,t){var n=this.isDragging;if(this.cancel(),n){var r=t.velocity;this.startAnimation(r);var i=this.getProps().onDragEnd;i&&mu.postRender((function(){return i(e,t)}))}}},{key:"cancel",value:function(){this.isDragging=!1;var e=this.visualElement,t=e.projection,n=e.animationState;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0,!this.getProps().dragPropagation&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}},{key:"updateAxis",value:function(e,t,n){var r=this.getProps().drag;if(n&&Sd(e,r,this.currentDirection)){var i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,t,n){var r=t.min,i=t.max;return void 0!==r&&e<r?e=n?Xa(r,e,n.min):Math.max(e,r):void 0!==i&&e>i&&(e=n?Xa(i,e,n.max):Math.min(e,i)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}}},{key:"resolveConstraints",value:function(){var e,t=this,n=this.getProps(),r=n.dragConstraints,i=n.dragElastic,o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,a=this.constraints;r&&cd(r)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!r||!o)&&function(e,t){var n=t.top,r=t.left,i=t.bottom,o=t.right;return{x:bd(e.x,r,o),y:bd(e.y,n,i)}}(o.layoutBox,r),this.elastic=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:wd;return!1===e?e=0:!0===e&&(e=wd),{x:kd(e,"left","right"),y:kd(e,"top","bottom")}}(i),a!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&sd((function(e){!1!==t.constraints&&t.getAxisMotionValue(e)&&(t.constraints[e]=function(e,t){var n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(o.layoutBox[e],t.constraints[e]))}))}},{key:"resolveRefConstraints",value:function(){var e=this.getProps(),t=e.dragConstraints,n=e.onMeasureDragConstraints;if(!t||!cd(t))return!1;var r=t.current,i=this.visualElement.projection;if(!i||!i.layout)return!1;var o=function(e,t,n){var r=df(e,n),i=t.scroll;return i&&(lf(r.x,i.offset.x),lf(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),a=function(e,t){return{x:xd(e.x,t.x),y:xd(e.y,t.y)}}(i.layout.layoutBox,o);if(n){var u=n(function(e){var t=e.x,n=e.y;return{top:n.min,right:t.max,bottom:n.max,left:t.min}}(a));this.hasMutatedConstraints=!!u,u&&(a=$c(u))}return a}},{key:"startAnimation",value:function(e){var t=this,n=this.getProps(),r=n.drag,i=n.dragMomentum,o=n.dragElastic,a=n.dragTransition,u=n.dragSnapToOrigin,s=n.onDragTransitionEnd,l=this.constraints||{},c=sd((function(n){if(Sd(n,r,t.currentDirection)){var s=l&&l[n]||{};u&&(s={min:0,max:0});var c=o?200:1e6,f=o?40:1e7,d=be(be({type:"inertia",velocity:i?e[n]:0,bounceStiffness:c,bounceDamping:f,timeConstant:750,restDelta:1,restSpeed:10},a),s);return t.startAxisValueAnimation(n,d)}}));return Promise.all(c).then(s)}},{key:"startAxisValueAnimation",value:function(e,t){var n=this.getAxisMotionValue(e);return Ou(this.visualElement,e),n.start(vc(e,n,0,t,this.visualElement,!1))}},{key:"stopAnimation",value:function(){var e=this;sd((function(t){return e.getAxisMotionValue(t).stop()}))}},{key:"pauseAnimation",value:function(){var e=this;sd((function(t){var n;return null===(n=e.getAxisMotionValue(t).animation)||void 0===n?void 0:n.pause()}))}},{key:"getAnimationState",value:function(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}},{key:"getAxisMotionValue",value:function(e){var t="_drag".concat(e.toUpperCase()),n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}},{key:"snapToCursor",value:function(e){var t=this;sd((function(n){if(Sd(n,t.getProps().drag,t.currentDirection)){var r=t.visualElement.projection,i=t.getAxisMotionValue(n);if(r&&r.layout){var o=r.layout.layoutBox[n],a=o.min,u=o.max;i.set(e[n]-Xa(a,u,.5))}}}))}},{key:"scalePositionWithinConstraints",value:function(){var e=this;if(this.visualElement.current){var t=this.getProps(),n=t.drag,r=t.dragConstraints,i=this.visualElement.projection;if(cd(r)&&i&&this.constraints){this.stopAnimation();var o={x:0,y:0};sd((function(t){var n=e.getAxisMotionValue(t);if(n&&!1!==e.constraints){var r=n.get();o[t]=function(e,t){var n=.5,r=nd(e),i=nd(t);return i>r?n=ma(t.min,t.max-r,e.min):r>i&&(n=ma(e.min,e.max-i,t.min)),ga(0,1,n)}({min:r,max:r},e.constraints[t])}}));var a=this.visualElement.getProps().transformTemplate;this.visualElement.current.style.transform=a?a({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),sd((function(t){if(Sd(t,n,null)){var r=e.getAxisMotionValue(t),i=e.constraints[t],a=i.min,u=i.max;r.set(Xa(a,u,o[t]))}}))}}}},{key:"addListeners",value:function(){var e=this;if(this.visualElement.current){Ed.set(this.visualElement,this);var t=td(this.visualElement.current,"pointerdown",(function(t){var n=e.getProps(),r=n.drag,i=n.dragListener;r&&(void 0===i||i)&&e.start(t)})),n=function(){var t=e.getProps().dragConstraints;cd(t)&&t.current&&(e.constraints=e.resolveRefConstraints())},r=this.visualElement.projection,i=r.addEventListener("measure",n);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),mu.read(n);var o=Jf(window,"resize",(function(){return e.scalePositionWithinConstraints()})),a=r.addEventListener("didUpdate",(function(t){var n=t.delta,r=t.hasLayoutChanged;e.isDragging&&r&&(sd((function(t){var r=e.getAxisMotionValue(t);r&&(e.originPoint[t]+=n[t].translate,r.set(r.get()+n[t].translate))})),e.visualElement.render())}));return function(){o(),t(),i(),a&&a()}}}},{key:"getProps",value:function(){var e=this.visualElement.getProps(),t=e.drag,n=void 0!==t&&t,r=e.dragDirectionLock,i=void 0!==r&&r,o=e.dragPropagation,a=void 0!==o&&o,u=e.dragConstraints,s=void 0!==u&&u,l=e.dragElastic,c=void 0===l?wd:l,f=e.dragMomentum,d=void 0===f||f;return be(be({},e),{},{drag:n,dragDirectionLock:i,dragPropagation:a,dragConstraints:s,dragElastic:c,dragMomentum:d})}}])}();function Sd(e,t,n){return!(!0!==t&&t!==e||null!==n&&n!==e)}var Pd=function(e){function t(e){var n;return le(this,t),(n=ie(this,t,[e])).removeGroupControls=Ia,n.removeListeners=Ia,n.controls=new Ad(e),n}return ue(t,e),fe(t,[{key:"mount",value:function(){var e=this.node.getProps().dragControls;e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ia}},{key:"unmount",value:function(){this.removeGroupControls(),this.removeListeners()}}])}(Kf),Cd=function(e){return function(t,n){e&&mu.postRender((function(){return e(t,n)}))}},Td=function(e){function t(){var e;return le(this,t),(e=ie(this,t,arguments)).removePointerDownListener=Ia,e}return ue(t,e),fe(t,[{key:"onPointerDown",value:function(e){this.session=new dd(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ld(this.node)})}},{key:"createPanHandlers",value:function(){var e=this,t=this.node.getProps(),n=t.onPanSessionStart,r=t.onPanStart,i=t.onPan,o=t.onPanEnd;return{onSessionStart:Cd(n),onStart:Cd(r),onMove:i,onEnd:function(t,n){delete e.session,o&&mu.postRender((function(){return o(t,n)}))}}}},{key:"mount",value:function(){var e=this;this.removePointerDownListener=td(this.node.current,"pointerdown",(function(t){return e.onPointerDown(t)}))}},{key:"update",value:function(){this.session&&this.session.updateHandlers(this.createPanHandlers())}},{key:"unmount",value:function(){this.removePointerDownListener(),this.session&&this.session.end()}}])}(Kf),Rd=vu(queueMicrotask,!1).schedule,Md=ce.createContext({}),Dd={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function jd(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}var Id={correct:function(e,t){if(!t.target)return e;if("string"==typeof e){if(!ws.test(e))return e;e=parseFloat(e)}var n=jd(e,t.target.x),r=jd(e,t.target.y);return"".concat(n,"% ").concat(r,"%")}},Od={correct:function(e,t){var n=t.treeScale,r=t.projectionDelta,i=e,o=Os.parse(e);if(o.length>5)return i;var a=Os.createTransformer(e),u="number"!=typeof o[0]?1:0,s=r.x.scale*n.x,l=r.y.scale*n.y;o[0+u]/=s,o[1+u]/=l;var c=Xa(s,l,.5);return"number"==typeof o[2+u]&&(o[2+u]/=c),"number"==typeof o[3+u]&&(o[3+u]/=c),a(o)}},Ld=function(e){function t(){return le(this,t),ie(this,t,arguments)}return ue(t,e),fe(t,[{key:"componentDidMount",value:function(){var e=this,t=this.props,n=t.visualElement,r=t.layoutGroup,i=t.switchLayoutGroup,o=t.layoutId,a=n.projection;(function(e){for(var t in e)Kc[t]=e[t],ml(t)&&(Kc[t].isCSSVariable=!0)})(Nd),a&&(r.group&&r.group.add(a),i&&i.register&&o&&i.register(a),a.root.didUpdate(),a.addEventListener("animationComplete",(function(){e.safeToRemove()})),a.setOptions(be(be({},a.options),{},{onExitComplete:function(){return e.safeToRemove()}}))),Dd.hasEverUpdated=!0}},{key:"getSnapshotBeforeUpdate",value:function(e){var t=this,n=this.props,r=n.layoutDependency,i=n.visualElement,o=n.drag,a=n.isPresent,u=i.projection;return u&&(u.isPresent=a,o||e.layoutDependency!==r||void 0===r||e.isPresent!==a?u.willUpdate():this.safeToRemove(),e.isPresent!==a&&(a?u.promote():u.relegate()||mu.postRender((function(){var e=u.getStack();(!e||!e.members.length)&&t.safeToRemove()})))),null}},{key:"componentDidUpdate",value:function(){var e=this,t=this.props.visualElement.projection;t&&(t.root.didUpdate(),Rd.postRender((function(){!t.currentAnimation&&t.isLead()&&e.safeToRemove()})))}},{key:"componentWillUnmount",value:function(){var e=this.props,t=e.visualElement,n=e.layoutGroup,r=e.switchLayoutGroup,i=t.projection;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}},{key:"safeToRemove",value:function(){var e=this.props.safeToRemove;e&&e()}},{key:"render",value:function(){return null}}])}(ce.Component);function Vd(e){var t=ke(Tf(),2),n=t[0],r=t[1],i=ce.useContext(xf);return ae.jsx(Ld,be(be({},e),{},{layoutGroup:i,switchLayoutGroup:ce.useContext(Md),isPresent:n,safeToRemove:r}))}var Nd={borderRadius:be(be({},Id),{},{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:Id,borderTopRightRadius:Id,borderBottomLeftRadius:Id,borderBottomRightRadius:Id,boxShadow:Od},Bd=function(e,t){return e.depth-t.depth},zd=function(){return fe((function e(){le(this,e),this.children=[],this.isDirty=!1}),[{key:"add",value:function(e){eu(this.children,e),this.isDirty=!0}},{key:"remove",value:function(e){tu(this.children,e),this.isDirty=!0}},{key:"forEach",value:function(e){this.isDirty&&this.children.sort(Bd),this.isDirty=!1,this.children.forEach(e)}}])}();function Wd(e){var t=Ga(e)?e.get():e;return function(e){return!!(e&&"object"==Ae(e)&&e.mix&&e.toValue)}(t)?t.toValue():t}var Ud=["TopLeft","TopRight","BottomLeft","BottomRight"],qd=Ud.length,Hd=function(e){return"string"==typeof e?parseFloat(e):e},Kd=function(e){return"number"==typeof e||ws.test(e)};function Xd(e,t){return void 0!==e[t]?e[t]:e.borderRadius}var _d=Gd(0,.5,rs),Yd=Gd(.5,.95,Ia);function Gd(e,t,n){return function(r){return r<e?0:r>t?1:n(ma(e,t,r))}}function $d(e,t){e.min=t.min,e.max=t.max}function Zd(e,t){$d(e.x,t.x),$d(e.y,t.y)}function Jd(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Qd(e,t,n,r,i){return e=nf(e-=t,1/n,r),void 0!==i&&(e=nf(e,1/i,r)),e}function ev(e,t,n,r,i){var o=ke(n,3),a=o[0],u=o[1],s=o[2];!function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;if(xs.test(t)&&(t=parseFloat(t),t=Xa(a.min,a.max,t/100)-a.min),"number"==typeof t){var u=Xa(o.min,o.max,r);e===o&&(u-=t),e.min=Qd(e.min,t,n,u,i),e.max=Qd(e.max,t,n,u,i)}}(e,t[a],t[u],t[s],t.scale,r,i)}var tv=["x","scaleX","originX"],nv=["y","scaleY","originY"];function rv(e,t,n,r){ev(e.x,t,tv,n?n.x:void 0,r?r.x:void 0),ev(e.y,t,nv,n?n.y:void 0,r?r.y:void 0)}function iv(e){return 0===e.translate&&1===e.scale}function ov(e){return iv(e.x)&&iv(e.y)}function av(e,t){return e.min===t.min&&e.max===t.max}function uv(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function sv(e,t){return uv(e.x,t.x)&&uv(e.y,t.y)}function lv(e){return nd(e.x)/nd(e.y)}function cv(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}var fv=function(){return fe((function e(){le(this,e),this.members=[]}),[{key:"add",value:function(e){eu(this.members,e),e.scheduleRender()}},{key:"remove",value:function(e){if(tu(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){var t=this.members[this.members.length-1];t&&this.promote(t)}}},{key:"relegate",value:function(e){var t,n=this.members.findIndex((function(t){return e===t}));if(0===n)return!1;for(var r=n;r>=0;r--){var i=this.members[r];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}},{key:"promote",value:function(e,t){var n=this.lead;e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)&&(n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0),!1===e.options.crossfade&&n.hide())}},{key:"exitAnimationComplete",value:function(){this.members.forEach((function(e){var t=e.options,n=e.resumingFrom;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}},{key:"scheduleRender",value:function(){this.members.forEach((function(e){e.instance&&e.scheduleRender(!1)}))}},{key:"removeLeadSnapshot",value:function(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}])}();var dv=["","X","Y","Z"],vv={visibility:"hidden"},hv=0;function pv(e,t,n,r){var i=t.latestValues;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function mv(e){if(e.hasCheckedOptimisedAppear=!0,e.root!==e){var t=e.options.visualElement;if(t){var n=Nu(t);if(window.MotionHasOptimisedAnimation(n,"transform")){var r=e.options,i=r.layout,o=r.layoutId;window.MotionCancelOptimisedAnimation(n,"transform",mu,!(i||o))}var a=e.parent;a&&!a.hasCheckedOptimisedAppear&&mv(a)}}}function yv(e){var t=e.attachResizeListener,n=e.defaultParent,r=e.measureScroll,i=e.checkIsScrollRoot,o=e.resetTransform;return function(){return fe((function e(){var t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==n?void 0:n();le(this,e),this.id=hv++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=function(){return t.update()},this.projectionUpdateScheduled=!1,this.checkUpdateFailed=function(){t.isUpdating&&(t.isUpdating=!1,t.clearAllSnapshots())},this.updateProjection=function(){t.projectionUpdateScheduled=!1,t.nodes.forEach(xv),t.nodes.forEach(Pv),t.nodes.forEach(Cv),t.nodes.forEach(wv)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=i?i.root||i:this,this.path=i?[].concat(me(i.path),[i]):[],this.parent=i,this.depth=i?i.depth+1:0;for(var o=0;o<this.path.length;o++)this.path[o].shouldResetTransform=!0;this.root===this&&(this.nodes=new zd)}),[{key:"addEventListener",value:function(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new Fu),this.eventHandlers.get(e).add(t)}},{key:"notifyListeners",value:function(e){for(var t=this.eventHandlers.get(e),n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t&&t.notify.apply(t,r)}},{key:"hasListeners",value:function(e){return this.eventHandlers.has(e)}},{key:"mount",value:function(e){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.root.hasTreeAnimated;if(!this.instance){this.isSVG=mc(e),this.instance=e;var i=this.options,o=i.layoutId,a=i.layout,u=i.visualElement;if(u&&!u.current&&u.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(a||o)&&(this.isLayoutDirty=!0),t){var s,l=function(){return n.root.updateBlockedByResize=!1};t(e,(function(){n.root.updateBlockedByResize=!0,s&&s(),s=function(e,t){var n=Su.now(),r=function(i){var o=i.timestamp-n;o>=t&&(yu(r),e(o-t))};return mu.read(r,!0),function(){return yu(r)}}(l,250),Dd.hasAnimatedSinceResize&&(Dd.hasAnimatedSinceResize=!1,n.nodes.forEach(Sv))}))}o&&this.root.registerSharedNode(o,this),!1!==this.options.animate&&u&&(o||a)&&this.addEventListener("didUpdate",(function(e){var t=e.delta,r=e.hasLayoutChanged,i=e.hasRelativeLayoutChanged,o=e.layout;if(n.isTreeAnimationBlocked())return n.target=void 0,void(n.relativeTarget=void 0);var a=n.options.transition||u.getDefaultTransition()||Iv,s=u.getProps(),l=s.onLayoutAnimationStart,c=s.onLayoutAnimationComplete,f=!n.targetLayout||!sv(n.targetLayout,o),d=!r&&i;if(n.options.layoutRoot||n.resumeFrom||d||r&&(f||!n.currentAnimation)){n.resumeFrom&&(n.resumingFrom=n.resumeFrom,n.resumingFrom.resumingFrom=void 0),n.setAnimationOrigin(t,d);var v=be(be({},fu(a,"layout")),{},{onPlay:l,onComplete:c});(u.shouldReduceMotion||n.options.layoutRoot)&&(v.delay=0,v.type=!1),n.startAnimation(v)}else r||Sv(n),n.isLead()&&n.options.onExitComplete&&n.options.onExitComplete();n.targetLayout=o}))}}},{key:"unmount",value:function(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);var e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,yu(this.updateProjection)}},{key:"blockUpdate",value:function(){this.updateManuallyBlocked=!0}},{key:"unblockUpdate",value:function(){this.updateManuallyBlocked=!1}},{key:"isUpdateBlocked",value:function(){return this.updateManuallyBlocked||this.updateBlockedByResize}},{key:"isTreeAnimationBlocked",value:function(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}},{key:"startUpdate",value:function(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Tv),this.animationId++)}},{key:"getTransformTemplate",value:function(){var e=this.options.visualElement;return e&&e.getProps().transformTemplate}},{key:"willUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())this.options.onExitComplete&&this.options.onExitComplete();else if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&mv(this),!this.root.isUpdating&&this.root.startUpdate(),!this.isLayoutDirty){this.isLayoutDirty=!0;for(var t=0;t<this.path.length;t++){var n=this.path[t];n.shouldResetTransform=!0,n.updateScroll("snapshot"),n.options.layoutRoot&&n.willUpdate(!1)}var r=this.options,i=r.layoutId,o=r.layout;if(void 0!==i||o){var a=this.getTransformTemplate();this.prevTransformTemplateValue=a?a(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}}}},{key:"update",value:function(){if(this.updateScheduled=!1,this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Fv);this.isUpdating||this.nodes.forEach(Ev),this.isUpdating=!1,this.nodes.forEach(Av),this.nodes.forEach(gv),this.nodes.forEach(bv),this.clearAllSnapshots();var e=Su.now();gu.delta=ga(0,1e3/60,e-gu.timestamp),gu.timestamp=e,gu.isProcessing=!0,bu.update.process(gu),bu.preRender.process(gu),bu.render.process(gu),gu.isProcessing=!1}},{key:"didUpdate",value:function(){this.updateScheduled||(this.updateScheduled=!0,Rd.read(this.scheduleUpdate))}},{key:"clearAllSnapshots",value:function(){this.nodes.forEach(kv),this.sharedNodes.forEach(Rv)}},{key:"scheduleUpdateProjection",value:function(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,mu.preRender(this.updateProjection,!1,!0))}},{key:"scheduleCheckAfterUnmount",value:function(){var e=this;mu.postRender((function(){e.isLayoutDirty?e.root.didUpdate():e.root.checkUpdateFailed()}))}},{key:"updateSnapshot",value:function(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!nd(this.snapshot.measuredBox.x)&&!nd(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}},{key:"updateLayout",value:function(){if(this.instance&&(this.updateScroll(),this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty)){if(this.resumeFrom&&!this.resumeFrom.instance)for(var e=0;e<this.path.length;e++)this.path[e].updateScroll();var t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);var n=this.options.visualElement;n&&n.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}}},{key:"updateScroll",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=!(!this.options.layoutScroll||!this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){var n=i(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:n,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:n}}}},{key:"resetTransform",value:function(){if(o){var e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!ov(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,i=r!==this.prevTransformTemplateValue;e&&(t||Qc(this.latestValues)||i)&&(o(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}}},{key:"measure",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=this.measurePageBox(),n=this.removeElementScroll(t);return e&&(n=this.removeTransform(n)),function(e){Vv(e.x),Vv(e.y)}(n),{animationId:this.root.animationId,measuredBox:t,layoutBox:n,latestValues:{},source:this.id}}},{key:"measurePageBox",value:function(){var e,t=this.options.visualElement;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};var n=t.measureViewportBox();if(!(null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)&&!this.path.some(Bv)){var r=this.root.scroll;r&&(lf(n.x,r.offset.x),lf(n.y,r.offset.y))}return n}},{key:"removeElementScroll",value:function(e){var t,n={x:{min:0,max:0},y:{min:0,max:0}};if(Zd(n,e),null!==(t=this.scroll)&&void 0!==t&&t.wasRoot)return n;for(var r=0;r<this.path.length;r++){var i=this.path[r],o=i.scroll,a=i.options;i!==this.root&&o&&a.layoutScroll&&(o.wasRoot&&Zd(n,e),lf(n.x,o.offset.x),lf(n.y,o.offset.y))}return n}},{key:"applyTransform",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n={x:{min:0,max:0},y:{min:0,max:0}};Zd(n,e);for(var r=0;r<this.path.length;r++){var i=this.path[r];!t&&i.options.layoutScroll&&i.scroll&&i!==i.root&&ff(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),Qc(i.latestValues)&&ff(n,i.latestValues)}return Qc(this.latestValues)&&ff(n,this.latestValues),n}},{key:"removeTransform",value:function(e){var t={x:{min:0,max:0},y:{min:0,max:0}};Zd(t,e);for(var n=0;n<this.path.length;n++){var r=this.path[n];if(r.instance&&Qc(r.latestValues)){Jc(r.latestValues)&&r.updateSnapshot();var i={x:{min:0,max:0},y:{min:0,max:0}};Zd(i,r.measurePageBox()),rv(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}}return Qc(this.latestValues)&&rv(t,this.latestValues),t}},{key:"setTargetDelta",value:function(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}},{key:"setOptions",value:function(e){this.options=be(be(be({},this.options),e),{},{crossfade:void 0===e.crossfade||e.crossfade})}},{key:"clearMeasurements",value:function(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}},{key:"forceRelativeParentToResolveTarget",value:function(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==gu.timestamp&&this.relativeParent.resolveTargetDelta(!0)}},{key:"resolveTargetDelta",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);var r=!!this.resumingFrom||this!==n;if(t||r&&this.isSharedProjectionDirty||this.isProjectionDirty||null!==(e=this.parent)&&void 0!==e&&e.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize){var i=this.options,o=i.layout,a=i.layoutId;if(this.layout&&(o||a)){if(this.resolvedRelativeTargetAt=gu.timestamp,!this.targetDelta&&!this.relativeTarget){var u=this.getClosestProjectingParent();u&&u.layout&&1!==this.animationProgress?(this.relativeParent=u,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ud(this.relativeTargetOrigin,this.layout.layoutBox,u.layout.layoutBox),Zd(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if((this.relativeTarget||this.targetDelta)&&(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),function(e,t,n){od(e.x,t.x,n.x),od(e.y,t.y,n.y)}(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Zd(this.target,this.layout.layoutBox),af(this.target,this.targetDelta)):Zd(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;var s=this.getClosestProjectingParent();s&&!!s.resumingFrom==!!this.resumingFrom&&!s.options.layoutScroll&&s.target&&1!==this.animationProgress?(this.relativeParent=s,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ud(this.relativeTargetOrigin,this.target,s.target),Zd(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}}},{key:"getClosestProjectingParent",value:function(){if(this.parent&&!Jc(this.parent.latestValues)&&!ef(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}},{key:"isProjecting",value:function(){return!(!(this.relativeTarget||this.targetDelta||this.options.layoutRoot)||!this.layout)}},{key:"calcProjection",value:function(){var e,t=this.getLead(),n=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||!(null===(e=this.parent)||void 0===e)&&e.isProjectionDirty)&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===gu.timestamp&&(r=!1),!r){var i=this.options,o=i.layout,a=i.layoutId;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),this.layout&&(o||a)){Zd(this.layoutCorrected,this.layout.layoutBox);var u=this.treeScale.x,s=this.treeScale.y;(function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=n.length;if(i){var o,a;t.x=t.y=1;for(var u=0;u<i;u++){a=(o=n[u]).projectionDelta;var s=o.options.visualElement;s&&s.props.style&&"contents"===s.props.style.display||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ff(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,af(e,a)),r&&Qc(o.latestValues)&&ff(e,o.latestValues))}t.x<sf&&t.x>uf&&(t.x=1),t.y<sf&&t.y>uf&&(t.y=1)}})(this.layoutCorrected,this.treeScale,this.path,n),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});var l=t.target;l?(this.projectionDelta&&this.prevProjectionDelta?(Jd(this.prevProjectionDelta.x,this.projectionDelta.x),Jd(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),id(this.projectionDelta,this.layoutCorrected,l,this.latestValues),(this.treeScale.x!==u||this.treeScale.y!==s||!cv(this.projectionDelta.x,this.prevProjectionDelta.x)||!cv(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}}}},{key:"hide",value:function(){this.isVisible=!1}},{key:"show",value:function(){this.isVisible=!0}},{key:"scheduleRender",value:function(){var e,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){var n=this.getStack();n&&n.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}},{key:"createProjectionDeltas",value:function(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}},{key:"setAnimationOrigin",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.snapshot,i=r?r.latestValues:{},o=be({},this.latestValues),a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!n;var u,s={x:{min:0,max:0},y:{min:0,max:0}},l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),c=this.getStack(),f=!c||c.members.length<=1,d=!(!l||f||!0!==this.options.crossfade||this.path.some(jv));this.animationProgress=0,this.mixTargetDelta=function(n){var r=n/1e3;Mv(a.x,e.x,r),Mv(a.y,e.y,r),t.setTargetDelta(a),t.relativeTarget&&t.relativeTargetOrigin&&t.layout&&t.relativeParent&&t.relativeParent.layout&&(ud(s,t.layout.layoutBox,t.relativeParent.layout.layoutBox),function(e,t,n,r){Dv(e.x,t.x,n.x,r),Dv(e.y,t.y,n.y,r)}(t.relativeTarget,t.relativeTargetOrigin,s,r),u&&function(e,t){return av(e.x,t.x)&&av(e.y,t.y)}(t.relativeTarget,u)&&(t.isProjectionDirty=!1),u||(u={x:{min:0,max:0},y:{min:0,max:0}}),Zd(u,t.relativeTarget)),l&&(t.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=Xa(0,void 0!==n.opacity?n.opacity:1,_d(r)),e.opacityExit=Xa(void 0!==t.opacity?t.opacity:1,0,Yd(r))):o&&(e.opacity=Xa(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,r));for(var a=0;a<qd;a++){var u="border".concat(Ud[a],"Radius"),s=Xd(t,u),l=Xd(n,u);void 0===s&&void 0===l||(s||(s=0),l||(l=0),0===s||0===l||Kd(s)===Kd(l)?(e[u]=Math.max(Xa(Hd(s),Hd(l),r),0),(xs.test(l)||xs.test(s))&&(e[u]+="%")):e[u]=l)}(t.rotate||n.rotate)&&(e.rotate=Xa(t.rotate||0,n.rotate||0,r))}(o,i,t.latestValues,r,d,f)),t.root.scheduleUpdateProjection(),t.scheduleRender(),t.animationProgress=r},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}},{key:"startAnimation",value:function(e){var t=this;this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(yu(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=mu.update((function(){Dd.hasAnimatedSinceResize=!0,t.currentAnimation=yf(0,1e3,be(be({},e),{},{onUpdate:function(n){t.mixTargetDelta(n),e.onUpdate&&e.onUpdate(n)},onStop:function(){},onComplete:function(){e.onComplete&&e.onComplete(),t.completeAnimation()}})),t.resumingFrom&&(t.resumingFrom.currentAnimation=t.currentAnimation),t.pendingAnimation=void 0}))}},{key:"completeAnimation",value:function(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);var e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}},{key:"finishAnimation",value:function(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}},{key:"applyTransformsToTarget",value:function(){var e=this.getLead(),t=e.targetWithTransforms,n=e.target,r=e.layout,i=e.latestValues;if(t&&n&&r){if(this!==e&&this.layout&&r&&Nv(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};var o=nd(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+o;var a=nd(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+a}Zd(t,n),ff(t,i),id(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}},{key:"registerSharedNode",value:function(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new fv),this.sharedNodes.get(e).add(t);var n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}},{key:"isLead",value:function(){var e=this.getStack();return!e||e.lead===this}},{key:"getLead",value:function(){var e;return this.options.layoutId&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}},{key:"getPrevLead",value:function(){var e;return this.options.layoutId?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}},{key:"getStack",value:function(){var e=this.options.layoutId;if(e)return this.root.sharedNodes.get(e)}},{key:"promote",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.needsReset,n=e.transition,r=e.preserveFollowOpacity,i=this.getStack();i&&i.promote(this,r),t&&(this.projectionDelta=void 0,this.needsReset=!0),n&&this.setOptions({transition:n})}},{key:"relegate",value:function(){var e=this.getStack();return!!e&&e.relegate(this)}},{key:"resetSkewAndRotation",value:function(){var e=this.options.visualElement;if(e){var t=!1,n=e.latestValues;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),t){var r={};n.z&&pv("z",e,r,this.animationValues);for(var i=0;i<dv.length;i++)pv("rotate".concat(dv[i]),e,r,this.animationValues),pv("skew".concat(dv[i]),e,r,this.animationValues);for(var o in e.render(),r)e.setStaticValue(o,r[o]),this.animationValues&&(this.animationValues[o]=r[o]);e.scheduleRender()}}}},{key:"getProjectionStyles",value:function(e){var t,n;if(this.instance&&!this.isSVG){if(!this.isVisible)return vv;var r={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=Wd(null==e?void 0:e.pointerEvents)||"",r.transform=i?i(this.latestValues,""):"none",r;var o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){var a={};return this.options.layoutId&&(a.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,a.pointerEvents=Wd(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!Qc(this.latestValues)&&(a.transform=i?i({},""):"none",this.hasProjected=!1),a}var u=o.animationValues||o.latestValues;this.applyTransformsToTarget(),r.transform=function(e,t,n){var r="",i=e.x.translate/t.x,o=e.y.translate/t.y,a=(null==n?void 0:n.z)||0;if((i||o||a)&&(r="translate3d(".concat(i,"px, ").concat(o,"px, ").concat(a,"px) ")),(1!==t.x||1!==t.y)&&(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),n){var u=n.transformPerspective,s=n.rotate,l=n.rotateX,c=n.rotateY,f=n.skewX,d=n.skewY;u&&(r="perspective(".concat(u,"px) ").concat(r)),s&&(r+="rotate(".concat(s,"deg) ")),l&&(r+="rotateX(".concat(l,"deg) ")),c&&(r+="rotateY(".concat(c,"deg) ")),f&&(r+="skewX(".concat(f,"deg) ")),d&&(r+="skewY(".concat(d,"deg) "))}var v=e.x.scale*t.x,h=e.y.scale*t.y;return(1!==v||1!==h)&&(r+="scale(".concat(v,", ").concat(h,")")),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,u),i&&(r.transform=i(u,r.transform));var s=this.projectionDelta,l=s.x,c=s.y;for(var f in r.transformOrigin="".concat(100*l.origin,"% ").concat(100*c.origin,"% 0"),o.animationValues?r.opacity=o===this?null!==(n=null!==(t=u.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:u.opacityExit:r.opacity=o===this?void 0!==u.opacity?u.opacity:"":void 0!==u.opacityExit?u.opacityExit:0,Kc)if(void 0!==u[f]){var d=Kc[f],v=d.correct,h=d.applyTo,p=d.isCSSVariable,m="none"===r.transform?u[f]:v(u[f],o);if(h)for(var y=h.length,g=0;g<y;g++)r[h[g]]=m;else p?this.options.visualElement.renderState.vars[f]=m:r[f]=m}return this.options.layoutId&&(r.pointerEvents=o===this?Wd(null==e?void 0:e.pointerEvents)||"":"none"),r}}},{key:"clearSnapshot",value:function(){this.resumeFrom=this.snapshot=void 0}},{key:"resetTree",value:function(){this.root.nodes.forEach((function(e){var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()})),this.root.nodes.forEach(Fv),this.root.sharedNodes.clear()}}])}()}function gv(e){e.updateLayout()}function bv(e){var t,n=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){var r=e.layout,i=r.layoutBox,o=r.measuredBox,a=e.options.animationType,u=n.source!==e.layout.source;"size"===a?sd((function(e){var t=u?n.measuredBox[e]:n.layoutBox[e],r=nd(t);t.min=i[e].min,t.max=t.min+r})):Nv(a,n.layoutBox,i)&&sd((function(t){var r=u?n.measuredBox[t]:n.layoutBox[t],o=nd(i[t]);r.max=r.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[t].max=e.relativeTarget[t].min+o)}));var s={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};id(s,i,n.layoutBox);var l={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};u?id(l,e.applyTransform(o,!0),n.measuredBox):id(l,i,n.layoutBox);var c=!ov(s),f=!1;if(!e.resumeFrom){var d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){var v=d.snapshot,h=d.layout;if(v&&h){var p={x:{min:0,max:0},y:{min:0,max:0}};ud(p,n.layoutBox,v.layoutBox);var m={x:{min:0,max:0},y:{min:0,max:0}};ud(m,i,h.layoutBox),sv(p,m)||(f=!0),d.options.layoutRoot&&(e.relativeTarget=m,e.relativeTargetOrigin=p,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:n,delta:l,layoutDelta:s,hasLayoutChanged:c,hasRelativeLayoutChanged:f})}else if(e.isLead()){var y=e.options.onExitComplete;y&&y()}e.options.transition=void 0}function xv(e){e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function wv(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function kv(e){e.clearSnapshot()}function Fv(e){e.clearMeasurements()}function Ev(e){e.isLayoutDirty=!1}function Av(e){var t=e.options.visualElement;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Sv(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Pv(e){e.resolveTargetDelta()}function Cv(e){e.calcProjection()}function Tv(e){e.resetSkewAndRotation()}function Rv(e){e.removeLeadSnapshot()}function Mv(e,t,n){e.translate=Xa(t.translate,0,n),e.scale=Xa(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Dv(e,t,n,r){e.min=Xa(t.min,n.min,r),e.max=Xa(t.max,n.max,r)}function jv(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}var Iv={duration:.45,ease:[.4,0,.1,1]},Ov=function(e){return("undefined"==typeof navigator?"undefined":Ae(navigator))<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e)},Lv=Ov("applewebkit/")&&!Ov("chrome/")?Math.round:Ia;function Vv(e){e.min=Lv(e.min),e.max=Lv(e.max)}function Nv(e,t,n){return"position"===e||"preserve-aspect"===e&&!function(e,t,n){return Math.abs(e-t)<=n}(lv(t),lv(n),.2)}function Bv(e){var t;return e!==e.root&&(null===(t=e.scroll)||void 0===t?void 0:t.wasRoot)}var zv=yv({attachResizeListener:function(e,t){return Jf(e,"resize",t)},measureScroll:function(){return{x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}},checkIsScrollRoot:function(){return!0}}),Wv={current:void 0},Uv=yv({measureScroll:function(e){return{x:e.scrollLeft,y:e.scrollTop}},defaultParent:function(){if(!Wv.current){var e=new zv({});e.mount(window),e.setOptions({layoutScroll:!0}),Wv.current=e}return Wv.current},resetTransform:function(e,t){e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:function(e){return"fixed"===window.getComputedStyle(e).position}}),qv={pan:{Feature:Td},drag:{Feature:Pd,ProjectionNode:Uv,MeasureLayout:Vd}};function Hv(e,t){var n=$a(e),r=new AbortController;return[n,be(be({passive:!0},t),{},{signal:r.signal}),function(){return r.abort()}]}function Kv(e){return!("touch"===e.pointerType||Zf())}function Xv(e,t,n){var r=e.props;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);var i=r["onHover"+n];i&&mu.postRender((function(){return i(t,ed(t))}))}var _v=function(e){function t(){return le(this,t),ie(this,t,arguments)}return ue(t,e),fe(t,[{key:"mount",value:function(){var e=this,t=this.node.current;t&&(this.unmount=function(e,t){var n=ke(Hv(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}),3),r=n[0],i=n[1],o=n[2],a=function(e){if(Kv(e)){var n=e.target,r=t(n,e);if("function"==typeof r&&n){var o=function(e){Kv(e)&&(r(e),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)}}};return r.forEach((function(e){e.addEventListener("pointerenter",a,i)})),o}(t,(function(t,n){return Xv(e.node,n,"Start"),function(t){return Xv(e.node,t,"End")}})))}},{key:"unmount",value:function(){}}])}(Kf),Yv=function(e){function t(){var e;return le(this,t),(e=ie(this,t,arguments)).isActive=!1,e}return ue(t,e),fe(t,[{key:"onFocus",value:function(){var e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}},{key:"onBlur",value:function(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}},{key:"mount",value:function(){var e=this;this.unmount=Vl(Jf(this.node.current,"focus",(function(){return e.onFocus()})),Jf(this.node.current,"blur",(function(){return e.onBlur()})))}},{key:"unmount",value:function(){}}])}(Kf);function Gv(e,t){var n="".concat(t,"PointerCapture");if(e.target instanceof Element&&n in e.target&&void 0!==e.pointerId)try{e.target[n](e.pointerId)}catch(r){}}var $v=function(e,t){return!!t&&(e===t||$v(e,t.parentElement))},Zv=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);var Jv=new WeakSet;function Qv(e){return function(t){"Enter"===t.key&&e(t)}}function eh(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function th(e){return Qf(e)&&!Zf()}function nh(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=ke(Hv(e,n),3),i=r[0],o=r[1],a=r[2],u=function(e){var n=e.currentTarget;if(n&&th(e)&&!Jv.has(n)){Jv.add(n),Gv(e,"set");var r=t(n,e),i=function(e,t){n.removeEventListener("pointerup",a),n.removeEventListener("pointercancel",u),Gv(e,"release"),th(e)&&Jv.has(n)&&(Jv.delete(n),"function"==typeof r&&r(e,{success:t}))},a=function(e){e.isTrusted&&function(e,t){return e.clientX<t.left||e.clientX>t.right||e.clientY<t.top||e.clientY>t.bottom}(e,n instanceof Element?n.getBoundingClientRect():{left:0,top:0,right:window.innerWidth,bottom:window.innerHeight})?i(e,!1):i(e,!(n instanceof Element)||$v(n,e.target))},u=function(e){i(e,!1)};n.addEventListener("pointerup",a,o),n.addEventListener("pointercancel",u,o),n.addEventListener("lostpointercapture",u,o)}};return i.forEach((function(e){var t=!1;(e=n.useGlobalTarget?window:e)instanceof HTMLElement&&(t=!0,!function(e){return Zv.has(e.tagName)||-1!==e.tabIndex}(e)&&null===e.getAttribute("tabindex")&&(e.tabIndex=0)),e.addEventListener("pointerdown",u,o),t&&e.addEventListener("focus",(function(e){return function(e,t){var n=e.currentTarget;if(n){var r=Qv((function(){if(!Jv.has(n)){eh(n,"down");var e=Qv((function(){eh(n,"up")}));n.addEventListener("keyup",e,t),n.addEventListener("blur",(function(){return eh(n,"cancel")}),t)}}));n.addEventListener("keydown",r,t),n.addEventListener("blur",(function(){return n.removeEventListener("keydown",r)}),t)}}(e,o)}),o)})),a}function rh(e,t,n){var r=e.props;if(!(e.current instanceof HTMLButtonElement&&e.current.disabled)){e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);var i=r["onTap"+("End"===n?"":n)];i&&mu.postRender((function(){return i(t,ed(t))}))}}var ih=function(e){function t(){return le(this,t),ie(this,t,arguments)}return ue(t,e),fe(t,[{key:"mount",value:function(){var e=this,t=this.node.current;t&&(this.unmount=nh(t,(function(t,n){return rh(e.node,n,"Start"),function(t,n){var r=n.success;return rh(e.node,t,r?"End":"Cancel")}}),{useGlobalTarget:this.node.props.globalTapTarget}))}},{key:"unmount",value:function(){}}])}(Kf),oh=new WeakMap,ah=new WeakMap,uh=function(e){var t=oh.get(e.target);t&&t(e)},sh=function(e){e.forEach(uh)};function lh(e,t,n){var r=function(e){var t=e.root,n=ye(e,j),r=t||document;ah.has(r)||ah.set(r,{});var i=ah.get(r),o=JSON.stringify(n);return i[o]||(i[o]=new IntersectionObserver(sh,be({root:t},n))),i[o]}(t);return oh.set(e,n),r.observe(e),function(){oh.delete(e),r.unobserve(e)}}var ch={some:0,all:1},fh=function(e){function t(){var e;return le(this,t),(e=ie(this,t,arguments)).hasEnteredView=!1,e.isInView=!1,e}return ue(t,e),fe(t,[{key:"startObserver",value:function(){var e=this;this.unmount();var t=this.node.getProps().viewport,n=void 0===t?{}:t,r=n.root,i=n.margin,o=n.amount,a=void 0===o?"some":o,u=n.once,s={root:r?r.current:void 0,rootMargin:i,threshold:"number"==typeof a?a:ch[a]};return lh(this.node.current,s,(function(t){var n=t.isIntersecting;if(e.isInView!==n&&(e.isInView=n,!u||n||!e.hasEnteredView)){n&&(e.hasEnteredView=!0),e.node.animationState&&e.node.animationState.setActive("whileInView",n);var r=e.node.getProps(),i=r.onViewportEnter,o=r.onViewportLeave,a=n?i:o;a&&a(t)}}))}},{key:"mount",value:function(){this.startObserver()}},{key:"update",value:function(){if(!(("undefined"==typeof IntersectionObserver?"undefined":Ae(IntersectionObserver))>"u")){var e=this.node,t=e.props,n=e.prevProps;["amount","margin","root"].some(function(e){var t=e.viewport,n=void 0===t?{}:t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=r.viewport,o=void 0===i?{}:i;return function(e){return n[e]!==o[e]}}(t,n))&&this.startObserver()}}},{key:"unmount",value:function(){}}])}(Kf);var dh={inView:{Feature:fh},tap:{Feature:ih},focus:{Feature:Yv},hover:{Feature:_v}},vh={layout:{ProjectionNode:Uv,MeasureLayout:Vd}},hh=ce.createContext({strict:!1}),ph=ce.createContext({});function mh(e){var t=function(e,t){if(Pc(e)){var n=e.initial,r=e.animate;return{initial:!1===n||Ec(n)?n:void 0,animate:Ec(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,ce.useContext(ph)),n=t.initial,r=t.animate;return ce.useMemo((function(){return{initial:n,animate:r}}),[yh(n),yh(r)])}function yh(e){return Array.isArray(e)?e.join(" "):e}var gh=Symbol.for("motionComponentSymbol");function bh(e,t,n){return ce.useCallback((function(r){r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&("function"==typeof n?n(r):cd(n)&&(n.current=r))}),[t])}function xh(e,t,n,r,i){var o,a,u=ce.useContext(ph).visualElement,s=ce.useContext(hh),l=ce.useContext(Ff),c=ce.useContext(Ef).reducedMotion,f=ce.useRef(null);r=r||s.renderer,!f.current&&r&&(f.current=r(e,{visualState:t,parent:u,props:n,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:c}));var d=f.current,v=ce.useContext(Md);d&&!d.projection&&i&&("html"===d.type||"svg"===d.type)&&function(e,t,n,r){var i=t.layoutId,o=t.layout,a=t.drag,u=t.dragConstraints,s=t.layoutScroll,l=t.layoutRoot;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:wh(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!a||u&&cd(u),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,layoutScroll:s,layoutRoot:l})}(f.current,n,i,v);var h=ce.useRef(!1);ce.useInsertionEffect((function(){d&&h.current&&d.update(n,l)}));var p=n[Vu],m=ce.useRef(!!p&&!(null!==(o=window.MotionHandoffIsComplete)&&void 0!==o&&o.call(window,p))&&(null===(a=window.MotionHasOptimisedAnimation)||void 0===a?void 0:a.call(window,p)));return kf((function(){d&&(h.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),Rd.render(d.render),m.current&&d.animationState&&d.animationState.animateChanges())})),ce.useEffect((function(){d&&(!m.current&&d.animationState&&d.animationState.animateChanges(),m.current&&(queueMicrotask((function(){var e;null===(e=window.MotionHandoffMarkAsComplete)||void 0===e||e.call(window,p)})),m.current=!1))})),d}function wh(e){if(e)return!1!==e.options.allowProjection?e.projection:wh(e.parent)}function kh(e){var t,n,r=e.preloadedFeatures,i=e.createVisualElement,o=e.useRender,a=e.useVisualState,u=e.Component;function s(e,t){var n,r=be(be(be({},ce.useContext(Ef)),e),{},{layoutId:Fh(e)}),s=r.isStatic,l=mh(e),c=a(e,s);if(!s&&oa){ce.useContext(hh).strict;var f=function(e){var t=bc.drag,n=bc.layout;if(!t&&!n)return{};var r=be(be({},t),n);return{MeasureLayout:null!=t&&t.isEnabled(e)||null!=n&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(r);n=f.MeasureLayout,l.visualElement=xh(u,c,r,i,f.ProjectionNode)}return ae.jsxs(ph.Provider,{value:l,children:[n&&l.visualElement?ae.jsx(n,be({visualElement:l.visualElement},r)):null,o(u,e,bh(c,l.visualElement,t),c,s,l.visualElement)]})}r&&function(e){for(var t in e)bc[t]=be(be({},bc[t]),e[t])}(r),s.displayName="motion.".concat("string"==typeof u?u:"create(".concat(null!==(n=null!==(t=u.displayName)&&void 0!==t?t:u.name)&&void 0!==n?n:"",")"));var l=ce.forwardRef(s);return l[gh]=u,l}function Fh(e){var t=e.layoutId,n=ce.useContext(xf).id;return n&&void 0!==t?n+"-"+t:t}var Eh=function(){return{style:{},transform:{},transformOrigin:{},vars:{}}};function Ah(e,t,n){for(var r in t)!Ga(t[r])&&!Xc(r,n)&&(e[r]=t[r])}function Sh(e,t){var n={};return Ah(n,e.style||{},e),Object.assign(n,function(e,t){var n=e.transformTemplate;return ce.useMemo((function(){var e={style:{},transform:{},transformOrigin:{},vars:{}};return Oc(e,t,n),Object.assign({},e.vars,e.style)}),[t])}(e,t)),n}function Ph(e,t){var n={},r=Sh(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":"pan-".concat("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}var Ch=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Th(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Ch.has(e)}var Rh=function(e){return!Th(e)};try{!function(e){e&&(Rh=function(t){return t.startsWith("on")?!Th(t):e(t)})}(require("@emotion/is-prop-valid").default)}catch(Hp){}var Mh=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Dh(e){return"string"==typeof e&&!e.includes("-")&&!!(Mh.indexOf(e)>-1||/[A-Z]/.test(e))}var jh=function(){return be(be({},{style:{},transform:{},transformOrigin:{},vars:{}}),{},{attrs:{}})};function Ih(e,t,n,r){var i=ce.useMemo((function(){var n=jh();return Bc(n,t,Wc(r),e.transformTemplate),be(be({},n.attrs),{},{style:be({},n.style)})}),[t]);if(e.style){var o={};Ah(o,e.style,e),i.style=be(be({},o),i.style)}return i}function Oh(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(t,n,r,i,o){var a=i.latestValues,u=(Dh(t)?Ih:Ph)(n,a,o,t),s=function(e,t,n){var r={};for(var i in e)"values"===i&&"object"==Ae(e.values)||(Rh(i)||!0===n&&Th(i)||!t&&!Th(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==ce.Fragment?be(be(be({},s),u),{},{ref:r}):{},c=n.children,f=ce.useMemo((function(){return Ga(c)?c.get():c}),[c]);return ce.createElement(t,be(be({},l),{},{children:f}))}}var Lh=function(e){return function(t,n){var r=ce.useContext(ph),i=ce.useContext(Ff),o=function(){return function(e,t,n,r){var i=e.scrapeMotionValuesFromProps,o=e.createRenderState,a=e.onUpdate,u={latestValues:Vh(t,n,r,i),renderState:o()};return a&&(u.onMount=function(e){return a(be({props:t,current:e},u))},u.onUpdate=function(e){return a(e)}),u}(e,t,r,i)};return n?o():wf(o)}};function Vh(e,t,n,r){var i={},o=r(e,{});for(var a in o)i[a]=Wd(o[a]);var u=e.initial,s=e.animate,l=Pc(e),c=Cc(e);t&&c&&!l&&!1!==e.inherit&&(void 0===u&&(u=t.initial),void 0===s&&(s=t.animate));var f=!!n&&!1===n.initial,d=(f=f||!1===u)?s:u;if(d&&"boolean"!=typeof d&&!Fc(d))for(var v=Array.isArray(d)?d:[d],h=0;h<v.length;h++){var p=Du(e,v[h]);if(p){var m=p.transitionEnd,y=(p.transition,ye(p,I));for(var g in y){var b=y[g];if(Array.isArray(b))b=b[f?b.length-1:0];null!==b&&(i[g]=b)}for(var x in m)i[x]=m[x]}}return i}var Nh={useVisualState:Lh({scrapeMotionValuesFromProps:_c,createRenderState:Eh})},Bh=["x","y","width","height","cx","cy","r"],zh={useVisualState:Lh({scrapeMotionValuesFromProps:Yc,createRenderState:jh,onUpdate:function(e){var t=e.props,n=e.prevProps,r=e.current,i=e.renderState,o=e.latestValues;if(r){var a=!!t.drag;if(!a)for(var u in o)if(wu.has(u)){a=!0;break}if(a){var s=!n;if(n)for(var l=0;l<Bh.length;l++){var c=Bh[l];t[c]!==n[c]&&(s=!0)}s&&mu.read((function(){Uc(r,i),mu.render((function(){Bc(i,o,Wc(r.tagName),t.transformTemplate),Hc(r,i)}))}))}}}})};function Wh(e,t){return function(n){var r=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{forwardMotionProps:!1}).forwardMotionProps;return kh(be(be({},Dh(n)?zh:Nh),{},{preloadedFeatures:e,useRender:Oh(r),createVisualElement:t,Component:n}))}}var Uh=function(e,t){return Dh(e)?new Gc(t):new vf(t,{allowProjection:e!==ce.Fragment})},qh=jf(Wh(be(be(be(be({},Gf),dh),qv),vh),Uh)),Hh=ce.createContext(null),Kh=function(){var e=ce.use(Hh);if(!e)throw new Error("Drawer components must be wrapped in <Drawer />");return e},Xh={open:{"--transform":"0%","--backdrop-opacity":1,transition:{"--transform":{duration:.4,ease:ia["emphasized-decelerate"]},"--backdrop-opacity":{duration:.4}}},closed:{"--transform":"100%","--backdrop-opacity":0,transition:{"--transform":{duration:.25,ease:ia["emphasized-accelerate"]},"--backdrop-opacity":{duration:.4,delay:.1}}}};ne({W:function(e){var t=e.open,n=e.defaultOpen,r=e.onOpenChange,i=e.children,o=e.scrollLockTarget,a=Yo(r),u=ke(ce.useState(void 0),2),s=u[0],l=u[1],c=ke(ce.useState(null!=n&&n),2),f=c[0],d=c[1],v=null!=t?t:f,h=ce.useCallback((function(e){d(e),null==a||a(e)}),[a]);return ra(v,o),ae.jsx(Hh,{value:{open:v,setOpen:h,labelId:s,setLabelId:l},children:i})},Z:function(e){var t=e.children,n=e.asChild,r=ye(e,O),i=Kh().setOpen;return ae.jsx(n?Ut:"button",be(be({},r),{},{onClick:function(e){var t;null==(t=r.onClick)||t.call(r,e),e.defaultPrevented||i(!0)},children:t}))},_:function(e){var t=e.children,n=e.asChild,r=ye(e,L),i=Kh().setOpen;return ae.jsx(n?Ut:"button",be(be({},r),{},{onClick:function(e){var t;null==(t=r.onClick)||t.call(r,e),e.defaultPrevented||i(!1)},children:t}))},$:function(e){var t=e.children,n=e.persistExitAnimation,r=ye(e,V),i=(!ua.current&&sa(),ke(ce.useState(aa.current),1)[0]),o=Kh(),a=o.labelId,u=o.open,s=o.setOpen,l=ce.useRef(null),c=ce.useRef(!1);ce.useLayoutEffect((function(){var e=l.current;if(e){e.open||e.showModal();var t=function(e){e.preventDefault(),s(!1)};return e.addEventListener("cancel",t),function(){e.removeEventListener("cancel",t)}}}),[u,s]),ce.useEffect((function(){u&&(c.current=!1);var e=l.current;if(!i&&n&&e)return function(){var t=!!e&&!l.current,n=!c.current;if(t&&n){var r=e.cloneNode(!0);r.setAttribute("inert",""),r.setAttribute("tabindex","-1"),document.body.appendChild(r),r.close(),r.showModal(),r.focus(),bf(r,Xh.closed,Xh.closed.transition).then((function(){r.remove()}))}}}),[u,n,i]);return ae.jsx(Df,{initial:!1,onExitComplete:function(){return c.current=!0},children:u&&ae.jsxs(qh.dialog,be(be({},r),{},{ref:l,role:"dialog","aria-modal":"true","aria-labelledby":a,initial:"closed",animate:"open",exit:"closed",variants:i?{}:Xh,className:Ot("bg-background-high border-border mx-auto flex w-full max-w-screen flex-col overflow-y-auto border *:shrink-0","max-md:mt-auto max-md:max-h-[calc(100dvh-(--spacing(12)))] max-md:translate-y-(--transform) max-md:rounded-xl max-md:rounded-b-none max-md:border-b-0","md:mr-0 md:h-full md:max-h-screen md:max-w-160 md:translate-x-(--transform) md:border-0 md:border-l","backdrop:bg-[rgba(0,0,0,0.2)] backdrop:opacity-(--backdrop-opacity) backdrop:backdrop-blur-sm",!i&&"will-change-transform",r.className),onClick:function(e){if("DIALOG"===e.target.nodeName){var t=e.target.getBoundingClientRect(),n=t.top,r=t.left,i=t.width,o=t.height;(n>e.clientY||e.clientY>n+o||r>e.clientX||e.clientX>r+i)&&(s(!1),e.stopPropagation())}},children:[ae.jsx("div",{className:"safari:block sr-only hidden","aria-hidden":"true",autoFocus:!0,tabIndex:-1}),t]}))})},f:function(e){var t=e.children,n=ye(e,N),r=n.asChild?Ut:"header";return ae.jsx(r,be(be({},n),{},{className:Ot("bg-background-highlight border-border sticky top-0 z-10 border-b p-3 md:px-5 md:py-4",n.className),children:t}))},t:function(e){var t,n=e.children,r=ye(e,B),i=Kh().setLabelId,o=ce.useId(),a=null!==(t=r.id)&&void 0!==t?t:o;ce.useLayoutEffect((function(){return i(a),function(){return i(void 0)}}),[a,i]);var u=r.asChild?Ut:"p";return ae.jsx(u,be(be({},r),{},{id:a,className:Ot("text-sm md:text-base",r.className),children:n}))},g:function(e){var t=e.children,n=ye(e,z),r=n.asChild?Ut:"div";return ae.jsx(r,be(be({},n),{},{className:Ot("bg-background-highlight border-border sticky bottom-0 z-10 mt-auto flex gap-2 border-t p-3 pb-8 md:p-5 md:pb-5",n.className),children:t}))}}),ce.createContext({getIndex:function(){return 0},invalidate:function(){}}),ce.createContext({activeIndex:0,length:0,next:function(){},previous:function(){}}),Ot("ring-focus flex size-6 cursor-pointer items-center justify-center rounded-md opacity-60 outline-0 hover:opacity-100 focus-visible:ring-4");
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */
var _h=zt("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),Yh=function(e){return"object"==Ae(e)&&null!==e&&"children"in e};function Gh(e){return"string"==typeof e?e:"number"==typeof e?e.toString():Array.isArray(e)?e.map(Gh).join(""):ce.isValidElement(e)&&Yh(e.props)?Gh(e.props.children):""}var $h=ce.createContext(null),Zh=function(){var e=ce.use($h);if(null==e)throw new Error("useListboxContext must be used within a Listbox");return e},Jh=function(e){return"object"==Ae(e)&&null!==e&&"id"in e},Qh=function(e){return"object"!=Ae(e)&&null!==e},ep=function(e,t){return Jh(e)&&Jh(t)?e.id===t.id:Qh(e)&&Qh(t)?e===t:JSON.stringify(e)===JSON.stringify(t)},tp=function(e){var t=e.ref,n=e.children,r=e.placeholder,i=e.variant,o=e.className,a=ye(e,q);return ae.jsxs("button",be(be({ref:t,type:"button",className:Ot(Jo({variant:i}),"flex items-center gap-1.5 enabled:cursor-pointer","relative w-full pr-10 pl-4",o)},a),{},{children:[ae.jsx("span",{className:"flex flex-1 items-center gap-1.5 truncate text-left",children:null!=n?n:ae.jsx("span",{className:"text-foreground/40",children:r})}),ae.jsx(ea,{className:"text-foreground/80 absolute top-1/2 right-3 -translate-y-1/2 text-base"})]}))},np=function(e){var t=e.ref,n=e.value,r=e.children,i=e.disabled,o=e.className,a=e.withCheckmark,u=void 0===a||a,s=ye(e,K),l=Zh(),c=l.highlightedIndex,f=l.value,d=l.getIsSelected,v=l.getItemProps,h=l.handleSelect,p=Si({label:Gh(r)}),m=p.ref,y=p.index,g=ai([m,t]),b=c===y,x=Array.isArray(f)?f.some((function(e){return d(e,n)})):f&&d(f,n);return ae.jsxs("button",be(be({ref:g,role:"option","aria-selected":b&&x,"data-selected":x||void 0,"data-highlighted":b||void 0,tabIndex:b?0:-1,disabled:i||void 0,"data-disabled":i||void 0,className:Ot("data-highlighted:bg-foreground/5 text-foreground/80 relative mx-1 flex cursor-pointer items-center gap-1.5 rounded-lg px-4 py-2 text-left text-sm outline-none select-none first-of-type:mt-1 last-of-type:mb-1 data-disabled:pointer-events-none data-disabled:opacity-50",u&&"pr-8",o)},v(be(be({},s),{},{onClick:function(e){var t;h(y),null==(t=s.onClick)||t.call(s,e)}}))),{},{children:[r,x&&u&&ae.jsx(_h,{strokeWidth:2.5,className:"text-accent absolute top-1/2 right-3 -translate-y-1/2 text-sm"})]}))},rp=function(e){var t=e.ref,n=e.onKeyDown,r=e.onChange,i=ye(e,X),o=Zh(),a=o.highlightedIndex,u=o.setHighlightedIndex,s=o.handleSelect;return ae.jsx(Xo,be({ref:t,onKeyDown:function(e){"Enter"===e.key&&(e.preventDefault(),s(a)),null==n||n(e)},onChange:function(e){null==r||r(e),u(0)}},i))};ne({o:function(e){var t=e.children,n=e.ref,r=function(e){var t=e.value,n=e.onChange,r=e.disabled,i=e.invalid,o=e.placement,a=void 0===o?"bottom":o,u=e.getIsSelected,s=void 0===u?ep:u,l=e.matchReferenceWidth,c=void 0===l||l,f=ke(ce.useState(!1),2),d=f[0],v=f[1],h=ke(ce.useState(!1),2),p=h[0],m=h[1],y=ke(ce.useState(null),2),g=y[0],b=y[1],x=ke(ce.useState([]),2),w=x[0],k=x[1],F=ce.useRef([]),E=ce.useMemo((function(){return t?w.findIndex((function(e){return s(e.value,t)})):-1}),[w,t,s]),A=Ao({placement:a,open:d,onOpenChange:v,whileElementsMounted:qr,middleware:[ri({padding:4}),ni({padding:4}),ti(4),ii({apply:function(e){var t=e.rects,n=e.elements,r=e.availableHeight;n.floating.style.setProperty("--listbox-max-height","".concat(r,"px")),c&&n.floating.style.setProperty("--listbox-width","".concat(t.reference.width,"px"))},padding:4})]}),S=ce.useCallback((function(e){if(null!==e&&w[e]){if(Array.isArray(t)){var r=t.some((function(t){return s(w[e].value,t)}));return n(r?t.filter((function(t){return!s(w[e].value,t)})):[].concat(me(t),[w[e].value]))}n(w[e].value),v(!1)}}),[n,w,t,s]),P=Oo(A.context,{listRef:F,activeIndex:g,selectedIndex:E,onNavigate:b,virtual:p||void 0,loop:p||void 0}),C=ce.useCallback((function(e){d?b(e):S(e)}),[d,S]),T=ce.useRef([]);ce.useEffect((function(){T.current=w.map((function(e){return e.label}))}),[w]);var R=To([P,Bo(A.context,{enabled:!(p&&d),listRef:T,activeIndex:g,selectedIndex:E,onMatch:C}),bo(A.context,{enabled:!r,event:"mousedown"}),Fo(A.context),Vo(A.context,{role:"listbox"})]);return ce.useMemo((function(){return be(be({elementsRef:F,labelsRef:T,highlightedIndex:g,setHighlightedIndex:b,value:t,invalid:i,disabled:r,setOptions:k,handleSelect:S,setIsSearchable:m,getIsSelected:s},R),A)}),[g,t,i,r,k,S,s,R,A])}(ye(e,W));return ce.useImperativeHandle(n,(function(){return r})),ae.jsx($h,{value:r,children:t})},p:function(e){var t=e.ref,n=e.id,r=e.children,i=e.className,o=e.variant,a=e.placeholder,u=ye(e,U),s=Zh(),l=$t(),c=s.invalid||(null==l?void 0:l["aria-errormessage"])||void 0,f=ai([s.refs.setReference,t]);return ae.jsx(tp,be(be({ref:f,placeholder:a,variant:o,className:i,disabled:s.disabled,"data-state":s.context.open?"open":"closed","data-invalid":c,id:null!=n?n:null==l?void 0:l.id,"aria-describedby":null==l?void 0:l["aria-describedby"],"aria-labelledby":null==l?void 0:l["aria-labelledby"]},s.getReferenceProps(u)),{},{children:r}))},q:function(e){var t=e.ref,n=e.children,r=e.className,i=e.style,o=ye(e,H),a=Zh(),u=a.setOptions,s=a.setIsSearchable,l=a.refs,c=a.elementsRef,f=a.labelsRef,d=a.context,v=a.floatingStyles,h=a.getFloatingProps;ce.useEffect((function(){var e=function(t){return ce.Children.toArray(t).reduce((function(t,n){return ce.isValidElement(n)&&(n.type===np&&function(e){return"object"==Ae(e)&&null!==e&&"value"in e}(n.props)?t.push({value:n.props.value,label:Gh(n.props.children),disabled:n.props.disabled}):Yh(n.props)&&t.push.apply(t,me(e(n.props.children)))),t}),[])};u(e(n))}),[n,u]),ce.useEffect((function(){ce.Children.toArray(n).some((function(e){return ce.isValidElement(e)&&e.type===rp}))&&s(!0)}),[n,s]);var p=ai([l.setFloating,t]);return d.open?ae.jsx(zo,{context:d,children:ae.jsx(mo,{context:d,children:ae.jsx("div",be(be({ref:p,"data-state":d.open?"open":"closed",className:Ot("border-border-soft bg-background-highlight text-foreground z-50 flex flex-col items-stretch rounded-xl border p-0 focus:outline-none","overflow-y-auto overscroll-contain","max-h-(--listbox-max-height) w-(--listbox-width)",r),style:be(be({},v),i)},h(be({},o))),{},{children:ae.jsx(Ai,{elementsRef:c,labelsRef:f,children:n})}))})}):null},N:np});
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */
var ip=zt("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),op=zt("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),ap=zt("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),up=ce.createContext(null),sp=function(){var e=ce.use(up);if(!e)throw new Error("useQuantitySelectorContext needs to be used within a QuantitySelector");return e};
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */ne({a:function(e){var t=e.quantity,n=e.onChange,r=e.onDelete,i=e.min,o=void 0===i?1:i,a=e.max,u=void 0===a?1/0:a,s=e.children,l=e.className,c=e.size,f=ye(e,_),d=ce.useReducer((function(e,t){return Math.min(Math.max(t,o),u)}),null!=t?t:o),v=ke(d,2),h=v[0],p=v[1],m=void 0!==t,y=m?t:h,g=ce.useCallback((function(e){m?null==n||n(e):p(e)}),[m,n]),b=ce.useMemo((function(){return{quantity:y,onChange:g,onDelete:r,min:o,max:u,size:c}}),[y,g,r,o,u,c]);return ae.jsx(up.Provider,{value:b,children:ae.jsx("div",be(be({className:Ot("flex items-center justify-between gap-2",l)},f),{},{children:s}))})},d:function(e){var t=e.onClick,n=e["aria-label"],r=void 0===n?"Decrease quantity":n,i=ye(e,Y),o=sp(),a=o.quantity,u=o.onChange,s=o.min,l=o.size;return ae.jsx(_t,be(be({"data-testid":"quantity-selector-decrease","aria-label":r,square:!0,disabled:a<=s,onClick:function(e){null==t||t(e),!e.defaultPrevented&&u(a-1)},size:l},i),{},{children:ae.jsx(ip,{})}))},G:function(e){var t=e["aria-label"],n=void 0===t?"Delete":t,r=ye(e,G),i=sp().size;return ae.jsx(_t,be(be({"data-testid":"quantity-selector-delete","aria-label":n,square:!0,size:i},r),{},{children:ae.jsx(op,{})}))},H:function(e){var t=e.onClick,n=e.ariaLabel,r=void 0===n?"Increase quantity":n,i=ye(e,$),o=sp(),a=o.quantity,u=o.onChange,s=o.max,l=o.size;return ae.jsx(_t,be(be({"data-testid":"quantity-selector-increase","aria-label":r,square:!0,disabled:a>=s,onClick:function(e){null==t||t(e),e.defaultPrevented||u(a+1)},size:l},i),{},{children:ae.jsx(ap,{})}))},J:function(e){var t=e.className,n=e.render,r=void 0===n?function(e){return e}:n,i=ye(e,Z),o=sp().quantity;return ae.jsx("span",be(be({className:Ot("min-w-[2.2ch] text-center tabular-nums select-none",t)},i),{},{children:r(o)}))}}),ce.createContext({}),ce.createContext({transformPagePoint:function(e){return e},isStatic:!1,reducedMotion:"never"});var lp=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"];new Set(lp);(function(e){e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()})("framerAppearId");var cp=function(e,t,n){return n>t?t:n<e?e:n},fp={test:function(e){return"number"==typeof e},parse:parseFloat,transform:function(e){return e}},dp=be(be({},fp),{},{transform:function(e){return cp(0,1,e)}});be({},fp);var vp=function(e){return Math.round(1e5*e)/1e5},hp=/-?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)/g;var pp=/^(?:#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\))$/i,mp=function(e,t){return function(n){return!!("string"==typeof n&&pp.test(n)&&n.startsWith(e)||t&&!function(e){return null==e}(n)&&Object.prototype.hasOwnProperty.call(n,t))}},yp=function(e,t,n){return function(r){if("string"!=typeof r)return r;var i=ke(r.match(hp),4),o=i[0],a=i[1],u=i[2],s=i[3];return xe(xe(xe(xe({},e,parseFloat(o)),t,parseFloat(a)),n,parseFloat(u)),"alpha",void 0!==s?parseFloat(s):1)}},gp=be(be({},fp),{},{transform:function(e){return Math.round(function(e){return cp(0,255,e)}(e))}}),bp={test:mp("rgb","red"),parse:yp("red","green","blue"),transform:function(e){var t=e.red,n=e.green,r=e.blue,i=e.alpha,o=void 0===i?1:i;return"rgba("+gp.transform(t)+", "+gp.transform(n)+", "+gp.transform(r)+", "+vp(dp.transform(o))+")"}};var xp={test:mp("#"),parse:function(e){var t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:bp.transform},wp=function(e){return{test:function(t){return"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length},parse:parseFloat,transform:function(t){return"".concat(t).concat(e)}}},kp=wp("%");be({},kp);var Fp={test:mp("hsl","hue"),parse:yp("hue","saturation","lightness"),transform:function(e){var t=e.hue,n=e.saturation,r=e.lightness,i=e.alpha,o=void 0===i?1:i;return"hsla("+Math.round(t)+", "+kp.transform(vp(n))+", "+kp.transform(vp(r))+", "+vp(dp.transform(o))+")"}},Ep=function(e){return bp.test(e)||xp.test(e)||Fp.test(e)},Ap=function(e){return bp.test(e)?bp.parse(e):Fp.test(e)?Fp.parse(e):xp.parse(e)},Sp=function(e){return"string"==typeof e?e:e.hasOwnProperty("red")?bp.transform(e):Fp.transform(e)},Pp=/(?:#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\))/gi;var Cp="number",Tp="color",Rp=/var[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\([\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*--(?:[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*|[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*,(?:[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:(?![\t-\r \(\)\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uD800-\uDFFF\uFEFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\((?:(?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|\((?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])*\))*\))+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)\)|#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\)|-?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)/gi;function Mp(e){var t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,a=t.replace(Rp,(function(e){return Ep(e)?(r.color.push(o),i.push(Tp),n.push(Ap(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(Cp),n.push(parseFloat(e))),++o,"${}"})).split("${}");return{values:n,split:a,indexes:r,types:i}}function Dp(e){return Mp(e).values}function jp(e){var t=Mp(e),n=t.split,r=t.types,i=n.length;return function(e){for(var t="",o=0;o<i;o++)if(t+=n[o],void 0!==e[o]){var a=r[o];t+=a===Cp?vp(e[o]):a===Tp?Sp(e[o]):e[o]}return t}}var Ip=function(e){return"number"==typeof e?0:e};var Op={test:function(e){var t,n;return isNaN(e)&&"string"==typeof e&&((null===(t=e.match(hp))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(Pp))||void 0===n?void 0:n.length)||0)>0},parse:Dp,createTransformer:jp,getAnimatableNone:function(e){var t=Dp(e);return jp(e)(t.map(Ip))}};be({},Op),be({},fp);var Lp=new Set(["x","y","z"]);lp.filter((function(e){return!Lp.has(e)})),ce.createContext({});var Vp=function(e){return("undefined"==typeof navigator?"undefined":Ae(navigator))<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e)};Vp("applewebkit/")&&!Vp("chrome/")&&Math.round,ce.createContext({strict:!1});try{require("@emotion/is-prop-valid").default}catch(Kp){}
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */zt("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);zt("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);zt("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var Np=It({base:["relative h-10 w-fit rounded-xl px-3.5","flex items-center justify-center gap-2.5","text-xs leading-none text-(--button-text-color) [--button-text-color:var(--color-foreground)]","[&_.lucide]:stroke-1 [&_.lucide]:text-xl","focus-visible:ring-focus focus:outline-none focus-visible:ring-4"],variants:{square:{true:"w-12 px-0",false:""}}}),Bp=ne("F",(function(e){var t=e.children,n=e.className,r=e.asChild,i=void 0!==r&&r,o=e.isLoading,a=void 0!==o&&o,u=e.square,s=void 0!==u&&u,l=e.ref,c=ye(e,J),f=i?Ut:"button";return ae.jsx(f,be(be({className:Ot(Np({square:s}),a&&"text-transparent",n),ref:l},c),{},{children:ae.jsx(qt,{child:t,asChild:i,children:function(e){return ae.jsxs(ae.Fragment,{children:[e,a&&ae.jsx("span",{"data-button-spinner":!0,className:Ot("absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2","text-(--button-text-color)"),children:ae.jsx(Kt,{})})]})}})}))}));try{Bp.displayName="FlatButton",Bp.__docgenInfo={description:"",displayName:"FlatButton",props:{asChild:{defaultValue:{value:"false"},description:"",name:"asChild",required:!1,type:{name:"boolean | undefined"}},square:{defaultValue:{value:"false"},description:"",name:"square",required:!1,type:{name:"boolean | undefined"}},isLoading:{defaultValue:{value:"false"},description:"",name:"isLoading",required:!1,type:{name:"boolean | undefined"}}}}}catch(Xp){}
/**
       * @license lucide-react v0.476.0 - ISC
       *
       * This source code is licensed under the ISC license.
       * See the LICENSE file in the root directory of this source tree.
       */var zp=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((function(e,t,n){return Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t})).join(" ").trim()},Wp={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},Up=ce.forwardRef((function(e,t){var n=e.color,r=void 0===n?"currentColor":n,i=e.size,o=void 0===i?24:i,a=e.strokeWidth,u=void 0===a?2:a,s=e.absoluteStrokeWidth,l=e.className,c=void 0===l?"":l,f=e.children,d=e.iconNode,v=ye(e,Q);return ce.createElement("svg",be(be({ref:t},Wp),{},{width:o,height:o,stroke:r,strokeWidth:s?24*Number(u)/Number(o):u,className:zp("lucide",c)},v),[].concat(me(d.map((function(e){var t=ke(e,2),n=t[0],r=t[1];return ce.createElement(n,r)}))),me(Array.isArray(f)?f:[f])))})),qp=ne("c",(function(e,t){var n=ce.forwardRef((function(n,r){var i,o=n.className,a=ye(n,ee);return ce.createElement(Up,be({ref:r,iconNode:t,className:zp("lucide-".concat((i=e,i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase())),o)},a))}));return n.displayName="".concat(e),n}));ne("M",qp("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])),ne("X",qp("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]))}}}))}();

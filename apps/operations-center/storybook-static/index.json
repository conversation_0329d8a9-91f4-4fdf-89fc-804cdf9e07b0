{"v": 5, "entries": {"ui-flatbutton--docs": {"id": "ui-flatbutton--docs", "title": "UI/FlatButton", "name": "Docs", "importPath": "./src/components/ui/flat-button.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "ui-flatbutton--default": {"type": "story", "id": "ui-flatbutton--default", "name": "<PERSON><PERSON><PERSON>", "title": "UI/FlatButton", "importPath": "./src/components/ui/flat-button.stories.tsx", "componentPath": "./src/components/ui/flat-button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-flatbutton--with-text": {"type": "story", "id": "ui-flatbutton--with-text", "name": "With Text", "title": "UI/FlatButton", "importPath": "./src/components/ui/flat-button.stories.tsx", "componentPath": "./src/components/ui/flat-button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-flatbutton--with-icon": {"type": "story", "id": "ui-flatbutton--with-icon", "name": "With Icon", "title": "UI/FlatButton", "importPath": "./src/components/ui/flat-button.stories.tsx", "componentPath": "./src/components/ui/flat-button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-flatbutton--with-icon-and-text": {"type": "story", "id": "ui-flatbutton--with-icon-and-text", "name": "With Icon And Text", "title": "UI/FlatButton", "importPath": "./src/components/ui/flat-button.stories.tsx", "componentPath": "./src/components/ui/flat-button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-flatbutton--square": {"type": "story", "id": "ui-flatbutton--square", "name": "Square", "title": "UI/FlatButton", "importPath": "./src/components/ui/flat-button.stories.tsx", "componentPath": "./src/components/ui/flat-button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-flatbutton--loading": {"type": "story", "id": "ui-flatbutton--loading", "name": "Loading", "title": "UI/FlatButton", "importPath": "./src/components/ui/flat-button.stories.tsx", "componentPath": "./src/components/ui/flat-button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-flatbutton--loading-square": {"type": "story", "id": "ui-flatbutton--loading-square", "name": "Loading Square", "title": "UI/FlatButton", "importPath": "./src/components/ui/flat-button.stories.tsx", "componentPath": "./src/components/ui/flat-button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-flatbutton--disabled": {"type": "story", "id": "ui-flatbutton--disabled", "name": "Disabled", "title": "UI/FlatButton", "importPath": "./src/components/ui/flat-button.stories.tsx", "componentPath": "./src/components/ui/flat-button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-flatbutton--custom-style": {"type": "story", "id": "ui-flatbutton--custom-style", "name": "Custom Style", "title": "UI/FlatButton", "importPath": "./src/components/ui/flat-button.stories.tsx", "componentPath": "./src/components/ui/flat-button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-flatbutton--all-variants": {"type": "story", "id": "ui-flatbutton--all-variants", "name": "All Variants", "title": "UI/FlatButton", "importPath": "./src/components/ui/flat-button.stories.tsx", "componentPath": "./src/components/ui/flat-button.tsx", "tags": ["dev", "test", "autodocs"]}}}
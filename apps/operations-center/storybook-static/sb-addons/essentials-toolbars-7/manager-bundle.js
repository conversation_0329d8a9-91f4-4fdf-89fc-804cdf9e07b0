try{
(()=>{var n=__REACT__,{Children:se,Component:ie,Fragment:ue,Profiler:ce,PureComponent:pe,StrictMode:me,Suspense:de,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:be,cloneElement:Se,createContext:_e,createElement:Te,createFactory:ye,createRef:Oe,forwardRef:ve,isValidElement:Ce,lazy:fe,memo:Ie,startTransition:Ee,unstable_act:xe,useCallback:v,useContext:ge,useDebugValue:ke,useDeferredValue:Ae,useEffect:g,useId:Re,useImperativeHandle:he,useInsertionEffect:Le,useLayoutEffect:Be,useMemo:Pe,useReducer:Me,useRef:L,useState:B,useSyncExternalStore:Ne,useTransition:De,version:Ve}=__REACT__;var Ge=__STORYBOOK_API__,{ActiveTabs:Ue,Consumer:Ke,ManagerContext:Ye,Provider:$e,RequestResponseError:qe,addons:k,combineParameters:ze,controlOrMetaKey:je,controlOrMetaSymbol:Ze,eventMatchesShortcut:Je,eventToShortcut:Qe,experimental_MockUniversalStore:Xe,experimental_UniversalStore:et,experimental_requestResponse:tt,experimental_useUniversalStore:rt,isMacLike:ot,isShortcutTaken:at,keyToSymbol:nt,merge:lt,mockChannel:st,optionOrAltSymbol:it,shortcutMatchesShortcut:ut,shortcutToHumanString:ct,types:P,useAddonState:pt,useArgTypes:mt,useArgs:dt,useChannel:bt,useGlobalTypes:M,useGlobals:A,useParameter:St,useSharedState:_t,useStoryPrepared:Tt,useStorybookApi:N,useStorybookState:yt}=__STORYBOOK_API__;var It=__STORYBOOK_COMPONENTS__,{A:Et,ActionBar:xt,AddonPanel:gt,Badge:kt,Bar:At,Blockquote:Rt,Button:ht,ClipboardCode:Lt,Code:Bt,DL:Pt,Div:Mt,DocumentWrapper:Nt,EmptyTabContent:Dt,ErrorFormatter:Vt,FlexBar:wt,Form:Ht,H1:Wt,H2:Ft,H3:Gt,H4:Ut,H5:Kt,H6:Yt,HR:$t,IconButton:D,IconButtonSkeleton:qt,Icons:R,Img:zt,LI:jt,Link:Zt,ListItem:Jt,Loader:Qt,Modal:Xt,OL:er,P:tr,Placeholder:rr,Pre:or,ProgressSpinner:ar,ResetWrapper:nr,ScrollArea:lr,Separator:V,Spaced:sr,Span:ir,StorybookIcon:ur,StorybookLogo:cr,Symbols:pr,SyntaxHighlighter:mr,TT:dr,TabBar:br,TabButton:Sr,TabWrapper:_r,Table:Tr,Tabs:yr,TabsState:Or,TooltipLinkList:w,TooltipMessage:vr,TooltipNote:Cr,UL:fr,WithTooltip:H,WithTooltipPure:Ir,Zoom:Er,codeCommon:xr,components:gr,createCopyToClipboardFunction:kr,getStoryHref:Ar,icons:Rr,interleaveSeparators:hr,nameSpaceClassNames:Lr,resetComponents:Br,withReset:Pr}=__STORYBOOK_COMPONENTS__;var U={type:"item",value:""},K=(r,t)=>({...t,name:t.name||r,description:t.description||r,toolbar:{...t.toolbar,items:t.toolbar.items.map(e=>{let o=typeof e=="string"?{value:e,title:e}:e;return o.type==="reset"&&t.toolbar.icon&&(o.icon=t.toolbar.icon,o.hideIcon=!0),{...U,...o}})}}),Y=["reset"],$=r=>r.filter(t=>!Y.includes(t.type)).map(t=>t.value),S="addon-toolbars",q=async(r,t,e)=>{e&&e.next&&await r.setAddonShortcut(S,{label:e.next.label,defaultShortcut:e.next.keys,actionName:`${t}:next`,action:e.next.action}),e&&e.previous&&await r.setAddonShortcut(S,{label:e.previous.label,defaultShortcut:e.previous.keys,actionName:`${t}:previous`,action:e.previous.action}),e&&e.reset&&await r.setAddonShortcut(S,{label:e.reset.label,defaultShortcut:e.reset.keys,actionName:`${t}:reset`,action:e.reset.action})},z=r=>t=>{let{id:e,toolbar:{items:o,shortcuts:a}}=t,c=N(),[_,i]=A(),l=L([]),u=_[e],C=v(()=>{i({[e]:""})},[i]),f=v(()=>{let s=l.current,m=s.indexOf(u),d=m===s.length-1?0:m+1,p=l.current[d];i({[e]:p})},[l,u,i]),I=v(()=>{let s=l.current,m=s.indexOf(u),d=m>-1?m:0,p=d===0?s.length-1:d-1,b=l.current[p];i({[e]:b})},[l,u,i]);return g(()=>{a&&q(c,e,{next:{...a.next,action:f},previous:{...a.previous,action:I},reset:{...a.reset,action:C}})},[c,e,a,f,I,C]),g(()=>{l.current=$(o)},[]),n.createElement(r,{cycleValues:l.current,...t})},W=({currentValue:r,items:t})=>r!=null&&t.find(e=>e.value===r&&e.type!=="reset"),j=({currentValue:r,items:t})=>{let e=W({currentValue:r,items:t});if(e)return e.icon},Z=({currentValue:r,items:t})=>{let e=W({currentValue:r,items:t});if(e)return e.title},J=({active:r,disabled:t,title:e,icon:o,description:a,onClick:c})=>n.createElement(D,{active:r,title:a,disabled:t,onClick:t?()=>{}:c},o&&n.createElement(R,{icon:o,__suppressDeprecationWarning:!0}),e?`\xA0${e}`:null),Q=({right:r,title:t,value:e,icon:o,hideIcon:a,onClick:c,disabled:_,currentValue:i})=>{let l=o&&n.createElement(R,{style:{opacity:1},icon:o,__suppressDeprecationWarning:!0}),u={id:e??"_reset",active:i===e,right:r,title:t,disabled:_,onClick:c};return o&&!a&&(u.icon=l),u},X=z(({id:r,name:t,description:e,toolbar:{icon:o,items:a,title:c,preventDynamicIcon:_,dynamicTitle:i}})=>{let[l,u,C]=A(),[f,I]=B(!1),s=l[r],m=!!s,d=r in C,p=o,b=c;_||(p=j({currentValue:s,items:a})||p),i&&(b=Z({currentValue:s,items:a})||b),!b&&!p&&console.warn(`Toolbar '${t}' has no title or icon`);let F=v(x=>{u({[r]:x})},[r,u]);return n.createElement(H,{placement:"top",tooltip:({onHide:x})=>{let G=a.filter(({type:E})=>{let h=!0;return E==="reset"&&!s&&(h=!1),h}).map(E=>Q({...E,currentValue:s,disabled:d,onClick:()=>{F(E.value),x()}}));return n.createElement(w,{links:G})},closeOnOutsideClick:!0,onVisibleChange:I},n.createElement(J,{active:f||m,disabled:d,description:e||"",icon:p,title:b||""}))}),ee=()=>{let r=M(),t=Object.keys(r).filter(e=>!!r[e].toolbar);return t.length?n.createElement(n.Fragment,null,n.createElement(V,null),t.map(e=>{let o=K(e,r[e]);return n.createElement(X,{key:e,id:e,...o})})):null};k.register(S,()=>k.add(S,{title:S,type:P.TOOL,match:({tabId:r})=>!r,render:()=>n.createElement(ee,null)}));})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }

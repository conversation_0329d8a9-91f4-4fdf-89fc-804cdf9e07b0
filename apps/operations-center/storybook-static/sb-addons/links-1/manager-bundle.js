try{
(()=>{var O=__STORYBOOK_API__,{ActiveTabs:d,Consumer:T,ManagerContext:y,Provider:A,RequestResponseError:h,addons:a,combineParameters:v,controlOrMetaKey:R,controlOrMetaSymbol:b,eventMatchesShortcut:k,eventToShortcut:I,experimental_MockUniversalStore:P,experimental_UniversalStore:g,experimental_requestResponse:x,experimental_useUniversalStore:C,isMacLike:M,isShortcutTaken:U,keyToSymbol:D,merge:N,mockChannel:B,optionOrAltSymbol:K,shortcutMatchesShortcut:V,shortcutToHumanString:f,types:q,useAddonState:G,useArgTypes:L,useArgs:Y,useChannel:$,useGlobalTypes:H,useGlobals:Q,useParameter:j,useSharedState:w,useStoryPrepared:z,useStorybookApi:F,useStorybookState:J}=__STORYBOOK_API__;var e="storybook/links",n={NAVIGATE:`${e}/navigate`,REQUEST:`${e}/request`,RECEIVE:`${e}/receive`};a.register(e,t=>{t.on(n.REQUEST,({kind:u,name:i})=>{let l=t.storyId(u,i);t.emit(n.RECEIVE,l)})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }

/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as RoutesImport } from './routes/routes'
import { Route as ReportsImport } from './routes/reports'
import { Route as IndexImport } from './routes/index'

// Create/Update Routes

const RoutesRoute = RoutesImport.update({
  id: '/routes',
  path: '/routes',
  getParentRoute: () => rootRoute,
} as any)

const ReportsRoute = ReportsImport.update({
  id: '/reports',
  path: '/reports',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/reports': {
      id: '/reports'
      path: '/reports'
      fullPath: '/reports'
      preLoaderRoute: typeof ReportsImport
      parentRoute: typeof rootRoute
    }
    '/routes': {
      id: '/routes'
      path: '/routes'
      fullPath: '/routes'
      preLoaderRoute: typeof RoutesImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/reports': typeof ReportsRoute
  '/routes': typeof RoutesRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/reports': typeof ReportsRoute
  '/routes': typeof RoutesRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/reports': typeof ReportsRoute
  '/routes': typeof RoutesRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/reports' | '/routes'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/reports' | '/routes'
  id: '__root__' | '/' | '/reports' | '/routes'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  ReportsRoute: typeof ReportsRoute
  RoutesRoute: typeof RoutesRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  ReportsRoute: ReportsRoute,
  RoutesRoute: RoutesRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/reports",
        "/routes"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/reports": {
      "filePath": "reports.tsx"
    },
    "/routes": {
      "filePath": "routes.tsx"
    }
  }
}
ROUTE_MANIFEST_END */

import {
  createRootRoute,
  createRoute,
  createRouter,
  lazyRouteComponent,
  Link,
  Outlet,
} from '@tanstack/react-router';
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools';
import React, { Suspense } from 'react';

const PARENT_ROUTE = createRootRoute({
  component: () => (
    <>
      <div className="p-2 flex gap-2 justify-center text-lg">
        <Link to="/" className="[&.active]:font-bold">
          Home
        </Link>{' '}
        {/* @ts-ignore */}
        <Link to="/reports" className="[&.active]:font-bold">
          Reports
        </Link>
        {/* @ts-ignore */}
        <Link to="/operations" className="[&.active]:font-bold">
          Operations
        </Link>
      </div>
      <hr />
      <Outlet />
      <TanStackRouterDevtools />
    </>
  ),
});

const MAIN_ROUTES = [
  {
    path: '/',
    name: 'index',
  },
  {
    path: '/reports',
    name: 'reports',
  },
];

export const MAIN_ROUTES_INSTANCES = MAIN_ROUTES.map((route) => {
  const Component = lazyRouteComponent(() => import(`./${route.name}.tsx`));

  return createRoute({
    getParentRoute: () => PARENT_ROUTE,
    path: route.path,
    component: Component,
  });
});

const MICROFRONTENDS = [
  {
    /* @ts-ignore */
    component: React.lazy(() => import(`operations/App`)),
    path: '/operations',
  },
];

const MICROFRONTENDS_ROUTES = MICROFRONTENDS.map((microfrontend) => {
  return createRoute({
    getParentRoute: () => PARENT_ROUTE,
    path: `${microfrontend.path}/*`,
    component: function () {
      return (
        <Suspense
          fallback={
            <div className="p-2 text-center">Loading {microfrontend.path}...</div>
          }
        >
          <microfrontend.component />
        </Suspense>
      );
    },
  });
});

const routeTree = PARENT_ROUTE.addChildren([
  ...MAIN_ROUTES_INSTANCES,
  ...MICROFRONTENDS_ROUTES,
]);

export const mainRouter = createRouter({ routeTree });
